name: Vercel Preview Deployment
env:
  VERCEL_ORG_ID: ${{ secrets.VERCEL_ORG_ID }}
  VERCEL_PROJECT_ID: ${{ secrets.VERCEL_PROJECT_ID }}
on:
  push:
    branches:
      - main
      - preview

jobs:
  Deploy-Preview:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Install Vercel CLI
        run: npm install --global vercel@latest
      - name: Pull Vercel Environment Information
        run: |
          if [ "${{ github.ref }}" = "refs/heads/main" ]; then
            vercel --prod --yes --token ${{ secrets.VERCEL_TOKEN }}
          else
            vercel --yes --token ${{ secrets.VERCEL_TOKEN }}
          fi
