{"format": "custom", "id": "footer", "label": "Footer", "repeatable": false, "status": true, "json": {"Main": {"phone_number": {"type": "Text", "config": {"label": "Phone Number", "placeholder": ""}}, "address": {"type": "Text", "config": {"label": "Address", "placeholder": ""}}, "email": {"type": "Text", "config": {"label": "Email", "placeholder": ""}}, "menu": {"type": "Group", "config": {"label": "<PERSON><PERSON>", "repeat": true, "fields": {"menu_item_name": {"type": "Text", "config": {"label": "<PERSON><PERSON>em Name", "placeholder": ""}}, "menu_item_link": {"type": "Link", "config": {"label": "<PERSON><PERSON>em <PERSON>", "placeholder": "", "select": null}}, "parent_menu_item_name": {"type": "Text", "config": {"label": "<PERSON><PERSON>u <PERSON>em Name", "placeholder": ""}}}}}, "linkedin_link": {"type": "Link", "config": {"label": "LinkedIn Link", "placeholder": "", "select": null}}, "facebook_link": {"type": "Link", "config": {"label": "Facebook Link", "placeholder": "", "select": null}}, "instagram_link": {"type": "Link", "config": {"label": "Instagram Link", "placeholder": "", "select": null}}, "youtube_link": {"type": "Link", "config": {"label": "YouTube Link", "placeholder": "", "select": null}}, "accreditations_title": {"type": "StructuredText", "config": {"label": "Accreditations title", "placeholder": "", "allowTargetBlank": true, "multi": "paragraph,preformatted,heading1,heading2,heading3,heading4,heading5,heading6,strong,em,hyperlink,image,embed,list-item,o-list-item,rtl"}}, "accreditations_items": {"type": "Group", "config": {"label": "Accreditations items", "repeat": true, "fields": {"accreditation_image": {"type": "Image", "config": {"label": "Accreditation image", "constraint": {}, "thumbnails": []}}, "accreditation_link": {"type": "Link", "config": {"label": "Accreditation link", "placeholder": "", "select": null}}}}}}}}