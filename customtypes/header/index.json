{"format": "custom", "id": "header", "label": "Header", "repeatable": false, "status": true, "json": {"Main": {"navigation_menu": {"type": "Group", "config": {"label": "Navigation Menu", "repeat": true, "fields": {"menu_item_name": {"type": "Text", "config": {"label": "<PERSON><PERSON>em Name", "placeholder": ""}}, "menu_item_link": {"type": "Link", "config": {"label": "<PERSON><PERSON>em <PERSON>", "placeholder": "", "select": null}}, "parent_menu_item_name": {"type": "Text", "config": {"label": "<PERSON><PERSON>u <PERSON>em Name", "placeholder": ""}}, "relate_to": {"type": "Link", "config": {"label": "Relate to", "select": "document"}}}}}, "phone_number": {"type": "Text", "config": {"label": "Phone Number", "placeholder": ""}}}}}