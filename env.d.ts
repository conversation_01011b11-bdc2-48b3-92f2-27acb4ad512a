export {};

declare global {
  namespace NodeJS {
    interface ProcessEnv {
      NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY: string;
      NEXT_PUBLIC_SUPABASE_KEY: string;
      NEXT_PUBLIC_SUPABASE_URL: string;
      SENDGRID_API_KEY: string;
      SENDGRID_MAILING_ID: string;
      SUPABASE_SERVICE_ROLE_KEY: string;
      PRISMIC_ACCESS_TOKEN: string;
      STRIPE_PUBLISHABLE_KEY: string;
      STRIPE_SECRET_KEY: string;
      GOCARDLESS_ACCESS_TOKEN: string;
      GOCARDLESS_WEBHOOK_ENDPOINT_SECRET: string;
      SUPABASE_SERVICE_ROLE_KEY: string;
      NEXT_PUBLIC_CURRENT_SITE_URL: string;
      NEXT_PUBLIC_GOOGLE_MAP_ID: string;
    }
  }
}
