import { slicesFetch } from "@/slices/fetch";
import { UpdateUserDto } from "@/utils/api/types/dto";
import { Content } from "@prismicio/client";
import { Plan } from "./plans";
import { BlogTagsDocument } from "prismicio-types";

export type UserType = UpdateUserDto & { email: string };

export type ServiceResponse<T = any> =
  | {
      data: T;
      error?: null;
    }
  | {
      data?: null;
      error: {
        message: string;
        details?: any;
      };
    };


export type PageMode = "commercial" | "residential";

export type GoogleReview = {
  author_name: string
  author_url: string
  language: "en"
  original_language: "en"
  profile_photo_url: string
  rating: number
  relative_time_description: string
  text: string
  time: number
  translated: boolean
}


export type SlicesFetchType = typeof slicesFetch


export type ArticlesType = {
  page: number,
  results_per_page: number,
  results_size: number,
  total_results_size: number,
  total_pages: number,
  next_page:string,
  prev_page:number | null,
  results: Content.BlogPageDocument<string>[],
  version: string,
  license: string
}


export type SlicesContextData = {
  plans_section: { plans: { data: Plan, error: Object | null } },
  peace_plan_subscription_benefits: { plans: { data: Plan, error: Object | null } },
  reviews_section: { reviews: GoogleReview[] },
  blog_section: { 
    articles: ArticlesType,
     tags:  BlogTagsDocument<string>[]
   }
}
