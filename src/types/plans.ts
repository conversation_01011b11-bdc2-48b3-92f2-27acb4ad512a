export interface Appliance {
  name: string
  recurring_interval: SubIntervalTypes
  unit_amount: number
  unit_amount_decimal: string
}

export type ApplianceTypes = "gas_cooker_hob" |
"gas_fire"|
"gas_oven"

export type PeacePlanTypes = "homeowner" | "landlord";
export type SubIntervalTypes = "year" | "month";
export type PlanTypes = "basic" | "standard" | "premium" | "peace"

export type PlanPrice = {
  id: string
active: boolean
currency: "gbp"
metadata: {
  plan_name: string
}
type: "recurring"
unit_amount: number,
unit_decimal: string,
product: {
  id: string
  name: string
  description: string | null
  active: boolean
  metadata: {
    plan_name: string
  }
}
recurring: {
  inverval: SubIntervalTypes
  interval_count: number
}
}

export type PlanAppliances = {
  "name": string,
  "recurring_interval":SubIntervalTypes,
  "unit_amount": number,
  "unit_amount_decimal": string
}
export interface Plan {
  prices: PlanPrice[]
  additionalAppliances: PlanAppliances[]
}

export type CustomerPlan = {
  

    cancel_at: number
    cancel_at_period_end: boolean
    canceled_at: number

    "id": string,
    "current_period_end": number,
    "current_period_start": number,
    "status": "active",
    "items": any
    // [
    //     {
    //         "id": "si_QAH26knzk9sSJt",
    //         "object": "subscription_item",
    //         "billing_thresholds": null,
    //         "created": 1716550838,
    //         "discounts": [],
    //         "metadata": {},
    //         "plan": {
    //             "id": "price_1P9nzwHmUxu9ZaPGVdqQChUH",
    //             "object": "plan",
    //             "active": true,
    //             "aggregate_usage": null,
    //             "amount": 12000,
    //             "amount_decimal": "12000",
    //             "billing_scheme": "per_unit",
    //             "created": **********,
    //             "currency": "gbp",
    //             "interval": "year",
    //             "interval_count": 1,
    //             "livemode": false,
    //             "metadata": {},
    //             "meter": null,
    //             "nickname": null,
    //             "product": "prod_Pz5N88bqOJa7sj",
    //             "tiers_mode": null,
    //             "transform_usage": null,
    //             "trial_period_days": null,
    //             "usage_type": "licensed"
    //         },
    //         "price": {
    //             "id": "price_1P9nzwHmUxu9ZaPGVdqQChUH",
    //             "object": "price",
    //             "active": true,
    //             "billing_scheme": "per_unit",
    //             "created": **********,
    //             "currency": "gbp",
    //             "custom_unit_amount": null,
    //             "livemode": false,
    //             "lookup_key": null,
    //             "metadata": {},
    //             "nickname": null,
    //             "product": "prod_Pz5N88bqOJa7sj",
    //             "recurring": {
    //                 "aggregate_usage": null,
    //                 "interval": "year",
    //                 "interval_count": 1,
    //                 "meter": null,
    //                 "trial_period_days": null,
    //                 "usage_type": "licensed"
    //             },
    //             "tax_behavior": "unspecified",
    //             "tiers_mode": null,
    //             "transform_quantity": null,
    //             "type": "recurring",
    //             "unit_amount": 12000,
    //             "unit_amount_decimal": "12000"
    //         },
    //         "quantity": 1,
    //         "subscription": "sub_1PJwUDHmUxu9ZaPGdVQpMNLY",
    //         "tax_rates": []
    //     }
    // ],
    "metadata": {
        "additionalAppliances": ApplianceTypes,
        "plan_name": PlanTypes,
        "userId":string
    },
    "plan": {
        "id": string,
        "object": string,
        "active": boolean,
        "aggregate_usage": null,
        "amount": number,
        "amount_decimal": string,
        "billing_scheme": string,
        "created": number,
        "currency": string,
        "interval": SubIntervalTypes,
        "interval_count": number,
        "livemode": false,
        "metadata": {},
        "meter": null,
        "nickname": null,
        "product": {
            "id": string,
            "object": string,
            "active": boolean,
            "attributes": [],
            "created": number,
            "default_price": string,
            "description": null,
            "images": [],
            "livemode": boolean,
            "marketing_features": [],
            "metadata": {
                "plan_name": PlanTypes
            },
            "name": string,
            "package_dimensions": null,
            "shippable": null,
            "statement_descriptor": null,
            "tax_code": string,
            "type": string,
            "unit_label": null,
            "updated": number,
            "url": null
        },
        "tiers_mode": null,
        "transform_usage": null,
        "trial_period_days": null,
        "usage_type": "licensed"
    }

}