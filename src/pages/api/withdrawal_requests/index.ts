import type { NextApiRequest, NextApiResponse } from 'next';
import { createClient, User } from '@supabase/supabase-js';
import { getAuthorizationHeader } from '../../../utils/api/get-authorization-header';

export default async function GET(req: NextApiRequest, res: NextApiResponse) {
  const auth = getAuthorizationHeader(req);
  if (!auth) {
    return res.status(400).json({ message: 'Authorization header is missing' });
  }
  
  const supabase = createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL,
    process.env.NEXT_PUBLIC_SUPABASE_KEY,
    {
      global: {
        headers: {
          Authorization: auth.header,
        },
      },
    }
  );

  const { data, error } = await supabase.auth.getUser(auth.jwt);

  if (error) {
    return res.status(400).json({ message: 'Failed to get user', error });
  }

  const user: User = data.user;

  res.status(200).json({ message: 'Got data!', data: data.user });
}
