import { NextRequest, NextResponse } from "next/server";
import { createClient } from "@/utils/supabase/server";
import { createClient as createPrismicClient } from "@/prismicio";
import { BlogPageDocument } from "prismicio-types";

export async function GET(request: NextRequest) {
    console.log("GET sitemap.xml Route");
    console.log(request.cookies.getAll());

    const supabase = createClient();
    const prismicClient = createPrismicClient();

    // Fetch all blog posts from Prismic
    const blogPages = await prismicClient.getAllByType("blog_page") as BlogPageDocument<string>[];

    // Base URL for the website
    const baseUrl = "https://www.pleasantplumbers.com";

    // Static routes
    const staticRoutes = [
        "",
        "/protect",
        "/repair",
        "/install",
        "/emergencies",
        "/support",
        "/earn",
        "/careers",
        "/privacy-policy",
        "/terms-of-use",
        "/learn",
        "/refer/plan",
        "/refer/boiler-service",
        "/refer/boiler-installation",
        "/refer/central-heating-powerflush",
        "/refer/central-heating-control-upgrade",
        "/commercial-survey-offer",
    ];

    // Start building the sitemap
    let sitemap = `<?xml version="1.0" encoding="utf-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">`;

    // Add static routes to sitemap
    for (const route of staticRoutes) {
        sitemap += `
  <url>
    <loc>${baseUrl}${route}</loc>
  </url>`;
    }

    // Add static routes with commercial mode
    for (const route of [
        "",
        "/protect",
        "/repair",
        "/install",
        "/emergencies",
        "/support",
        "/earn",
        "/careers",
        "/privacy-policy",
        "/terms-of-use",
        "/learn",
    ]) {
        sitemap += `
  <url>
    <loc>${baseUrl}${route}?mode=commercial</loc>
  </url>`;
    }

    // Add auth routes
    for (const route of [
        "/login",
        "/register",
        "/profile",
        "/cdn-cgi/l/email-protection",
    ]) {
        sitemap += `
  <url>
    <loc>${baseUrl}${route}</loc>
  </url>`;
    }

    // Add blog posts to sitemap
    for (const blog of blogPages) {
        // Using the slug for the URL
        const blogSlug = blog.uid || blog.slugs[0];

        // Only add the blog post if it has a valid slug
        if (blogSlug) {
            sitemap += `
  <url>
    <loc>${baseUrl}/learn/${blogSlug}</loc>
  </url>`;
        }
    }

    // Close the sitemap XML
    sitemap += `
</urlset>`;

    // Return the sitemap as XML
    return new NextResponse(sitemap, {
        headers: {
            "Content-Type": "text/xml",
        },
    });
}
