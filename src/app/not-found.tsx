import Header from "@/components/Header";
import * as styles from "./not-found.css";

import { notFound } from "next/navigation";
import { createClient } from "../prismicio";
import FloatingContactWidgets from "@/components/FloatingContactWidgets";
import { WHATS_UP_NUMBER } from "@/utils/constants";
import NotFoundComponent from "@/components/NotFoundComponent";

async function getData () {
  try {
    const client = createClient(
    );

    const headerQuery = await client.getByType(
      "header"
    );

    const footerQuery = await client.getByType(
      "footer"
    );

    return {
      props: {
        header: headerQuery.results[0],
        footer: footerQuery.results[0],
      },
    };
  } catch (error) {
    notFound();
  }
}


export default async function NotFound() {
  const { props } = await getData();

  return (
    <div
      className={styles.root}
    >
      <Header
        header={props.header}
      />
      <main
        className={styles.main}
      >
        <NotFoundComponent />
      </main>    
      <FloatingContactWidgets
        phoneNumber={String(props.header.data.phone_number)}
        whatsAppNumber={WHATS_UP_NUMBER}
      />
    </div>
  );
}