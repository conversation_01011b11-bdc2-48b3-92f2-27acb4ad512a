import { createClient } from "@/prismicio";
import { PageMode } from "@/types/common";
import Footer from "@/components/Footer";
import FloatingContactWidgets from "@/components/FloatingContactWidgets";
import { WHATS_UP_NUMBER } from "@/utils/constants";
import { notFound } from "next/navigation";
import * as styles from "./styles.css";
import HeaderSection from "@components/ProtectYourBusiness/components/Header/HeaderSection";
import Link from "next/link";
import ReferAnEngineerSection from "src/components/ReferAnEngineer/ReferAnEngineerSection";
import ReferralBonusSection from "src/components/ReferAnEngineer/ReferralBonusSection";
import LeaderboardChallenge from "@components/ReferAnEngineer/LeaderboardChallenge/LeaderboardChallenge";
import WhyEngineersChoose from "@components/ReferAnEngineer/WhyEngineersChoose";
import HowToReferSection from "@components/ReferAnEngineer/HowToReferSection";
import GetInfoPackSection from "@components/ReferAnEngineer/GetInfoPackSection";
import UnlimitedEarningPotentialSection from "@components/ReferAnEngineer/UnlimitedEarningPotentialSection";
import YourMoveSection from "@components/ReferAnEngineer/YourMoveSection";

// Define PageProps interface
interface PageProps {
  searchParams: { mode?: PageMode };
}

async function getData() {
  const client = createClient();

  try {
    // Fetch header and footer data
    const headerQuery = await client.getByType("header");
    const footerQuery = await client.getByType("footer");
    const emergencyLocationsQuery = await client.getByType(
      "emergency_locations"
    );

    return {
      header: headerQuery.results[0],
      footer: footerQuery.results[0],
      emergencyLocations: emergencyLocationsQuery.results[0].data.locations,
    };
  } catch (error) {
    console.error("Error fetching data:", error);
    notFound();
  }
}

export default async function ReferEngineer({ searchParams }: PageProps) {
  const data = await getData();

  return (
    <div className={styles.root}>
      <HeaderSection
        buttonComponent={
          <Link
            style={{ textDecoration: "none", fontWeight: 400 }}
            // className={styles.joinButton}
            href="/become-subcontractor"
          >
            Join Our Team
          </Link>
        }
      />

      <main>
        <ReferAnEngineerSection />
        <ReferralBonusSection />
        <LeaderboardChallenge />
        <WhyEngineersChoose />
        <HowToReferSection />
        <GetInfoPackSection />
        <UnlimitedEarningPotentialSection />
        <YourMoveSection />
      </main>

      <Footer footer={data.footer} />

      <FloatingContactWidgets
        phoneNumber={String(data.header.data.phone_number)}
        whatsAppNumber={WHATS_UP_NUMBER}
      />
    </div>
  );
}
