import ReferPage from "@/components/ReferPage";
import { createClient } from "@/prismicio";
import { slicesFetch } from "@/slices/fetch";
import { GoogleReview, PageMode } from "@/types/common";
import { getSliceDataObject, returnMetaDataRobots } from "@/utils/helpers";
import { Metadata } from "next";
import { notFound } from "next/navigation";
import {  ReferPageDocument } from "prismicio-types";


type PageProps = {
  params: {
    uid: string
  },
  searchParams: { mode: PageMode }
}

async function getData (uid: string, searchParams: Record<string, string>) {
  try {
    const client = createClient();

    const page = await client.getByUID("refer_page", uid) as any as ReferPageDocument<string> | undefined;

    if (!page) throw "Not found";

    const headerQuery = await client.getByType(
      "header"
    );

    const footerQuery = await client.getByType(
      "footer"
    );

    const slicesData = await getSliceDataObject(page.data.slices, slicesFetch,searchParams);

    const headerAnchorItems = page.data.slices.map(({primary}) => "header_anchor_name" in primary ? primary?.header_anchor_name : null);

    return {
      props: {
        header: headerQuery.results[0],
        footer: footerQuery.results[0],
        page,
        slicesData,
        headerAnchorItems
      },
    };
  } catch (error) {
    notFound();
  }
      
}

export async function generateMetadata(
  { params, searchParams }: PageProps
): Promise<Metadata> {
  const { props } = await getData(params.uid, searchParams);

  return {
    title: props.page.data.meta_title,
    description: props.page.data.meta_description,
    robots: returnMetaDataRobots({noFollow: props.page.data.no_follow, noIndex:  props.page.data.no_index}),
  };
}

export default async function Page(
  { params, searchParams, }: PageProps
) {
  const { props } = await getData(
    params.uid,searchParams
  );

  return (
    <ReferPage
      searchParams={searchParams}
      header={props.header}
      footer={props.footer}
      page={props.page}
      slicesData={props.slicesData}
      headerAnchorItems={props.headerAnchorItems}
      uid={params.uid}
    />
  );
}
