"use client";

import PaymentPage from "@/components/PaymentPage";
import useStore from "@/hooks/useStore";
import { observer } from "mobx-react-lite";
import { RedirectType, redirect } from "next/navigation";

function Page() {
  const { auth } = useStore();

  if (!auth.isAuthorizationInProgress && !auth.isAuthorized) {
    redirect("/", RedirectType.replace);
  }

  return (
    <PaymentPage
      priceId=""
      onSubmit={() => {}}
    />
  );
}

export default observer(Page);