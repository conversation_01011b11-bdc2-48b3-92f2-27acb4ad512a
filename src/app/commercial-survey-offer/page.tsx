import { createClient } from "@/prismicio";
import { PageMode } from "@/types/common";
import { getSliceDataObject, returnMetaDataRobots } from "@/utils/helpers";
import { Metadata } from "next";
import { notFound } from "next/navigation";
import { LandingPageDocument } from "prismicio-types";
import { Content } from "@prismicio/client";
import * as prismic from "@prismicio/client";
import { slicesFetch } from "@/slices/fetch";
import Footer from "@/components/Footer";
import FloatingContactWidgets from "@/components/FloatingContactWidgets";
import { WHATS_UP_NUMBER } from "@/utils/constants";
import * as styles from "./styles.css";
import ProblemAwarenessSection
  from "@components/ProtectYourBusiness/components/ProblemAwarenessSection/ProblemAwarenessSection";
import SolutionSection from "@components/ProtectYourBusiness/components/SolutionSection/SolutionSection";
import WhatsIncludedSection from "@components/ProtectYourBusiness/components/WhatsIncludedSection/WhatsIncludedSection";
import WhyPleasantPlumbersSection
  from "@components/ProtectYourBusiness/components/WhyPleasantPlumbersSection/WhyPleasantPlumbersSection";
import AccreditationSection from "@components/ProtectYourBusiness/components/Accreditation/AccreditationSection";
import ExpertCtaSection from "@components/ProtectYourBusiness/components/ExpertCtaSection/ExpertCtaSection";
import ProtectYourBusiness from "@components/ProtectYourBusiness/components/ProtectYourBusiness/ProtectYourBusiness";
import LimitedTimeOffer from "@components/ProtectYourBusiness/components/LimitedOfferSection/LimitedTimeOffer";
import HeaderSection from "@components/ProtectYourBusiness/components/Header/HeaderSection";
import dynamic from 'next/dynamic';
import Container from "@components/Container";

interface PageProps {
  searchParams: { mode: PageMode }
}

async function getData(searchParams: { mode: PageMode }) {
  try {
    const client = createClient();
    const footerQuery = await client.getByType("footer");

    // Try to get the landing page with UID "protect-your-business"
    let pageQuery;
    try {
      pageQuery = await client.getByUID("landing_page", "protect-your-business");
    } catch (error) {
      console.error("Failed to fetch protect-your-business page:", error);
      pageQuery = null;
    }

    if (!pageQuery) {
      console.warn("protect-your-business page not found in Prismic");

      return {
        props: {
          footer: footerQuery.results[0],
          page: {
            data: {
              title_residential: "Protect Your Business",
              description_residential: "Business protection services",
              no_follow_residential: false,
              no_index_residential: false,
              title_commercial: "Protect Your Business",
              description_commercial: "Business protection services",
              no_follow_commercial: false,
              no_index_commercial: false,
              slices: []
            }
          },
          slicesData: {},
        },
      };
    }

    const slicesData = await getSliceDataObject(pageQuery.data.slices, slicesFetch, searchParams);

    return {
      props: {
        footer: footerQuery.results[0],
        page: pageQuery,
        slicesData,
      },
    };
  } catch (error) {
    console.error("Error in getData:", error);
    notFound();
  }
}

export async function generateMetadata({ searchParams }: PageProps): Promise<Metadata> {
  const { props } = await getData(searchParams);
  const page = props.page as LandingPageDocument<string>;

  if (searchParams.mode === "commercial") {
    return {
      title: page.data.title_commercial,
      description: page.data.description_commercial,
      robots: returnMetaDataRobots({
        noFollow: page.data.no_follow_commercial,
        noIndex: page.data.no_index_commercial,
      }),
      openGraph: {
        title: String(page.data.title_commercial),
        description: String(page.data.description_commercial),
        images: [page.data.image_commercial?.url ?? ""],
      },
    };
  }

  return {
    title: "Commercial Plumbing Survey – Now Only £200 (Was £420)",
    description: page.data.description_residential ?? "",
    robots: returnMetaDataRobots({
      noFollow: page.data.no_follow_residential,
      noIndex: page.data.no_index_residential,
    }),
    openGraph: {
      title: "Commercial Plumbing Survey – Now Only 200 (Was £420)",
      description: page.data.description_residential ?? "",
      images: [page.data.image_residential?.url ?? ""],
    },
  };
}

const DynamicPartnersSection = dynamic(
  () => import('@/slices/PartnersSection'),
  { ssr: false }
);

export default async function Page({ searchParams }: PageProps) {
  const { props } = await getData(searchParams);

  return (
    <>
      <div className={styles.root}>
        <main>
          <HeaderSection />
          <ProtectYourBusiness />
          <ProblemAwarenessSection />
          <SolutionSection />
          <WhatsIncludedSection />
          <WhyPleasantPlumbersSection />
          {/*<AccreditationSection accreditationsItems={props.footer.data.accreditations_items!} />*/}
          <Container>
            <DynamicPartnersSection
              slice={{
                primary: {
                  title: [{
                    type: "paragraph",
                    text: "Vetted and Accredited by",
                    spans: [],
                  }] as prismic.RichTextField,
                  description: [] as prismic.RichTextField
                },
                items: (props.footer.data.accreditations_items || []).map(item => ({
                  partner_logo: item.accreditation_image
                })),
                slice_type: "partners_section",
                variation: "default"
              } as unknown as Content.PartnersSectionSlice}
              index={0}
              slices={props.page.data.slices}
              context={props.slicesData}
            />
          </Container>
          <ExpertCtaSection />
          <LimitedTimeOffer />
        </main>
        <Footer
          footer={props.footer}
        />
      </div>
      <FloatingContactWidgets
        phoneNumber={props.footer.data.phone_number || ""}
        whatsAppNumber={WHATS_UP_NUMBER}
      />
    </>
  );
}
