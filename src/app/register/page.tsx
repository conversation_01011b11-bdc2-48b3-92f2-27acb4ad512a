"use client";

import { CountdownWrapperActions } from "@/components/CountdownWrapper/CountdownWrapper.types";
import FloatingContactWidgets from "@/components/FloatingContactWidgets";
import RegisterPage from "@/components/RegisterPage";
import { RegisterFormValues } from "@/components/RegisterPage/RegisterPage";
import VerifyEmailByOtpPage from "@/components/VerifyEmailByOtpPage";
import useStore from "@/hooks/useStore";
import { UserLoginError } from "@/stores/authStore";
import { HEADER_PHONE_NUMBER, WHATS_UP_NUMBER } from "@/utils/constants";
import { observer } from "mobx-react-lite";
import { RedirectType, redirect } from "next/navigation";
import { useCallback, useRef, useState } from "react";

function Page() {
  const { auth, alert } = useStore();

  const countdownRef = useRef<CountdownWrapperActions>(null);

  if (auth.isAuthorized) {
    redirect("/profile", RedirectType.replace);
  }

  const [registerValues, setRegisterValues] = useState<RegisterFormValues>({
    full_name: "",
    mobile_number: "",
    email: "",
  });

  const [step, setStep] = useState<"register" | "verify-email">("register");

  const onResend = useCallback(
    async () => {
      try {
        await auth.resendSignUpEmailAPI(registerValues.email);
        countdownRef.current?.startTimer();
        alert.addAlert({ content: `Please check ${registerValues.email}`, type: "info" });
      } catch (e) {
        
      }
    },
    [registerValues],
  );
  

  const handleRegister = async (data: RegisterFormValues) => {
    try {
      await auth.register(data);

      setRegisterValues(data);

      setStep("verify-email");
    } catch (error) {
      const e = error as UserLoginError;

      if(e.details?.status === 429) {
        alert.addAlert({ content: "Wait 1 minute for a code resend.", type: "error" });
        throw e;
      }
      alert.addAlert({ content: String(e.message), type: "error" });
      throw e;
    }
  };

  if (step === "register") return (
    <>
      <RegisterPage
        onSubmit={handleRegister}
      />
      <FloatingContactWidgets
        phoneNumber={String(HEADER_PHONE_NUMBER)}
        whatsAppNumber={WHATS_UP_NUMBER}
      />
    </>
  );

  return (
    <>
      <VerifyEmailByOtpPage
        countdownRef={countdownRef}
        forCase="sign-up"
        verifyActionText="Become a member"
        onBack={() => setStep("register")}
        onResend={onResend}
        values={{ email: registerValues.email ?? "" }}
      />
      <FloatingContactWidgets
        phoneNumber={String(HEADER_PHONE_NUMBER)}
        whatsAppNumber={WHATS_UP_NUMBER}
      />
    </>
  );
}

export default observer(Page);