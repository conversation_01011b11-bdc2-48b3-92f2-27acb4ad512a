"use client";

import { CountdownWrapperActions } from "@/components/CountdownWrapper/CountdownWrapper.types";
import FloatingContactWidgets from "@/components/FloatingContactWidgets";
import LoginPage from "@/components/LoginPage";
import { LoginFormValues } from "@/components/LoginPage/LoginPage";
import VerifyEmailByOtpPage from "@/components/VerifyEmailByOtpPage";
import useStore from "@/hooks/useStore";
import { UserLoginError } from "@/stores/authStore";
import { HEADER_PHONE_NUMBER, WHATS_UP_NUMBER } from "@/utils/constants";
import { observer } from "mobx-react-lite";
import { RedirectType, redirect } from "next/navigation";
import { useCallback, useRef, useState } from "react";

function Page() {
  const { auth, alert } = useStore();

  const countdownRef = useRef<CountdownWrapperActions>(null);

  if (auth.isAuthorized) {
    redirect("/profile", RedirectType.replace);
  }

  const [loginValues, setLoginValues] = useState<LoginFormValues>({
    email: "",
  });

  const [step, setStep] = useState<"login" | "verify-email">("login");


  const onResend = useCallback(
    async () => {
      try {
        await handleLogIn(loginValues);
        alert.addAlert({ content: `Please check ${loginValues.email}`, type: "info" });
        countdownRef.current?.startTimer();

      
      } catch (e) {
      
      }

    },
    [loginValues],
  );
  

  const handleLogIn = useCallback( async (data: LoginFormValues) => {
    try {
      await auth.login(data);

      setLoginValues(data);

      setStep("verify-email");
    } catch (error) {
      const e = error as UserLoginError;
      if(e.details?.status === 429) {
        alert.addAlert({ content: "Wait 1 minute for a code resend.", type: "error" });
        throw e;
      }
      alert.addAlert({ content: String(e.message), type: "error" });
      throw e;

    }
  }, []);

  if (step === "login") return (
    <>
      <LoginPage
        onSubmit={handleLogIn}
      />
      <FloatingContactWidgets
        phoneNumber={String(HEADER_PHONE_NUMBER)}
        whatsAppNumber={WHATS_UP_NUMBER}
      />
    </>
  );

  return (
    <>
      <VerifyEmailByOtpPage
        countdownRef={countdownRef}
        forCase="login"
        verifyActionText="Log In"
        onBack={() => setStep("login")}
        onResend={onResend}
        values={loginValues}
      />
      <FloatingContactWidgets
        phoneNumber={String(HEADER_PHONE_NUMBER)}
        whatsAppNumber={WHATS_UP_NUMBER}
      />
    </>
  );
}

export default observer(Page);