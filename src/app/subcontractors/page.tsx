import { createClient } from "@/prismicio";
import { PageMode } from "@/types/common";
import Footer from "@/components/Footer";
import FloatingContactWidgets from "@/components/FloatingContactWidgets";
import { WHATS_UP_NUMBER } from "@/utils/constants";
import { notFound } from "next/navigation";
import HeaderSection from "@components/ProtectYourBusiness/components/Header/HeaderSection";
import PaymentBenefitsSection from "@components/Subcontractors/PaymentBenefits/PaymentBenefitsSection";
import SubcontractorHero from "@components/Subcontractors/BecomeSubcontractor/SubcontractorHero";
import SubcontractorTestimonialSection from "@components/Subcontractors/TestimonialSection/SubcontractorTestimonialSection";
import SubcontractorBenefits from "@components/Subcontractors/Benefits/SubcontractorBenefits";
import SubcontractorAdminFocus from "@components/Subcontractors/AdminFocus/SubcontractorAdminFocus";
import SubcontractorFAQ from "@components/Subcontractors/FAQ/SubcontractorFAQ";
import * as styles from "@components/Subcontractors/BecomeSubcontractor/SubcontractorHero.css";
import Link from "next/link";
import Button from "@components/Button";
import ReferAnEngineer from "@components/Subcontractors/ReferAnEngineer";
import { Metadata } from "next";

// Define PageProps interface
interface PageProps {
  searchParams: { mode?: PageMode }
}

async function getData() {
  const client = createClient();

  try {
    // Fetch header and footer data
    const headerQuery = await client.getByType("header");
    const footerQuery = await client.getByType("footer");
    const emergencyLocationsQuery = await client.getByType("emergency_locations");

    return {
      header: headerQuery.results[0],
      footer: footerQuery.results[0],
      emergencyLocations: emergencyLocationsQuery.results[0].data.locations,
    };
  } catch (error) {
    console.error("Error fetching data:", error);
    notFound();
  }
}

export const metadata: Metadata = {
  title: "Plumbing Sub-contractor Jobs in London | Apply Now",
  description: "Join Pleasant Plumbers as a trusted subcontractor. Get consistent plumbing jobs across London with fast pay, no admin, and zero lead chasing. Apply today.",
};

export default async function SubcontractorsPage({ searchParams }: PageProps) {
  const data = await getData();

  return (
    <div>
      <HeaderSection
        buttonText="Join Our Team"
        showModal={false}
        buttonComponent={<Link
            style={{ textDecoration: "none", fontWeight: 400 }}
            // className={styles.joinButton}
            href="/become-subcontractor"
        >
            Join Our Team
        </Link>}
      />
      <main>
        <SubcontractorHero />
        <PaymentBenefitsSection />
        <SubcontractorTestimonialSection />
        <ReferAnEngineer/>
        <SubcontractorBenefits />
        <SubcontractorAdminFocus />
        <SubcontractorFAQ />
      </main>

      <Footer
        footer={data.footer}
      />

      <FloatingContactWidgets
        phoneNumber={String(data.header.data.phone_number)}
        whatsAppNumber={WHATS_UP_NUMBER}
      />
    </div>
  );
}
