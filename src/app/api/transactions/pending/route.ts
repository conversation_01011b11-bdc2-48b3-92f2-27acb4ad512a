import { NextRequest, NextResponse } from "next/server";
import { createClient } from "@/utils/supabase/server";
import { isAuthenticated } from "@/utils/api/is-authenticated";
import { ServiceResponse } from "@/utils/api/types/common";

export async function GET(request: NextRequest) {
  const supabase = createClient();

  const { error: authError } = await isAuthenticated(supabase);

  if (authError) {
    return NextResponse.json<ServiceResponse>(
      {
        error: authError,
      },
      { status: 403 }
    );
  }

  const getTransactions = await supabase
    .from("transactions")
    .select("*")
    .eq("status", "Pending");

  if (getTransactions.error) {
    return NextResponse.json(
      {
        error: {
          message: "Failed to get transactions",
          data: getTransactions.error,
        },
      },
      { status: 400 }
    );
  }

  const transactions = getTransactions.data;

  const body = {
    message: "Success",
    data: {
      transactions,
    },
  };

  return NextResponse.json<ServiceResponse>(body, { status: 200 });
}
