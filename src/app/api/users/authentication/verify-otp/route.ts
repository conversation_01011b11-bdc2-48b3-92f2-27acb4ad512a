import { NextRequest, NextResponse } from "next/server";
import { createClient } from "@supabase/supabase-js";

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
);

export async function POST(req: NextRequest) {
  try {
    const body = await req.json();
    const { email, otp } = body;
  } catch (error) {
    return NextResponse.json({
      error: {
        message: "Failed to parse JSON",
        details: error,
      },
    });
  }
}
