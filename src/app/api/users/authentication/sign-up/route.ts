import { NextRequest, NextResponse } from "next/server";
import { createClient } from "@supabase/supabase-js";
import { SignUpWithOtpDtoSchema } from "./types";

// Production sign-up
const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
);

export async function POST(req: NextRequest) {
  try {
    const body = await req.json();
    const validationResult = await SignUpWithOtpDtoSchema.safeParseAsync(body);

    if (!validationResult.success) {
      return NextResponse.json(
        {
          error: {
            message: "Validation error",
            details: validationResult.error,
          },
        },
        { status: 403 }
      );
    }

    const { email, full_name, mobile_number, member_id } =
      validationResult.data;

    const { data: userExistsData, error: userExistsError } = await supabase.rpc(
      "check_user_exists",
      { email_input: email }
    );

    // If there is any error with RPC
    if (userExistsError) {
      return NextResponse.json(
        {
          error: {
            message: "Failed to validate email, try again later",
            details: userExistsError,
          },
        },
        {
          status: 403,
        }
      );
    }

    // Feature request:
    // We can make flow, where we can re-activate account based on status:
    // If account was deleted by admin - no way to return account;
    // If account was deleted by user itself, he can re-activate it

    const { user_id } = userExistsData;

    if (user_id) {
      return NextResponse.json(
        {
          error: {
            message: "User with specified email already exists",
            details: {
              email,
            },
          },
        },
        {
          status: 403,
        }
      );
    }

    let metadata: Record<string, string> = {
      full_name,
      mobile_number,
    };

    if (member_id) {
      const { data: profile, error } = await supabase
        .from("profiles")
        .select("id")
        .match({ ref_id: member_id })
        .single();

      if (error) {
        return NextResponse.json(
          {
            error: {
              message: "Failed to find member with specified ID",
              details: {
                memberId: member_id,
              },
            },
          },
          { status: 404 }
        );
      }

      metadata.member_id = profile.id;
    }

    const { error: signUpError } = await supabase.auth.signInWithOtp({
      email,
      options: {
        shouldCreateUser: true,
        data: metadata,
      },
    });

    if (signUpError) {
      console.error(signUpError);

      return NextResponse.json(
        {
          error: {
            message: "Failed to sign up",
            details: signUpError.message,
          },
        },
        { status: 403 }
      );
    }

    return NextResponse.json({
      data: {
        message: "Signed up successfully, check your email inbox for OTP code",
        details: {
          email,
        },
      },
    });
  } catch (error) {
    return NextResponse.json(
      {
        error: {
          message: "Failed to parse JSON",
          details: error,
        },
      },
      {
        status: 500,
      }
    );
  }
}
