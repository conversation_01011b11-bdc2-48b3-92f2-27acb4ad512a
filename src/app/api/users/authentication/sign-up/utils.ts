import { SignUpDto } from "@/utils/api/types/dto";
import { SupabaseClient } from "@supabase/supabase-js";

export async function generateReferralMemberId(
  dto: SignUpDto,
  supabase: SupabaseClient
): Promise<string> {
  const getInitials = (full_name: string): string => {
    const split = full_name.split(" ");
    const f = split[0][0] ?? "A";
    const s = split[1][0] ?? "A";

    return `${f.toUpperCase()}${s.toUpperCase()}`;
  };

  const getRandomCode = (length = 6) => {
    let result = "";
    for (let i = 0; i < length; i++) {
      result += Math.floor(Math.random() * 10).toString();
    }
    return result;
  };

  let createdUnique = false;

  const initials = getInitials(dto.full_name);
  let ref_id = "";

  while (createdUnique === false) {
    const code = getRandomCode();
    ref_id = `${initials}${code}FF`;

    const { data: exist } = await supabase
      .from("profiles")
      .select("*")
      .eq("ref_id", ref_id)
      .single();

    if (exist) continue;

    createdUnique = true;
  }
  return ref_id;
}
