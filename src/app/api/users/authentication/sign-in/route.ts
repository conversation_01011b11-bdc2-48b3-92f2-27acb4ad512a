import { NextRequest, NextResponse } from "next/server";
import { ServiceResponse } from "@/utils/api/types/common";
import { SignInDtoSchema } from "./types";
import { createClient } from "@supabase/supabase-js";

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
);

export async function POST(req: NextRequest) {
  try {
    const body = await req.json();
    const validationResult = await SignInDtoSchema.safeParseAsync(body);

    if (!validationResult.success) {
      return NextResponse.json(
        {
          error: {
            message: "Validation error",
            details: validationResult.error,
          },
        },
        { status: 403 }
      );
    }

    const { email } = validationResult.data;

    const { data: userExistsData, error: userExistsError } = await supabase.rpc(
      "check_user_exists",
      {
        email_input: email,
      }
    );

    if (userExistsError) {
      return NextResponse.json<ServiceResponse>(
        {
          error: {
            message:
              "Failed to call RPC function. Try again later or contact administration",
            details: userExistsError,
          },
        },
        { status: 400 }
      );
    }

    const { user_id, status } = userExistsData;

    if (user_id === null) {
      return NextResponse.json<ServiceResponse>(
        {
          error: {
            message: "User with specified email does not exist",
            details: {
              email,
            },
          },
        },
        { status: 400 }
      );
    }

    switch (status) {
      case "deleted": {
        return NextResponse.json<ServiceResponse>(
          {
            error: {
              message:
                "Account was deleted. To restore access - contact administraion via email: <EMAIL>",
            },
          },
          { status: 403 }
        );
      }
    }

    const { error: signInWithOtpError } = await supabase.auth.signInWithOtp({
      email,
      options: {
        shouldCreateUser: false,
      },
    });

    if (signInWithOtpError) {
      return NextResponse.json<ServiceResponse>(
        {
          error: {
            message: "Failed to sign-in with OTP",
            details: signInWithOtpError,
          },
        },
        { status: 500 }
      );
    }

    return NextResponse.json<ServiceResponse>(
      {
        data: {
          message: "Check your inbox for verification code",
        },
      },
      { status: 200 }
    );
  } catch (error) {
    return NextResponse.json<ServiceResponse>({
      error: {
        message: "Failed to sign-in",
        details: error,
      },
    });
  }
}
