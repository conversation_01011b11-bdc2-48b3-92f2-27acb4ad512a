import { createClient } from "@/utils/supabase/server";
import { NextRequest, NextResponse } from "next/server";
import { isAuthenticated } from "@/utils/api/is-authenticated";
import { ServiceResponse } from "@/utils/api/types/common";

export async function GET(request: NextRequest): Promise<NextResponse<any>> {
  const supabase = createClient();

  const { error: authError } = await isAuthenticated(supabase);

  if (authError) {
    return NextResponse.json<ServiceResponse>(
      {
        error: authError,
      },
      { status: 403 }
    );
  }

  const { data: subscriptions, error: getSubscriptionsError } = await supabase
    .from("plan_subscriptions")
    .select("*");

  if (getSubscriptionsError) {
    return NextResponse.json(
      {
        error: {
          message: "Failed to get subscriptions",
          details: getSubscriptionsError,
        },
      },
      { status: 400 }
    );
  }

  return NextResponse.json(
    {
      data: {
        subscriptions,
      },
    },
    { status: 200 }
  );
}
