import { env } from "process";
import { NextRequest, NextResponse } from "next/server";
import { createClient as createAdminSupabaseClient } from "@supabase/supabase-js";
import { createClient } from "@/utils/supabase/server";
import { UpdateUserDto, UpdateUserDtoSchema } from "@/utils/api/types/dto";
import { ServiceResponse } from "@/utils/api/types/common";
import { isAuthenticated } from "@/utils/api/is-authenticated";

export async function GET(request: NextRequest): Promise<NextResponse<any>> {
  const supabase = createClient();

  const { data: authData, error: authError } = await isAuthenticated(supabase);

  if (authError) {
    return NextResponse.json<ServiceResponse>(
      {
        error: authError,
      },
      { status: 403 }
    );
  }

  const { data: profile, error: getProfileError } = await supabase
    .from("profiles")
    .select(
      `
      id,
      updated_at,
      full_name,
      date_of_birth,
      mobile_number,
      post_code,
      address_details,
      residential_status,
      airing_cupboard_cylinder,
      any_cover,
      bathrooms_count,
      boiler_age,
      boiler_fuel_source,
      boiler_serviced_recently,
      carbon_monoxide_alarm,
      home_ownership_status,
      power_flush_carried_out,
      property_type,
      radiators_count,
      smart_thermostat,
      tanks_in_loft,
      thermostat_radiators_valves,
      zohoRecordId,
      ref_id,
      available_balance,
      customer_id,
      ref_by:ref_by_id (
            id,
            ref_id
          )
        `
    )
    .match({ id: authData.id })
    .single();

  if (getProfileError) {
    return NextResponse.json(
      {
        error: {
          message: "Failed to get profile info",
          data: getProfileError,
        },
      },
      {
        status: 400,
      }
    );
  }

  const response = {
    email: authData.email,
    ...profile,
  };

  const body = {
    message: "Success",
    data: {
      user: response,
    },
  };

  return NextResponse.json<ServiceResponse>(body, { status: 200 });
}

export async function PATCH(request: NextRequest): Promise<NextResponse<any>> {
  const supabase = createClient();
  const getUser = await supabase.auth.getUser();

  if (getUser.error) {
    const body = {
      error: {
        message: "Failed to get current user",
        data: getUser.error,
      },
    };

    return NextResponse.json(body, { status: 400 });
  }

  const userId = getUser.data.user.id;
  const body = await request.json();

  const parseResult = await UpdateUserDtoSchema.safeParseAsync(body);
  if (!parseResult.success) {
    return NextResponse.json(
      {
        error: {
          message: "Failed to update user",
          data: parseResult.error,
        },
      },
      { status: 400 }
    );
  }

  const dto: UpdateUserDto = parseResult.data;

  const { data: updateUserData, error: updateUserError } = await supabase
    .from("profiles")
    .update(dto)
    .eq("id", userId)
    .select("*")
    .single();

  if (updateUserError) {
    return NextResponse.json(
      {
        error: {
          message: "Failed to update user",
          details: updateUserError,
        },
      },
      { status: 400 }
    );
  }

  const { error: updateMetaError } = await updateUserMetadata(userId, {
    full_name: updateUserData.full_name,
    mobile_number: updateUserData.mobile_number,
  });

  if (updateMetaError) {
    return NextResponse.json<ServiceResponse>({ error: updateMetaError });
  }

  const user = {
    ...updateUserData,
    email: getUser.data.user.email,
    id: getUser.data.user.id,
  };

  const responseBody = {
    message: "Success",
    data: {
      user,
    },
  };

  return NextResponse.json<ServiceResponse>(responseBody, { status: 200 });
}

async function updateUserMetadata(
  userId: string,
  metadata: Record<string, any>
): Promise<ServiceResponse> {
  const supabase = await createAdminSupabaseClient(
    env.NEXT_PUBLIC_SUPABASE_URL,
    env.SUPABASE_SERVICE_ROLE_KEY
  );

  const { error: updateMetaError } = await supabase.auth.admin.updateUserById(
    userId,
    {
      user_metadata: metadata,
    }
  );

  if (updateMetaError) {
    return {
      error: {
        message: "Failed to update user's metadata",
        details: updateMetaError,
      },
    };
  }

  return {
    data: {
      message: "Success",
      userId,
    },
  };
}
