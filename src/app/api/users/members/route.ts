import { NextRequest, NextResponse } from "next/server";
import { createClient } from "@supabase/supabase-js";

export async function GET(req: NextRequest) {
  const memberId = req.nextUrl.searchParams.get("id");

  const supabase = createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL,
    process.env.SUPABASE_SERVICE_ROLE_KEY
  );

  const { error } = await supabase
    .from("profiles")
    .select("id")
    .match({ ref_id: memberId })
    .single();

  if (error) {
    return NextResponse.json(
      {
        error: {
          message: "Failed to find member with specified ID",
          details: {
            memberId,
          },
        },
      },
      { status: 404 }
    );
  }

  return NextResponse.json({
    data: {
      message: "Success",
    },
  });
}
