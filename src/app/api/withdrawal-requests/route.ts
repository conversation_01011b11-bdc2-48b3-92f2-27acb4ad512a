import { createClient } from "@/utils/supabase/server";
import { NextRequest, NextResponse } from "next/server";
import {
  CreateWithdrawalRequest,
  CreateWithdrawalRequestSchema,
} from "@/utils/services/withdrawal/types";
import { ServiceResponse } from "@/utils/api/types/common";
import { isAuthenticated } from "@/utils/api/is-authenticated";

export async function POST(request: NextRequest) {
  const supabase = createClient();

  const { data: authData, error: authError } = await isAuthenticated(supabase);

  if (authError) {
    return NextResponse.json<ServiceResponse>(
      {
        error: authError,
      },
      { status: 403 }
    );
  }

  const userId = authData.id;
  const body = await request.json();
  const parseResult = await CreateWithdrawalRequestSchema.safeParseAsync(body);

  if (!parseResult.success) {
    return NextResponse.json(
      {
        error: {
          message: "Validation error",
          data: parseResult.error,
        },
      },
      { status: 403 }
    );
  }

  const dto: CreateWithdrawalRequest = parseResult.data;

  const getProfile = await supabase
    .from("profiles")
    .select("id, available_balance")
    .match({ id: userId })
    .single();

  if (getProfile.error) {
    return NextResponse.json(
      {
        error: {
          message: "Failed to get available balance for current user",
          data: getProfile.error,
        },
      },
      { status: 403 }
    );
  }

  const { available_balance } = getProfile.data;

  if (available_balance < dto.amount_to_withdraw) {
    return NextResponse.json(
      {
        error: {
          message: "Available balance is lower than amount to withdraw",
          data: {
            available_balance,
            amount_to_withdraw: dto.amount_to_withdraw,
          },
        },
      },
      { status: 403 }
    );
  }

  const createWithdrawalRequest = await supabase.rpc(
    "create_withdrawal_request",
    {
      user_id: userId,
      amount_to_withdraw: dto.amount_to_withdraw,
      account_holder: dto.account_holder,
      account_number: dto.account_number,
      sort_code: dto.sort_code,
    }
  );

  console.log("Create Withdrawal Request", createWithdrawalRequest);

  if (createWithdrawalRequest.error || createWithdrawalRequest.data !== 1) {
    return NextResponse.json(
      {
        error: {
          message: "Failed to create withdrawal request",
          data: createWithdrawalRequest.error ?? null,
        },
      },
      { status: 403 }
    );
  }

  return NextResponse.json({
    message: "Success",
    data: createWithdrawalRequest.data,
  });
}
