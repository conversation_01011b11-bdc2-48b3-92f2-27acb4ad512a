import { ServiceResponse } from "@/types/common";
import axios, { AxiosResponse } from "axios";
import { CreateEmergencyLocationDto, LocationReview } from "./types";
import { createSupabaseAdminClient } from "@/utils/supabase/admin";
import { createClient as createPrismicClient } from "@/prismicio";

function sanitizeGoogleReviewsResponseData(reviews: any): LocationReview[] {
  if (!Array.isArray(reviews)) {
    return [];
  }

  const sanitized = reviews.map((r) => {
    return {
      author_name: r.author_name,
      author_url: r.author_url,
      language: r.language,
      original_language: r.original_language,
      profile_photo_url: r.profile_photo_url,
      rating: r.rating,
      relative_time_description: r.relative_time_description,
      text: r.text,
      time: r.time,
      translated: r.translated,
    };
  });

  return sanitized;
}

function getExpirationTimestamp(): Date {
  const lifetime = 1000 * 60; // One minute

  const now = new Date();
  const expireAt = new Date(now.getTime() + lifetime);

  console.log("Now: ", now);
  console.log("Expire At: ", expireAt);

  return expireAt;
}

export async function getGoogleReviewsByPlaceId(
  placeId: string
): Promise<ServiceResponse<LocationReview[]>> {
  console.log("Fetching reviews from an API...");

  const getReviewsUrl = new URL(
    "https://maps.googleapis.com/maps/api/place/details/json"
  );

  getReviewsUrl.searchParams.append("place_id", placeId);
  getReviewsUrl.searchParams.append(
    "key",
    process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY ?? ""
  );

  try {
    const getReviewsResponse: AxiosResponse = await axios.get(
      getReviewsUrl.toString()
    );

    const data = sanitizeGoogleReviewsResponseData(
      getReviewsResponse.data.result.reviews
    );

    return {
      data,
    };
  } catch (error) {
    return {
      error: {
        message: "Failed to get response from Google Reviews API",
        details: error,
      },
    };
  }
}

export async function upsertLocationReviewsByLocationId(
  locationId: string
): Promise<ServiceResponse<LocationReview[]>> {
  console.log("Data expired, upsert new reviews...");
  const supabase = await createSupabaseAdminClient();

  const { data: getLocationsData, error: getLocationsError } = await supabase
    .from("emergency_locations")
    .select("place_id")
    .match({ id: locationId })
    .single();

  if (getLocationsError) {
    return {
      error: {
        message: "Failed to find location",
        details: {
          locationId,
        },
      },
    };
  }

  const { data: reviews, error: getReviewsError } =
    await getGoogleReviewsByPlaceId(getLocationsData.place_id);

  if (getReviewsError) {
    return {
      error: getReviewsError,
    };
  }

  const { data: upsertReviewsData, error: upsertReviewsError } =
    await supabase.rpc("upsert_emergency_location_reviews", {
      reviews,
      locationid: locationId,
    });

  if (upsertReviewsError) {
    return {
      error: {
        message: "Failed to upsert reviews to database",
        details: upsertReviewsError,
      },
    };
  }

  return {
    data: reviews,
  };
}

export async function createLocationWithReviewsData(
  location: string,
  placeId: string
): Promise<ServiceResponse<LocationReview[]>> {
  console.log("CreateLocationWithReviewsData...");
  const supabase = await createSupabaseAdminClient();
  const expireAt = getExpirationTimestamp();

  const locationData: CreateEmergencyLocationDto = {
    uuid: location,
    place_id: placeId,
    expire_at: expireAt,
  };

  const { data: emergencyLocation, error: createEmergencyLocationError } =
    await supabase
      .from("emergency_locations")
      .insert(locationData)
      .select("id")
      .single();

  if (createEmergencyLocationError) {
    return {
      error: {
        message: "Failed to insert new emergency location",
        details: {
          data: locationData,
          log: createEmergencyLocationError,
        },
      },
    };
  }

  const { data: reviews, error: getGoogleReviewsError } =
    await getGoogleReviewsByPlaceId(placeId);

  if (getGoogleReviewsError) {
    return {
      error: getGoogleReviewsError,
    };
  }

  const { error: upsertReviewsError } = await supabase.rpc(
    "upsert_emergency_location_reviews",
    {
      locationid: emergencyLocation.id,
      reviews,
    }
  );

  if (upsertReviewsError) {
    return {
      error: {
        message: "Failed to upsert reviews to database",
        details: upsertReviewsError,
      },
    };
  }

  return {
    data: reviews,
  };
}

export async function getPrismicPlaceIdByLocation(
  location: string
): Promise<ServiceResponse> {
  console.log("Fetch data from Prismic...");
  const prismic = createPrismicClient();

  const {
    results: [emergencyLocations],
  } = await prismic.getByType("emergency_locations");

  const prismicLocation = emergencyLocations.data.locations.find(
    (item) => item.uuid?.toString() === location
  );

  if (!prismicLocation) {
    return {
      error: {
        message: "Failed to find reviews for specified location",
        details: {
          location,
        },
      },
    };
  }

  const { place_id } = prismicLocation;

  if (!place_id) {
    return {
      error: {
        message: "Prismic did not have enough data to get reviews information",
        details: {
          place_id,
        },
      },
    };
  }

  return {
    data: place_id.toString(),
  };
}
