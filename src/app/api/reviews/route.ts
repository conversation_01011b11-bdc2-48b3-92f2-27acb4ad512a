import { NextRequest, NextResponse } from "next/server";
import { createSupabaseAdminClient } from "../../../utils/supabase/admin";
import { ServiceResponse } from "@/types/common";
import {
  createLocationWithReviewsData,
  getPrismicPlaceIdByLocation,
  upsertLocationReviewsByLocationId,
} from "./utils";
import { LocationReview } from "./types";

export async function GET(request: NextRequest) {
  try {
    let reviews: LocationReview[] = [];
    const supabase = createSupabaseAdminClient();

    let location = request.nextUrl.searchParams.get("location");

    if (!location) {
      return NextResponse.json<ServiceResponse>(
        {
          error: {
            message: "No reviews location specified",
          },
        },
        { status: 403 }
      );
    }

    location = location.trim().toLowerCase();

    const { data: getEmergencyLocationData, error: getEmergencyLocationError } =
      await supabase.rpc("get_emergency_location_id", {
        location,
      });

    if (getEmergencyLocationError) {
      return NextResponse.json<ServiceResponse>(
        {
          error: {
            message: "Failed to run RPC",
            details: getEmergencyLocationError,
          },
        },
        { status: 500 }
      );
    }

    // If locationId is null - no location is found
    let { location_id, is_expired } = getEmergencyLocationData;

    console.log({
      location_id,
      is_expired,
    });

    if (!location_id) {
      const { data: placeId, error: getPrismicPlaceIdError } =
        await getPrismicPlaceIdByLocation(location);

      if (getPrismicPlaceIdError) {
        return NextResponse.json<ServiceResponse>(
          {
            error: getPrismicPlaceIdError,
          },
          { status: 404 }
        );
      }

      const { data: createdReviews, error: createLocationWithReviewsError } =
        await createLocationWithReviewsData(location, placeId);

      if (createLocationWithReviewsError) {
        return NextResponse.json<ServiceResponse>(
          {
            error: createLocationWithReviewsError,
          },
          { status: 500 }
        );
      }

      reviews = createdReviews;
    } else if (is_expired) {
      const {
        data: upsertedLocationReviews,
        error: upsertLocationReviewsError,
      } = await upsertLocationReviewsByLocationId(location_id);

      if (upsertLocationReviewsError) {
        return NextResponse.json<ServiceResponse>(
          {
            error: upsertLocationReviewsError,
          },
          { status: 500 }
        );
      }

      reviews = upsertedLocationReviews;
    } 
    
    console.log("Fetch reviews from Database cache");
    const query = supabase
      .from("emergency_location_reviews")
      .select("*")
      .match({ emergency_location_id: location_id })
      .or("hidden.is.null,hidden.eq.false");

    const { data, error: getReviewsFromDatabaseError } = await query;

    if (getReviewsFromDatabaseError) {
      return NextResponse.json<ServiceResponse>(
        {
          error: {
            message: "Failed to retrieve reviews",
            details: getReviewsFromDatabaseError,
          },
        },
        { status: 500 }
      );
    }

    reviews = data;
    

    return NextResponse.json<ServiceResponse<LocationReview[]>>(
      {
        data: reviews,
      },
      { status: 200 }
    );
  } catch (error) {
    return NextResponse.json(
      {
        data: [],
        error: String(error),
      },
      { status: 500 }
    );
  }
}
