import { NextRequest, NextResponse } from "next/server";
import { createClient } from "@/utils/supabase/server";
import { isAuthenticated } from "@/utils/api/is-authenticated";
import { ServiceResponse } from "../../../utils/api/types/common";

export async function GET(request: NextRequest) {
  console.log("GET API Route cookies");
  console.log(request.cookies.getAll());

  const supabase = createClient();

  const { data, error } = await isAuthenticated(supabase);

  if (error) {
    return NextResponse.json<ServiceResponse>(
      {
        error,
      },
      { status: 403 }
    );
  }

  return NextResponse.json<ServiceResponse>({
    data,
  });
}
