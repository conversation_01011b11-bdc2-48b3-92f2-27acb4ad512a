import { NextRequest, NextResponse } from "next/server";

export async function POST(
  req: NextRequest,
) {
  const { email } = await req.json(
  );
  const url = "https://api.sendgrid.com/v3/marketing/contacts";

  console.log(
    email
  );

  const data = {
    contacts: [{ email }],
    list_ids: [process.env.SENDGRID_MAILING_ID],
  };
  const headers = {
    Authorization: `Bearer ${process.env.SENDGRID_API_KEY}`,
    "Content-Type": "application/json",
  };

  const options = {
    method: "PUT",
    headers: headers,
    body: JSON.stringify(
      data
    ),
  };
  const response = await fetch(
    url, options
  );
  const json = await response.json(
  );
  if (json.errors) {
    return NextResponse.json(
      {
        message: json.errors,
      }
    );
  } else {
    return NextResponse.json(
      {
        message:
        "Your email has been succesfully added to the mailing list. Welcome 👋",
      }
    );
  }
}
