import { NextResponse } from "next/server";
import { createClient } from "@/utils/supabase/server";
import { isAuthenticated } from "@/utils/api/is-authenticated";
import { ServiceResponse } from "../../../types/common";
import { GetEarnProgramTasksResponse } from "./types";

export async function GET() {
  console.log("GET /api/earn-program");

  const supabase = createClient();

  const { error: authError } = await isAuthenticated(supabase);

  if (authError) {
    return NextResponse.json<ServiceResponse>(
      {
        error: authError,
      },
      { status: 401 }
    );
  }

  const { data: tasks, error } = await supabase
    .from("earn_program_tasks")
    .select("id, task, status, created_at, record_id")
    .order("created_at");

  if (error) {
    console.log(`Error. Code: ${error.code}`);
    return NextResponse.json(
      {
        error: {
          message: "Failed to get tasks",
          data: error,
        },
      },
      { status: 403 }
    );
  }

  const response: GetEarnProgramTasksResponse = { tasks: [] };

  for (const task of tasks) {
    const taskExists = response.tasks.find(
      (t) => t.record_id === task.record_id
    );

    if (taskExists) {
      taskExists.history.push({
        id: task.id,
        status: task.status,
        created_at: task.created_at,
      });
    } else {
      response.tasks.push({
        name: task.task,
        record_id: task.record_id,
        history: [
          { id: task.id, status: task.status, created_at: task.created_at },
        ],
      });
    }
  }

  return NextResponse.json(
    {
      data: response,
    },
    { status: 200 }
  );
}
