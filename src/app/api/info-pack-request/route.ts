import { NextRequest, NextResponse } from "next/server";

export async function POST(req: NextRequest) {
  try {
    const { email } = await req.json();

    if (!email || !email.trim()) {
      return NextResponse.json({ error: "Email is required" }, { status: 400 });
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return NextResponse.json(
        { error: "Invalid email format" },
        { status: 400 }
      );
    }

    // Send notification email using SendGrid
    const notificationEmail =
      process.env.INFO_PACK_NOTIFICATION_EMAIL ||
      process.env.SENDGRID_SENDER_EMAIL ||
      "<EMAIL>";

    const sendGridData = {
      personalizations: [
        {
          to: [
            { email: notificationEmail },
            { email: "<EMAIL>" }
          ],
          subject: "New Info Pack Request - Referral Program",
        },
      ],
      from: {
        email:
          process.env.SENDGRID_FROM_EMAIL || "<EMAIL>",
        name: "<PERSON> Plumbers",
      },
      content: [
        {
          type: "text/html",
          value: `
            <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
              <h2 style="color: #2D5A27;">New Info Pack Request</h2>
              <p><strong>${email}</strong> requested an info pack for a referral.</p>
              <div style="background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;">
                <h3 style="margin-top: 0; color: #2D5A27;">Request Details:</h3>
                <p><strong>Email:</strong> ${email}</p>
                <p><strong>Requested at:</strong> ${new Date().toLocaleString()}</p>
              </div>
              <p style="color: #666; font-size: 14px;">
                Please send the referral info pack to this email address.
              </p>
            </div>
          `,
        },
      ],
    };

    const sendGridResponse = await fetch(
      "https://api.sendgrid.com/v3/mail/send",
      {
        method: "POST",
        headers: {
          Authorization: `Bearer ${process.env.SENDGRID_API_KEY}`,
          "Content-Type": "application/json",
        },
        body: JSON.stringify(sendGridData),
      }
    );

    if (!sendGridResponse.ok) {
      console.error("SendGrid error:", await sendGridResponse.text());
      return NextResponse.json(
        { error: "Failed to send notification" },
        { status: 500 }
      );
    }

    return NextResponse.json({
      message: "Info pack request submitted successfully",
    });
  } catch (error) {
    console.error("API error:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
