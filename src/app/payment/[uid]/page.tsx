"use client";

import { CountdownWrapperActions } from "@/components/CountdownWrapper/CountdownWrapper.types";
import FloatingContactWidgets from "@/components/FloatingContactWidgets";
import LoginPage from "@/components/LoginPage";
import PaymentPage from "@/components/PaymentPage";
import RegisterPage, { RegisterFormValues } from "@/components/RegisterPage/RegisterPage";
import Steps from "@/components/Steps/Steps";
import VerifyEmailByOtpPage from "@/components/VerifyEmailByOtpPage";
import useStore from "@/hooks/useStore";
import { UserLoginError } from "@/stores/authStore";
import { HEADER_PHONE_NUMBER, WHATS_UP_NUMBER } from "@/utils/constants";
import { observer } from "mobx-react-lite";
import { useSearchParams } from "next/navigation";
import { useCallback, useEffect, useMemo, useRef, useState } from "react";

interface PageProps {
  params: {
    uid: string
  },
}

function Page({ params,}: PageProps) {
  const { auth, alert } = useStore();

  const countdownRef = useRef<CountdownWrapperActions>(null);

  const [values, setValues] = useState<{ email: string }>({
    email: "",
  });

  const [authType, setAuthType] = useState<"register" | "login">("register");

  const [step, setStep] = useState<"auth" | "verify-email" | "payment">(auth.isAuthorized ? "payment" : "auth");

  const handleAuth = async (data: { email: string }) => {
    try {
      await auth.login(data);

      setValues(data);

      setStep("verify-email");
    } catch (error) {
      const e = error as UserLoginError;
      
      if(e.details?.status === 429) {
        alert.addAlert({ content: "Wait 1 minute for a code resend.", type: "error" });
        throw e;
      }
      alert.addAlert({ content: String(e.message), type: "error" });
      throw e;
    }
  };

  const onResendLogin = useCallback(
    async () => {
      try {
        await handleAuth(values);
        countdownRef.current?.startTimer();
        alert.addAlert({ content: `Please check ${values.email}`, type: "info" });
        
      } catch (e) {
        
      }
    },
    [values],
  );
  

  const onResendRegister = useCallback(
    async () => {
      try {
        await auth.resendSignUpEmailAPI(values.email);
        countdownRef.current?.startTimer();
        alert.addAlert({ content: `Please check ${values.email}`, type: "info" });
        
      } catch (e) {
        
      }

    },
    [values,],
  );
  

  const handleRegister = async (data: RegisterFormValues) => {
    try {
      await auth.register(data);

      setValues(data);

      setStep("verify-email");
    } catch (error) {
      const e = error as UserLoginError;

      if(e.details?.status === 429) {
        alert.addAlert({ content: "Wait 1 minute for a code resend.", type: "error" });
        throw e;
      }
      alert.addAlert({ content: String(e.message), type: "error" });
      throw e;
    }
  };

  useEffect(() => {
    setStep(!auth.isAuthorizationInProgress && auth.isAuthorized ? "payment" : "auth");
  }, [auth.isAuthorizationInProgress, auth.isAuthorized]);

  const steps = (
    <Steps
      value={step}
      options={[
        {
          value: "auth",
          label: "1",
        },
        {
          value: "verify-email",
          label: "2"
        },
        {
          value: "payment",
          label: "3",
        },
      ]}
    />
  );

  if (step === "auth") {
    if (authType === "login") return (
      <>
        <LoginPage
          onSubmit={handleAuth}
          content={steps}
        />
        <FloatingContactWidgets
          phoneNumber={String(HEADER_PHONE_NUMBER)}
          whatsAppNumber={WHATS_UP_NUMBER}
        />
      </>
    );

    if (authType === "register") return (
      <>
        <RegisterPage
          isPaymentType
          onSubmit={handleRegister}
          content={steps}
        />
        <FloatingContactWidgets
          phoneNumber={String(HEADER_PHONE_NUMBER)}
          whatsAppNumber={WHATS_UP_NUMBER}
        />
      </>
    );
  }

  if (step === "verify-email") return (
    <>
      <VerifyEmailByOtpPage
        forCase={authType === "register" ? "sign-up" : "login"}
        countdownRef={countdownRef}
        dontRedirect
        content={steps}
        verifyActionText={authType === "login" ? "Log In" : "Become a member"}
        onBack={() => setStep("auth")}
        // onSuccess={() => setStep("payment")}
        onResend={authType === "register" ?  onResendRegister : onResendLogin}
        values={values}
      />
      <FloatingContactWidgets
        phoneNumber={String(HEADER_PHONE_NUMBER)}
        whatsAppNumber={WHATS_UP_NUMBER}
      />
    </>
  );

  return (
    <>  
      <PaymentPage
        content={steps}
        priceId={params.uid}
        onSubmit={() => {}}
      />
      <FloatingContactWidgets
        phoneNumber={String(HEADER_PHONE_NUMBER)}
        whatsAppNumber={WHATS_UP_NUMBER}
      />
    </>
  );
}

export default observer(Page);