import HomePage from "@/components/HomePage";
import { slicesFetch } from "@/slices/fetch";
import { PageMode } from "@/types/common";
import { getSliceDataObject } from "@/utils/helpers";
import { Metadata } from "next";
import { createClient } from "../prismicio";
import "../styles/removeScrollPadding.css";

interface PageProps {
  searchParams: { mode: PageMode }
}

async function getData(searchParams: Record<string, string>) {
  const client = createClient();

  const headerQuery = await client.getByType("header");

  if (!headerQuery.results.length) {
    throw new Error("Header is not defined");
  }

  const footerQuery = await client.getByType("footer");

  if (!footerQuery.results.length) {
    throw new Error("Footer is not defined");
  }


  const emergencyLocationsQuery = await client.getByType(
    "emergency_locations"
  );

  const page = await client.getByUID("landing_page", "homepage");

  const slicesData = await getSliceDataObject(page.data.slices, slicesFetch, searchParams);

  

  return {
    props: {
      header: headerQuery.results[0],
      footer: footerQuery.results[0],
      page,
      slicesData,
      emergencyLocations: emergencyLocationsQuery.results[0].data.locations,
    },
  };
}

export async function generateMetadata({searchParams}: PageProps): Promise<Metadata> {
  const { props } = await getData(searchParams);

  const icons = [
    {
      rel: "icon",
      url: "/favicon.ico",
      sizes: "any",
    },
    {
      rel: "icon",
      url: "/favicon.svg",
      type: "image/svg+xml",
    },
    {
      rel: "icon",
      url: "/favicon.png",
      type: "image/png",
      sizes: "32x32",
    },
    {
      rel: "icon",
      url: "/favicon.png",
      type: "image/png",
      sizes: "180x180",
    },
  ];


  const commonMetaData = {
    icons,
    openGraph: {
      siteName: "Pleasant Plumbers",
    }
  };

  if(searchParams.mode === "commercial") {
    return {
      ...commonMetaData,
      title: props.page.data.title_commercial,
      description: props.page.data.description_commercial,
      openGraph: {
        title: String(props.page.data.title_commercial),
        description: String(props.page.data.description_commercial),
        images: props.page.data.image_commercial?.url
          ? {
            url: props.page.data.image_commercial.url,
            width: props.page.data.image_commercial.dimensions.width,
            height: props.page.data.image_commercial.dimensions.height,
          }
          : undefined,
        ...commonMetaData.openGraph
      }
    }; 
  }

  return  {
    ...commonMetaData,
    title: props.page.data.title_residential,
    description: props.page.data.description_residential,
    openGraph: {
      title: String(props.page.data.title_residential),
      description: String(props.page.data.description_residential),
      images: props.page.data.image_residential?.url
        ? {
          url: props.page.data.image_residential.url,
          width: props.page.data.image_residential.dimensions.width,
          height: props.page.data.image_residential.dimensions.height,
        }
        : undefined,
      ...commonMetaData.openGraph
    }
  };

}

export default async function Home({searchParams}: PageProps) {
  const { props } = await getData(searchParams);

  return (
    <HomePage
      emergencyLocations={props.emergencyLocations}
      header={props.header}
      footer={props.footer}
      slices={(searchParams.mode === "commercial" ? (props.page.data.slices2.length ? props.page.data.slices2 : props.page.data.slices) :props.page.data.slices)}
      slicesData={props.slicesData}
    />
  );
}
