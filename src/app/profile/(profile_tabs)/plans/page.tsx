import ProfilePlansPage from "@/components/ProfilePlansPage";

import { createClient } from "@/utils/supabase/server";
import { CustomerPlan, Plan } from "@/types/plans";

async function Page() {
  const supabase = createClient();
  // const { auth } = useStore();

  // if (!auth.isAuthorizationInProgress && !auth.isAuthorized) {
  //   redirect("/login", RedirectType.replace);
  // }

  const plans = await supabase.functions.invoke<Plan>("stripe/plans", {
    method: "GET",
  });

  const customerPlans = await supabase.functions.invoke<CustomerPlan[]>(
    "stripe/customers/plans",
    { method: "GET" }
  );

  // await auth.getCustomerPlanAPI();

  if (plans.data === null) return "Unexpected server error!";

  return (
    <ProfilePlansPage
      serverPlans={plans.data}
      initCustomerPlans={customerPlans.data}
      // customerPlanError={!!customerPlans.error}
    />
  );
}

export default Page;
