"use client";

import ChangePasswordPage from "@/components/ChangePasswordPage";
import useStore from "@/hooks/useStore";
import { observer } from "mobx-react-lite";
import { RedirectType, notFound, redirect } from "next/navigation";


// TODO: Remove this tab if we don't need it in the future for sure
function Page() {

  if(true) notFound();

  const { auth } = useStore();

  if (!auth.isAuthorizationInProgress && !auth.isAuthorized) {
    redirect("/login", RedirectType.replace);
  }

  return (
    <ChangePasswordPage/>
  );
}

export default observer(Page);