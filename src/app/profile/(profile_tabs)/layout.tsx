import ProfileLayout from "@/components/ProfileLayout";
import { createClient } from "../../../prismicio";

async function getHeaderData() {
  const client = createClient(
  );
  
  const headerQuery = await client.getByType(
    "header"
  );

  const emergencyLocationsQuery = await client.getByType(
    "emergency_locations"
  );
  
  if (!headerQuery.results.length) {
    throw new Error("Header is not defined");
  }

  return {
    header: headerQuery.results[0],
    emergencyLocations: emergencyLocationsQuery.results[0].data.locations,
  };
}

export default async function Layout(
  {
    children,
  }: Readonly<{
  children: React.ReactNode;
}>
) {
  const { header, emergencyLocations } = await getHeaderData();

  return (
    <ProfileLayout
      header={header}
      emergencyLocations={emergencyLocations}
    >
      {children}
    </ProfileLayout>
  );
}
