"use client";

import PersonalInfoPage from "@/components/PersonalInfoPage";
import useStore from "@/hooks/useStore";
import { observer } from "mobx-react-lite";
import { RedirectType, redirect } from "next/navigation";

function Page() {
  const { auth } = useStore();

  if (!auth.isAuthorizationInProgress && !auth.isAuthorized) {
    redirect("/login", RedirectType.replace);
  }

  return (
    <PersonalInfoPage />
  );
}

export default observer(Page);