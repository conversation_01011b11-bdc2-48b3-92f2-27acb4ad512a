"use client";

import HomeInfoPage from "@/components/HomeInfoPage";
import useStore from "@/hooks/useStore";
import { observer } from "mobx-react-lite";
import { RedirectType, redirect } from "next/navigation";

function Page() {
  const { auth } = useStore();

  if (!auth.isAuthorizationInProgress && !auth.isAuthorized) {
    redirect("/login", RedirectType.replace);
  }

  return (
    <HomeInfoPage/>
  );
}

export default observer(Page);
