import BlogPage from "@/components/BlogPage";
import { createClient } from "@/prismicio";
import { PageMode } from "@/types/common";
import { returnMetaDataRobots } from "@/utils/helpers";
import { Metadata } from "next";
import { notFound } from "next/navigation";
import { BlogPageDocument } from "prismicio-types";
import { generateSlug } from "@/utils/generateSlugFromTitle";

interface PageProps {
  params: {
    slug: string;
  };
  searchParams: { mode: PageMode };
}

async function getData(slug: string) {
  try {
    const client = createClient();
    const blogPages = await client.getAllByType("blog_page", {
      fetchLinks: ["blog_tags.name"],
    }) as BlogPageDocument<string>[];
    const blog = blogPages.find((bp) => slug === generateSlug(bp.data.title as string));
    if (!blog) throw "Not found";

    const headerQuery = await client.getByType("header");
    const footerQuery = await client.getByType("footer");

    return {
      props: {
        header: headerQuery.results[0],
        footer: footerQuery.results[0],
        blog,
      },
    };
  } catch (error) {
    notFound();
  }
}

export async function generateMetadata({ params, searchParams }: PageProps): Promise<Metadata> {
  const { props } = await getData(params.slug);

  if (searchParams.mode === "commercial") {
    return {
      title: props.blog.data.title_commercial,
      description: props.blog.data.description_commercial,
      robots: returnMetaDataRobots({
        noFollow: props.blog.data.no_follow_commercial,
        noIndex: props.blog.data.no_index_commercial,
      }),
      openGraph: {
        title: String(props.blog.data.title_commercial),
        description: String(props.blog.data.description_commercial),
        images: [props.blog.data.image_commercial?.url ?? ""],
      },
    };
  }

  return {
    title: props.blog.data.title_residential,
    description: props.blog.data.description_residential,
    robots: returnMetaDataRobots({
      noFollow: props.blog.data.no_follow_residential,
      noIndex: props.blog.data.no_index_residential,
    }),
    openGraph: {
      title: String(props.blog.data.title_residential),
      description: String(props.blog.data.description_residential),
      images: [props.blog.data.image_residential?.url ?? ""],
    },
  };
}

export default async function Page({ params }: PageProps) {
  const { props } = await getData(params.slug);
  return (
    <BlogPage
      header={props.header}
      footer={props.footer}
      page={props.blog}
    />
  );
}

