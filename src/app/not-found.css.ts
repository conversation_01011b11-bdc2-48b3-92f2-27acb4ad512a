import { breakpoints } from "@/styles/constants.css";
import { mode } from "@/styles/functions.css";
import { theme } from "@/styles/themes.css";
import { style } from "@vanilla-extract/css";

export const root = style({
  paddingTop: 150,
  display: "flex",
  flexDirection: "column",
  minHeight: "100vh",

  selectors: {
    [mode("residential")]: {
      backgroundColor: theme.colors.primary.castletonGreen,
    }
  },
});

export const main = style({
  display: "flex",
  flexDirection: "column",
  flex: "1 1",
  gap: 10,

  "@media": {
    [breakpoints.tablet]: {
      gap: 20,
    }
  }
});
