import { Option } from "@/components/DropdownInput/DropdownInput.types";
import { UpdateUserDto } from "@/utils/api/types/dto";
import { createClient } from "@/utils/supabase/client";
import { makeAutoObservable } from "mobx";
import { deleteUserAction } from "./actions";
import { CustomerPlan } from "@/types/plans";

export type UserInfo = UpdateUserDto & {
  id: string;
  email: string;
  ref_id: string;
  ref_by: {
    id: string;
    ref_id: string;
  } | null;
  available_balance: number;
};

export interface UserLoginData {
  email: string;
}

export type UserLoginError = {
  message: string;
  details: {
    name: string;
    message: string;
    status: number;
  };
};

export interface UserRegisterData {
  full_name: string;
  mobile_number: string;
  email: string;
  member_id?: string;
}

const supabase = createClient();

class AuthStore {
  user: UserInfo | null | undefined = undefined;

  userIsLoading = true;

  logoutConfirmationModalIsOpen = false;

  deleteAccountConfirmationModalIsOpen = false;

  customerPlansLoading: boolean = false;
  customerPlans: CustomerPlan[] | null = null;
  customerPlansError: any = null;

  constructor() {
    makeAutoObservable(this);
  }

  get isAuthorized() {
    return Boolean(this.user?.email);
  }

  get isAuthorizationInProgress() {
    return this.user === undefined;
  }

  setUser = (data:  UserInfo | null | undefined) => {
    this.user = data;
  };
  setUserIsLoading = (load = false) => {
    this.userIsLoading = load;
  };

  setCustomerPlanLoading = (loading: boolean = false) => {
    this.customerPlansLoading = loading;
  };

  setCustomerPlan = (plan:  CustomerPlan[] | null ) => {
    this.customerPlans = plan;
  };
  setCustomerErrorPlan = (error:any ) => {
    this.customerPlansError = error;
  };

  clearUserData = () => {
    this.user = null;
  };

  setDeleteAccountConfirmationModalIsOpen = (
    value = !this.deleteAccountConfirmationModalIsOpen
  ) => {
    this.deleteAccountConfirmationModalIsOpen = value;
  };

  setLogoutConfirmationModalIsOpen = (
    value = !this.logoutConfirmationModalIsOpen
  ) => {
    this.logoutConfirmationModalIsOpen = value;
  };


  getCustomerPlanAPI = async () => {
    this.setCustomerPlanLoading(true);

    try {
      const customerPlans = await supabase.functions.invoke<CustomerPlan[]>(
        "stripe/customers/plans",
        { method: "GET" }
      );
      
      if(customerPlans.error) {
        throw customerPlans.error;
      }
      
      this.setCustomerPlan(customerPlans.data && customerPlans.data.length ? customerPlans.data : null);
      
    } catch (e) {
      this.setCustomerErrorPlan(e);
    }

    this.setCustomerPlanLoading(false);
  };

  login = async (data: UserLoginData) => {
    try {
      const response = await fetch("/api/users/authentication/sign-in", {
        method: "POST",
        body: JSON.stringify(data),
      });

      const { error: signInError } = await response.json();

      if (signInError) {
        throw signInError;
      }
    } catch (error) {
      throw error;
    }
  };

  logout = async () => {
    const { error } = await supabase.auth.signOut({ scope: "local" });

    if (error) throw `Failed to logout. ${error.message}`;

    this.clearUserData();
  };

  resendSignUpEmailAPI = async (email: string) => {

    try {
      const { error } = await supabase.auth.resend({
        type: "signup",
        email,
      });

      if (error) {
        throw error;
      }
    } catch (error) {
      throw error;
    }
  };

  register = async (data: UserRegisterData) => {
    try {
      const response = await fetch("/api/users/authentication/sign-up", {
        method: "POST",
        body: JSON.stringify(data),
      });

      const { error: signUpError } = await response.json();

      if (signUpError) {
        throw signUpError;
      }
    } catch (error) {
      throw error;
    }
  };

  verifyEmailOTP = async (email: string, token: string) => {
    const { error } = await supabase.auth.verifyOtp({
      email,
      token,
      type: "email",
    });

    if (error) throw `Failed to verify Email. ${error.message}`;

    await this.initialize();
  };

  sendResetPasswordConfirmationToEmail = async (email: string) => {
    const { error } = await supabase.auth.resetPasswordForEmail(email, {
      redirectTo: window.location.origin + "/reset-password",
    });

    if (error) throw `Failed to remember password. ${error.message}`;
  };

  resetPassword = async (newPassword: string) => {
    const { error } = await supabase.auth.updateUser({
      password: newPassword,
    });

    if (error) throw `Failed to reset password. ${error.message}`;
  };

  resetEmail = async (newEmail: string) => {
    const { error } = await supabase.auth.updateUser({ email: newEmail });

    if (error) throw `Failed to reset email. ${error.message}`;

    if (!this.user) return;

    this.setUser({ ...this.user, email: newEmail });
  };

  updatePersonalInfo = async (data: Partial<UpdateUserDto>) => {
    try {
      const response = await fetch("/api/users/profile", {
        method: "PATCH",
        body: JSON.stringify(data),
      });

      if (!response.ok) throw "Failed to update Personal Info";

      if (!this.user) return;

      this.setUser({ ...this.user, ...data });
    } catch (error) {
      throw `${error}`;
    }
  };

  deleteUser = async () => {
    if (!this.user) return;

    const { error } = await deleteUserAction();

    if (error) throw `Failed to delete User. ${error.message}`;

    await this.logout();
  };

  initialize = async () => {
    try {

      this.setUserIsLoading(true);

      const userInfoResponse = await fetch("/api/users/profile", {
        method: "GET",
      });

      // const userDataParsed = await userInfoResponse.json() as {data: {user: {0: UserInfo, email: string, id: string },}};
      const userDataParsed = (await userInfoResponse.json()) as {
        data: { user: UserInfo & { email: string; id: string } };
      };

      if (!userDataParsed) {
        this.clearUserData();
        return;
      }
      this.setUser(userDataParsed.data.user);
    } catch (error) {
      this.clearUserData();
    }

    this.setUserIsLoading(false);
  };

 
}

export default AuthStore;
