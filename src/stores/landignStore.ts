import { makeAutoObservable } from "mobx";

class LandingStore {
  bookingModalIsOpen: boolean = false;

  setBookingModalIsOpen = (value = !this.bookingModalIsOpen) => {
    this.bookingModalIsOpen = value;
  };

  headerIsExpanded: boolean = true;

  setHeaderIsExpanded = (value: boolean) => {
    this.headerIsExpanded = value;
  };

  headerMenuIsOpen: boolean = false;

  setHeaderMenuIsOpen = (value = !this.headerMenuIsOpen) => {
    this.headerMenuIsOpen = value;
  };

  opportunitiesModalIsOpen: boolean = false;

  setOpportunitiesModalIsOpen = (value = !this.opportunitiesModalIsOpen) => {
    this.opportunitiesModalIsOpen = value;
  };
  
  callUsModalIsOpen: boolean = false;
  
  setCallUsModalIsOpen = (value = !this.opportunitiesModalIsOpen) => {
    this.callUsModalIsOpen = value;
  };
  constructor() {
    makeAutoObservable(this);
  }
}

export default LandingStore;