import { makeAutoObservable } from "mobx";
import { ReactNode } from "react";

interface AlertProps {
  id: string
  content: ReactNode
  type?: "success" | "error" | "info"
  delay?: number
}

class AlertStore {
  alerts: AlertProps[] = [];

  setAlertValue = (newAlerts: AlertProps[]) => {
    this.alerts = newAlerts;
  };

  addAlert = ({ content, type = "success", delay = 5000 }: Omit<AlertProps, "id">) => {
    const alertId = JSON.stringify(content) + Date.now();


    this.setAlertValue([...this.alerts, {
      id: alertId,
      content,
      type,
      delay,
    }]);

    setTimeout(() => {
      this.setAlertValue(this.alerts.filter(alert => alert.id !== alertId));
    }, delay);
  };

  removeAlert = (alertId: string) => {
    this.setAlertValue(this.alerts.filter(alert => alert.id !== alertId));
  };

  constructor() {
    makeAutoObservable(this);
  }
}

export default AlertStore;