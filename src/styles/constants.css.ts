export const breakpointValues = {
  sm: 375,
  md: 768,
  lg: 1440,
  xl: 1920
} as const;

export const breakpoints = {
  mobile: `screen and (min-width: ${breakpointValues.sm}px)`,
  tablet: `screen and (min-width: ${breakpointValues.md}px)`,
  desktop: `screen and (min-width: ${breakpointValues.lg}px)`,
  ultraWide: `screen and (min-width: ${breakpointValues.xl}px)`,
} as const;

export const sprinklesBreakpoints = {
  mobile: {},
  tablet: { "@media": breakpoints.tablet },
  desktop: { "@media": breakpoints.desktop },
  ultraWide: { "@media": breakpoints.ultraWide },
};
