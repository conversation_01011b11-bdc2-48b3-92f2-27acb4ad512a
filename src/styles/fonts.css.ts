import { fontFace } from "@vanilla-extract/css";

export const primaryFont = fontFace(
  [
    {
      src: "url(\"/assets/fonts/RocaOne-BdIt.woff2\")",
      fontWeight: "500 900",
      fontDisplay: "swap",
    },
    {
      src: "url(\"/assets/fonts/RocaOne-Lt.woff2\")",
      fontWeight: "100 400",
      fontDisplay: "swap",
    },
  ]
);

export const secondaryFont = fontFace(
  [
    {
      src: "url(\"/assets/fonts/HelveticaNeueCyr-Roman.woff2\")",
      fontWeight: "100 400",
      fontDisplay: "swap",
    },
    {
      src: "url(\"/assets/fonts/HelveticaNeueCyr-Medium.woff2\")",
      fontWeight: "500 900",
      fontDisplay: "swap",
    },
  ]
);
