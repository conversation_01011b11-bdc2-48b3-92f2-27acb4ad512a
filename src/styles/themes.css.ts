import { createTheme } from "@vanilla-extract/css";
import { primaryFont, secondaryFont } from "./fonts.css";

export const [themeClass, theme] = createTheme(
  {
    colors: {
      primary: {
        castletonGreen: "#003D23",
        castletonGreen20: "#003D2333",
        castletonGreenPressed: "#002B19",
        asidGreen: "#13F975",
        asidGreenPressed: "#14E46C",
        ivory: "#F5F1E8",
        softWhite: "#FFFDF8",
        softWhite20: "#FFFDF820",
        softWhitePressed: "#F7F5F0",
        orange: "#FBB449",
        error: "#D9351F",
        error20: "#D9351F20",
        errorPressed: "#C22A15",
      },
      grayscale: {
        100: "#DEDED6",
        200: "#969691",
      },
    },
    fonts: {
      primary: primaryFont,
      secondary: secondaryFont,
    },
  }
);