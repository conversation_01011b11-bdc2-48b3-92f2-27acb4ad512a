import {
  createVar
} from "@vanilla-extract/css";
import {
  createSprinkles,
  defineProperties
} from "@vanilla-extract/sprinkles";
import {
  breakpoints, sprinklesBreakpoints
} from "./constants.css";

const colsVar = createVar(
);

const gridMaxColumns = 10;

// EXAMPLE:
// <div
//       className={gridSprinkle({
//         type: "grid",
//       })}
// >
//       <div
//         className={gridSprinkle({
//           type: "item",
//           cols: { desktop: 1, tablet: 5, mobile: 10 },
//         })}
//       >
//         GRID CHECK
//       </div>
// </div>

export const gridProperties = defineProperties(
  {
    conditions: sprinklesBreakpoints,
    defaultCondition: "mobile",
    properties: {
      justifyContent: ["center", "flex-start", "flex-end", "space-between"],
      alignItems: ["stretch", "start", "end", "center"],
      justifyItems: ["center", "flex-start", "flex-end", "space-between", "stretch"],
      justifySelf: ["stretch", "start", "end", "center"],
      alignSelf: ["stretch", "start", "end", "center"],
      display: ["none", "flex", "block", "inline", "inline-flex", "grid"],
      type: {
        item: {
          gridColumn: `span ${colsVar}`,
        },
        grid: {
          display: "grid",
          gridTemplateColumns: `repeat(${gridMaxColumns}, 1fr)`,
          columnGap: 12,

          "@media": {
            [breakpoints.tablet]: {
              columnGap: 24,
            }
          }
        },
      },
      cols: Array(
        gridMaxColumns
      )
        .fill(
          0
        )
        .reduce(
          (
            o, _, idx
          ) => ({
            ...o,
            [idx + 1]: {
              vars: {
                [colsVar]: `${idx + 1}` 
              } 
            },
          }),
          {
          }
        ),
    },
  }
);

export const gridSprinkle = createSprinkles(
  gridProperties
);

// It's a good idea to export the Sprinkles type too
export type Sprinkles = Parameters<typeof gridSprinkle>[0];
