import { style } from "@vanilla-extract/css";
import { theme } from "@/styles/themes.css";
import { breakpoints } from "@/styles/constants.css";
import { mode } from "@/styles/functions.css";

export const root = style({
  backgroundColor: theme.colors.primary.ivory,
  color: theme.colors.primary.castletonGreen,
  padding: "40px 0",

  "@media": {
    [breakpoints.tablet]: {
      padding: "60px 0",
    },
    [breakpoints.desktop]: {
      padding: "80px 0",
    },
    [breakpoints.ultraWide]: {
      padding: "100px 0",
    },
  },

  selectors: {
    [mode("residential")]: {
      backgroundColor: theme.colors.primary.castletonGreen,
      color: theme.colors.primary.softWhite,
    }
  },
});

export const contentWrapper = style({
  display: "flex",
  flexDirection: "column",
  gap: "32px",
  maxWidth: "1200px",
  margin: "0 auto",

  "@media": {
    [breakpoints.tablet]: {
      flexDirection: "row",
      alignItems: "center",
      gap: "40px",
    },
    [breakpoints.desktop]: {
      gap: "60px",
    },
    [breakpoints.ultraWide]: {
      gap: "80px",
      maxWidth: "1600px",
    },
  },

  selectors: {
    [mode("residential")]: {
      padding: "40px"
    }
  },
});

export const imageWrapper = style({
  position: "relative",
  width: "100%",
  height: "300px",
  borderRadius: "16px",
  overflow: "hidden",
  order: 2, // Image comes after title but before description on mobile

  "@media": {
    [breakpoints.tablet]: {
      flex: "1 1 45%",
      height: "400px",
      order: 1, // Image moves to the left on tablet and up
    },
    [breakpoints.desktop]: {
      height: "550px",
      width: "617px"
    },
    [breakpoints.ultraWide]: {
      height: "600px",
      borderRadius: "24px",
    },
  },
});

export const image = style({
  objectFit: "cover",
});

export const textWrapper = style({
  display: "flex",
  flexDirection: "column",
  gap: "20px",

  "@media": {
    [breakpoints.tablet]: {
      flex: "1 1 55%",
      gap: "24px",
      order: 2,
    },
    [breakpoints.desktop]: {
      gap: "32px",
      width: "600px"
    },
    [breakpoints.ultraWide]: {
      gap: "40px",
    },
  },
});

export const title = style({
  fontSize: "32px",
  lineHeight: "1.2",
  color: theme.colors.primary.castletonGreen,
  fontFamily: theme.fonts.primary,
  order: 1,

  "@media": {
    [breakpoints.tablet]: {
      fontSize: "32px",
    },
    [breakpoints.desktop]: {
      fontSize: "60px",
      lineHeight: "1.1",
      maxWidth: "600px",
    },
    [breakpoints.ultraWide]: {
      fontSize: "48px",
      lineHeight: "1.1",
    },
  },

  selectors: {
    [mode("residential")]: {
      color: theme.colors.primary.castletonGreen,
    }
  },
});
export const italicStrong = style({
  fontStyle: "italic",
  fontWeight: "bold",

  selectors: {
    [mode("residential")]: {
      color: theme.colors.primary.asidGreen,
    }
  },
});

export const description = style({
  fontWeight: "500",
  fontSize: "16px",
  order: 3, // Description comes after image on mobile

  "@media": {
    [breakpoints.tablet]: {
      fontSize: "18px",
      order: 2, // Order doesn't matter on tablet+ as it's inside textWrapper
    },
    [breakpoints.desktop]: {
      fontSize: "20px",
    },
    [breakpoints.ultraWide]: {
      fontSize: "22px",
    },
  },

  selectors: {
    [mode("residential")]: {
      color: theme.colors.primary.castletonGreen,
    }
  },
});

export const boroughsGrid = style({
  width: "100%",
  display: "grid",
  gridTemplateColumns: "repeat(2, 1fr)", // Two columns on mobile
  rowGap: "12px",
  columnGap: "15px",
  marginTop: "16px",
  paddingLeft: 0,
  listStyle: "none",
  order: 4, // List comes after description

  "@media": {
    [breakpoints.tablet]: {
      gridTemplateColumns: "repeat(2, 1fr)",
      marginTop: "24px",
      maxWidth: "450px",
      order: 3, // Order doesn't matter on tablet+ as it's inside textWrapper
    },
    [breakpoints.desktop]: {
      gridTemplateColumns: "repeat(3, 1fr)",
      maxWidth: "600px",
    },
    [breakpoints.ultraWide]: {
      maxWidth: "800px",
      rowGap: "16px",
    },
  },
});

export const boroughItem = style({
  display: "flex",
  alignItems: "center",
  fontSize: "14px",
  gap: "8px",
  fontWeight: "500",

  "@media": {
    [breakpoints.tablet]: {
      fontSize: "15px",
    },
    [breakpoints.desktop]: {
      fontSize: "16px",
    },
    [breakpoints.ultraWide]: {
      fontSize: "18px",
      gap: "12px",
    },
  },

  selectors: {
    [mode("residential")]: {
      color: theme.colors.primary.castletonGreen,
    }
  },
});

export const locationIcon = style({
  flexShrink: 0,

  "@media": {
    [breakpoints.ultraWide]: {
      width: "16px",
      height: "20px",
    },
  },
});

export const button = style({
  backgroundColor: theme.colors.primary.asidGreen,
  color: theme.colors.primary.castletonGreen,
  marginTop: "16px",
  padding: "12px 24px",
  fontWeight: "600",
  fontSize: "15px",
  borderRadius: "999px",
  width: "fit-content",
  order: 5, // Button comes last

  "@media": {
    [breakpoints.tablet]: {
      marginTop: "24px",
      order: 4, // Order doesn't matter on tablet+ as it's inside textWrapper
    },
    [breakpoints.desktop]: {
      // marginTop: "32px",
      fontSize: "28px",
    },
    [breakpoints.ultraWide]: {
      marginTop: "40px",
      padding: "16px 32px",
      fontSize: "18px",
      // Make the button appear larger on ultra-wide screens through CSS
      transform: "scale(1.1)",
      transformOrigin: "left center",
    },
  },

  selectors: {
    [mode("residential")]: {
      backgroundColor: theme.colors.primary.asidGreen,
      color: theme.colors.primary.castletonGreen,
    },
    [mode("residential") + ":hover"]: {
      backgroundColor: theme.colors.primary.asidGreenPressed,
      color: theme.colors.primary.castletonGreen,
    }
  },
});

