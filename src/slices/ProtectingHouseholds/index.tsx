import { SliceComponentProps } from "@prismicio/react";
import ProtectingHouseholds, { ProtectingHouseholdsProps as ComponentProps } from "./ProtectingHouseholds";

/**
 * Props for `ProtectingHouseholds`.
 */
export type ProtectingHouseholdsProps = SliceComponentProps<any>;

/**
 * Component for "ProtectingHouseholds" Slices.
 */
const ProtectingHouseholdsSlice = ({ slice, context }: ProtectingHouseholdsProps): JSX.Element => {
  // Create a properly typed props object for the component
  const componentProps: ComponentProps = {
    slice: slice,
    context: context
  };

  return <ProtectingHouseholds {...componentProps} />;
};

export default ProtectingHouseholdsSlice;
