"use client";
import Container from "@/components/Container";
import { Content } from "@prismicio/client";
import { SliceComponentProps } from "@prismicio/react";
import * as styles from "./PeacePlanCta.css";
import { PrismicRichText } from "node_modules/@prismicio/react/dist/react-server/PrismicRichText";
import { PrismicNextImage } from "@prismicio/next";
import Typography from "@/components/Typography";
import Button from "@/components/Button";
import { toCamelCase } from "@/utils/helpers";
import Link from "next/link";
import useStore from "@/hooks/useStore";

export type PeacePlanCtaProps = SliceComponentProps<Content.PeacePlanCtaSlice>;

const PeacePlanCta = ({ slice }: PeacePlanCtaProps): JSX.Element => {
  const { auth } = useStore();
  return (
    <Container
      removeBorderRadius
    >
      <section
        data-slice-type={slice.slice_type}
        data-slice-variation={slice.variation}
        className={styles.root}
        id={slice.primary.header_anchor_name ? toCamelCase(slice.primary.header_anchor_name) : ""}
      >
        <div
          className={styles.imageContainer}
        >
          <PrismicNextImage
            field={slice.primary.image}
            className={styles.image}
          />
        </div>
        <div
          className={styles.title}
        >
          <PrismicRichText
            field={slice.primary.title}
          />
        </div>
        <div
          className={styles.description}
        >
          <PrismicRichText
            field={slice.primary.description}
          />
        </div>
        <Button
          color='secondary'
          isAnimated
          className={styles.button}
          as={Link}
          href={auth.isAuthorized ? "/profile/plans#peace" : "/protect"}
          alternateHover
        >See other plans</Button>
      </section>
    </Container>
  );
};

export default PeacePlanCta;
