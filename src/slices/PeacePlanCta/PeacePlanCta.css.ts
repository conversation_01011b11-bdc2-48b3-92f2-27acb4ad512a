import { breakpoints } from "@/styles/constants.css";
import { theme } from "@/styles/themes.css";
import { globalStyle, style } from "@vanilla-extract/css";

export const root = style({
  position: "relative",
  padding: "32px 20px",
  backgroundColor: theme.colors.primary.asidGreen,
  borderRadius: 16,
  marginBottom: 10,

  "@media": {
    [breakpoints.tablet]: {
      maxWidth: 1304,
      marginTop: 77,
      marginRight: "auto",
      marginBottom: 0,
      marginLeft: "auto",
      padding: "72px 140px 54px 530px",
      borderRadius: "16px 16px 0 0",
    },
  },
});

export const title = style({
  marginBottom: 16,
  fontFamily: theme.fonts.primary,
  fontSize: "36px",
  fontWeight: 400,
  lineHeight: "95%",
  letterSpacing: "-0.72px",
  color: theme.colors.primary.castletonGreen,
  textAlign: "center",

  "@media": {
    [breakpoints.tablet]: {
      marginBottom: 50,
      fontSize: "56px",
      letterSpacing: "-1.12px",
      textAlign: "left",
    },
  },
});

globalStyle(`${title} strong`, {
  "@media": {
    [breakpoints.tablet]: {
      fontSize: "64px",
      letterSpacing: "-1.28px",
    },
  },
});

export const description = style({
  position: "relative",
  zIndex: 1,
  marginBottom: 206,
  fontFamily: theme.fonts.secondary,
  fontSize: 16,
  fontWeight: 400,
  lineHeight: "130%",
  color: theme.colors.primary.castletonGreen,
  textAlign: "center",

  "@media": {
    [breakpoints.tablet]: {
      marginBottom: 58,
      fontSize: 20,
      textAlign: "left",
      lineHeight: "120%",
      opacity: 0.8,
    },
  },
});

export const imageContainer = style({
  position: "absolute",
  left: 0,
  bottom: 0,
  width: 314,
  height: 293,
  overflow: "hidden",

  "@media": {
    [breakpoints.tablet]: {
      top: -77,
      left: -16,
      bottom: 0,
      width: 530,
      minHeight: 497,
      height: "initial"
    },
  },
});

export const image = style({
  display: "block",
  objectFit: "cover",
  maxWidth: "100%",
  height: "100%",
});

export const button = style({
  position: "relative",
  zIndex: 1,
  width: 315,
  margin: "0 auto",
  minHeight: "56px !important",

  "@media": {
    [breakpoints.tablet]: {
      margin: "initial",
      width: 235,
    },
  },
});
