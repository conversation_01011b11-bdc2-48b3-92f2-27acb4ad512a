"use client";
import Container from "@/components/Container";
import { Content } from "@prismicio/client";
import { SliceComponentProps } from "@prismicio/react";
import * as styles from "./HorizontalListSection.css";
import classNames from "classnames";
import { gridSprinkle } from "@/styles/sprinkles.css";
import Typography from "@/components/Typography";
import { PrismicRichText } from "node_modules/@prismicio/react/dist/react-server/PrismicRichText";
import IconButton from "@/components/IconButton";
import { ChevronIcon } from "@/assets/icons/ChevronIcon";
import MemoCheckMarkIcon from "@/assets/icons/CheckMarkIcon";
import Divider from "@/components/Divider/Divider";
import Button from "@/components/Button";
import useScrollHorList from "@/hooks/useScrollHorList";
import BookServiceButton from "@/components/BookServiceButton/BookServiceButton";

/**
 * Props for `HorizontalListSection`.
 */
export type HorizontalListSectionProps =
  SliceComponentProps<Content.HorizontalListSectionSlice>;

/**
 * Component for "HorizontalListSection" Slices.
 */
const HorizontalListSection = ({
  slice,
}: HorizontalListSectionProps): JSX.Element => {
  const {handleNext,handlePrev,setRef} = useScrollHorList();

  return (
    <Container>
      <section
        className={classNames(styles.root,)}
        data-slice-type={slice.slice_type}
        data-slice-variation={slice.variation}
      >
       
        <Typography
          as={"h2"}
          variant="h2"
          fontFamily="primary"
          className={styles.title}
        >
          <PrismicRichText
            field={slice.primary.title}
          />
           
        </Typography>
        <div
          className={styles.controlsWrapper}
        >
          <IconButton
            onClick={handlePrev}
          ><ChevronIcon
              turn="left"
          /></IconButton >
          <IconButton
            onClick={handleNext}
          ><ChevronIcon
              turn="right"
          /></IconButton>
        </div>
        <ul
          ref={setRef}
          className={ classNames(styles.listWrapper)}
        >
          {slice.items.map(({ description,title },idx) => {
            return (
              <li
                key={`${idx}_horizontal_list_item`}
                className={classNames(styles.listItem)}
              >
                <div
                  className={classNames(styles.circleWrapper)}
                >
                  <MemoCheckMarkIcon/>
                </div>
                <div
                  className={styles.listItemTitle}
                >
                  <PrismicRichText
                    field={title}
                  />
                </div>
                <div
                  className={styles.listItemDescriptionWrapper}
                >
                  <Divider
                    withOpacity
                  />
                  <Typography
                    className={styles.listItemDescription}
                    variant="bodyMedium"
                  ><PrismicRichText
                      field={description}
                  />
                  </Typography>
                </div>
                <div
                  className={styles.listItemButtonWrapper}
                >
                  <BookServiceButton
                    className={styles.bookServiceButton}
                  />
                </div>
              </li>
            );
          })}
        </ul>
      
      </section>
    </Container>
  );
};

export default HorizontalListSection;
