import { breakpoints } from "@/styles/constants.css";
import { theme } from "@/styles/themes.css";
import { globalStyle, style } from "@vanilla-extract/css";


const TRANSITION = "400ms ease-in-out";

export const root = style({
  display: "grid",
  gap: 32,
  marginTop: 60,
  marginBottom: 40,
  justifyContent: "start",
  "@media": {
    [breakpoints.tablet]: {
      marginTop: 70,
      marginBottom: 70,
      gap: 72,
      gridTemplateColumns: "1fr auto"
    }
  }
});

export const controlsWrapper = style({
  gridRowStart: 3,
  display: "flex",
  columnGap: 8,
  gridColumn: "span 2",
  justifySelf: "center",
  "@media": {
    [breakpoints.tablet]: {
      alignSelf: "center",
      gridRowStart: "auto",
      gridColumn: "auto",
      justifySelf: "auto",
    }
  }
});

export const title = style({
  gridColumn: "span 2",
  "@media": {
    [breakpoints.tablet]: {
      gridColumn: "auto",
    }
  }

});

export const circleWrapper = style({
  width: "3rem",
  height: "3rem",
  borderRadius: "50%",
  display: "grid",
  placeItems: "center",
  fontSize: 20,
  backgroundColor: theme.colors.primary.asidGreen,
  color: theme.colors.primary.castletonGreen,
  marginBottom: "auto",
  transition: `all ${TRANSITION}`,

  "@media": {
    [breakpoints.tablet]: {
      backgroundColor: theme.colors.primary.castletonGreen,
      color: theme.colors.primary.softWhite,
    }
  }
});

export const listWrapper = style({
  display: "grid",
  gridAutoFlow: "column",
  columnGap: 24,
  listStyle: "none",
  padding: 0,
  overflow: "auto",
  gridColumn: "span 2",
  scrollSnapType: "x mandatory",
  // margin: "0 10px",
  "@media": {
    [breakpoints.tablet]: {
      margin: 0,
    }
  }
});

export const listItem = style({
  scrollSnapAlign: "center",
  display: "flex",
  flexDirection: "column",
  borderRadius: 24,
  backgroundColor: theme.colors.primary.castletonGreen,
  color: theme.colors.primary.softWhite,
  width: "calc(100dvw - 20px)",
  height: 328,
  padding: "20px 20px 24px 20px",
  "@media": {
    [breakpoints.tablet]: {
      transition: `background-color ${TRANSITION}`,
      width: 419,
      height: 388,
      padding: "32px 32px 0 32px",
      backgroundColor: theme.colors.primary.softWhite,
      color: theme.colors.primary.castletonGreen,
      selectors: {
        "&:hover": {
          color: theme.colors.primary.softWhite,
          backgroundColor: theme.colors.primary.castletonGreen,
        }
      }
    }
  },
});

export const listItemTitle = style({
  fontSize: 28,
  letterSpacing: -0.32,
  fontWeight: 500,
  zIndex: 2,
  "@media": {
    [breakpoints.tablet]: {
      fontSize: 32,
    }
    
  }
});

export const listItemDescriptionWrapper = style({
  display: "grid",
  rowGap: 16,
  marginTop: 16,
  marginBottom: 40,
  "@media": {
    [breakpoints.tablet]: {
      marginBottom: -65,
      transition: `margin-bottom ${TRANSITION}`
    }
  }
});

export const listItemDescription = style({
  
});

export const listItemButtonWrapper = style({
  "@media": {
    [breakpoints.tablet]: {
      zIndex: 1,
      backgroundColor: "inherit",
      paddingTop: 40,
      paddingBottom: 32,
    }
  }
});


globalStyle(`${listItem}:hover ${circleWrapper}`,{
  "@media": {
    [breakpoints.tablet]: {
      backgroundColor: theme.colors.primary.asidGreen,
      color: theme.colors.primary.castletonGreen
    }
  }
});

globalStyle(`${listItem}:hover ${listItemDescriptionWrapper}`,{
  "@media": {
    [breakpoints.tablet]: {
      marginBottom: 0,
    }
  }
});

export const bookServiceButton = style({
  minWidth: "100%",
  minHeight: "56px",

  "@media": {
    [breakpoints.tablet]: {
      minWidth: "inherit",
    }
  }
});
