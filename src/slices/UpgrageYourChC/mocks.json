[{"__TYPE__": "SharedSliceContent", "variation": "default", "primary": {"title": {"__TYPE__": "StructuredTextContent", "value": [{"type": "paragraph", "content": {"text": "Achieve Optimal Heating Performance With A Central Heating Powerflush", "spans": [{"type": "em", "start": 36, "end": 69}]}, "direction": "ltr"}]}, "description": {"__TYPE__": "StructuredTextContent", "value": [{"type": "paragraph", "content": {"text": "Ensure your heating system runs efficiently with a thorough Powerflush. Designed to remove sludge, rust, and debris, a Powerflush enhances heating efficiency and ensures optimal performance. Experience the benefits of a cleaner, more effective heating system and lower energy bills.", "spans": []}, "direction": "ltr"}]}, "image": {"origin": {"id": "main", "url": "https://images.unsplash.com/photo-1589321599763-d66926c29613", "width": 3168, "height": 4752}, "url": "https://images.unsplash.com/photo-1589321599763-d66926c29613", "width": 3168, "height": 4752, "edit": {"zoom": 1, "crop": {"x": 0, "y": 0}, "background": "transparent"}, "credits": null, "alt": null, "__TYPE__": "ImageContent", "thumbnails": {}}, "second_description": {"__TYPE__": "StructuredTextContent", "value": [{"type": "paragraph", "content": {"text": "Are you thinking about enhancing your home's heating system? ", "spans": []}, "direction": "ltr"}]}, "third_description": {"__TYPE__": "StructuredTextContent", "value": [{"type": "paragraph", "content": {"text": "You're in the right place!", "spans": []}, "direction": "ltr"}]}, "header_anchor_name": {"__TYPE__": "<PERSON><PERSON><PERSON><PERSON>", "value": "handsome", "type": "Text"}}, "items": []}, {"__TYPE__": "SharedSliceContent", "variation": "second", "primary": {"title": {"__TYPE__": "StructuredTextContent", "value": [{"type": "paragraph", "content": {"text": "Achieve Optimal Heating Performance With A Central Heating Powerflush", "spans": [{"type": "em", "start": 36, "end": 69}, {"type": "strong", "start": 36, "end": 69}]}, "direction": "ltr"}]}, "description": {"__TYPE__": "StructuredTextContent", "value": [{"type": "paragraph", "content": {"text": "Ensure your heating system runs efficiently with a thorough Powerflush. Designed to remove sludge, rust, and debris, a Powerflush enhances heating efficiency and ensures optimal performance. Experience the benefits of a cleaner, more effective heating system and lower energy bills.", "spans": []}, "direction": "ltr"}]}, "image": {"origin": {"id": "main", "url": "https://images.unsplash.com/photo-1496181133206-80ce9b88a853", "width": 5243, "height": 3495}, "url": "https://images.unsplash.com/photo-1496181133206-80ce9b88a853", "width": 5243, "height": 3495, "edit": {"zoom": 1, "crop": {"x": 0, "y": 0}, "background": "transparent"}, "credits": null, "alt": null, "__TYPE__": "ImageContent", "thumbnails": {}}, "right_description": {"__TYPE__": "StructuredTextContent", "value": [{"type": "paragraph", "content": {"text": "Nisi officia duis aute dolor enim consectetur quis proident fugiat dolor deserunt enim. Dolor sint ea ut quis non magna nostrud voluptate do pariatur proident minim culpa aliqua et. Sunt non quis deserunt qui."}}]}, "left_description": {"__TYPE__": "StructuredTextContent", "value": [{"type": "paragraph", "content": {"text": "Culpa veniam ut dolore in qui. Cillum enim ipsum et labore laborum officia officia dolore qui laboris amet."}}]}, "second_description": {"__TYPE__": "StructuredTextContent", "value": [{"type": "paragraph", "content": {"text": "Are you thinking about enhancing your home's heating system? ", "spans": []}, "direction": "ltr"}]}, "third_description": {"__TYPE__": "StructuredTextContent", "value": [{"type": "paragraph", "content": {"text": "You're in the right place!", "spans": []}, "direction": "ltr"}]}, "header_anchor_name": {"__TYPE__": "<PERSON><PERSON><PERSON><PERSON>", "value": "extra", "type": "Text"}}, "items": [{"__TYPE__": "GroupItemContent", "value": []}]}, {"__TYPE__": "SharedSliceContent", "variation": "third", "primary": {"title": {"__TYPE__": "StructuredTextContent", "value": [{"type": "paragraph", "content": {"text": "Upgrade Your Home with our Boiler Service", "spans": [{"type": "strong", "start": 27, "end": 41}]}, "direction": "ltr"}]}, "description": {"__TYPE__": "StructuredTextContent", "value": [{"type": "paragraph", "content": {"text": "Discover our modern boiler range for efficient heating and hot water, offering comfort and convenience. Choose from compact combi boilers to versatile system and regular boilers for your ideal home solution.", "spans": []}, "direction": "ltr"}]}, "second_description": {"__TYPE__": "StructuredTextContent", "value": [{"type": "paragraph", "content": {"text": "Are you thinking about enhancing your home boiler? ", "spans": []}, "direction": "ltr"}]}, "third_description": {"__TYPE__": "StructuredTextContent", "value": [{"type": "paragraph", "content": {"text": "You're in the right place!", "spans": []}, "direction": "ltr"}]}, "image": {"origin": {"id": "main", "url": "https://images.unsplash.com/photo-1547082299-de196ea013d6", "width": 4272, "height": 2848}, "url": "https://images.unsplash.com/photo-1547082299-de196ea013d6", "width": 4272, "height": 2848, "edit": {"zoom": 1, "crop": {"x": 0, "y": 0}, "background": "transparent"}, "credits": null, "alt": null, "__TYPE__": "ImageContent", "thumbnails": {}}, "header_anchor_name": {"__TYPE__": "<PERSON><PERSON><PERSON><PERSON>", "value": "fun", "type": "Text"}}, "items": [{"__TYPE__": "GroupItemContent", "value": []}]}, {"__TYPE__": "SharedSliceContent", "variation": "fourth", "primary": {"title": {"__TYPE__": "StructuredTextContent", "value": [{"type": "paragraph", "content": {"text": "Qui anim eiusmod duis dolor eiusmod amet anim in."}}]}, "description": {"__TYPE__": "StructuredTextContent", "value": [{"type": "paragraph", "content": {"text": "Ea sit enim in cupidatat Lorem occaecat sit cupidatat ad. Anim aliquip non do qui esse dolore duis ex ex proident ullamco ut."}}]}, "second_description": {"__TYPE__": "StructuredTextContent", "value": [{"type": "paragraph", "content": {"text": "Non laboris ipsum sunt irure deserunt reprehenderit excepteur et fugiat dolore irure ad laboris fugiat minim."}}]}, "third_description": {"__TYPE__": "StructuredTextContent", "value": [{"type": "paragraph", "content": {"text": "Elit et exercitation id irure non proident qui. Ut ea aliqua commodo pariatur nisi duis laboris sunt consectetur deserunt eu ad magna reprehenderit nostrud. Deserunt ipsum est sunt aute nostrud dolor in excepteur culpa nulla."}}]}, "image": {"origin": {"id": "main", "url": "https://images.unsplash.com/photo-1596195689404-24d8a8d1c6ea", "width": 5000, "height": 4613}, "url": "https://images.unsplash.com/photo-1596195689404-24d8a8d1c6ea", "width": 5000, "height": 4613, "edit": {"zoom": 1, "crop": {"x": 0, "y": 0}, "background": "transparent"}, "credits": null, "alt": null, "__TYPE__": "ImageContent", "thumbnails": {}}, "header_anchor_name": {"__TYPE__": "<PERSON><PERSON><PERSON><PERSON>", "value": "label", "type": "Text"}, "move_to_anchor_id": {"__TYPE__": "<PERSON><PERSON><PERSON><PERSON>", "value": "nodded", "type": "Text"}}, "items": [{"__TYPE__": "GroupItemContent", "value": []}]}]