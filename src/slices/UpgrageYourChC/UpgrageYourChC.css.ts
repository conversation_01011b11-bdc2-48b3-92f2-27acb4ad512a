import { breakpoints } from "@/styles/constants.css";
import { theme } from "@/styles/themes.css";
import { style, styleVariants } from "@vanilla-extract/css";

export const container = style({
  ":after": {
    content: "",
    display: "block",
    maxWidth: "100%",
    height: 1,
    backgroundColor: theme.colors.grayscale[100],
    margin: "0 20px",
  },

  "@media": {
    [breakpoints.tablet]: {
      ":after": {
        content: "none",
      },
    },
  },
});

export const root = style({
  // display: "grid",
  // gridTemplateColumns: "repeat(10, 1fr)",
  padding: "40px 0",
  "@media": {
    [breakpoints.tablet]: {
      padding: "70px 0",
      rowGap: 60
    }}
});

const secondDescriptionBase = style({
  fontWeight: 700,
  lineHeight: "120%", 
  fontSize: 22,
  display: "grid",
  alignItems: "end",
  textAlign: "center",
  marginBottom: 32,
  gridColumn: "span 10",
  
  "@media": {
    [breakpoints.tablet]: {
      textAlign: "start",
      letterSpacing: "-0.3px",
      fontSize: 30,
      marginBottom: 0,
    }
  }
});
const secondDescriptionDefault = style({
  marginTop: 30,
  "@media": {
    [breakpoints.tablet]: {
      marginTop: 0,
      borderTop: `1px solid ${theme.colors.grayscale[100]}`,
      gridColumn: "span 4"
    }
  }
});

const secondDescriptionSecond = style({
  gridColumn: "span 10",
  marginTop: 32,
  "@media": {
    [breakpoints.tablet]: {
      marginTop: 0,
      gridColumn: "span 4",
      gridRow: 4
    }
  }
});
const secondDescriptionThird = style({
  marginTop: 30,
  "@media": {
    [breakpoints.tablet]: {
      marginTop: 0,
      gridColumn: "-1 / 7",
    }
  }
});

const secondDescriptionFourth= style({
  display: "none"
});


export const secondDescription = styleVariants({
  default: [secondDescriptionBase, secondDescriptionDefault],
  second: [secondDescriptionBase, secondDescriptionSecond],
  third: [secondDescriptionBase,secondDescriptionThird],
  fourth: [secondDescriptionFourth]
});



const titleBase = style({
  gridColumn: "span 10",
  textAlign: "center",
  gridRow: -1,
  "@media": {
    [breakpoints.tablet]: {
      gridRow: 0,
      textAlign: "start",
    }
  }
});

const titleDefault = style({
  "@media": {
    [breakpoints.tablet]: {
      gridColumn: "6 / 11",
    }
  }
});
const titleSecond = style({
  "@media": {
    [breakpoints.tablet]: {
      gridColumn: "span  10",
    }
  }
});

const titleThird = style({
  "@media": {
    [breakpoints.tablet]: {
      gridColumn: "span 5",
    }
  }
});

const titleFourth = style({
  marginBottom: 24,
  "@media": {
    [breakpoints.tablet]: {
      marginBottom: 0,
      gridRow:  "span 2",
      gridColumn: "span 5",
    }
  }
});

export const title = styleVariants({
  default: [titleBase, titleDefault],
  second: [titleBase, titleSecond],
  third: [titleBase,titleThird],
  fourth: [titleBase,titleFourth],
});


const descriptionBase = style({
  opacity: .8,
  gridColumn: "span 10",
  gridRow: 6,
  padding: "0 20px",
  marginTop: 32,
  "@media": {
    [breakpoints.tablet]: {
      gridColumn: "6 / 11",
      marginTop: 0,
      padding: 0,
      gridRow: "auto",
    }
  }
});

const descriptionDefault= style({
  "@media": {
    [breakpoints.tablet]: {
      gridColumn: "6 / 10",
    }
  }
});
const descriptionSecond= style({
  gridColumn: "span 10",
  fontSize: "20px !important",
  "@media": {
    [breakpoints.tablet]: {
      gridColumn: "-1 / 7",
      gridRow: "span 2",
      marginRight: 40
    }
  }
});
const descriptionThird= style({
  "@media": {
    [breakpoints.tablet]: {
      gridColumn: "-1 / 7",
      gridRow: -1,
    }
  }
});

const descriptionFourth = style({
  "@media": {
    [breakpoints.tablet]: {
      gridColumn: "-1 / 7",
      gridRow: -1,
    }
  }

});

export const description = styleVariants({
  default: [descriptionBase, descriptionDefault],
  second: [descriptionBase, descriptionSecond],
  third: [descriptionBase,descriptionThird],
  fourth: [descriptionBase,descriptionFourth],
});

const firstDescriptionBase= style({
  gridColumn: "span 10",
  textAlign: "center",
  "@media": {
    [breakpoints.tablet]: {
      textAlign: "start",
    }
  }

});
const firstDescriptionDefault= style({
  fontSize: "18px !important",

  "@media": {
    [breakpoints.tablet]: {
      gridColumn: "span 4",
      fontSize: "24px !important",
    }
  }
});
const firstDescriptionSecond= style({
  fontSize: "18px !important",

  "@media": {
    [breakpoints.tablet]: {
      gridColumn: "span 4",
      fontSize: "24px !important",
    }
  }
});
const firstDescriptionThird= style({
  fontSize: "18px !important",
  "@media": {
    [breakpoints.tablet]: {
      fontSize: "24px !important",
      gridColumn: "span 5",
    }
  }
});

const firstDescriptionFourth= style({
  display: "none"
});



export const firstDescription = styleVariants({
  default: [firstDescriptionBase, firstDescriptionDefault],
  second: [firstDescriptionBase, firstDescriptionSecond],
  third: [firstDescriptionBase,firstDescriptionThird],
  fourth: [firstDescriptionFourth]
});

export const imageWrapper = style({
  borderRadius: 24,
  gridColumn: "span 10",
  overflow: "hidden",
  height: 171,
  "@media": {
    [breakpoints.tablet]: {
      height: 629,
    }
  }
});

export const image = style({
  width: "100%",
  height: "100%",
  objectFit: "cover",
});


const dividerBase = style({
  margin: "24px 20px",
  position: "relative",
  borderTop: `1px solid ${theme.colors.grayscale[100]}`,
  gridColumn: "span 10",
  display: "flex",
  alignItems: "center",
  justifyContent: "center",
  "@media": {
    [breakpoints.tablet]: {
      // margin: "70px 0"
      margin: 0
    }
  }
});

const dividerDefault = style({
  display: "flex",
  "@media": {
    [breakpoints.tablet]: {
      display: "none",
    }
  }
});
const dividerSecond = style({
  margin: "40px 20px",
  "@media": {
    [breakpoints.tablet]: {
      margin: 0,
      marginTop: 20,
    }
  }
});
const dividerThird = style({});
const dividerFourth = style({
  display: "none"
});

export const divider  = styleVariants({
  default: [dividerBase, dividerDefault],
  second: [dividerBase, dividerSecond],
  third: [dividerBase,dividerThird],
  fourth: [dividerBase,dividerFourth],
});

export const divideIconBase = style({
  borderRadius: "50%",
  width: "3rem",
  height: "3rem",
  backgroundColor: theme.colors.primary.asidGreen,
  margin: "0 auto",
  fontSize: 24,
  display: "grid",
  placeItems: "center",
  zIndex: 1,
  position: "absolute",

});

const divideIconDefault= style({
  display: "none",
  "@media": {
    [breakpoints.tablet]: {

    }
  }
});
const divideIconSecond= style({
  "@media": {
    [breakpoints.tablet]: {

    }
  }
});
const divideIconThird= style({
  display: "none"
});


export const divideIcon  = styleVariants({
  default: [divideIconBase, divideIconDefault],
  second: [divideIconBase, divideIconSecond],
  third: [divideIconBase,divideIconThird],
  fourth: []
});


const buttonWrapperBase = style({
  gridColumn: "span 10",
  gridRow: 7,
  display: "grid",
  marginTop: 24,
  padding: "0 20px",
  "@media": {
    [breakpoints.tablet]: {
      marginTop: 0,
      gridColumn: "-1 / 7",
      gridRow: "auto",
      padding: 0,
      justifySelf: "start"


    }
  }
});

export const buttonWrapper  = styleVariants({
  default: [{  display: "none"}],
  second: [{  display: "none"}],
  third: [{  display: "none"}],
  fourth: [buttonWrapperBase]
});
