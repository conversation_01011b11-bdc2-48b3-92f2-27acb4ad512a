import Container from "@/components/Container";
import { Content } from "@prismicio/client";
import { PrismicRichText, SliceComponentProps } from "@prismicio/react";
import * as styles  from "./UpgrageYourChC.css";
import classNames from "classnames";
import { gridSprinkle } from "@/styles/sprinkles.css";
import Typography from "@/components/Typography";
import { PrismicNextImage } from "@prismicio/next";
import Divider from "@/components/Divider/Divider";
import MemoFireIcon from "@/assets/icons/FireIcon";
import Button from "@/components/Button";
import { toCamelCase } from "@/utils/helpers";
import GetCoverButton from "@/components/GetCoverButton/GetCoverButton";

/**
 * Props for `UpgrageYourChC`.
 */
export type UpgrageYourChCProps =
  SliceComponentProps<Content.UpgrageYourChCSlice>;

/**
 * Component for "UpgrageYourChC" Slices.
 */
const UpgrageYourChC = ({ slice }: UpgrageYourChCProps): JSX.Element => {

  return (
    <Container
      className={styles.container}
    >
      <section
        id={slice.primary.header_anchor_name ? toCamelCase(slice.primary.header_anchor_name) : ""}
        className={classNames(gridSprinkle({type: "grid", }), styles.root)}
        data-slice-type={slice.slice_type}
        data-slice-variation={slice.variation}
      >
        <div
          className={classNames(styles.divider[slice.variation])}
        >
          <div
            className={styles.divideIcon[slice.variation]}
          >
            <MemoFireIcon/>
          </div>
        </div>
        <Typography
          className={classNames(styles.firstDescription[slice.variation])}
          variant="subTitleMedium"
        >
          <PrismicRichText
            field={slice.primary.second_description}
          />
        </Typography>
        
        <Typography
          className={classNames(styles.title[slice.variation])}
          as={"h4"}
          variant="h4"
          fontFamily="primary"
        >
          <PrismicRichText
            field={slice.primary.title}
          />
        </Typography>
      
        <div
          className={classNames(styles.secondDescription[slice.variation])}
        ><PrismicRichText
            field={slice.primary.third_description}
        /></div>
        <Typography
          className={classNames(styles.description[slice.variation])}
          variant="bodyMedium"
        ><PrismicRichText
            field={slice.primary.description}
        /></Typography>
        <div
          className={styles.buttonWrapper[slice.variation]}
        >
          <GetCoverButton
            anchorId={slice.variation === "fourth" ? toCamelCase(slice.primary.move_to_anchor_id as string) : undefined}
            planName="peace"
          />
        </div>
        <div
          className={classNames(styles.imageWrapper)}
        >
          <PrismicNextImage
            className={styles.image}
            field={slice.primary.image}
          />
        </div>

      
      </section>
    </Container>
  );
};

export default UpgrageYourChC;
