"use client";

import { Content } from "@prismicio/client";
import { PrismicRichText, SliceComponentProps } from "@prismicio/react";
import * as styles from "./PhoneAnimationSection.css";
import Container from "@/components/Container";
import { gridSprinkle } from "@/styles/sprinkles.css";
import classNames from "classnames";
import Typography from "@/components/Typography";
import { PrismicNextImage } from "@prismicio/next";
import useObserveIntoView from "@/hooks/useObserveIntoView";
import { useCallback, useRef, useState } from "react";

/**
 * Props for `PhoneAnimationSection`.
 */
export type PhoneAnimationSectionProps =
  SliceComponentProps<Content.PhoneAnimationSectionSlice>;

/**
 * Component for "PhoneAnimationSection" Slices.
 */
const PhoneAnimationSection = ({
  slice,
}: PhoneAnimationSectionProps): JSX.Element => {

  const containerRef = useRef<HTMLDivElement>(null);
  const videoRef = useRef<HTMLVideoElement>(null);
  const [enableAnimation, setEnableAnimation] = useState(false);


  const onInView = useCallback(
    () => {
      setEnableAnimation(true); 
      if(!videoRef.current) return;
      videoRef.current.play();
    },
    [ videoRef.current],
  );

  const onOutOfView = useCallback(
    () => {
      setEnableAnimation(false);
      if(!videoRef.current) return;
      videoRef.current.currentTime = 0;
      videoRef.current.pause();
    },
    [ videoRef.current],
  );

  

  useObserveIntoView(containerRef, { onInView, onOutOfView, threshold: 0.5 });

  return (
    <section
      ref={containerRef}
      data-slice-type={slice.slice_type}
      data-slice-variation={slice.variation}
      className={styles.root}
    >
      <Container
        removeBg
        className={classNames(styles.container, gridSprinkle({type: "grid"}))}
      >
        <div
          className={classNames(styles.leftSide, gridSprinkle({type: "item", cols: {tablet: 6, mobile: 10}}))}
        >
          <Typography
            isGreenItalic
            variant="h4"
            as="h4"
            className={styles.title}
            fontFamily="primary"
          ><PrismicRichText
              field={slice.primary.title}
          /></Typography>
          <div
            className={styles.list}
          >
            {slice.items.map(({description,icon},idx) => {
              return (
                <div
                  key={`${idx}_phone_anim_list_item`}
                  className={styles.listItem[enableAnimation ? "active" : "unactive"]}
                >
                  <div
                    className={styles.logoCircle}
                  >
                    <PrismicNextImage
                      className={styles.logoCircleImg}
                      field={icon}
                    />
                  </div>
                  
                  <div
                    className={styles.listItemDescriptionWrapper}
                  >
                    <Typography
                      variant="bodySmall"
                      className={styles.listItemDescription}
                    >
                      <PrismicRichText
                        field={description}
                      />
                    </Typography>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
        <div
          className={classNames(gridSprinkle({type: "item", justifySelf: "center", cols: {tablet: 4, mobile: 10}}))}
        >
          <div
            className={ styles.phoneVideoWrapper}
          >
            <div
              className={styles.videoWrapper}
            >
              <video
                ref={videoRef}
                className={styles.phoneVideo}
                muted
                loop
                playsInline
                
              >
                <source
                  src={slice.primary.video_in_phone as string}
                  type="video/webm"
                />
                {"I'm sorry; your browser doesn't support HTML5 video."}
              </video>
            </div>
            {/* <PrismicNextImage
              className={styles.phoneImg}
              field={slice.primary.phone_image}
            /> */}
          </div>
        </div>

      </Container>
    </section>
  );
};

export default PhoneAnimationSection;
