import { breakpoints } from "@/styles/constants.css";
import { theme } from "@/styles/themes.css";
import { globalStyle, style, styleVariants } from "@vanilla-extract/css";

export const root = style({
  backgroundColor: theme.colors.primary.castletonGreen,
  padding: "40px 0",
  "@media": {
    [breakpoints.tablet]: {
      padding: "80px 0"
    }}
});

export const container = style({
  backgroundColor: "transparent",
  
});

export const list = style({
  display: "grid",
  rowGap: 12,
  marginTop: 32,
  marginBottom: 40,
  "@media": {
    [breakpoints.tablet]: {
      marginTop: 0,
      marginBottom: 0,
      rowGap: 24,
    }
  }
  // justifyContent: "start"

});

export const title = style({
  color: theme.colors.primary.softWhite,
  textAlign: "center",
  fontSize: "36px !important",

  "@media": {
    [breakpoints.tablet]: {
      textAlign: "start",
      fontSize: "56px !important",
      maxWidth: 734,
    }}
});

const listItemBase = style({
  backgroundColor: theme.colors.primary.softWhite,
  overflow: "hidden",
  display: "grid",
  borderRadius: 16,
  position: "relative",
  gridAutoFlow: "column",
  padding: 16,
  columnGap: 12,
  gridTemplateColumns: "auto 1fr",
  "@media": {
    [breakpoints.tablet]: {
      backgroundColor: "transparent",
      alignItems: "center",
      height: 96,
      columnGap: 0,
      padding: 8,
      borderRadius: 0,
      width: "100%",
      selectors: {
        "&:after": {
          transition: "width 500ms ease-in-out",
          minWidth: 96,
          width: "0%",
          content: "''",
          position: "absolute",
          inset: 0,
          borderRadius: 48,
          backgroundColor: theme.colors.primary.softWhite,
        }
      }
    }
  
  }
});

const listItemActive = style({
  "@media": {
    [breakpoints.tablet]: {
      selectors: {
        "&:after": {
          width: "100%",
        }
      }

    }
  }
});

export const listItem = styleVariants({
  active: [listItemBase,listItemActive],
  unactive: [listItemBase,],
});

export const logoCircle = style({
  borderRadius: "50%",
  height: 40,
  width: 40,
  padding: 12,
  backgroundColor: theme.colors.primary.asidGreen,
  color: theme.colors.primary.castletonGreen,
  zIndex: 1,
  "@media": {
    [breakpoints.tablet]: {
      padding: 26,
      height: 80,
      width: 80,
    }
  }

});
export const logoCircleImg = style({
  width: "100%",
  height: "100%",
  objectFit: "scale-down"
});

export const leftSide = style({
  display: "grid",

  "@media": {
    [breakpoints.tablet]: {
      rowGap: 80,
      gridAutoRows: "max-content"
    }
  }
});

export const listItemDescriptionWrapper = style({
  display: "grid",
  // transition: "grid-template-columns 250ms ease-in-out",
  // gridTemplateColumns: "0fr",
  minHeight: 0,
  width: "fit-content",
  zIndex: 1
  
});

export const listItemDescription = style({
  display: "grid",
  overflow: "hidden",
  position: "relative",
  left: "auto",
  marginRight: 0,
  fontSize: "18px !important",

  "@media": {
    [breakpoints.tablet]: {
      left: "24px",
      marginRight: "32px",
    }
  }
});

export const phoneVideoWrapper = style({
  position:"relative",
  
  borderRadius: 52,
  overflow: "hidden",
  padding: 12,
  width: 320,
  "@media": {
    [breakpoints.tablet]: {
      minHeight: 840,
      width: "fit-content",
    }
  }
  // backgroundColor: theme.colors.primary.softWhite
});

export const phoneImg = style({
  position: "absolute",
  inset: 0,
  zIndex: 1,
  width: "100%",
  height: "100%",
});

export const phoneVideo = style({
  width: "100%"
  
});

export const videoWrapper = style({
  position: "relative",
  overflow: "hidden",
  borderRadius: 52,
});

// globalStyle(`${listItem}:hover ${listItemDescriptionWrapper}`, {
//   gridTemplateColumns: "1fr",
// });

