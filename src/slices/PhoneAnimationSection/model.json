{"id": "phone_animation_section", "type": "SharedSlice", "name": "PhoneAnimationSection", "description": "PhoneAnimationSection", "variations": [{"id": "default", "name": "<PERSON><PERSON><PERSON>", "docURL": "...", "version": "initial", "description": "<PERSON><PERSON><PERSON>", "imageUrl": "", "primary": {"title": {"type": "StructuredText", "config": {"label": "Title", "placeholder": "", "allowTargetBlank": true, "multi": "paragraph,preformatted,heading1,heading2,heading3,heading4,heading5,heading6,strong,em,hyperlink,image,embed,list-item,o-list-item,rtl"}}, "header_anchor_name": {"type": "Text", "config": {"label": "Header anchor name", "placeholder": ""}}, "video_in_phone": {"type": "Text", "config": {"label": "Video in phone", "placeholder": ""}}}, "items": {"description": {"type": "StructuredText", "config": {"label": "Description", "placeholder": "", "allowTargetBlank": true, "multi": "paragraph,preformatted,heading1,heading2,heading3,heading4,heading5,heading6,strong,em,hyperlink,image,embed,list-item,o-list-item,rtl"}}, "icon": {"type": "Image", "config": {"label": "Icon", "constraint": {}, "thumbnails": []}}}}]}