import axios from "axios";
import { headers } from "next/headers";
import { NextRequest } from "next/server";

const LONDON_ID = "london"; 

export const fetchSlice = async () => {
  const headersList = headers();

  const host = headersList.get("x-forwarded-host");
  // console.log("MyHost", host);
  const protocol = headersList.get("x-forwarded-proto") || "http";
  const origin = `${protocol}://${host}`;
  const referer = headersList.get("referer");
  let locationId: string = LONDON_ID;

  // const client = createClient();

  // const page = await client.getByUID("refer_page", uid) as any as ReferPageDocument<string> | undefined;

  // const {results: [emergencyLocations]} = await client.getByType(
  //   "emergency_locations"
  // );
  
  // const response = await fetch(`${origin}/api/reviews`);
  
  // const {reviews, emergencyLocations} = await response.json() as {reviews: GoogleReview[], emergencyLocations:  GroupField<Simplify<EmergencyLocationsDocumentDataLocationsItem>>};
  
  if (referer) {
    const request = new NextRequest(referer);
    const {pathname} = request.nextUrl;
    if(pathname.includes("emergencies")) {
      locationId = pathname.split("/emergencies/")[1] || LONDON_ID;
    }
  }


    
  try {
    
    const response = await axios.get(`${origin}/api/reviews?location=${locationId}`);

    return {
      reviews: (response.data.data ?? []) 
    };
  } catch (e) {
    console.error(e);
    return {
      reviews: [] 
    };
  }


      
};