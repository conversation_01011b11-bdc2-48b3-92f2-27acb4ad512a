import { breakpoints } from "@/styles/constants.css";
import { mode } from "@/styles/functions.css";
import { theme } from "@/styles/themes.css";
import {
  createVar,
  globalStyle,
  keyframes,
  style,
  styleVariants,
} from "@vanilla-extract/css";

export const darkBackground = style({
});

export const container = style({
  scrollSnapAlign: "start",
  scrollSnapStop: "always",
  backgroundColor: theme.colors.primary.ivory,
  paddingTop: 0,
  paddingBottom: 0,

  selectors: {
    [`${darkBackground} &`]: {
      backgroundColor: theme.colors.primary.castletonGreen,
      color: theme.colors.primary.softWhite,
    },
  }
});

export const wrapper = style({
  overflow: "hidden",
  position: "relative",
  zIndex: 1,
  backgroundColor: theme.colors.primary.ivory,

  selectors: {
    [`${darkBackground}&`]: {
      backgroundColor: theme.colors.primary.castletonGreen,
      color: theme.colors.primary.softWhite,
    },
  }
});

export const root = style({
  padding: "40px 20px",
  willChange: "transform",

  //height: "100%",
  "@media": {
    [breakpoints.tablet]: {
      padding: "120px 48px 72px",
    },
  },
});

const titleBase = style({
  fontFamily: theme.fonts.primary,
  marginBottom: "15px",
  position: "relative",
  willChange: "transform, opacity",
  "@media": {
    [breakpoints.tablet]: {
      marginBottom: "74px",
    },
  },
});

globalStyle(`${darkBackground} ${titleBase} strong`, {
  color: theme.colors.primary.asidGreen,
});

globalStyle('span.font-light', {
  fontWeight: 300,
});

const slideTitle = keyframes({
  from: {
    opacity: 0,
    transform: "translateY(50px)",
  },
  to: {
    opacity: 1,
    transform: "translateY(0px)",
  },
});

export const title = styleVariants({
  base: [titleBase],
  beforeAnimate: {
    opacity: 0,
  },
  animate: {
    animation: `1s ${slideTitle} forwards`,
  },
});

const controlBase = style({
  gap: "16px",
  justifyContent: "center",
  willChange: "opacity",
  "@media": {
    [breakpoints.tablet]: {
      justifyContent: "flex-end",
    },
  },
});

export const controlFade = keyframes({
  from: {
    opacity: 0,
  },
  to: {
    opacity: 1,
  },
});

export const control = styleVariants({
  base: [controlBase],
  beforeAnimate: {
    opacity: 0,
  },
  animate: {
    animation: `1s ${controlFade} forwards`,
  },
});

export const reviewsList = style({
  display: "flex",
  gap: 10,
  flexWrap: "nowrap",
  overflowX: "auto",
  overflowY: "hidden",
  scrollSnapType: "x mandatory",
  scrollBehavior: "smooth",
  margin: "0 -30px",
  padding: "16px 30px",
  position: "relative",

  "@media": {
    [breakpoints.tablet]: {
      gap: 24,
      width: "auto",
      margin: "0 calc((100vw - 100%) / -2)",
      padding: "24px calc((100vw - 100%) / 2) 24px",
    },
  },
});

export const positionIdx = createVar();

const reviewBlockBase = style({
  // vars: {
  //   [positionIdx]: "0"
  // },
  margin: 0,
  borderRadius: "16px",
  padding: "20px",
  backgroundColor: theme.colors.primary.softWhite,
  color: theme.colors.primary.castletonGreen,
  scrollSnapAlign: "center",
  display: "flex",
  flexDirection: "column",
  minWidth: "calc(100vw - 60px)",
  willChange: "transform, opacity",
  width: "min-content",

  // left: `calc(16px * var(--position_idx))`,

  "@media": {
    [breakpoints.tablet]: {
      scrollSnapAlign: "none",
      height: 360,
      padding: "32px",
      minWidth: "500px",
    },
  },
});

// const cardAnimation = keyframes({
//   "0%": {
//     transform: "translateX(calc(-16px * var(--position-idx)))",
//   },
//   "100%": {
//     transform: "translateX(0)",
//   },
// });

const reviewBlockCardUnstack = keyframes({
  from: {
    opacity: 0,
    transform: "translateY(100%)",
  },
  to: {
    transform: "translateX(0)",
    opacity: 1,
  },
});

export const reviewBlock = styleVariants({
  base: [reviewBlockBase],
  beforeAnimate: {
    opacity: 0,
  },
  animate: {
    animation: `1s ${reviewBlockCardUnstack} forwards`,
    animationDelay: `calc(${positionIdx} * 100ms)`,
  },
});

export const reviewInfo = style({
  marginTop: "auto",
  display: "flex",
  alignItems: "center",
  justifyContent: "space-between",
  gap: "20px",
});

export const reviewTitle = style({
  marginBottom: "6px",
});

export const reviewContent = style({
  WebkitBoxOrient: "vertical",
  WebkitLineClamp: 6,
  display: "-webkit-box",
  overflow: "hidden",
  textOverflow: "ellipsis",
  marginBottom: 27,

  "@media": {
    [breakpoints.tablet]: {
      marginBottom: "auto",
    },
  },
});

export const reviewRating = style({
  marginBottom: 10,
  color: "#FBB449" ,
  "@media": {
    [breakpoints.tablet]: {
      marginBottom: 20,
    },
  },
});

const slideFromTopKeyFrame = keyframes({
  from: {
    transform: "translateY(0)",
  },
  to: {
    transform: "translateY(15%)",
  },
});

export const slideFromTop = style({
  willChange: "transform",
  animation: `1s ${slideFromTopKeyFrame}`,
});
