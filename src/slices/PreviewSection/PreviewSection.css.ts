import { breakpoints } from "@/styles/constants.css";
import { theme } from "@/styles/themes.css";
import {
  globalStyle,
  keyframes,
  style,
  styleVariants,
} from "@vanilla-extract/css";

export const cards = style({
  display: "flex",
  flexDirection: "column",
  gap: 10,

  "@media": {
    [breakpoints.tablet]: {
      gap: 18,
      //paddingTop: 60,
      //marginTop: -60,
    }
  }
});

export const container = style({
  position: "sticky",
  top: 140,
  overflow: "visible",
  height: "calc(100vh - 200px)",
  scrollSnapAlign: "end",
  scrollSnapStop: "always",
});

export const root = style({
  display: "flex",
  flexDirection: "column-reverse",
  minHeight: "calc(100vh - 140px)",
  position: "relative",
  overflow: "hidden",
  marginBottom: 10,
  //minHeight: "max-content",
  borderRadius: 16,
  "@media": {
    [breakpoints.tablet]: {
      minHeight: "calc(100vh - 200px)",
      flexDirection: "row-reverse",
      borderRadius: 24,
      // aspectRatio: "16 / 9",
      marginBottom: 18,
    },
  },
});

export const rootVatiants = styleVariants({
  hero: [
    root,
    {
      backgroundColor: theme.colors.primary.castletonGreen,
    },
  ],
  withoutReferring: [
    root,
    {
      backgroundColor: theme.colors.primary.castletonGreen,
    },
  ],
  slider: [
    root,
    {
      backgroundColor: theme.colors.primary.ivory,
    },
  ],
  default: [
    root,
    {
      backgroundColor: theme.colors.primary.ivory,
      height: "calc(100vh - 140px)",

      "@media": {
        [breakpoints.tablet]: {
          height: "auto",
        }
      }
    },
  ],
  noAnimation: [root, {
    "@media": {
      [breakpoints.tablet]: {
      }
    }
  }]
});

export const title = style({
  fontFamily: theme.fonts.primary,
  marginBottom: "24px",

  "@media": {
    [breakpoints.tablet]: {
      marginBottom: "40px",
    },
  },

  selectors: {
    [`${rootVatiants.withoutReferring} &`]: {
      margin: "auto 0",
    },
  },
});

globalStyle(`${title} em`, {
  color: theme.colors.primary.asidGreen,
});

export const subtitle = style({
  marginBottom: "20px",
  maxWidth: 405,

  "@media": {
    [breakpoints.tablet]: {
      marginBottom: "24px",
    },
  },
});

export const content = style({
  position: "relative",
  display: "flex",
  flexDirection: "column",
  alignItems: "flex-start",
  padding: "40px 20px",
  color: theme.colors.primary.ivory,
  zIndex: 1,

  selectors: {
    [`${root}:not(${rootVatiants.hero}) &:after`]: {
      content: "",
      position: "absolute",
      top: 0,
      bottom: 0,
      left: 0,
      right: 0,
      background: "linear-gradient(47deg, rgba(4, 13, 13, 0.01) 22.36%, rgba(4, 13, 13, 0.5) 110.24%)",
      pointerEvents: "none",
    },
    [`${root}:not(${rootVatiants.hero}) &`]: {
      position: "absolute",
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
    },
    [`${rootVatiants.hero} &`]: {
      "@media": {
        [breakpoints.tablet]: {
          paddingTop: 120,
        },
      },
    }
  },

  "@media": {
    [breakpoints.tablet]: {
      padding: "120px 72px 48px",
    }
  }
});

export const info = style({
  margin: "auto 0",

  selectors: {
    [`${rootVatiants.hero} &`]: {
      margin: 0,
    },
    [`${rootVatiants.withoutReferring} &`]: {
      margin: 0,
      height: "100%",
      display: "flex",
      flexDirection: "column",
    },
  },
});

export const action = style({
  width: "100%",

  "@media": {
    [breakpoints.tablet]: {
      width: "max-content",
      selectors: {
        [`${rootVatiants.hero} &`]: {
          marginTop: "auto",
        },
      },
    },
  },
});

const imgScaleBase = style({
  width: "100% !important",
  height: "100% !important",
  objectFit: "cover",
  objectPosition: "center",
  willChange: "scale",
});

const scaleDecrease = keyframes({
  from: {
    scale: 1.2,
  },
  to: {
    scale: 1,
  },
});

export const imgScale = styleVariants({
  base: [imgScaleBase],
  animate: {
    animation: `2s ${scaleDecrease} forwards`,
  },
});

export const isHero = style({
  objectFit: "contain",
  objectPosition: "bottom right",

  "@media": {
    [breakpoints.tablet]: {
      marginLeft: "auto",
      marginTop: "auto",
      //left: "auto !important",
      width: "60% !important",
      height: "auto !important",
    }
  }
});

//export const previewborder = style({
//  bottom: 20,
//  position: "sticky",
//  maxWidth: "100vw",
//  marginLeft: 10,
//  marginRight: 10,
//  height: "calc(100vh - 160px)",
//  boxShadow: `0 0 0 60px ${theme.colors.primary.ivory}`,
//  //boxShadow: "0 0 0 60px red",
//  borderRadius: 16,
//  pointerEvents: "none",

//  "@media": {
//    [breakpoints.tablet]: {
//      marginLeft: "auto",
//      marginRight: "auto",
//      height: "calc(100vh - 220px)",
//      maxWidth: "1440px",
//      bottom: 40,
//      borderRadius: 24,
//    }
//  },
//});

export const alignContainer = style({
  scrollSnapAlign: "end",
  scrollSnapStop: "always",
});
