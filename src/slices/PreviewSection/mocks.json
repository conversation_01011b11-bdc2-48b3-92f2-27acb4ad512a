[{"__TYPE__": "SharedSliceContent", "variation": "default", "primary": {"title": {"__TYPE__": "StructuredTextContent", "value": [{"type": "paragraph", "content": {"text": "Voluptate esse nostrud laborum Lorem proident incididunt tempor eiusmod minim amet ad est."}}]}, "subtitle": {"__TYPE__": "StructuredTextContent", "value": [{"type": "paragraph", "content": {"text": "Laborum est veniam aliquip ea quis reprehenderit."}}]}}, "items": [{"__TYPE__": "GroupItemContent", "value": [["link_for_learn_more", {"__TYPE__": "Link<PERSON><PERSON>nt", "value": {"__TYPE__": "ExternalLink", "url": "https://prismic.io"}}], ["preview_image", {"origin": {"id": "main", "url": "https://images.unsplash.com/photo-1496181133206-80ce9b88a853", "width": 5243, "height": 3495}, "url": "https://images.unsplash.com/photo-1496181133206-80ce9b88a853?rect=0%2C0%2C5243%2C3495&w=768&h=512", "width": 768, "height": 512, "edit": {"zoom": 1, "crop": {"x": 0, "y": 0}, "background": "transparent"}, "credits": null, "alt": null, "__TYPE__": "ImageContent", "thumbnails": {"tablet": {"origin": {"id": "tablet", "url": "https://images.unsplash.com/photo-1515378791036-0648a3ef77b2", "width": 5616, "height": 3744}, "url": "https://images.unsplash.com/photo-1515378791036-0648a3ef77b2?rect=0%2C0%2C5616%2C3744&w=375&h=250", "width": 375, "height": 250, "edit": {"zoom": 1, "crop": {"x": 0, "y": 0}, "background": "transparent"}, "credits": null, "alt": null}}}]]}]}, {"__TYPE__": "SharedSliceContent", "variation": "hero", "primary": {"title": {"__TYPE__": "StructuredTextContent", "value": [{"type": "paragraph", "content": {"text": "Aute labore Lorem sit aute mollit dolore. Fugiat ullamco dolore dolor sunt id in cupidatat magna deserunt occaecat. Velit amet ipsum irure amet pariatur irure elit sit proident sunt aute ex occaecat."}}]}, "subtitle": {"__TYPE__": "StructuredTextContent", "value": [{"type": "paragraph", "content": {"text": "Deserunt deserunt exercitation aute. Nisi velit labore amet est proident."}}]}, "action_text": {"__TYPE__": "<PERSON><PERSON><PERSON><PERSON>", "value": "cabin", "type": "Text"}, "action_link": {"__TYPE__": "Link<PERSON><PERSON>nt", "value": {"__TYPE__": "ExternalLink", "url": "http://twitter.com"}}}, "items": [{"__TYPE__": "GroupItemContent", "value": [["preview_image", {"origin": {"id": "main", "url": "https://images.unsplash.com/photo-1547082299-de196ea013d6", "width": 4272, "height": 2848}, "url": "https://images.unsplash.com/photo-1547082299-de196ea013d6?rect=0%2C0%2C4272%2C2848&w=768&h=512", "width": 768, "height": 512, "edit": {"zoom": 1, "crop": {"x": 0, "y": 0}, "background": "transparent"}, "credits": null, "alt": null, "__TYPE__": "ImageContent", "thumbnails": {"tablet": {"origin": {"id": "tablet", "url": "https://images.unsplash.com/photo-1515378960530-7c0da6231fb1", "width": 5616, "height": 3744}, "url": "https://images.unsplash.com/photo-1515378960530-7c0da6231fb1?rect=0%2C0%2C5616%2C3744&w=375&h=250", "width": 375, "height": 250, "edit": {"zoom": 1, "crop": {"x": 0, "y": 0}, "background": "transparent"}, "credits": null, "alt": null}}}]]}]}, {"__TYPE__": "SharedSliceContent", "variation": "without<PERSON><PERSON><PERSON><PERSON>", "primary": {"title": {"__TYPE__": "StructuredTextContent", "value": [{"type": "paragraph", "content": {"text": "Duis magna velit enim aute aliquip id voluptate sunt occaecat. Dolore occaecat est et deserunt."}}]}, "subtitle": {"__TYPE__": "StructuredTextContent", "value": [{"type": "paragraph", "content": {"text": "Aliquip qui culpa aliqua cillum enim sunt et qui anim. Incididunt commodo fugiat laborum ullamco sint ea laborum cupidatat mollit adipisicing consectetur labore laboris reprehenderit amet. Cupidatat excepteur nulla occaecat esse nisi cillum veniam velit magna cupidatat elit dolor sunt irure ipsum."}}]}}, "items": [{"__TYPE__": "GroupItemContent", "value": [["preview_image", {"origin": {"id": "main", "url": "https://images.unsplash.com/photo-1504198070170-4ca53bb1c1fa", "width": 2747, "height": 4120}, "url": "https://images.unsplash.com/photo-1504198070170-4ca53bb1c1fa?rect=0%2C0%2C2747%2C4120&w=768&h=1152", "width": 768, "height": 1152, "edit": {"zoom": 1, "crop": {"x": 0, "y": 0}, "background": "transparent"}, "credits": null, "alt": null, "__TYPE__": "ImageContent", "thumbnails": {"tablet": {"origin": {"id": "tablet", "url": "https://images.unsplash.com/photo-1515378791036-0648a3ef77b2", "width": 5616, "height": 3744}, "url": "https://images.unsplash.com/photo-1515378791036-0648a3ef77b2?rect=0%2C0%2C5616%2C3744&w=375&h=250", "width": 375, "height": 250, "edit": {"zoom": 1, "crop": {"x": 0, "y": 0}, "background": "transparent"}, "credits": null, "alt": null}}}]]}]}, {"__TYPE__": "SharedSliceContent", "variation": "slider", "primary": {}, "items": [{"__TYPE__": "GroupItemContent", "value": [["preview_image", {"origin": {"id": "main", "url": "https://images.unsplash.com/photo-1593642633279-1796119d5482", "width": 4016, "height": 6016}, "url": "https://images.unsplash.com/photo-1593642633279-1796119d5482?rect=0%2C1%2C4016%2C6014&w=768&h=1150", "width": 768, "height": 1150, "edit": {"zoom": 1, "crop": {"x": 0, "y": 1}, "background": "transparent"}, "credits": null, "alt": null, "__TYPE__": "ImageContent", "thumbnails": {"tablet": {"origin": {"id": "tablet", "url": "https://images.unsplash.com/photo-1587614295999-6c1c13675117", "width": 3810, "height": 5715}, "url": "https://images.unsplash.com/photo-1587614295999-6c1c13675117?rect=2%2C0%2C3807%2C5715&w=375&h=563", "width": 375, "height": 563, "edit": {"zoom": 1, "crop": {"x": 2, "y": 0}, "background": "transparent"}, "credits": null, "alt": null}}}], ["title", {"__TYPE__": "StructuredTextContent", "value": [{"type": "paragraph", "content": {"text": "Aute aute mollit non qui occaecat."}}]}], ["subtitle", {"__TYPE__": "StructuredTextContent", "value": [{"type": "paragraph", "content": {"text": "Minim laboris commodo anim pariatur nisi enim nostrud reprehenderit. Nostrud mollit mollit magna do officia non proident incididunt pariatur."}}]}], ["preview_in_full_screen", {"__TYPE__": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": true}], ["action_text", {"__TYPE__": "<PERSON><PERSON><PERSON><PERSON>", "value": "cold", "type": "Text"}], ["action_link", {"__TYPE__": "Link<PERSON><PERSON>nt", "value": {"__TYPE__": "ExternalLink", "url": "https://slicemachine.dev"}}]]}]}]