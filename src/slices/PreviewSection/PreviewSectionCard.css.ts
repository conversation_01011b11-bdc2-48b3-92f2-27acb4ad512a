import { breakpoints } from "@/styles/constants.css";
import { mode, modeGlobal } from "@/styles/functions.css";
import { windowHeight } from "@/styles/global.css";
import { theme } from "@/styles/themes.css";
import {
  globalStyle,
  keyframes,
  style,
  styleVariants,
} from "@vanilla-extract/css";

export const container = style({
  overflow: "visible",
  minHeight: "calc(100vh - 140px)",
  scrollSnapAlign: "start",
  scrollMarginTop: 140,
  scrollSnapStop: "always",
  // paddingBottom: "20px !important",
  paddingLeft: 10,
  paddingRight: 10,
  margin: "0 auto",

  "@media": {
    [breakpoints.tablet]: {
      height: "calc(100vh - 170px)",
      // paddingBottom: "40px !important",
    },
  },

  selectors: {
    [mode("residential")]: {
      backgroundColor: theme.colors.primary.castletonGreen,
    },
  },
});

export const root = style({
  display: "flex",
  flexDirection: "column",
  height: `calc(100vh - (100vh - ${windowHeight}) - 160px)`,
  willChange: "height",
  // transition: "height 0.3s cubic-bezier(0.4, 0, 0.2, 1)",
  // safari scale fix
  // "@supports": {
  //   "(-webkit-touch-callout: none)": {
  //     height: "calc(100dvh - 160px)",
  //     transition: "none",
  //   },
  // },
  position: "relative",
  overflow: "hidden",
  //minHeight: "max-content",
  borderRadius: 16,
  "@media": {
    [breakpoints.tablet]: {
      // minHeight: "calc(100vh - 200px)",
      minHeight: "calc(100vh - 182px)",
      flexDirection: "row-reverse",
      borderRadius: 24,
      // aspectRatio: "16 / 9",
      // marginBottom: 18,
    },
  },
});

export const rootManyMode = style({
  "@media": {
    [breakpoints.tablet]: {
      height: `calc(100vh - (100vh - ${windowHeight}) - 150px)`,
    },
  },
});

export const rootVatiants = styleVariants({
  hero: [
    root,
    {
      backgroundColor: theme.colors.primary.castletonGreen,

      selectors: {
        [mode("residential")]: {
          backgroundColor: theme.colors.primary.asidGreen,
        },
      },
    },
  ],
  withoutReferring: [
    root,
    {
      backgroundColor: theme.colors.primary.castletonGreen,
    },
  ],
  slider: [
    root,
    {
      backgroundColor: theme.colors.primary.ivory,
    },
  ],
  default: [
    root,
    {
      backgroundColor: theme.colors.primary.ivory,
    },
  ],
  noAnimation: [
    root,
    {
      //aspectRatio: "9 / 16",
      height: "auto !important",
      "@media": {
        [breakpoints.tablet]: {
          //aspectRatio: "16 / 9",
        },
      },
    },
  ],
});

export const title = style({
  fontFamily: theme.fonts.primary,
  marginBottom: "24px",
  wordSpacing: "1px",
  fontSize: "36px",
  "@media": {
    [breakpoints.tablet]: {
      marginBottom: "40px",
      fontSize: "52px",
    },
    [breakpoints.desktop]: {
      fontSize: "80px"
    },
    // 'screen and (max-width: 460px)': {
    //   fontSize: "38px",
    //   marginBottom: "10px",
    // }
  },

  selectors: {
    [`${rootVatiants.withoutReferring} &`]: {
      margin: "auto 0",
    },
    [mode("residential")]: {
      color: theme.colors.primary.ivory,
    },
  },
});

globalStyle(`${title} em`, {
  color: theme.colors.primary.asidGreen,
});

export const heroTitle = style({
  selectors: {
    [mode("residential")]: {
      color: theme.colors.primary.castletonGreen,
    },
  },
});

globalStyle(`${modeGlobal("residential")} ${heroTitle} em`, {
  color: theme.colors.primary.castletonGreen,
  fontWeight: "bold",
});

export const subtitle = style({
  marginBottom: "20px",
  maxWidth: 405,
  "@media": {
    [breakpoints.tablet]: {
      marginBottom: "24px",
    },
  },
});

export const heroSubtitle = style({
  fontSize: "16px",
  "@media": {
    'screen and (max-width: 460px)': {
      fontSize: "14px",
    },
    [breakpoints.desktop]: {
      fontSize: "20px",
      maxWidth: 515,
    }
  },
  selectors: {
    [mode("residential")]: {
      color: theme.colors.primary.castletonGreen,
    },
  },
});

export const content = style({
  position: "relative",
  display: "flex",
  flexDirection: "column",
  alignItems: "flex-start",
  padding: "40px 20px 20px 20px",
  color: theme.colors.primary.ivory,
  zIndex: 1,
  minWidth: "50%",

  selectors: {
    [`${root}:not(${rootVatiants.hero}) &:after`]: {
      content: "",
      position: "absolute",
      top: 0,
      bottom: 0,
      left: 0,
      right: 0,
      background:
          "linear-gradient(47deg, rgba(4, 13, 13, 0.01) 22.36%, rgba(4, 13, 13, 0.5) 110.24%)",
      pointerEvents: "none",
    },
    [`${root}:not(${rootVatiants.hero}) &`]: {
      position: "absolute",
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
    },
    [`${rootVatiants.hero} &`]: {
      height: "100%",

      "@media": {
        [breakpoints.tablet]: {
          paddingTop: 50,
        },
      },
    },
  },

  "@media": {
    [breakpoints.tablet]: {
      padding: "120px 0 48px 72px",
    },
  },
});

export const info = style({
  margin: "auto 0",

  selectors: {
    [`${rootVatiants.hero} &`]: {
      margin: 0,
    },
    [`${rootVatiants.withoutReferring} &`]: {
      margin: 0,
      height: "100%",
      display: "flex",
      flexDirection: "column",
    },
  },
});

export const action = style({
  width: "100%",
  position: "relative",

  "@media": {
    [breakpoints.tablet]: {
      top: 0,
      width: "max-content",
    },
  },

  selectors: {
    [`${rootVatiants.hero} &`]: {
      // marginTop: "auto",
    },
  },
});

const imgScaleBase = style({
  width: "100% !important",
  height: "100% !important",
  objectFit: "cover",
  objectPosition: "center",
  willChange: "scale",
  // position: "absolute",
  // top: "425px"
});

const scaleDecrease = keyframes({
  from: {
    scale: 1.2,
  },
  to: {
    scale: 1,
  },
});

export const imgScale = styleVariants({
  base: [imgScaleBase],
  animate: {
    animation: `2s ${scaleDecrease} forwards`,
  },
});

export const isHero = style({
  position: "absolute",
  // bottom: 0,
  top: "125px",
  objectFit: "contain",
  objectPosition: "bottom center",
  // height: "47% !important",

  "@media": {
    [breakpoints.tablet]: {
      position: "static",
      marginLeft: "auto",
      marginTop: "auto",
      width: "50% !important",
      height: "auto !important",
      transformOrigin: "bottom right",
      transform: "scale(1.2)",
    },
    [breakpoints.desktop]: {
      height: "85% !important",
    }
  },
});

export const noAnim = style({
  position: "relative",
  top: "auto",
  order: 2,
});