import Button from "@/components/Button";
import Container from "@/components/Container";
import Link from "@/components/Link";
import Typography from "@/components/Typography";
import useIsAnimationExist from "@/hooks/useIsAnimationExist";
import useObserveIntoView from "@/hooks/useObserveIntoView";
import { gridSprinkle } from "@/styles/sprinkles.css";
import { PrismicNextImage } from "@prismicio/next";
import classNames from "classnames";
import { PrismicRichText } from "node_modules/@prismicio/react/dist/react-server/PrismicRichText";
import { PreviewSectionSliceSlider } from "prismicio-types";
import { FC, useCallback, useLayoutEffect, useRef } from "react";
import * as styles from "./PreviewSectionCard.css";
import { observer } from "mobx-react-lite";
import ModeProvider from "@/components/ModeProvider";

const PREVIEW_IMG_ID = "anim--preview_image";

interface Props {
  title: PreviewSectionSliceSlider["items"][0]["title"]
  subtitle: PreviewSectionSliceSlider["items"][0]["subtitle"]
  action_text: PreviewSectionSliceSlider["items"][0]["action_text"]
  action_link: PreviewSectionSliceSlider["items"][0]["action_link"]
  preview_image: PreviewSectionSliceSlider["items"][0]["preview_image"]
  isFullScreen: PreviewSectionSliceSlider["items"][0]["preview_in_full_screen"]
  variation: "hero" | "default" | "withoutReferring"
  singleMode?: boolean
}

const PreviewSectionCard: FC<Props> = observer(({
  title,
  subtitle,
  action_text,
  action_link,
  preview_image,
  isFullScreen,
  variation,
  singleMode = false,
}) => {
  // const { landign } = useStore();
  const isWithAnimation = useIsAnimationExist();

  const containerRef = useRef<HTMLDivElement>(null);

  const onInView = useCallback(
    (target?: HTMLElement) => {
      if (!target || !isWithAnimation) return;
      const imgElements = target.querySelectorAll(`#${PREVIEW_IMG_ID}`);
      
      imgElements.forEach(img =>  img.classList.add(styles.imgScale.animate));
    },
    [isWithAnimation]
  );

  useObserveIntoView(containerRef, { onInView, isDisabled: !isWithAnimation });

  useLayoutEffect(() => {
    return () => {
      document.documentElement.style.removeProperty("scroll-snap-type");
    };
  }, []);

  return (
    <Container
      removeBg
      withAnimation={isWithAnimation}
      ref={containerRef}
      className={styles.container}
    >
      <section
        style={{ position: "relative" }}
        className={classNames(styles.rootVatiants[variation], {
          [styles.rootVatiants.noAnimation]: !isWithAnimation, 
          [styles.rootManyMode]: !singleMode,}
        )}
      >
        <PrismicNextImage
          id={PREVIEW_IMG_ID}
          className={classNames(
            styles.imgScale.base,
            gridSprinkle({
              display: { mobile: "none", tablet: "block" },
            }),
            {
              [styles.isHero]: variation === "hero"
            }
          )}
          field={preview_image}
        />
        <PrismicNextImage
          id={PREVIEW_IMG_ID}
          className={classNames(
            styles.imgScale.base,
            gridSprinkle({ display: { tablet: "none" } }),
            {
              [styles.isHero]: variation === "hero",
              [styles.noAnim]: !isWithAnimation
            }
          )}
          field={(preview_image as any).tablet}
        />
        <div
          className={styles.content}
        >
          <div
            className={styles.info}
          >
            {variation === "hero" ? (
              <>
                <Typography
                  className={classNames(styles.title, styles.heroTitle,)}
                  variant="h2"
                  as={"h1"}
                >
                  <PrismicRichText
                    field={title}
                  />
                </Typography>
                <Typography
                  className={classNames(styles.subtitle, styles.heroSubtitle)}
                >
                  <PrismicRichText
                    field={subtitle}
                  />
                </Typography>
              </>
            ) : (
              <>
                <Typography
                  className={styles.subtitle}
                >
                  <PrismicRichText
                    field={subtitle}
                  />
                </Typography>
                <Typography
                  className={styles.title}
                  variant="h2"
                  as={"h1"}
                >
                  <PrismicRichText
                    field={title}
                  />
                </Typography>
              </>
            )}
          </div>
          {variation === "hero" && (
            <ModeProvider>
              {(mode) => (
                <Button
                  as={Link}
                  field={action_link}
                  className={styles.action}
                  isAnimated
                  color={mode === "residential" ? "secondary" : "primary"}
                  alternateHover={mode === "residential"}
                >
                  {action_text}
                </Button>          
              )}
            </ModeProvider>
          )}
          {variation === "default" && (
            <Button
              as={Link}
              field={action_link}
              className={styles.action}
            >
              Learn More
            </Button>
          )}
        </div>
      </section>
    </Container>
  );
});

export default PreviewSectionCard;