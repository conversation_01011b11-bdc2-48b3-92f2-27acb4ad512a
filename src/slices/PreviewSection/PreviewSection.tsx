"use client";

import { Content } from "@prismicio/client";
import { SliceComponentProps } from "@prismicio/react";
import { observer } from "mobx-react-lite";
import { Fragment } from "react";
import * as styles from "./PreviewSection.css";
import PreviewSectionCard from "./PreviewSectionCard";

export type PreviewSectionProps =
  SliceComponentProps<Content.PreviewSectionSlice>;

const PreviewSection = observer(({ slice }: PreviewSectionProps): JSX.Element => {
  return (
    <>
      {slice.variation === "slider" ? (
        <div
          className={styles.cards}
          data-slice-type={slice.slice_type}
          data-slice-slides-length={slice.items.length}
          data-slice-variation={slice.variation}
        >
          {slice.items.map((item, index) => {
            let type: "hero" | "default" | "withoutReferring";
            
            if (item.preview_in_full_screen && !(item.action_link as any)?.url) {
              type = "withoutReferring";
            } else if (item.preview_in_full_screen && (item.action_link as any)?.url) {
              type = "default";            
            } else {
              type = "hero";
            }
            
            return (
              <Fragment
                key={`${index}Fragment`}
              >
                <PreviewSectionCard
                  title={item.title}
                  subtitle={item.subtitle}
                  preview_image={item.preview_image}
                  isFullScreen={item.preview_in_full_screen}
                  action_text={item.action_text}
                  action_link={item.action_link}
                  variation={type}
                  singleMode={slice.items.length === 1}
                />
              </Fragment>
            );
          })}
        </div>
      ) : (
        null   
      )}
    </>
  );
});

export default PreviewSection;
