"use client";
import Container from "@/components/Container";
import { Content } from "@prismicio/client";
import { SliceComponentProps } from "@prismicio/react";
import * as styles from "./ReferAndEarn.css";
import Typography from "@/components/Typography";
import { PrismicRichText } from "node_modules/@prismicio/react/dist/react-server/PrismicRichText";
import { PrismicNextImage } from "@prismicio/next";
import Button from "@/components/Button";
import useStore from "@/hooks/useStore";
import Link from "next/link";

/**
 * Props for `ReferAndEarn`.
 */
export type ReferAndEarnProps = SliceComponentProps<Content.ReferAndEarnSlice>;

/**
 * Component for "ReferAndEarn" Slices.
 */
const ReferAndEarn = ({ slice }: ReferAndEarnProps): JSX.Element => {
  const { auth } = useStore();
  return (
    <Container>
      <section
        className={styles.root}
        data-slice-type={slice.slice_type}
        data-slice-variation={slice.variation}
      >
        <Typography
          fontFamily="primary"
          className={styles.title}
          variant="h2"
          isGreenItalic
        >
          <PrismicRichText
            field={slice.primary.title}
          />
        </Typography>
        <div
          className={styles.imgWrapper}
        >
          <PrismicNextImage
            className={styles.img}
            field={slice.primary.image}
          />
        </div>
        <div
          className={styles.descriptionWrapper}
        >
          <Typography
            className={styles.description}
            variant="bodySmall"
          >
            <PrismicRichText
              field={slice.primary.description}
            />
          </Typography>
          <div
            className={styles.buttonWrapper}
          >
            <Button
              as={Link}
              isAnimated
              href={auth.isAuthorized ? "/profile" : "/login"}
            >
              {slice.primary.button_text}
            </Button>
          </div>
        </div>
      </section>
    </Container>
  );
};

export default ReferAndEarn;
