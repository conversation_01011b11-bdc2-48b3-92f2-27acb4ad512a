import { breakpoints } from "@/styles/constants.css";
import { theme } from "@/styles/themes.css";
import { style } from "@vanilla-extract/css";


export const root = style({
  borderRadius: 24,
  background: theme.colors.primary.castletonGreen,
  display: "grid",
  color: theme.colors.primary.softWhite,
  position: "relative",
  overflow: "hidden",
  marginTop: 40,
  marginBottom: 40,
  "@media": {
    [breakpoints.tablet]: {
      gridAutoFlow: "column",
      gridAutoColumns: "1fr",
      justifyContent: "space-between",
      alignItems: "center",
      columnGap: 32,
      overflow: "unset",
      minHeight: 352,
      marginBottom: 90,
      marginTop: 90
    }
  }
});

export const  title = style({
  padding: "40px 20px 32px 20px",
  zIndex: 1,
  "@media": {
    [breakpoints.tablet]: {
      padding: "64px 0 64px 64px",
    }
  }
});
export const  imgWrapper = style({
  alignSelf: "end",
  height: 286,
  width:"100%",
  gridRowEnd: 4,
  
  "@media": {
    [breakpoints.tablet]: {
      position: "absolute",
      width: "auto",
      height: "110%",
      gridRowEnd: "auto",
      justifySelf: "center"
    }
  }
});
export const  img = style({
  width: "100%",
  height: "100%",
  objectFit: "contain",
  "@media": {
    [breakpoints.tablet]: {
      objectFit: "cover",
    }
  }
});
export const  description = style({});
export const  descriptionWrapper = style({
  display: "grid",
  rowGap: 32,
  padding: "0px 20px 24px 20px",
  justifySelf: "end",
  zIndex: 1,
  "@media": {
    [breakpoints.tablet]: {
      padding: "64px 64px 64px 0",
      maxWidth: 355
    }
  }
});

export const buttonWrapper = style({
  display: "grid",
  "@media": {
    [breakpoints.tablet]: {
      position: "relative",
      bottom: "auto",
      left: "auto",
      right: "auto",
    }
  },
  zIndex: 1,
  position: "absolute",
  bottom: 24,
  left: 20,
  right: 20,
});