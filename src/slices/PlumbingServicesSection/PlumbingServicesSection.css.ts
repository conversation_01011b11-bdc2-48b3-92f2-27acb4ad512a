import { breakpoints } from "@/styles/constants.css";
import { theme } from "@/styles/themes.css";
import { style } from "@vanilla-extract/css";


export const container = style({
  backgroundColor: theme.colors.primary.castletonGreen,
});

export const root = style({
  display: "grid",
  // gridTemplateColumns: 'repeat(3, 1fr)',
  gridAutoFlow: "row",
  gap: 16,
  background: theme.colors.primary.castletonGreen,
  marginTop: 40,
  borderBottomLeftRadius: 24,
  borderBottomRightRadius: 24,
  position: "relative",
  paddingBottom: 12,
  "@media": {
    [breakpoints.tablet]: {
      marginTop: 120,
      gridTemplateColumns: "repeat(3, 1fr)",
      gridAutoFlow: "column",
      paddingBottom: 180,

    }
  }
});

export const listItem = style({

});

const cardPadding = style({
  padding: "32px 24px",
  "@media": {
    [breakpoints.tablet]: {
      padding: "48px 52px",

    }
  }
});

const card = style({
  backgroundColor: theme.colors.primary.softWhite,
  borderRadius: 16,
  overflow: "hidden",
  position: "sticky",
  boxShadow: "0px -5px 7px 0px rgba(0,0,0,0.28)",
  "@media": {
    [breakpoints.tablet]: {
      boxShadow: "none",
      position: "static",
      minHeight: 504,
      borderRadius: 24,
    }
  }
});



export const leftListWrapper = style([card,cardPadding,{
  top: 120,
  "@media": {
    [breakpoints.tablet]: {
      top: "auto",
      transform: "translateY(90px) rotate(-5deg)",
      transformOrigin: "100px 10px"
    }
  }
}]);

export const shrinkList = style({
  fontSize: 16,
  fontWeight: 500,
  "@media": {
    [breakpoints.tablet]: {
      fontSize: 20,

    }
  }
});

export const rightListWrapper = style([card,cardPadding,{
  top: 120,
  "@media": {
    [breakpoints.tablet]: {
      top: "auto",
      transform: "translateY(90px)  rotate(5deg)",
      transformOrigin: "200px 10px"
    }
  }

}]);


export const middleCard = style([card,{
  zIndex: 1,
  gridRow: 3,
  minHeight: 457,
  top: 0,
  "@media": {
    [breakpoints.tablet]: {
      top: "auto",
      gridRow:"auto",
      minHeight: "auto",
    }
  }
}]);


export const middleCardContent = style([cardPadding, {
  position: "relative",
  display: "grid",
  alignContent: "end",
  height: "100%",
}]);

export const imgWrapper = style({
  position: "absolute",
  left: 0,
  right: 0,
  bottom: 0,
  top: 0,
});
export const img = style({
  width: "100%",
  height: "100%",
  objectFit: "cover"
});
export const buttonWrapper = style({
  display: "grid",
  "@media": {
    [breakpoints.tablet]: {
      justifyContent: "center",
      
    }
  }
});