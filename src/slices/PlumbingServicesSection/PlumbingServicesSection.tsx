import Container from "@/components/Container";
import { Content, RichTextField } from "@prismicio/client";
import { PrismicRichText, SliceComponentProps } from "@prismicio/react";
import * as styles from "./PlumbingServicesSection.css";
import ShrinksList from "@/components/ShrinksList/ShrinksList";
import { PrismicNextImage } from "@prismicio/next";
import BookServiceButton from "@/components/BookServiceButton/BookServiceButton";
import { toCamelCase } from "@/utils/helpers";

/**
 * Props for `PlumbingServicesSection`.
 */
export type PlumbingServicesSectionProps =
  SliceComponentProps<Content.PlumbingServicesSectionSlice>;

/**
 * Component for "PlumbingServicesSection" Slices.
 */
const PlumbingServicesSection = ({
  slice,
}: PlumbingServicesSectionProps): JSX.Element => {


  const [leftList, rightList] = slice.items.reduce<[RichTextField[],RichTextField[]]>((prev, acc, ) => {
    if(acc.left_list.length) prev[0].push(acc.left_list);
    if(acc.right_list.length) prev[1].push(acc.right_list);
    return prev;
  }, [[],[]]);

  return (
   
    <section
      data-slice-type={slice.slice_type}
      data-slice-variation={slice.variation}
      id={slice.primary.header_anchor_name ? toCamelCase(slice.primary.header_anchor_name) : ""}
      className={styles.container}
    >
      <Container
        className={styles.root}
      >
        <ShrinksList
          maxAmount={30}
          className={styles.leftListWrapper}
          listClassName={styles.shrinkList}
          data={leftList}
        />
        <div
          className={styles.middleCard}
        >
          <div
            className={styles.middleCardContent}
          >
            <div
              className={styles.imgWrapper}
            ><PrismicNextImage
                className={styles.img}
                field={slice.primary.image}
            /></div>    
            <div
              className={styles.buttonWrapper}
            >
              <BookServiceButton/>    
            </div>
          </div>
        </div>
        <ShrinksList
          maxAmount={30}
          listClassName={styles.shrinkList}
          className={styles.rightListWrapper}
          data={rightList}
        />

      </Container>
    </section>
  );
};

export default PlumbingServicesSection;
