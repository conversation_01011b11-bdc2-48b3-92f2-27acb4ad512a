import { breakpoints } from "@/styles/constants.css";
import { mode } from "@/styles/functions.css";
import { theme } from "@/styles/themes.css";
import { style, styleVariants } from "@vanilla-extract/css";

export const container = style({
  selectors: {
    [mode("residential")]: {
      backgroundColor: theme.colors.primary.castletonGreen,
    }
  },
});

export const root = style({
  padding: "40px 20px",

  "@media": {
    [breakpoints.tablet]: {
      padding: "80px 0 0",
    }
  }
});

export const title = style({
  fontFamily: theme.fonts.primary,
  marginBottom: "24px",

  "@media": {
    [breakpoints.tablet]: {
      marginBottom: 0,
    },
  },

  selectors: {
    [mode("residential")]: {
      color: theme.colors.primary.softWhite,
    }
  },
});

export const description = style({
  marginBottom: 32,
  opacity: 0.8,

  "@media": {
    [breakpoints.tablet]: {
      marginBottom: 0,
    },
  },

  selectors: {
    [mode("residential")]: {
      color: theme.colors.primary.softWhite,
    }
  },
});



export const paper = style({
  marginTop: 16,
  padding: "0 0 20px",

  "@media": {
    [breakpoints.tablet]: {
      marginTop: 30,
      borderRadius: 24,
      padding: "0 40px",
    }
  }
});

export const block = style({
  rowGap: 20,
  flexDirection: "column",
  gap: 16,
  marginBottom: 20,
  scrollMarginTop: 90,
  "@media": {
    [breakpoints.tablet]: {
      paddingTop: 70,
      marginBottom: 40,
    }
  }
});

export const itemContainer = style({
  borderRadius: 16,
  backgroundColor: theme.colors.primary.softWhite,
  transition: "background-color 50ms, transform 350ms",

  ":active": {
    backgroundColor: theme.colors.primary.softWhitePressed,
    color: theme.colors.primary.castletonGreenPressed,
    transform: "scale(0.95)",
  },

  "@media": {
    [breakpoints.tablet]: {
      borderRadius: 24,
    }
  }
});

export const itemHeader = style({
  cursor: "pointer",
  width: "100%",
  padding: "20px",
  minHeight: "52px",
  display: "flex",
  alignItems: "center",
  justifyContent: "space-between",
  border: "none",
  backgroundColor: "transparent",
  textAlign: "left",
  color: "inherit",
  font: "inherit",

  "@media": {
    [breakpoints.tablet]: {
      padding: "0 40px",
      minHeight: "110px",
    }
  }
});

export const itemBody = style({
  padding: "0 20px 20px",

  "@media": {
    [breakpoints.tablet]: {
      padding: "0 40px 40px",
    }
  }
});

export const itemIndicatorBase = style({
  minWidth: 30,
  width: 30,
  height: "auto",
  aspectRatio: "1 / 1",
  transitionProperty: "transform",
  transitionDuration: "200ms",
  transitionTimingFunction: "ease",
});

export const itemIndicator = styleVariants({
  isActive: [itemIndicatorBase],
  isInactive: [itemIndicatorBase, {
    transform: "rotateZ(180deg)",
  }],
});

export const blockTitle = style({
  paddingTop: 20,
  fontFamily: theme.fonts.primary,

  selectors: {
    [mode("residential")]: {
      color: theme.colors.primary.softWhite,
    }
  },
});
