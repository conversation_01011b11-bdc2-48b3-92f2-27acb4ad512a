"use client";

import Container from "@/components/Container";
import { Content } from "@prismicio/client";
import { SliceComponentProps } from "@prismicio/react";
import * as styles from "./DocumentWithNavigationAndAccordions.css";
import { useCallback, useEffect, useLayoutEffect, useMemo, useRef, useState } from "react";
import classNames from "classnames";
import { gridSprinkle } from "@/styles/sprinkles.css";
import Typography from "@/components/Typography";
import { PrismicRichText } from "node_modules/@prismicio/react/dist/react-server/PrismicRichText";
import Link from "next/link";
import { Simplify } from "prismicio-types";
import Accordion from "@/components/Accordion";
import ArrowIcon from "@/assets/icons/ArrowIcon";
import DropdownInput from "@/components/DropdownInput";
import NavList from "@/components/NavList";
import { NavListOption } from "@/components/NavList/NavList.types";
import { usePathname } from "next/navigation";
import { parseStringToValidId } from "@/utils/helpers";
import { Option, onChangeSelectType } from "@/components/DropdownInput/DropdownInput.types";
import ModeProvider from "@/components/ModeProvider";

export type DocumentWithNavigationAndAccordionsProps =
  SliceComponentProps<Content.DocumentWithNavigationAndAccordionsSlice>;

const DocumentWithNavigationAndAccordions = ({
  slice,
}: DocumentWithNavigationAndAccordionsProps): JSX.Element => {
  const documentRef = useRef<HTMLDivElement>(null);

  const [activeTopic, setActiveTopic] = useState<Option>({
    label: String(slice.items[0].accordion_group_name),
    value: parseStringToValidId(String(slice.items[0].accordion_group_name), true),
  });
  const [activeSection, setActiveSection] = useState(String(slice.items[0].accordion_group_name));
  const [expandedAnswer, setExpandedAnswer] = useState<string | null>(
    `${slice.items[0].accordion_title}-${slice.items[0].accordion_group_name}-0`
  );

  
  

  useEffect(() => {
    const handleScroll = () => {
      if (!documentRef.current) return;

      const documentRect = documentRef.current.getBoundingClientRect();

      const deeperElement = document.elementFromPoint(documentRect.left + documentRect.width / 2, 100);

      if (!deeperElement) return;
      const closestDocumentBlock = deeperElement.closest(`.${styles.block}`);

      if (!closestDocumentBlock) return;

      setActiveSection(closestDocumentBlock.getAttribute("id") ?? String(slice.items[0].accordion_group_name));
    };

    window.addEventListener("scroll", handleScroll);

    return () => {
      window.removeEventListener("scroll", handleScroll);
    };
  }, []);

  const navOptions = useMemo(() => {
    const list: NavListOption[] = [];
    new Set(slice.items.map(item => item.accordion_group_name)).forEach((item) => list.push({label: `${item}`, href: `#${item}`}));
    return list;
  }, [slice.items]);

  const dropdownOptions = useMemo(() => navOptions.map(({href,label}) => ({value: href, label}) ) , [navOptions]); 

  useLayoutEffect(() => {
    if(dropdownOptions.length < 1 || !window)  return;
    const activeTabOptions = dropdownOptions.find(({value}) => value === window.location.hash);
    if(!activeTabOptions) return;
    setActiveTopic(activeTabOptions);
    
  },[dropdownOptions]);

  useLayoutEffect(() => {
    if(!window || !documentRef.current || !window.location.hash) return;
    const activeBlock = documentRef.current.querySelector(window.location.hash);
    if(!activeBlock) return;
    activeBlock?.scrollIntoView();
  }, []);

  const onDropdownChange = useCallback<onChangeSelectType>(
    (selected) => {
      setActiveTopic(selected);
      if(window) {  
        window.location.hash = selected && selected.value ? `${selected.value}` : "";
      }
    },
    [],
  );
  

  return (
    <Container
      className={styles.container}
    >
      <section
        className={classNames(styles.root, gridSprinkle({ type: "grid" }))}
        data-slice-type={slice.slice_type}
        data-slice-variation={slice.variation}
      >
        <Typography
          variant="h2"
          as={"h1"}
          className={classNames(styles.title, gridSprinkle({ type: "item", cols: { mobile: 10, tablet: 7 } }))}
        >
          <PrismicRichText
            field={slice.primary.title}
          />
        </Typography>
        <Typography
          className={classNames(styles.description, gridSprinkle({ type: "item", cols: { mobile: 10, tablet: 3 }, justifySelf: { mobile: "start", tablet: "end" }, alignSelf: "center" }))}
        >
          <PrismicRichText
            field={slice.primary.description}
          />
        </Typography>
        {dropdownOptions.length > 1 && (
          <div
            className={classNames(gridSprinkle({ type: "item", cols: 10, display: { mobile: "flex", tablet: "none" } }))}
          > 
            <DropdownInput
              value={activeTopic}
              options={dropdownOptions}
              placeholder="Select from list"
              label="Sort by topic:"
              isSearchable={false}
              onChange={onDropdownChange}
              enableDarkMode
            />
          </div>)}
        <ModeProvider>
          {(mode) => (
            <NavList
              darkMode={mode === "residential"}
              active={activeSection}
              options={navOptions}
              className={classNames(gridSprinkle({ type: "item", cols: { mobile: 10, tablet: 2 }, display: { mobile: "none", tablet: "flex" } }))}
            />
          )}
        </ModeProvider>
        <div
          ref={documentRef}
          className={classNames(styles.paper, gridSprinkle({ type: "item", cols: { mobile: 10, tablet: 8 } }))}
        >
          {Object.entries(slice.items.reduce((accum, item) => {
            if (!accum[String(item.accordion_group_name)]) {
              accum[String(item.accordion_group_name)] = [];
            }

            accum[String(item.accordion_group_name)].push(item);

            return accum;
          }, {} as { [K: string]: Simplify<Content.DocumentWithNavigationAndAccordionsSliceDefaultItem>[] })).map(([groupName, items], index) => {
            return (
              <div
                className={classNames(styles.block, gridSprinkle({ display: { mobile: activeTopic?.label === String(groupName) ? "flex" : "none", tablet: "flex" } }))}
                id={groupName}
                key={index}
              >
                <Typography
                  className={classNames(styles.blockTitle, gridSprinkle({ display: { mobile: "none", tablet: "block" } }))}
                  variant="h4"
                >
                  {groupName}
                </Typography>
                {items.map((item, index) => (
                  <div
                    key={index}
                    className={styles.itemContainer}
                  >
                    <button
                      className={styles.itemHeader}
                      onClick={(
                      ) => setExpandedAnswer(expandedAnswer === `${item.accordion_title}-${groupName}-${index}` ? null : `${item.accordion_title}-${groupName}-${index}`)}
                    >
                      <Typography
                        variant="subTitleMedium"
                      >
                        {item.accordion_title}
                      </Typography>
                      <ArrowIcon
                        className={expandedAnswer === `${item.accordion_title}-${groupName}-${index}` ? styles.itemIndicator.isActive : styles.itemIndicator.isInactive}
                      />
                    </button>
                    <Accordion
                      isOpen={expandedAnswer === `${item.accordion_title}-${groupName}-${index}`}
                    >
                      <div
                        className={styles.itemBody}
                      >
                        <Typography
                          variant="bodySmall"
                        >
                          <PrismicRichText
                            field={item.accordion_content}
                          />
                        </Typography>
                      </div>                  
                    </Accordion>
                  </div>

                ))}
              </div>          
            );})}
        </div>
      </section>
    </Container>
  );
};

export default DocumentWithNavigationAndAccordions;
