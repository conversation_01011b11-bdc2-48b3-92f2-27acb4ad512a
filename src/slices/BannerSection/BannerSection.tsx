"use client";

import Button from "@/components/Button";
import Container from "@/components/Container";
import Typography from "@/components/Typography";
import { gridSprinkle } from "@/styles/sprinkles.css";
import { Content } from "@prismicio/client";
import { PrismicNextImage, PrismicNextLink } from "@prismicio/next";
import { SliceComponentProps } from "@prismicio/react";
import classNames from "classnames";
import { PrismicRichText } from "node_modules/@prismicio/react/dist/react-server/PrismicRichText";
import * as styles from "./BannerSection.css";
import ModeProvider from "@/components/ModeProvider";

export type BannerSectionProps =
  SliceComponentProps<Content.BannerSectionSlice>;

const BannerSection = ({ slice }: BannerSectionProps): JSX.Element => {
  return (
    <Container
      notFullHeight
      className={styles.container}
    >
      <section
        className={classNames(styles.root, gridSprinkle({ type: "grid" }), {
          [styles.rootIsDefault]: slice.variation === "default",
        })}
        data-slice-type={slice.slice_type}
        data-slice-variation={slice.variation}
      >
        {slice.variation === "default" && (
          <>
            <div
              className={classNames(styles.previewWrapper, gridSprinkle({ type: "item", cols: { mobile: 10, tablet: 5 }, display: { mobile: "none", tablet: "block" } }))}
            >
              <PrismicNextImage
                fill
                objectFit="contain"
                objectPosition="bottom center"
                field={slice.primary.preview}
              />
            </div>
            <div
              className={classNames(styles.content, gridSprinkle({ type: "item", cols: { mobile: 10, tablet: 5 } }))}
            >
              <Typography
                className={styles.title}
                variant="h3"
                as={"h1"}
              >
                <PrismicRichText
                  field={slice.primary.title}
                />          
              </Typography>
              <Typography
                className={styles.description}
                variant="bodySmall"
              >
                <PrismicRichText
                  field={slice.primary.description}
                />          
              </Typography>
              <ModeProvider>
                {(mode) => (
                  <Button
                    isAnimated
                    as={PrismicNextLink}
                    field={slice.primary.action_link}
                    color={mode === "residential" ? "secondary" : "primary"}
                  >
                    {slice.primary.action_text}
                  </Button>
                )}
              </ModeProvider>
            </div>
            <div
              className={classNames(styles.previewWrapper, gridSprinkle({ type: "item", cols: { mobile: 10, tablet: 5 }, display: { mobile: "block", tablet: "none" } }))}
            >
              <PrismicNextImage
                fill
                objectFit="contain"
                objectPosition="bottom center"
                field={slice.primary.preview}
              />
            </div>
          </>
        )}
        {slice.variation === "textOnly" && (
          <>
            <Typography
              className={classNames(styles.title, gridSprinkle({ type: "item", cols: { mobile: 10, tablet: 5 } }))}
              variant="h2"
            >
              <PrismicRichText
                field={slice.primary.title}
              />          
            </Typography>
            <div
              className={gridSprinkle({ type: "item", cols: { mobile: 10, tablet: 1 } })}
            />
            <Typography
              className={classNames(styles.description, gridSprinkle({ type: "item", cols: { mobile: 10, tablet: 4 } }))}
            >
              <PrismicRichText
                field={slice.primary.description}
              />          
            </Typography>
          </>
        )}
      </section>
    </Container>
  );
};

export default BannerSection;
