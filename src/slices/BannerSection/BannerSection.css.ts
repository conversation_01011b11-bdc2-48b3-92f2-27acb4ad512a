import { breakpoints } from "@/styles/constants.css";
import { mode, modeGlobal } from "@/styles/functions.css";
import { theme } from "@/styles/themes.css";
import { globalStyle, style } from "@vanilla-extract/css";

export const container = style({
  selectors: {
    [mode("residential")]: {
      backgroundColor: theme.colors.primary.castletonGreen,
    }
  },
});

export const root = style({
  position: "relative",
  overflow: "hidden",
  backgroundColor: theme.colors.primary.castletonGreen,
  color: theme.colors.primary.ivory,
  borderRadius: 16,
  padding: "40px 20px 0",

  "@media": {
    [breakpoints.tablet]: {
      borderRadius: 24,
      padding: "72px 48px 0",
    },
  },

  selectors: {
    [mode("residential")]: {
      backgroundColor: theme.colors.primary.ivory,
    }
  },
});

export const rootIsDefault = style({
  minHeight: 460,

  "@media": {
    [breakpoints.tablet]: {
      minHeight: 540,
    },
  },
});

export const content = style({
  display: "flex",
  flexDirection: "column",
  padding: "0 0 25px",

  "@media": {
    [breakpoints.tablet]: {
      alignItems: "flex-start",
      padding: "0 0 72px",
    },
  },
});

export const title = style({
  fontFamily: theme.fonts.primary,
  marginBottom: 32,

  "@media": {
    [breakpoints.tablet]: {
      marginBottom: 40,
    },
  },

  selectors: {
    [mode("residential")]: {
      color: theme.colors.primary.castletonGreen,
    }
  },
});

globalStyle(`${modeGlobal("residential")} ${title} strong`, {
  color: theme.colors.primary.castletonGreen,
});

export const description = style({
  maxWidth: 480,
  marginBottom: 32,

  "@media": {
    [breakpoints.tablet]: {
      marginBottom: "auto",
    },
  },

  selectors: {
    [mode("residential")]: {
      color: theme.colors.primary.castletonGreen,
    }
  },
});

globalStyle(`${description} p`, {
  marginBottom: 24,
});

export const previewWrapper = style({
  position: "relative",
  minHeight: "45vh",

  "@media": {
    [breakpoints.tablet]: {
      height: "100%",
    },
  },
});

globalStyle(`${title} em`, {
  color: theme.colors.primary.asidGreen,
});