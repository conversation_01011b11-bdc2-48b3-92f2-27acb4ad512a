import { breakpoints } from "@/styles/constants.css";
import { mode } from "@/styles/functions.css";
import { theme } from "@/styles/themes.css";
import {
  createVar,
  globalStyle,
  keyframes,
  style,
  styleVariants,
} from "@vanilla-extract/css";

export const positionIdx = createVar();

export const container = style({
  scrollSnapAlign: "center !important",
  paddingTop: 0,
  paddingBottom: 0,

  selectors: {
    [mode("residential")]: {
      backgroundColor: theme.colors.primary.castletonGreen,
    }
  },
});

export const root = style({
  overflowY: "hidden",
  borderRadius: 16,
  height: "max-content",
  marginBottom: "10px",
  padding: "20px",
  paddingTop: "40px",
  backgroundColor: theme.colors.primary.castletonGreen,
  color: theme.colors.primary.ivory,
  willChange: "transform",
  "@media": {
    [breakpoints.tablet]: {
      borderRadius: 24,
      marginBottom: "18px",
      padding: "72px 48px",
    },
  },

  selectors: {
    [mode("residential")]: {
      backgroundColor: theme.colors.primary.ivory,
    }
  },
});

const titleBase = style({
  maxWidth: 420,
  textAlign: "center",
  fontFamily: theme.fonts.primary,
  marginBottom: 40,
  willChange: "opacity, transform",
  "@media": {
    [breakpoints.tablet]: {
      textAlign: "left",
      marginBottom: "auto",
    },
  },

  selectors: {
    [mode("residential")]: {
      color: theme.colors.primary.castletonGreen,
    }
  },
});


const titleSlide = keyframes({
  from: {
    opacity: 0,
    transform: "translateY(100px)",
  },
  to: {
    opacity: 1,
    transform: "translateY(0)",
  },
});

export const title = styleVariants({
  base: [titleBase],
  animate: {
    animation: `1s ${titleSlide} forwards`,
  },
});

globalStyle(`${title.base} em`, {
  color: theme.colors.primary.asidGreen,
});

const descriptionBase = style({
  marginBottom: "32px",
  willChange: "opacity, transform",
  "@media": {
    [breakpoints.tablet]: {
      marginBottom: 0,
    },
  },
});

const descriptionSlide = keyframes({
  from: {
    opacity: 0,
    transform: "translateX(100px)",
  },
  to: {
    opacity: 1,
    transform: "translateX(0)",
  },
});

export const description = styleVariants({
  base: [descriptionBase],
  animate: {
    animation: `1s ${descriptionSlide} forwards`,
  },
});

export const partners = style({
  flexWrap: "wrap",
  display: "flex",
  gap: 20,

  "@media": {
    [breakpoints.tablet]: {
      gap: 24,
    },
  },
});

export const partnerBase = style({
  flex: "1 1",
  minWidth: "30%",
  position: "relative",
  backgroundColor: theme.colors.primary.ivory,
  padding: "16px 20px",
  aspectRatio: "1.66 / 1",
  borderRadius: 8,
  display: "flex",
  alignItems: "center",
  justifyContent: "center",
  willChange: "transform, opacity",
  boxShadow: `0 0 0 3px ${theme.colors.primary.castletonGreen}, 0 0 0 4px ${theme.colors.primary.asidGreen}`,
  "@media": {
    [breakpoints.tablet]: {
      padding: 20,
      aspectRatio: "4 / 2.5",
      borderRadius: 12,
    }
  },

  selectors: {
    [mode("residential")]: {
      backgroundColor: theme.colors.primary.softWhite,
      boxShadow: `0 0 0 3px ${theme.colors.primary.ivory}, 0 0 0 4px ${theme.colors.primary.asidGreen}`,
    }
  },
});

const partnerCardUnstack = keyframes({
  from: {
    opacity: 0,
    transform: `translateY(calc(${positionIdx} * 100%))`,
  },
  to: {
    opacity: 1,
    transform: "translateY(0)",
  },
});

export const partner = styleVariants({
  base: [partnerBase],
  animate: {
    animation: `1s ${partnerCardUnstack} forwards`,
  },
});

export const info = style({
  display: "flex",
  flexDirection: "column",
});

const slideFromTopKeyFrame = keyframes({
  from: {
    transform: "translateY(0)",
  },
  to: {
    transform: "translateY(15%)",
  },
});

export const slideFromTop = style({
  willChange: "transform",
  animation: `1s ${slideFromTopKeyFrame}`,
});

export const partnerLogo = style({
  height: "100%",
  width: "100%",
  mixBlendMode: "darken",
});