"use client";

import { Content } from "@prismicio/client";
import { PrismicRichText, SliceComponentProps } from "@prismicio/react";
import * as styles from "./PartnersSection.css";
import Container from "@/components/Container";
import Typography from "@/components/Typography";
import classNames from "classnames";
import { gridSprinkle } from "@/styles/sprinkles.css";
import { PrismicNextImage } from "@prismicio/next";
import { useCallback, useContext, useRef } from "react";
import useObserveIntoView from "@/hooks/useObserveIntoView";
import { assignInlineVars } from "@vanilla-extract/dynamic";
import useIsAnimationExist from "@/hooks/useIsAnimationExist";

export type PartnersSectionProps =
  SliceComponentProps<Content.PartnersSectionSlice>;

const ANIM_PARTNER_ID = "anim--partner_card";
const ANIM_TITLE_ID = "anim--title";
const ANIM_DESC_ID = "anim--description";

const PartnersSection = ({ slice }: PartnersSectionProps): JSX.Element => {
  const containerRef = useRef<HTMLDivElement>(null);
  const isWithAnimation = useIsAnimationExist();

  const onInView = useCallback(
    (target?: HTMLElement) => {
      if (!target || !isWithAnimation) return;
      const partnerCards = target.querySelectorAll(`#${ANIM_PARTNER_ID}`);
      const title = target.querySelector(`#${ANIM_TITLE_ID}`);
      const description = target.querySelector(`#${ANIM_DESC_ID}`);

      partnerCards.forEach((item) => {
        item.classList.add(styles.partner.animate);
      });

      title?.classList.add(styles.title.animate);

      description?.classList.add(styles.description.animate);
    },
    [isWithAnimation]
  );

  useObserveIntoView(containerRef, { onInView, isDisabled: !isWithAnimation, threshold: 0.1 });

  return (
    <Container
      className={styles.container}
      ref={containerRef}
      withAnimation={isWithAnimation}
      notFullHeight
    >
      <section
        className={classNames(styles.root, gridSprinkle({ type: "grid" }))}
        data-slice-type={slice.slice_type}
        data-slice-variation={slice.variation}
      >
        <div
          className={classNames(
            styles.info,
            gridSprinkle({ type: "item", cols: { mobile: 10, tablet: 5 } })
          )}
        >
          <Typography
            id={ANIM_TITLE_ID}
            variant="h3"
            className={classNames(styles.title.base)}
          >
            <PrismicRichText
              field={slice.primary.title}
            />
          </Typography>
        </div>
        <div
          className={gridSprinkle({
            type: "item",
            cols: { mobile: 10, tablet: 5 },
          })}
        >
          <div
            className={styles.partners}
          >
            {slice.items.map((item, index, arr) => (
              <div
                id={ANIM_PARTNER_ID}
                className={styles.partner.base}
                style={assignInlineVars({
                  [styles.positionIdx]: `${Math.floor((arr.length - 1 + index) / 2)}`,
                })}
                key={index}
              >
                <div
                  className={styles.partnerLogo}
                  style={{ position: "relative" }}
                >
                  <PrismicNextImage
                    fill
                    objectFit="contain"
                    objectPosition="center"
                    field={item.partner_logo}
                  />
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>
    </Container>
  );
};

export default PartnersSection;
