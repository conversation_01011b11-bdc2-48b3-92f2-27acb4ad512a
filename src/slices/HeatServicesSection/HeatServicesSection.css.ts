import { breakpoints } from "@/styles/constants.css";
import { theme } from "@/styles/themes.css";
import { style, styleVariants } from "@vanilla-extract/css";

export const container = style({
  backgroundColor: theme.colors.primary.softWhite,
  marginTop: 10,

  "@media": {
    [breakpoints.tablet]: {
      marginTop: 20,
    },
  },
});

export const root = style({
  display: "grid",
  gap: 32,
  marginTop: 60,
  marginBottom: 40,

  "@media": {
    [breakpoints.tablet]: {
      marginTop: 70,
      marginBottom: 70,
      gap: 72,
      gridTemplateColumns: "1fr auto"
    }
  }
});

export const controlsWrapper = style({
  gridRowStart: 3,
  display: "flex",
  columnGap: 8,
  gridColumn: "span 2",
  justifySelf: "center",

  "@media": {
    [breakpoints.tablet]: {
      alignSelf: "center",
      gridRowStart: "auto",
      gridColumn: "auto",
      justifySelf: "auto",
    },

    [breakpoints.desktop]: {
      display: "none",
    }
  }
});

export const title = style({
  gridColumn: "span 2",
  "@media": {
    [breakpoints.tablet]: {
      gridColumn: "auto",
    }
  }

});


export const listWrapper = style({
  display: "grid",
  gridAutoFlow: "column",
  columnGap: 44,
  listStyle: "none",
  padding: "0 20px",
  overflow: "auto",
  gridColumn: "span 2",
  scrollSnapType: "x mandatory",
  justifyContent: "start",
  // margin: "0 10px",
  "@media": {
    [breakpoints.tablet]: {
      margin: 0,
    }
  }
});


export const listItemWrapperBase = style({
  scrollSnapAlign: "center",
  position: "relative",
  borderRadius: 24,
  overflow: "hidden",
  
  display: "grid",
  alignContent: "space-between",
  width: "calc(100vw - 50px)",
  padding: 24,
  minHeight: 387,
  "@media": {
    [breakpoints.tablet]: {
      width: 480,
      minHeight: 500,
      padding: 48
    }
  },
});


export const listItemWrapperPrimary = style({
  backgroundColor: theme.colors.primary.ivory,
});
export const listItemWrapperSecondary = style({
  backgroundColor: theme.colors.primary.castletonGreen,
});

export const listItemWrapper = styleVariants({
  primary: [listItemWrapperBase,listItemWrapperPrimary],
  secondary: [listItemWrapperBase,listItemWrapperSecondary],
});


const imgWrapperBase = style({
  position: "absolute",
  bottom: 0,
});

const imgWrapperSecondary = style({
  bottom: 0,
  right: 0,
  
});

const imgWrapperPrimary = style({
  bottom: 0,
  right: -75,
  overflow: "hidden",
  borderRadius: 16,
  "@media": {
    [breakpoints.tablet]: {
      right: 32,
    }
  }
  
});

export const imgWrapper = styleVariants({
  primary: [imgWrapperBase, imgWrapperPrimary],
  secondary: [imgWrapperBase, imgWrapperSecondary],
});

const imgBase = style({
  height: "100%",
  objectFit: "cover"
});

const imgPrimary = style({
  width: 200,
  "@media": {
    [breakpoints.tablet]: {
      width: 250,
    }
  }
});

const imgSecondary = style({
  width: 150,
  "@media": {
    [breakpoints.tablet]: {
      width: 200,
    }
  }
});

export const img = styleVariants({
  primary: [imgBase, imgPrimary],
  secondary: [imgBase, imgSecondary],
});



export const listItemButton = style({});

export const insideListWrapper = style({
  zIndex: 1,
  position: "relative"
});
