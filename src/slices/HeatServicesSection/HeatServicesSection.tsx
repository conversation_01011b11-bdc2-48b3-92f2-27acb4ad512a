"use client";
import Container from "@/components/Container";
import useScrollHorList from "@/hooks/useScrollHorList";
import { Content } from "@prismicio/client";
import { SliceComponentProps } from "@prismicio/react";
import classNames from "classnames";
import * as styles from "./HeatServicesSection.css";
import { toCamelCase } from "@/utils/helpers";
import Typography from "@/components/Typography";
import IconButton from "@/components/IconButton";
import { ChevronIcon } from "@/assets/icons/ChevronIcon";
import { PrismicRichText } from "node_modules/@prismicio/react/dist/react-server/PrismicRichText";
import ShrinksList from "@/components/ShrinksList/ShrinksList";
import { PrismicNextImage } from "@prismicio/next";
import Button from "@/components/Button";
import BookServiceButton from "@/components/BookServiceButton/BookServiceButton";

/**
 * Props for `HeatServicesSection`.
 */
export type HeatServicesSectionProps =
  SliceComponentProps<Content.HeatServicesSectionSlice>;

/**
 * Component for "HeatServicesSection" Slices.
 */
const HeatServicesSection = ({
  slice,
}: HeatServicesSectionProps): JSX.Element => {
  const { handleNext,handlePrev,setRef } = useScrollHorList();

  return (
    <Container
      className={styles.container}
    >
      <section
        id={slice.primary.header_anchor_name ? toCamelCase(slice.primary.header_anchor_name) : ""}
        className={classNames(styles.root,)}
        data-slice-type={slice.slice_type}
        data-slice-variation={slice.variation}
      >
     
        <Typography
          as={"h2"}
          variant="h2"
          fontFamily="primary"
          className={styles.title}
        >
          <PrismicRichText
            field={slice.primary.title}
          />
         
        </Typography>
        <div
          className={styles.controlsWrapper}
        >
          <IconButton
            onClick={handlePrev}
          ><ChevronIcon
              turn="left"
          /></IconButton >
          <IconButton
            onClick={handleNext}
          ><ChevronIcon
              turn="right"
          /></IconButton>
        </div>
        <ul
          ref={setRef}
          className={ classNames(styles.listWrapper)}
        >
          {slice.items.map(({ image,list_items,dark_green_bg }) => {
            const splittedListItems = (list_items || "").split(/,[]?/);
            const variant = dark_green_bg ? "secondary" : "primary";
            return (
              <li
                key={splittedListItems[0]}
                className={styles.listItemWrapper[variant]}
              >
                <ShrinksList
                  variant={variant}
                  className={styles.insideListWrapper}
                  cols={1}
                  data={splittedListItems}
                />
                <div
                  className={styles.imgWrapper[variant]}
                >
                  <PrismicNextImage
                    className={styles.img[variant]}
                    field={image}
                  />
                </div>
                <div
                  className={styles.listItemButton}
                >
                  <BookServiceButton>Book an Expert</BookServiceButton>
                </div>
              </li>
            );
          })}
        </ul>
    
      </section>
    </Container>
  );
};

export default HeatServicesSection;
