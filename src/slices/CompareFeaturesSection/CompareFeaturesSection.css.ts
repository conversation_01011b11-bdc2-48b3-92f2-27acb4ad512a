import { breakpoints } from "@/styles/constants.css";
import { theme } from "@/styles/themes.css";
import { globalStyle, style, styleVariants } from "@vanilla-extract/css";

export const root = style(
  {
    padding: "16px 20px 40px",

    "@media": {
      [breakpoints.tablet]: {
        padding: "72px 48px",
      }
    },
  }
);

export const title = style(
  {
    paddingTop: 24,
    position: "sticky",
    top: 0,
    marginLeft: -30,
    marginRight: -30,
    fontFamily: theme.fonts.primary,
    paddingBottom: 24,
    textAlign: "center",
    backgroundColor: theme.colors.primary.ivory,
    zIndex: 10,

    "@media": {
      [breakpoints.tablet]: {
        position: "static",
        marginBottom: 48,
      }
    },
  }
);

export const table = style(
  {
    display: "grid",
    gridTemplateColumns: "1fr 1fr 1fr",
    marginRight: "-30px",
    marginLeft: "-30px",

    "@media": {
      [breakpoints.tablet]: {
        marginRight: 0,
        marginLeft: 0,
        gridTemplateColumns: "2fr 1fr 1fr 1fr",
      }
    },
  }
);

export const cell = style(
  {
    minHeight: 56,
    display: "flex",
    flexDirection: "column",
    alignItems: "center",
    justifyContent: "center",
    textAlign: "center",
    padding: "0 16px",

    "@media": {
      [breakpoints.tablet]: {
        padding: 0,
        minHeight: 72,
        borderBottom: `1px solid ${theme.colors.grayscale[100]}`,
      }
    },
  }
);

export const fixedCell = style(
  {
    position: "sticky",
    top: 146,
    backgroundColor: theme.colors.primary.ivory,
    borderBottom: `1px solid ${theme.colors.grayscale[100]}`,

    "@media": {
      [breakpoints.tablet]: {
        position: "static",
      }
    },
  }
);

export const highlightedCell = styleVariants(
  {
    base: [cell, {
      "@media": {
        [breakpoints.tablet]: {
          backgroundColor: theme.colors.primary.softWhite,
        }
      },
    }],
    first: [cell, {
      "@media": {
        [breakpoints.tablet]: {
          backgroundColor: theme.colors.primary.softWhite,
          borderTopLeftRadius: 16,
          borderTopRightRadius: 16,
        }
      },
    }],
    last: [cell, {
      "@media": {
        [breakpoints.tablet]: {
          backgroundColor: theme.colors.primary.softWhite,
          borderBottomLeftRadius: 16,
          borderBottomRightRadius: 16,
          borderBottom: "none !important",
        }
      },
    }]
  }
);

export const mainCell = style(
  [cell, {
    fontWeight: 500,
    gridColumn: "span 3",
    backgroundColor: theme.colors.primary.softWhite,
    minHeight: 36,

    "@media": {
      [breakpoints.tablet]: {
        gridColumn: "span 1",
        alignItems: "flex-start",
        backgroundColor: "transparent",
      }
    },
  }]
);

export const text = style({
  fontSize: "16px !important",

  "@media": {
    [breakpoints.tablet]: {
      fontSize: "20px !important",
    }
  }
});

export const mainText = style([text, {
  fontWeight: "500 !important",

  "@media": {
    [breakpoints.tablet]: {
      fontWeight: "400 !important",
    }
  }
}]);

globalStyle(`${mainText} b`, {
  fontWeight: 500,
});

export const check = style({
  height: 20,
  width: 20,

  "@media": {
    [breakpoints.tablet]: {
      height: 28,
      width: 28,
    }
  }
});