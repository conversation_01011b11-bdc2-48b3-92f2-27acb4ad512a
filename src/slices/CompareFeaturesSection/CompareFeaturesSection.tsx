import { Content } from "@prismicio/client";
import { SliceComponentProps } from "@prismicio/react";
import * as styles from "./CompareFeaturesSection.css";
import Container from "@/components/Container";
import Typography from "@/components/Typography";
import { PrismicRichText } from "node_modules/@prismicio/react/dist/react-server/PrismicRichText";
import { Fragment } from "react";
import classNames from "classnames";
import { gridSprinkle } from "@/styles/sprinkles.css";
import MemoCheckMarkIcon from "@/assets/icons/CheckMarkIcon";

const plans = [
  {
    name: "Basic",
    price: "£99.00",
    callouts: "5 per month",
    boilerAndControlsRepair: true,
    annualBoilerService: true,
    plumbingDrainsOrElectricalInspection: false,
    guaranteeOnAnyRepair: "3 month",
    priorityServiceWithin24Hours: true,
    gasElectricWaterHeatersReplacement: false,
    diagnostics: "30% discount",
  },
  {
    name: "Care",
    price: "£199.00",
    callouts: "10 per month",
    boilerAndControlsRepair: true,
    annualBoilerService: true,
    plumbingDrainsOrElectricalInspection: true,
    guaranteeOnAnyRepair: "6 month",
    priorityServiceWithin24Hours: true,
    gasElectricWaterHeatersReplacement: false,
    diagnostics: "50% discount",
  },
  {
    name: "Premium",
    price: "£399.00",
    callouts: "Unlimited",
    boilerAndControlsRepair: true,
    annualBoilerService: true,
    plumbingDrainsOrElectricalInspection: true,
    guaranteeOnAnyRepair: "One year",
    priorityServiceWithin24Hours: true,
    gasElectricWaterHeatersReplacement: true,
    diagnostics: "Free",
    isHighlighted: true,
  },
];

const features = [
  {
    name: <b>Included features</b>,
    render: (
      plan: typeof plans[0]
    ) => (
      <div
        className={classNames(
          styles.fixedCell, plan.isHighlighted ? styles.highlightedCell.first : styles.cell
        )}
      >
        <Typography
          variant="subTitleMedium"
        >
          {plan.name}
        </Typography>
        <Typography
          variant="bodySmall"
        >
          {plan.price}
        </Typography>
      </div>
    ),
  },
  {
    name: "Callouts",
    render: (
      plan: typeof plans[0]
    ) => (
      <div
        className={plan.isHighlighted ? styles.highlightedCell.base : styles.cell}
      >
        <Typography
          className={styles.text}
        >
          {plan.callouts}
        </Typography>
      </div>
    ),
  },
  {
    name: "Boiler and controls repair",
    render: (
      plan: typeof plans[0]
    ) => (
      <div
        className={plan.isHighlighted ? styles.highlightedCell.base : styles.cell}
      >
        {plan.boilerAndControlsRepair ? (
          <MemoCheckMarkIcon
            className={styles.check}
          />
        ) : (
          <svg
            width="18"
            height="2"
            viewBox="0 0 18 2"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <rect
              width="18"
              height="2"
              fill="#003D23"
            />
          </svg>
        )}
      </div>
    ),
  },
  {
    name: "Annual boiler service",
    render: (
      plan: typeof plans[0]
    ) => (
      <div
        className={plan.isHighlighted ? styles.highlightedCell.base : styles.cell}
      >
        {plan.annualBoilerService ? (
          <MemoCheckMarkIcon
            className={styles.check}
          />
        ) : (
          <svg
            width="18"
            height="2"
            viewBox="0 0 18 2"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <rect
              width="18"
              height="2"
              fill="#003D23"
            />
          </svg>
        )}
      </div>
    ),
  },
  {
    name: "Plumbing/drains or electrical inspection",
    render: (
      plan: typeof plans[0]
    ) => (
      <div
        className={plan.isHighlighted ? styles.highlightedCell.base : styles.cell}
      >
        {plan.plumbingDrainsOrElectricalInspection ? (
          <MemoCheckMarkIcon
            className={styles.check}
          />
        ) : (
          <svg
            width="18"
            height="2"
            viewBox="0 0 18 2"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <rect
              width="18"
              height="2"
              fill="#003D23"
            />
          </svg>
        )}
      </div>
    ),
  },
  {
    name: "Guarantee on any repair",
    render: (
      plan: typeof plans[0]
    ) => (
      <div
        className={plan.isHighlighted ? styles.highlightedCell.base : styles.cell}
      >
        <Typography
          className={styles.text}
        >
          {plan.guaranteeOnAnyRepair}
        </Typography>
      </div>
    ),
  },
  {
    name: "Priority service within 24 hours",
    render: (
      plan: typeof plans[0]
    ) => (
      <div
        className={plan.isHighlighted ? styles.highlightedCell.base : styles.cell}
      >
        {plan.priorityServiceWithin24Hours ? (
          <MemoCheckMarkIcon
            className={styles.check}
          />
        ) : (
          <svg
            width="18"
            height="2"
            viewBox="0 0 18 2"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <rect
              width="18"
              height="2"
              fill="#003D23"
            />
          </svg>
        )}
      </div>
    ),
  },
  {
    name: "Gas/electric water heaters replacement",
    render: (
      plan: typeof plans[0]
    ) => (
      <div
        className={plan.isHighlighted ? styles.highlightedCell.base : styles.cell}
      >
        {plan.gasElectricWaterHeatersReplacement ? (
          <MemoCheckMarkIcon
            className={styles.check}
          />
        ) : (
          <svg
            width="18"
            height="2"
            viewBox="0 0 18 2"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <rect
              width="18"
              height="2"
              fill="#003D23"
            />
          </svg>
        )}
      </div>
    ),
  },
  {
    name: "Diagnostics",
    render: (
      plan: typeof plans[0]
    ) => (
      <div
        className={plan.isHighlighted ? styles.highlightedCell.last : styles.cell}
      >
        <Typography
          className={styles.text}
        >
          {plan.diagnostics}
        </Typography>
      </div>
    ),
  },
];

export type CompareFeaturesSectionProps =
  SliceComponentProps<Content.CompareFeaturesSectionSlice>;

const CompareFeaturesSection = (
  {
    slice,
  }: CompareFeaturesSectionProps
): JSX.Element => {
  return (
    <Container>
      <section
        className={styles.root}
        data-slice-type={slice.slice_type}
        data-slice-variation={slice.variation}
      >
        <Typography
          className={styles.title}
          variant="h2"
        >
          <PrismicRichText
            field={slice.primary.title}
          />
        </Typography>
        <div
          className={styles.table}
        >
          {features.map(
            feature => (
              <Fragment
                key={String(feature.name)}
              >
                <div
                  className={classNames(
                    styles.mainCell, String(feature.name) === String(features[0].name) && gridSprinkle(
                      { display: { mobile: "none", tablet: "flex" } }
                    )
                  )}
                >
                  <Typography
                    className={styles.mainText}
                  >
                    {feature.name}
                  </Typography>
                </div>
                {plans.map(
                  plan => feature.render(
                    plan
                  )
                )}
              </Fragment>
            )
          )}
        </div>
      </section>
    </Container>
  );
};

export default CompareFeaturesSection;
