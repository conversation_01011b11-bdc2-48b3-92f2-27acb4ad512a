import Typography from "@/components/Typography";
import { Content } from "@prismicio/client";
import { SliceComponentProps, PrismicRichText } from "@prismicio/react";
import * as styles from "./Paragraph.css";

/**
 * Props for `Paragraph`.
 */
export type ParagraphProps = SliceComponentProps<Content.ParagraphSlice>;

/**
 * Component for "Paragraph" Slices.
 */
const Paragraph = ({ slice }: ParagraphProps): JSX.Element => {
  return (
    <Typography
      variant="bodySmall"
      className={styles.root}
    >
      <PrismicRichText
        field={slice.primary.content}
      />
    </Typography>
  );
};

export default Paragraph;
