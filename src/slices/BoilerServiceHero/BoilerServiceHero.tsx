import Container from "@/components/Container";
import { Content } from "@prismicio/client";
import { SliceComponentProps } from "@prismicio/react";
import * as styles from "./BoilerServiceHero.css";
import classNames from "classnames";
import Typography from "@/components/Typography";
import { PrismicRichText } from "node_modules/@prismicio/react/dist/react-server/PrismicRichText";
import { PrismicNextImage } from "@prismicio/next";
import BookServiceButton from "@/components/BookServiceButton/BookServiceButton";

export type BoilerServiceHeroProps =
  SliceComponentProps<Content.BoilerServiceHeroSlice>;

const BoilerServiceHero = ({ slice }: BoilerServiceHeroProps): JSX.Element => {

  return (
    <Container
      removeBorderRadius
      className={styles.container}
    >
      <section
        data-slice-type={slice.slice_type}
        data-slice-variation={slice.variation}
        className={styles.root}
      >
        <div
          className={classNames(styles.wrapper, {
            [styles.isReversed]: slice.variation !== "default",
          })}
        >
          <div
            className={styles.imageWrapper}
          >
            <PrismicNextImage
              field={slice.primary.image}
              className={styles.image}
            />
          </div>
          <div
            className={classNames(styles.info, {
              [styles.infoReversed]: slice.variation !== "default",
            })}
          >
            <Typography
              className={classNames(styles.title, {
                [styles.titleReversed]: slice.variation !== "default",
              })}
              variant="h1"
              fontFamily="primary"
              as={"h1"}
              isGreenItalic
            >
              <PrismicRichText
                field={slice.primary.title}
              />
            </Typography>
            <Typography
              variant="bodyMedium"
              className={styles.description}
            >
              <PrismicRichText
                field={slice.primary.description}
              />
            </Typography>
            <BookServiceButton
              color="primary"
              className={styles.button}
            />
          </div>
        </div>
        {/* Placeholder component for boiler_service_hero (variation:{" "}
        {slice.variation}) Slices */}
      </section>
    </Container>
  );
};

export default BoilerServiceHero;
