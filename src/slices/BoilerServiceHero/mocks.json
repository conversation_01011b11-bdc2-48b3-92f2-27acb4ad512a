[{"__TYPE__": "SharedSliceContent", "variation": "default", "primary": {"title": {"__TYPE__": "StructuredTextContent", "value": [{"type": "paragraph", "content": {"text": "New Boiler Installation", "spans": [{"type": "em", "start": 0, "end": 10}, {"type": "strong", "start": 0, "end": 10}]}, "direction": "ltr"}]}, "description": {"__TYPE__": "StructuredTextContent", "value": [{"type": "paragraph", "content": {"text": "Our team of experts can help you upgrade\nyour home, improve efficiency, save money\nand guarantee warmth and comfort", "spans": []}, "direction": "ltr"}]}, "image": {"origin": {"id": "main", "url": "https://images.unsplash.com/photo-1498050108023-c5249f4df085", "width": 3882, "height": 2584}, "url": "https://images.unsplash.com/photo-1498050108023-c5249f4df085", "width": 3882, "height": 2584, "edit": {"zoom": 1, "crop": {"x": 0, "y": 0}, "background": "transparent"}, "credits": null, "alt": null, "__TYPE__": "ImageContent", "thumbnails": {}}, "button_text": {"__TYPE__": "<PERSON><PERSON><PERSON><PERSON>", "type": "Text", "value": "Book Installation"}}, "items": []}, {"__TYPE__": "SharedSliceContent", "variation": "imageOnTheRight", "primary": {"title": {"__TYPE__": "StructuredTextContent", "value": [{"type": "paragraph", "content": {"text": "Explore\nBoiler Service", "spans": [{"type": "em", "start": 15, "end": 22}, {"type": "strong", "start": 15, "end": 22}]}, "direction": "ltr"}]}, "description": {"__TYPE__": "StructuredTextContent", "value": [{"type": "paragraph", "content": {"text": "Keep your boiler running smoothly with our\ncomprehensive boiler service. Our expert technicians\nwill perform thorough inspections and maintenance\nto ensure optimal performance and safety.", "spans": []}, "direction": "ltr"}]}, "image": {"origin": {"id": "main", "url": "https://images.unsplash.com/photo-1531771686035-25f47595c87a", "width": 3024, "height": 3778}, "url": "https://images.unsplash.com/photo-1531771686035-25f47595c87a", "width": 3024, "height": 3778, "edit": {"zoom": 1, "crop": {"x": 0, "y": 0}, "background": "transparent"}, "credits": null, "alt": null, "__TYPE__": "ImageContent", "thumbnails": {}}, "button_text": {"__TYPE__": "<PERSON><PERSON><PERSON><PERSON>", "type": "Text", "value": "Book service"}}, "items": [{"__TYPE__": "GroupItemContent", "value": []}]}]