import { breakpoints } from "@/styles/constants.css";
import { theme } from "@/styles/themes.css";
import { style } from "@vanilla-extract/css";

export const container = style({
  paddingLeft: "0 !important",
  paddingRight: "0 !important",
});

export const root = style({
  padding: "0 10px 10px",
  backgroundColor: theme.colors.primary.castletonGreen,
  borderRadius: "0 0 16px 16px",

  "@media": {
    [breakpoints.tablet]: {
      padding: "0 20px 20px",
    },
  },
});

export const wrapper = style({
  display: "flex",
  flexDirection: "column-reverse",

  "@media": {
    [breakpoints.tablet]: {
      gap: 145,
      flexDirection: "row",
    },
  },
});


export const isReversed = style({
  flexDirection: "column-reverse",

  "@media": {
    [breakpoints.tablet]: {
      gap: 48,
      flexDirection: "row-reverse",
      justifyContent: "space-between"
    },
  },
});

export const imageWrapper = style({
  maxWidth: "100%",
  overflow: "hidden",
  borderRadius: 16,
  backgroundColor: theme.colors.primary.asidGreen,

  "@media": {
    [breakpoints.tablet]: {
      borderRadius: 24,
      maxWidth: 576,
      width:"100%",
      minHeight: 685,
    },
  },
});

export const image = style({
  display: "block",
  maxWidth: "100%",
  height: "100%",
  maxHeight: "100%",
  objectFit: "cover",
});

export const info = style({
  display: "flex",
  flexDirection: "column",
  alignItems: "flex-start",
  marginRight: 20,
  marginLeft: 20,

  "@media": {
    [breakpoints.tablet]: {
      marginRight: 0,
      marginLeft: 0,
    },
  },
});

export const infoReversed = style({
  "@media": {
    [breakpoints.tablet]: {
      paddingLeft: 48,
    },
  },
});

export const title = style({
  marginTop: 40,
  marginBottom: 32,
  color: theme.colors.primary.softWhite,
  fontSize: "58px !important",
  lineHeight: "95% !important",
  letterSpacing: "-1.16px !important",

  "@media": {
    [breakpoints.tablet]: {
      width: "100%",
      maxWidth: "100%",
      marginTop: 97,
      marginBottom: 64,
      fontSize: "104px !important",
      lineHeight: "85% !important",
      letterSpacing: "-2.08px !important",
    },
  },
});

export const titleReversed = style({
  "@media": {
    [breakpoints.tablet]: {
      marginTop: 107,
      marginBottom: 56,
    },
  },
});

export const description = style({
  marginBottom: 40,
  color: theme.colors.primary.softWhite,

  "@media": {
    [breakpoints.tablet]: {
      marginBottom: "auto",
      maxWidth: 425,
    },
  },
});

export const button = style({
  width: "100%",
  minWidth: 315,
  marginBottom: 32,
  minHeight: "56px !important",

  "@media": {
    [breakpoints.tablet]: {
      width: "inherit",
      minWidth: "inherit",
      marginTop: "auto",
      marginBottom: 56,
    },
  }
});
