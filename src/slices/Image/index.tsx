import Typography from "@/components/Typography";
import { Content } from "@prismicio/client";
import { PrismicNextImage } from "@prismicio/next";
import { SliceComponentProps } from "@prismicio/react";
import { PrismicRichText } from "node_modules/@prismicio/react/dist/react-server/PrismicRichText";
import * as styles from "./Image.css";

/**
 * Props for `Image`.
 */
export type ImageProps = SliceComponentProps<Content.ImageSlice>;

/**
 * Component for "Image" Slices.
 */
const Image = ({ slice }: ImageProps): JSX.Element => {
  return (
    <figure
      className={styles.root}
    >
      <div
        className={styles.image}
      >
        <PrismicNextImage
          fill
          objectFit="cover"
          objectPosition="center"
          field={slice.primary.image}
        />      
      </div>
      {slice.primary.image_caption && (
        <figcaption
          className={styles.caption}
        >
          <Typography
            as="div"
            variant="note"
          >
            <PrismicRichText
              field={slice.primary.image_caption}
            />
          </Typography>
        </figcaption>      
      )}
    </figure>
  );
};

export default Image;
