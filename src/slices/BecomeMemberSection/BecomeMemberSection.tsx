"use client";

import MemoCheckMarkIcon from "@/assets/icons/CheckMarkIcon";
import Button from "@/components/Button";
import Container from "@/components/Container";
import Typography from "@/components/Typography";
import useIsAnimationExist from "@/hooks/useIsAnimationExist";
import { gridSprinkle } from "@/styles/sprinkles.css";
import { Content } from "@prismicio/client";
import { PrismicNextImage } from "@prismicio/next";
import { PrismicRichText, SliceComponentProps } from "@prismicio/react";
import classNames from "classnames";
import Link from "next/link";
import { useRef } from "react";
import * as styles from "./BecomeMemberSection.css";

export type BecomeMemberSectionProps =
  SliceComponentProps<Content.BecomeMemberSectionSlice>;

const BecomeMemberSection = ({
  slice,
}: BecomeMemberSectionProps): JSX.Element => {
  const containerRef = useRef<HTMLDivElement>(null);

  const isWithAnimation = useIsAnimationExist();

  return (
    <Container
      className={styles.container}
      ref={containerRef}
      notFullHeight
      withAnimation={isWithAnimation}
    >
      <section
        className={classNames(
          styles.root[slice.variation],
          gridSprinkle({
            type: "grid",
          })
        )}
        data-slice-type={slice.slice_type}
        data-slice-variation={slice.variation}
      >
        <div
          className={classNames(
            styles.subSection[slice.variation],
            gridSprinkle({ type: "item", cols: { mobile: 10, tablet: 5 } })
          )}
        >
          <Typography
            variant="h2"
            className={styles.title[slice.variation]}
          >
            <PrismicRichText
              field={slice.primary.title}
            />
          </Typography>
          {!!slice.primary.subtitle && !!slice.primary.subtitle.length &&  <Typography
            variant="subTitleMedium"
            className={styles.subtitle[slice.variation]}
          >
            <PrismicRichText
              field={slice.primary.subtitle}
            />
          </Typography>}
          <ul
            className={styles.points[slice.variation]}
          >
            {slice.items.map((item, index) => (
              <li
                key={index}
                className={styles.point[slice.variation]}
              >
                <div
                  className={styles.listIcon[slice.variation]}
                >
                  <MemoCheckMarkIcon/>
                </div>
                <Typography
                  className={styles.benefit[slice.variation]}
                  variant="bodySmall"
                >
                  <PrismicRichText
                    field={item.point}
                  />
                </Typography>
              </li>
            ))}
          </ul>
          <Button
            isAnimated
            className={styles.action}
            prefetch={false}
            as={Link}
            href="/register"
          >
            Become a member
          </Button>
        </div>
        <div
          className={classNames(
            styles.subSection[slice.variation],
            gridSprinkle({ type: "item", cols: { mobile: 10, tablet: 5 } })
          )}
        >
          <div
            className={styles.previewWrapper}
          >
            <PrismicNextImage
              className={styles.preview}
              field={slice.primary.preview}
            />
          </div>
        </div>
      </section>
    </Container>
  );
};

export default BecomeMemberSection;
