import { breakpoints } from "@/styles/constants.css";
import { mode, modeGlobal } from "@/styles/functions.css";
import { theme } from "@/styles/themes.css";
import { globalStyle, keyframes, style, styleVariants } from "@vanilla-extract/css";

export const container = style({
  scrollSnapAlign: "center !important",

  selectors: {
    [mode("residential")]: {
      background: theme.colors.primary.castletonGreen,
    }
  },
});

const rootBase = style({
  padding: "0 20px",
  paddingTop: "40px",
  borderRadius: 16,
  willChange: "transform",
  // backgroundColor: theme.colors.primary.softWhite,
  "@media": {
    [breakpoints.tablet]: {
      borderRadius: 24,
      padding: "0 48px",
      paddingTop: "72px",
      direction: "rtl",
      // backgroundColor: "transparent",
    },
  },
});

const rootAlternative = style({
  backgroundColor: theme.colors.primary.castletonGreen,

  selectors: {
    [mode("residential")]: {
      backgroundColor: theme.colors.primary.ivory,
    }
  },
});

export const root = styleVariants({
  default: [rootBase],
  alternative: [rootBase, rootAlternative]
});

const subSectionBase = style({
  direction: "ltr",
});

const subSectionOffset = style({
  marginBottom: -10,

  "@media": {
    [breakpoints.tablet]: {
      marginBottom: -20,
    },
  },
});


export const subSection = styleVariants({
  default: [subSectionBase, subSectionOffset],
  alternative: [subSectionBase],
});

const titleDefault = style({
  fontFamily: theme.fonts.primary,
  marginBottom: "24px",

  "@media": {
    [breakpoints.tablet]: {
      marginBottom: "32px",
    },
  },

  selectors: {
    [mode("residential")]: {
      color: theme.colors.primary.softWhite,
    }
  },
});

globalStyle(`${modeGlobal("residential")} ${titleDefault} strong`, {
  color: theme.colors.primary.asidGreen,
});

const titleAlternative = style({
  color: theme.colors.primary.ivory,

  selectors: {
    [mode("residential")]: {
      color: theme.colors.primary.castletonGreen,
    }
  },
});

globalStyle(`${modeGlobal("residential")} ${titleAlternative} strong`, {
  color: theme.colors.primary.castletonGreen,
});

export const title = styleVariants({
  default: [titleDefault],
  alternative: [titleDefault, titleAlternative]
});

const subtitleDefault = style({
  marginBottom: "24px",

  "@media": {
    [breakpoints.tablet]: {
      marginBottom: "40px",
    },
  },

  selectors: {
    [mode("residential")]: {
      color: theme.colors.primary.softWhite,
    }
  },
});

export const subtitleAlternative = style({
  color: theme.colors.primary.ivory,

  selectors: {
    [mode("residential")]: {
      color: theme.colors.primary.castletonGreen,
    }
  },
});

export const subtitle = styleVariants({
  default: [subtitleDefault],
  alternative: [subtitleDefault, subtitleAlternative]
});


const pointsDefault = style({
  margin: 0,
  marginBottom: "32px",
  display: "flex",
  flexDirection: "column",
  gap: "12px",
  padding: 0,
  
  "@media": {
    [breakpoints.tablet]: {
      gap: "10px",
      marginBottom: "48px",
    },
  },
});

const pointsAlternative = style({
  color: theme.colors.primary.ivory
});

export const points = styleVariants({
  default: [pointsDefault],
  alternative: [pointsDefault, pointsAlternative]
});

const pointDefault = style({
  display: "flex",
  gap: "10px",
});

const pointAlternative = style({

});

export const point = styleVariants({
  default: [pointDefault],
  alternative: [pointDefault, pointAlternative]
});

const listIconDefault = style({
  selectors: {
    [mode("residential")]: {
      color: theme.colors.primary.asidGreen,
    }
  },
});

const listIconAlternative = style({
  color: theme.colors.primary.asidGreen,

  selectors: {
    [mode("residential")]: {
      color: theme.colors.primary.castletonGreen,
    }
  },
});

export const listIcon = styleVariants({
  default: [listIconDefault],
  alternative: [listIconAlternative]
});


export const previewWrapper = style({
  aspectRatio: "9 / 11",
  display: "flex",

  "@media": {
    [breakpoints.tablet]: {
      direction: "rtl",
      aspectRatio: "auto",
      width: "100%",
      height: "100%",
    },
  },
});

export const preview = style({
  marginTop: "auto",
  width: "100%",
  height: "auto",
  objectFit: "contain",
  objectPosition: "bottom center",
});

export const action = style({
  width: "100%",

  "@media": {
    [breakpoints.tablet]: {
      marginBottom: "56px",
      width: "max-content",
    },
  },
});

const benefitBase = style({
  fontWeight: "500 !important",
});

const benefitDefault = style({
  selectors: {
    [mode("residential")]: {
      color: theme.colors.primary.ivory,
    }
  },
});

const benefitAlternative = style({
  selectors: {
    [mode("residential")]: {
      color: theme.colors.primary.castletonGreen,
    }
  },
});

export const benefit = styleVariants({
  default: [benefitDefault, benefitDefault],
  alternative: [benefitDefault, benefitAlternative]
});

const slideFromTopKeyFrame = keyframes({
  from: {
    transform: "translateY(0)",
  },
  to: {
    transform: "translateY(15%)",
  },
});

export const slideFromTop = style({
  willChange: "transform",
  animation: `1s ${slideFromTopKeyFrame}`,
});
