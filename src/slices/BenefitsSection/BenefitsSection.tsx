import { Content } from "@prismicio/client";
import { SliceComponentProps } from "@prismicio/react";
import * as styles from "./BenefitsSection.css";
import Container from "@/components/Container";
import { gridSprinkle } from "@/styles/sprinkles.css";
import Typography from "@/components/Typography";
import { PrismicRichText } from "node_modules/@prismicio/react/dist/react-server/PrismicRichText";
import classNames from "classnames";

export type BenefitsSectionProps =
  SliceComponentProps<Content.BenefitsSectionSlice>;

const BenefitsSection = ({ slice }: BenefitsSectionProps): JSX.Element => {
  return (
    <Container
      className={styles.container}
    >
      <section
        data-slice-type={slice.slice_type}
        data-slice-variation={slice.variation}
        className={classNames(styles.root, gridSprinkle({ type: "grid" }))}
      >
        <Typography
          variant="h3"
          as={"h2"}
          className={classNames(styles.title, gridSprinkle({ type: "item", cols: { mobile: 10, tablet: 6 } }))}
        >
          <PrismicRichText
            field={slice.primary.title}
          />
        </Typography>
        <Typography
          variant="bodySmall"
          className={classNames(styles.description, gridSprinkle({ type: "item", cols: { mobile: 10, tablet: 4 }, alignSelf: "center" }))}
        >
          <PrismicRichText
            field={slice.primary.description}
          />
        </Typography>
        <div
          className={classNames(styles.benefits, gridSprinkle({ type: "item", cols: 10 }))}
        >
          {slice.items.map((item, index) => (
            <div
              className={styles.benefit}
              key={index}
            >
              <Typography
                className={styles.benefitTitle}
                variant="subTitleMedium"
              >
                <svg
                  width="20"
                  height="20"
                  viewBox="0 0 20 20"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M9.31774 0.972514C9.48404 0.247973 10.516 0.247975 10.6823 0.972517L12.142 7.33235C12.202 7.59383 12.4062 7.798 12.6677 7.85802L19.0275 9.31774C19.752 9.48404 19.752 10.516 19.0275 10.6823L12.6677 12.142C12.4062 12.202 12.202 12.4062 12.142 12.6677L10.6823 19.0275C10.516 19.752 9.48404 19.752 9.31774 19.0275L7.85802 12.6677C7.798 12.4062 7.59383 12.202 7.33235 12.142L0.972514 10.6823C0.247973 10.516 0.247975 9.48404 0.972517 9.31774L7.33235 7.85802C7.59383 7.798 7.798 7.59383 7.85802 7.33235L9.31774 0.972514Z"
                    fill="#003D23"
                  />
                </svg>
                {item.benefit_title}
              </Typography>
              <Typography
                variant="bodySmall"
              >
                <PrismicRichText
                  field={item.benefit_content}
                />
              </Typography>
            </div>
          ))}
        </div>
      </section>
    </Container>
  );
};

export default BenefitsSection;
