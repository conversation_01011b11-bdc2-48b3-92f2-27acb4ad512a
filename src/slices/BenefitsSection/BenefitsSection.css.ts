import { breakpoints } from "@/styles/constants.css";
import { mode } from "@/styles/functions.css";
import { theme } from "@/styles/themes.css";
import { style } from "@vanilla-extract/css";

export const container = style({
  scrollSnapAlign: "start",

  selectors: {
    [mode("residential")]: {
      backgroundColor: theme.colors.primary.castletonGreen,
    }
  },
});

export const root = style({
  padding: "40px 20px",

  "@media": {
    [breakpoints.tablet]: {
      padding: "72px 48px",
    },
  },
});

export const title = style({
  fontFamily: theme.fonts.primary,
  marginBottom: 24,

  "@media": {
    [breakpoints.tablet]: {
      marginBottom: 0,
    },
  },

  selectors: {
    [mode("residential")]: {
      color: theme.colors.primary.softWhite,
    }
  },
});

export const description = style({
  marginBottom: 40,

  "@media": {
    [breakpoints.tablet]: {
      marginBottom: 0,
    },
  },

  selectors: {
    [mode("residential")]: {
      color: theme.colors.primary.softWhite,
      opacity: 0.8,
    }
  },
});

export const benefits = style({
  display: "flex",
  flexWrap: "wrap",
  gap: 16,

  "@media": {
    [breakpoints.tablet]: {
      marginTop: 80,
      gap: 24,
    },
  },
});

export const benefit = style({
  borderRadius: 16,
  backgroundColor: theme.colors.primary.softWhite,
  minWidth: "100%",
  flex: "1 1",
  padding: 20,

  "@media": {
    [breakpoints.tablet]: {
      padding: 32,
      minWidth: "30%",
    },
  },
});

export const benefitTitle = style({
  display: "flex",
  gap: 8,
  marginBottom: 16,
});