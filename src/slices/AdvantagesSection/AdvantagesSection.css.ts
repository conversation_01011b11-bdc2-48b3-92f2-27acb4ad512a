import { breakpoints } from "@/styles/constants.css";
import { theme } from "@/styles/themes.css";
import { style } from "@vanilla-extract/css";


export const root = style({
  display: "grid",
  columnGap: 86,
  rowGap: 40,
  backgroundColor: theme.colors.primary.castletonGreen,
  color: theme.colors.primary.softWhite,
  borderRadius: 24,
  padding: "40px 20px",
  "@media": {
    [breakpoints.tablet]: {
      marginTop: 80,
      paddingTop: 130,
      paddingLeft: 56,
      paddingRight: 56,
      paddingBottom: 80,
      gridTemplateColumns: "repeat(2,1fr)",
      rowGap: 0,
    }
  }

});

export const title = style({
  textAlign: "center",
  "@media": {
    [breakpoints.tablet]: {
      marginBottom: 72,
      gridColumn: "span 2",
    }}
});

export const listWrapper = style({
  display: "grid",
  rowGap: 24,
  alignContent: "start",
  padding: 0,
  listStyle: "none",

  "@media": {
    [breakpoints.tablet]: {
      rowGap: 56,
    }
  }
});

export const listItem = style({
  display: "grid",
  gridTemplateColumns: "auto 1fr",
  columnGap: 16,
  rowGap: 24
});

export const listItemTitle = style({
  fontSize: "18px !important",
  alignSelf: "end",

  "@media": {
    [breakpoints.tablet]: {
      fontSize: "24px !important",
    }
  }
});

export const bookButton = style({
  display: "grid",
  "@media": {
    [breakpoints.tablet]: {
      marginTop: 56,
      display: "block",
      gridColumnStart: 2,
    }}
});

export const listImgWrapper  = style({
  width: "1.5rem",
  height: "1.5rem"
});

export const listImg= style({
  width: "100%",
  height: "100%"
});

export const heroWrapper = style({
  width: "100%",
  height: 300,
  "@media": {
    [breakpoints.tablet]: {
      width: 540,
      height: 516,

    }
  },
  borderRadius: 24,
  overflow: "hidden",
  position: "relative"
});

const heroBase = style({
  width: "100%",
  height: "100%",
  objectFit: "cover"
});

export const hero = style([heroBase,{
  position: "absolute",
  inset: 0,
  zIndex: 9
}]);

export const hero2 = style([heroBase, {
  position: "absolute",
  inset: 0,
}]);

export const listItemDescription = style({
  gridColumn: "span 2"
});

