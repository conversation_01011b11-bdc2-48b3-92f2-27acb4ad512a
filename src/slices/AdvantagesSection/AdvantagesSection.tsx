import Container from "@/components/Container";
import { Content } from "@prismicio/client";
import { PrismicImage, PrismicRichText, SliceComponentProps } from "@prismicio/react";
import classNames from "classnames";
import * as styles from "./AdvantagesSection.css";
import Typography from "@/components/Typography";
import Divider from "@/components/Divider/Divider";
import BookServiceButton from "@/components/BookServiceButton/BookServiceButton";
import { toCamelCase } from "@/utils/helpers";
import { Fragment } from "react";

/**
 * Props for `AdvantagesSection`.
 */
export type AdvantagesSectionProps =
  SliceComponentProps<Content.AdvantagesSectionSlice>;

/**
 * Component for "AdvantagesSection" Slices.
 */
const AdvantagesSection =  ({ slice }: AdvantagesSectionProps): JSX.Element => {



  return (
    <Container>
      <section
        id={slice.primary.header_anchor_name ? toCamelCase(slice.primary.header_anchor_name) : ""}
        className={classNames(styles.root,)}
        data-slice-type={slice.slice_type}
        data-slice-variation={slice.variation}
      >
        <Typography
          isGreenItalic
          className={styles.title}
          variant="h2"
          fontFamily="primary"
          as={"h2"}
        >
          <PrismicRichText
            field={slice.primary.title}
          />
        </Typography>
        <div
          className={styles.heroWrapper}
        >
          <PrismicImage
            className={styles.hero}
            field={slice.primary.hero}
          />
          <PrismicImage
            className={styles.hero2}
            field={slice.primary.hero2}
          />
        </div>
        <ul
          className={styles.listWrapper}
        >
          {slice.items.map(({description,icon,title}, idx) => {
            return (
              <Fragment
                key={`${idx}_advantages_list_item`}
              >
                {!!idx && (<Divider
                  withOpacity
                />)}
                <li
                  
                  className={styles.listItem}
                >

                  <div
                    className={styles.listImgWrapper}
                  >
                    <PrismicImage
                      className={styles.listImg}
                      field={icon}
                    />
                  </div>
                  <Typography
                    variant="subTitleMedium"
                    className={styles.listItemTitle}
                  >
                    <PrismicRichText
                      field={title}
                    />
                  </Typography>
                  <Typography
                    className={styles.listItemDescription}
                    variant="bodySmall"
                  >
                    <PrismicRichText
                      field={description}
                    />
                  </Typography>
                </li>
              </Fragment>
            );
          })}
        </ul>
        <div
          className={styles.bookButton}
        >
          <BookServiceButton/>
        </div>
      </section>
    </Container>
  );
};

export default AdvantagesSection;
