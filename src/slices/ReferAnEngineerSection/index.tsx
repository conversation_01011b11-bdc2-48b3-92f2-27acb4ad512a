import {Content} from "@prismicio/client";
import {SliceComponentProps} from "@prismicio/react";
import ReferAnEngineerHero from "./ReferAnEngineerHero";

/**
 * Props for `ReferAnEngineerHero`.
 */
export type ReferAnEngineerSectionProps =
  SliceComponentProps<Content.ReferAnEngineerSectionSlice>;

/**
 * Component for "ReferAnEngineerHero" Slices.
 */
const ReferAnEngineerSection = ({
  slice,
}: ReferAnEngineerSectionProps): JSX.Element => {
  return (
    <ReferAnEngineerHero slice={slice} />
  );
};

export default ReferAnEngineerSection;
