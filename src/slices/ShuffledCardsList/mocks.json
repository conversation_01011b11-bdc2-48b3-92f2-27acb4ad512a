[{"__TYPE__": "SharedSliceContent", "variation": "default", "primary": {"title": {"__TYPE__": "StructuredTextContent", "value": [{"type": "paragraph", "content": {"text": "Why Choose Us?", "spans": []}, "direction": "ltr"}]}, "moving_text_divider": {"url": "https://images.unsplash.com/photo-1587496639254-67cb9f1fc5a4?crop=entropy&cs=srgb&fm=jpg&ixid=M3wzMzc0NjN8MHwxfHNlYXJjaHwyN3x8Y2l0cnVzJTIwZnJ1aXR8ZW58MHx8fHwxNzE2OTM1MjIwfDA&ixlib=rb-4.0.3&q=85", "alt": "sliced lemon on white surface", "origin": {"id": "mjVN3RJVKd0", "url": "https://images.unsplash.com/photo-1587496639254-67cb9f1fc5a4?crop=entropy&cs=srgb&fm=jpg&ixid=M3wzMzc0NjN8MHwxfHNlYXJjaHwyN3x8Y2l0cnVzJTIwZnJ1aXR8ZW58MHx8fHwxNzE2OTM1MjIwfDA&ixlib=rb-4.0.3&q=85", "width": 3024, "height": 4032}, "width": 3024, "height": 4032, "edit": {"background": "transparent", "zoom": 1, "crop": {"x": 0, "y": 0}}, "credits": null, "__TYPE__": "ImageContent", "thumbnails": {}}, "description": {"__TYPE__": "StructuredTextContent", "value": [{"type": "paragraph", "content": {"text": "For Your Boiler Needs", "spans": []}, "direction": "ltr"}]}, "header_anchor_name": {"__TYPE__": "<PERSON><PERSON><PERSON><PERSON>", "value": "usually", "type": "Text"}}, "items": [{"__TYPE__": "GroupItemContent", "value": [["title", {"__TYPE__": "<PERSON><PERSON><PERSON><PERSON>", "value": "wolf", "type": "Text"}], ["description", {"__TYPE__": "<PERSON><PERSON><PERSON><PERSON>", "value": "ahead", "type": "Text"}], ["type", {"__TYPE__": "<PERSON><PERSON><PERSON><PERSON>", "value": "green", "type": "Select"}]]}, {"__TYPE__": "GroupItemContent", "value": [["title", {"__TYPE__": "<PERSON><PERSON><PERSON><PERSON>", "type": "Text", "value": "wolf2"}], ["type", {"__TYPE__": "<PERSON><PERSON><PERSON><PERSON>", "type": "Select", "value": "white"}]]}, {"__TYPE__": "GroupItemContent", "value": [["title", {"__TYPE__": "<PERSON><PERSON><PERSON><PERSON>", "type": "Text", "value": "wolf3"}], ["type", {"__TYPE__": "<PERSON><PERSON><PERSON><PERSON>", "type": "Select", "value": "<PERSON><PERSON><PERSON>"}]]}, {"__TYPE__": "GroupItemContent", "value": [["title", {"__TYPE__": "<PERSON><PERSON><PERSON><PERSON>", "type": "Text", "value": "wolf4"}], ["type", {"__TYPE__": "<PERSON><PERSON><PERSON><PERSON>", "type": "Select", "value": "green"}]]}, {"__TYPE__": "GroupItemContent", "value": [["title", {"__TYPE__": "<PERSON><PERSON><PERSON><PERSON>", "type": "Text", "value": "wolf5"}], ["type", {"__TYPE__": "<PERSON><PERSON><PERSON><PERSON>", "type": "Select", "value": "green"}]]}]}, {"__TYPE__": "SharedSliceContent", "variation": "withoutMar<PERSON>e", "primary": {"title": {"__TYPE__": "StructuredTextContent", "value": [{"type": "paragraph", "content": {"text": "Why you may need a Powerflush?", "spans": []}, "direction": "ltr"}]}, "header_anchor_name": {"__TYPE__": "<PERSON><PERSON><PERSON><PERSON>", "value": "won", "type": "Text"}}, "items": [{"__TYPE__": "GroupItemContent", "value": [["type", {"__TYPE__": "<PERSON><PERSON><PERSON><PERSON>", "value": "white", "type": "Select"}], ["title", {"__TYPE__": "<PERSON><PERSON><PERSON><PERSON>", "value": "from", "type": "Text"}], ["description", {"__TYPE__": "<PERSON><PERSON><PERSON><PERSON>", "type": "Text", "value": "lorem lorem lorem lorem lorem lorem lorem lorem lorem lorem lorem lorem"}]]}, {"__TYPE__": "GroupItemContent", "value": [["title", {"__TYPE__": "<PERSON><PERSON><PERSON><PERSON>", "type": "Text", "value": "from2"}], ["type", {"__TYPE__": "<PERSON><PERSON><PERSON><PERSON>", "type": "Select", "value": "<PERSON><PERSON><PERSON>"}]]}, {"__TYPE__": "GroupItemContent", "value": [["title", {"__TYPE__": "<PERSON><PERSON><PERSON><PERSON>", "type": "Text", "value": "from3"}], ["type", {"__TYPE__": "<PERSON><PERSON><PERSON><PERSON>", "type": "Select", "value": "white"}]]}, {"__TYPE__": "GroupItemContent", "value": [["title", {"__TYPE__": "<PERSON><PERSON><PERSON><PERSON>", "type": "Text", "value": "from4"}], ["type", {"__TYPE__": "<PERSON><PERSON><PERSON><PERSON>", "type": "Select", "value": "<PERSON><PERSON><PERSON>"}]]}, {"__TYPE__": "GroupItemContent", "value": [["title", {"__TYPE__": "<PERSON><PERSON><PERSON><PERSON>", "type": "Text", "value": "from5"}], ["type", {"__TYPE__": "<PERSON><PERSON><PERSON><PERSON>", "type": "Select", "value": "white"}]]}, {"__TYPE__": "GroupItemContent", "value": [["title", {"__TYPE__": "<PERSON><PERSON><PERSON><PERSON>", "type": "Text", "value": "from6"}], ["type", {"__TYPE__": "<PERSON><PERSON><PERSON><PERSON>", "type": "Select", "value": "green"}]]}]}]