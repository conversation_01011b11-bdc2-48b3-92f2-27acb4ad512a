"use client";
import Container from "@/components/Container";
import Marquee from "@/components/Marquee";
import { Content, RichTextNodeType } from "@prismicio/client";
import { PrismicNextImage } from "@prismicio/next";
import { PrismicRichText, SliceComponentProps } from "@prismicio/react";
import * as styles from "./ShuffledCardsList.css";
import Typography from "@/components/Typography";
import { gridSprinkle } from "@/styles/sprinkles.css";
import classNames from "classnames";
import MemoCheckMarkIcon from "@/assets/icons/CheckMarkIcon";
import Button from "@/components/Button";
import { toCamelCase } from "@/utils/helpers";
import useScrollHorList from "@/hooks/useScrollHorList";
import IconButton from "@/components/IconButton";
import { ChevronIcon } from "@/assets/icons/ChevronIcon";
import BookServiceButton from "@/components/BookServiceButton";

/**
 * Props for `ShuffledCardsList`.
 */
export type ShuffledCardsListProps =
  SliceComponentProps<Content.ShuffledCardsListSlice>;

/**
 * Component for "ShuffledCardsList" Slices.
 */
const ShuffledCardsList = ({ slice }: ShuffledCardsListProps): JSX.Element => {

  const isDefault = slice.variation === "default";

  const {handleNext,handlePrev,setRef} = useScrollHorList();

  return (
    
    <section
      id={slice.primary.header_anchor_name ? toCamelCase(slice.primary.header_anchor_name) : ""}
      className={classNames(styles.container, "shuffled-cards-list")}
      data-slice-type={slice.slice_type}
      data-slice-variation={slice.variation}
    >
      {isDefault &&  <Marquee
        className={styles.marquee}
                     > 
        <div
          className={styles.movingTextWrapper}
        >
          <PrismicNextImage
            className={styles.movingTextDividerImg}
            field={slice.primary.moving_text_divider}
          />
          <Typography
            fontFamily="primary"
            variant="h1"
            as={"h2"}
          >
            <PrismicRichText
              field={slice.primary.title}
            />
          </Typography>
        </div>
      </Marquee>}
      <Container
        className={styles.root}
        notFullHeight
      >
        
        {!isDefault && (<Typography
          className={styles.title}
          fontFamily="primary"
          variant="h1"
        >
          <PrismicRichText
            field={slice.primary.title}
          /></Typography> )}
        {isDefault && <Typography

          variant="subTitleMedium"
          className={styles.description}
        >
          <PrismicRichText
            field={slice.primary.description}
          />
        </Typography>
        }
        <ul
          ref={setRef}
          className={classNames(styles.cardsList[slice.variation],)}
        >
          {slice.items.map(({description,title,type}, idx) => {
            const isGreenAndNotDefault = !isDefault && type === "green";
            return (
              <li
                key={title}
                className={classNames(styles.cardItem[type], styles.cardItemHeight[slice.variation] ,!isGreenAndNotDefault && styles.withGridColumnsCard, { [styles.shuffledCardDefault[idx as 0 | 1 | 2 | 3 |4]]: isDefault, [styles.shuffledCardWithoutMarquee[idx as 0 | 1 | 2 | 3 |4 | 5]]: !isDefault}, styles.cardItemOrder)}
              >
                {isGreenAndNotDefault ? (<Typography
                  as={"h4"}
                  variant="h4"
                  fontFamily="primary"
                >{title}</Typography>) : (<>
                  <div
                    className={classNames(styles.circleWrapper[`${type}-${slice.variation}`], {[styles.circleWrapperWithoutMarqueeType]: !isDefault})}
                  >
                    <MemoCheckMarkIcon
                      className={styles.checkMarkIcon}
                    />
                  </div>
                  <Typography
                    className={styles.cardItemTitle[slice.variation]}
                    variant="subTitleMedium"
                  >
                    {title}
                  </Typography>
                </>)}
                {isGreenAndNotDefault 
                  ? (
                    <BookServiceButton
                      color="secondary"
                      alternateHover
                    >Book an Expert</BookServiceButton>
                  )  
                  : (
                    <Typography
                      className={styles.cardDescription}
                      variant="bodySmall"
                    >
                      {description}
                    </Typography>
                  )
                }
              </li>
            );
          })}
        </ul>
        <div
          className={styles.listButtonsWrapper[slice.variation]}
        >
          <IconButton
            onClick={handlePrev}
          > 
            <ChevronIcon
              turn={"left"}
            />
          </IconButton>
          <IconButton
            onClick={handleNext}
          > 
            <ChevronIcon
              turn={"right"}
            />
          </IconButton>
        </div>
        {slice.variation === "withoutMarquee" && (
          <BookServiceButton
            color="primary"
            alternateHover
            className={styles.mobileBoolServiceButton}
          >
            Book an Expert
          </BookServiceButton>
        )}
      </Container>
    </section>
  );
};

export default ShuffledCardsList;
