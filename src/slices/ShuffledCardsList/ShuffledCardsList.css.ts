import { breakpointValues, breakpoints } from "@/styles/constants.css";
import { theme } from "@/styles/themes.css";
import { globalStyle, style, styleVariants } from "@vanilla-extract/css";

export const movingTextDividerImg = style({
  width: 86,
  height: "min-content"
});

export const movingTextWrapper = style({
  display: "flex",
  gap: "16px",
  width: "100%",
  height: 100,
  alignItems: "center"
});

export const description = style({
  display: "flex",
  justifyContent: "center",
  marginTop: 34,
  textAlign: "center",
  padding: "0 16px",
  "@media": {
    [breakpoints.tablet]: {
      padding: 0
    }
  }
});

export const root = style({
  display: "grid",
  paddingBottom: 30,
  "@media": {
    [breakpoints.tablet]: {
      paddingBottom: 70
    }
  }
});

export const container = style({
  marginTop: 40,
  "@media": {
    [breakpoints.tablet]: {
      marginTop: 80,
    }
  }
});





const circleWrapperBase = style({
  width: "2rem",
  height: "2rem",
  borderRadius: "50%",
  display: "grid",
  placeItems: "center",
  fontSize: 20,

  "@media": {
    [breakpoints.tablet]: {
      width: "3rem",
      height: "3rem",
    }
  }
});

export const checkMarkIcon = style({
  width: "13.33px",
  height: "13.33px",

  "@media": {
    [breakpoints.tablet]: {
      width: "20px",
      height: "20px",
    }
  }
});

const circleWrapperGreen = style({
  backgroundColor: theme.colors.primary.castletonGreen,
  color: theme.colors.primary.asidGreen,
});
const circleWrapperDarkGreen = style({
  backgroundColor: theme.colors.primary.softWhite,
  color: theme.colors.primary.castletonGreen,
});
const circleWrapperWhite = style({
  backgroundColor: theme.colors.primary.asidGreen,
  color: theme.colors.primary.castletonGreen,
});

export const circleWrapperWithoutMarqueeType = style( {
  gridColumn: 3
});




export const circleWrapper = styleVariants({
  ["white-default"]: [circleWrapperBase, circleWrapperWhite],
  ["white-withoutMarquee"]: [circleWrapperBase, circleWrapperGreen],
  ["green-default"]: [circleWrapperBase, circleWrapperGreen],
  ["green-withoutMarquee"]: [circleWrapperBase, circleWrapperGreen],
  ["darkGreen-default"]: [circleWrapperBase, circleWrapperDarkGreen],
  ["darkGreen-withoutMarquee"]: [circleWrapperBase, circleWrapperWhite],

});

const cardItemWhite = style({
  backgroundColor: theme.colors.primary.softWhite,
  color: theme.colors.primary.castletonGreen,
  
});
const cardItemDarkGreen = style({
  backgroundColor: theme.colors.primary.castletonGreen,
  color: theme.colors.primary.softWhite
});
const cardItemGreen = style({
  backgroundColor: theme.colors.primary.asidGreen,
  color: theme.colors.primary.castletonGreen,
});


const cardItemHeightBase = style({
  minHeight: "auto",
});

const cardItemBaseHeightWithoutMarquee = style({
  minHeight: 305,
  "@media": {
    [breakpoints.tablet]: {
      minHeight: 339,
    },
  }
});
const cardItemBaseHeightDefault = style({
  width: "100%",
  "@media": {
    [breakpoints.tablet]: {
      minHeight: 268,
    },
  }
});

const cardItemWithoutMarquee = style({
  width: "calc(100vw - 20px)",
  height: "100%",
  "@media": {
    [breakpoints.tablet]: {
      width: "inherit",
      height: "auto"
    }
  }
});



export const cardItemHeight = styleVariants({
  default: [cardItemHeightBase, cardItemBaseHeightDefault],
  withoutMarquee: [cardItemHeightBase,cardItemBaseHeightWithoutMarquee,cardItemWithoutMarquee]
});

const cardItemBase = style({
  scrollSnapAlign: "center",
  height: "auto",
  display: "grid",
  padding: "16px 20px 20px 20px",
  borderRadius: 16,
  columnGap: 16,
  rowGap: 16,
  alignItems: "center",
  transition: "transform 0.4s ease-in-out",
  alignContent: "space-between",
  justifyItems: "start",
  transform: "none",
  "@media": {
    [breakpoints.tablet]: {
      width: "30%",
      padding: 32,
      transform: "inherit"
    },
    "(prefers-reduced-motion: reduce)": {
      transform: "none !important"
    }
  }
});

export const withGridColumnsCard = style({
  gridTemplateColumns: "auto 1fr",
});

export const cardItem = styleVariants({
  white: [cardItemBase, cardItemWhite],
  darkGreen: [cardItemBase, cardItemDarkGreen],
  green: [cardItemBase, cardItemGreen],
});

export const cardItemOrder = style({
  selectors: {
    ["&:nth-child(4)"]: {
      order: "5",

      "@media": {
        [breakpoints.tablet]: {
          order: "initial"
        }
      }
    },
  }
});

export const shuffledCardDefault = styleVariants({
  [0]: {
    "@media": {
      [breakpoints.tablet]: {
        transform: "rotate(-13deg)",
        transformOrigin: "110% -200px"
      },
    }
  },
  [1]: {
    "@media": {
      [breakpoints.tablet]: {
        transform: "translateY(10%)"
      },
    }
   
  },
  [2]: {
    "@media": {
      [breakpoints.tablet]: {
        transform: "rotate(13deg)",
        transformOrigin: "-10% -200px"
      },
    }

  },
  [3]: {
    "@media": {
      [breakpoints.tablet]: {
        transform: "rotate(4deg)",
        transformOrigin: "200% 200%",
        zIndex: 9
      },
    }
  
  },
  [4]: {
    "@media": {
      [breakpoints.tablet]: {
        transform: "rotate(-4deg)",
        transformOrigin: "-200% 200%"
      },
    }
  },
});

export const shuffledCardWithoutMarquee = styleVariants({
  [0]: {
  },
  [1]: {
    "@media": {
      [breakpoints.tablet]: {
        transform: "rotate(-13deg)",
      },
    }
   
  },
  [2]: {
  },
  [3]: {
    "@media": {
      [breakpoints.tablet]: {
        transform: "rotate(16deg)",
        zIndex: 9
      },
    }
 
  },
  [4]: {
  },
  [5]: {
    display: "none",

    "@media": {
      [breakpoints.tablet]: {
        display: "grid",
        transform: "rotate(-13deg)",
      },
    }
   
  },
});


const cardsListDefault = style({
  
});

const cardsListWithoutMarquee = style({
  scrollSnapType: "x mandatory",
  display: "grid",
  gridAutoFlow: "column",
  overflowX: "auto",
  gap: 24,
  "@media": {
    [breakpoints.tablet]: {
      overflowX: "visible",
    }
  }

});

 

const cardsListBase = style({
  listStyle: "none",
  display: "grid",
  gap: 16,
  marginTop: 62,
  padding: 0,
  "@media": {
    [breakpoints.tablet]: {
      display: "flex",
      gap: 24,
      marginTop: 104,
      flexWrap: "wrap",
      justifyContent: "center",
    }
  }

});

export const cardsList = styleVariants({
  default: [cardsListBase,cardsListDefault],
  withoutMarquee: [cardsListBase,cardsListWithoutMarquee]
});



export const cardDescription = style({
  gridColumn: "1 / 4"
});

globalStyle(`${cardsListBase}:hover ${cardItemBase}`, {
  transform: "none"
});

export const title = style({
  display: "flex",
  justifyContent: "center",
  textAlign: "center",
  fontSize: "44px !important",

  "@media": {
    [breakpoints.tablet]: {
      fontSize: "80px !important"
    }
  }
});

export const cardItemTitle = styleVariants({
  default: [{
    fontSize: "18px !important",

    "@media": {
      [breakpoints.tablet]: {
        fontSize: "24px !important"
      }
    },
  }],
  withoutMarquee: [{
    gridRow: -1
  }]
});

export const marquee = style({
  margin: "0 auto"
  
});



export const listButtonsWrapper = styleVariants({
  default: [{
    display: "none"
  }],
  withoutMarquee: [{
    display: "flex",
    justifyContent: "center",
    gap: 16,
    marginBottom: 40,
    marginTop: 20,
    "@media": {
      [breakpoints.tablet]: {
        display: "none"
      }
    }
    
  }],
}
);

export const mobileBoolServiceButton = style({
  width: "100%",
  maxWidth: 315,
  minHeight: 56,
  margin: "0 auto 10px auto",

  "@media": {
    [breakpoints.tablet]: {
      display: "none"
    }
  }
});
