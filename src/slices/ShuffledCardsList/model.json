{"id": "shuffled_cards_list", "type": "SharedSlice", "name": "ShuffledCardsList", "description": "ShuffledCardsList", "variations": [{"id": "default", "name": "<PERSON><PERSON><PERSON>", "docURL": "...", "version": "initial", "description": "<PERSON><PERSON><PERSON>", "imageUrl": "", "primary": {"title": {"type": "StructuredText", "config": {"label": "Title", "placeholder": "", "allowTargetBlank": true, "multi": "paragraph,preformatted,heading1,heading2,heading3,heading4,heading5,heading6,strong,em,hyperlink,image,embed,list-item,o-list-item,rtl"}}, "description": {"type": "StructuredText", "config": {"label": "Description", "placeholder": "", "allowTargetBlank": true, "multi": "paragraph,preformatted,heading1,heading2,heading3,heading4,heading5,heading6,strong,em,hyperlink,image,embed,list-item,o-list-item,rtl"}}, "moving_text_divider": {"type": "Image", "config": {"label": "Moving text divider", "constraint": {}, "thumbnails": []}}, "header_anchor_name": {"type": "Text", "config": {"label": "Header anchor name", "placeholder": ""}}}, "items": {"title": {"type": "Text", "config": {"label": "Title", "placeholder": ""}}, "description": {"type": "Text", "config": {"label": "Description", "placeholder": ""}}, "type": {"type": "Select", "config": {"label": "Type", "placeholder": "", "options": ["white", "<PERSON><PERSON><PERSON>", "green"], "default_value": "white"}}}}, {"id": "withoutMar<PERSON>e", "name": "Without marquee", "docURL": "...", "version": "initial", "description": "<PERSON><PERSON><PERSON>", "imageUrl": "", "primary": {"title": {"type": "StructuredText", "config": {"label": "Title", "placeholder": "", "allowTargetBlank": true, "multi": "paragraph,preformatted,heading1,heading2,heading3,heading4,heading5,heading6,strong,em,hyperlink,image,embed,list-item,o-list-item,rtl"}}, "header_anchor_name": {"type": "Text", "config": {"label": "Header anchor name", "placeholder": ""}}}, "items": {"type": {"type": "Select", "config": {"label": "Type", "placeholder": "", "options": ["white", "<PERSON><PERSON><PERSON>", "green"], "default_value": "white"}}, "title": {"type": "Text", "config": {"label": "Title", "placeholder": ""}}, "description": {"type": "Text", "config": {"label": "Description", "placeholder": ""}}}}]}