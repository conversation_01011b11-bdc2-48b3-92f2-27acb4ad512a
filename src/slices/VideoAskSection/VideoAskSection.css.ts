import { breakpoints } from "@/styles/constants.css";
import { mode } from "@/styles/functions.css";
import { theme } from "@/styles/themes.css";
import { keyframes, style } from "@vanilla-extract/css";

export const root = style({
  padding: "40px 20px",

  "@media": {
    [breakpoints.tablet]: {
      padding: "92px 48px",
    }
  }
});

export const container = style({
  scrollSnapAlign: "center !important",

  selectors: {
    [mode("residential")]: {
      backgroundColor: theme.colors.primary.castletonGreen,
    }
  },
});

export const title = style({
  fontFamily: theme.fonts.primary,
  marginBottom: 32,
  maxWidth: 240,

  "@media": {
    [breakpoints.tablet]: {
      maxWidth: "none",
      marginBottom: 40,
    },
  },

  selectors: {
    [mode("residential")]: {
      color: theme.colors.primary.softWhite,
    }
  },
});

export const description = style({
  maxWidth: 420,
  fontWeight: "500 !important",
  marginBottom: 32,

  "@media": {
    [breakpoints.tablet]: {
      marginBottom: 100,
    },
  },

  selectors: {
    [mode("residential")]: {
      color: theme.colors.primary.softWhite,
    }
  },
});

export const videoAskControl = style({

});

export const videoAskAvatar = style({
  position: "relative",
  overflow: "hidden",
  borderRadius: "50%",
  height: 128,
  aspectRatio: "1 / 1",
  border: `4px solid ${theme.colors.primary.asidGreen}`,
});

export const videoAskButton = style({
  overflow: "hidden",
  position: "relative",
  cursor: "pointer",
  color: theme.colors.primary.castletonGreen,
  border: "none",
  backgroundColor: "transparent",
  padding: 0,
  borderRadius: "50%",
  height: 128,
  fontSize: 128,
  aspectRatio: "1 / 1",
  transitionProperty: "color",
  transitionDuration: "200ms",

  ":hover": {
    color: theme.colors.primary.asidGreen,
  },

  selectors: {
    [mode("residential")]: {
      color: theme.colors.primary.asidGreen,
    }
  },
});

const rotate = keyframes({
  from: {
    transform: "rotateZ(0deg)",
  },
  to: {
    transform: "rotateZ(360deg)",  
  }
});

export const videoAskButtonText = style({
  animation: `${rotate} 5s infinite forwards`,
});

export const videoAskButtonIcon = style({
  fontSize: 40,
  position: "absolute",
  top: 0,
  bottom: 0,
  left: 0,
  right: 0,
  margin: "auto",
  transitionProperty: "transform",
  transitionDuration: "200ms",

  selectors: {
    [`${videoAskButton}:hover &`]: {
      transform: "scale(1.3)",
    }
  }
});

export const blocks = style({
  display: "flex",
  flexDirection: "column",
  gap: 16,
  marginBottom: 32,

  "@media": {
    [breakpoints.tablet]: {
      gap: 24,
    }
  }
});

export const block = style({
  cursor: "pointer",
  borderRadius: 16,
  backgroundColor: theme.colors.primary.softWhite,
  padding: 16,
  transition: "background-color 50ms, transform 350ms",

  ":active": {
    backgroundColor: theme.colors.primary.softWhitePressed,
    color: theme.colors.primary.castletonGreenPressed,
    transform: "scale(0.95)",
  },
});

export const blockBody = style({
  minHeight: 158,
  display: "flex",
  flexDirection: "column",
  gap: 24,
});

export const blockIsActive = style({
  //height: "max-content",
});


export const blockHeader = style({
  display: "flex",
  justifyContent: "space-between",
  marginBottom: "auto",
});

export const blockTitle = style({
  fontWeight: "500 !important",
  WebkitBoxOrient: "vertical",
  WebkitLineClamp: 3,
  display: "-webkit-box",
  overflow: "hidden",
  textOverflow: "ellipsis",
});

export const blockContentWrapper = style({
  display: "flex",
});

export const blockLink = style({
  marginTop: "auto",
  textDecoration: "underline",
});

export const blockContent = style({
  WebkitBoxOrient: "vertical",
  display: "-webkit-box",
  textOverflow: "ellipsis",
});

export const blockIndicator = style({
  fontSize: 20,
  marginLeft: 16,
  flex: "0 0",
  transform: "rotateZ(180deg)",
  transitionProperty: "transform",
  transitionDuration: "200ms",
});

export const blockIndicatorIsActive = style({
  transform: "rotateZ(0deg)"
});

const appear = keyframes({
  from: {
    transform: "translateX(-150%)",
  },
  to: {
    transform: "translateX(0%)",
  }
});


export const videoAsk = style({
  animation: `${appear} 700ms forwards`,
  position: "fixed",
  top: 0,
  bottom: 0,
  left: 0,
  right: 0,
  width: "calc(100% - 32px)",
  height: "calc(100% - 32px)",
  margin: 16,
  zIndex: 9999,
  borderRadius: 24,
  boxShadow: "rgba(0, 0, 0, 0.2) 0px 3px 5px -1px, rgba(0, 0, 0, 0.14) 0px 6px 10px 0px, rgba(0, 0, 0, 0.12) 0px 1px 18px 0px",
  backgroundColor: theme.colors.primary.softWhite,

  "@media": {
    [breakpoints.tablet]: {
      width: 360,
      height: 640,
      left: 20,
      bottom: 0,
      top: 0,
      margin: "auto 0",
    }
  }
});

export const videoAskFrame = style({
  height: "100%",
  width: "100%",
  borderRadius: "inherit",
  border: "none",
});

export const videoAskFrameCloseButton = style({
  position: "absolute",
  zIndex: 10,
  top: 0,
  right: 0,
  transform: "translateX(25%) translateY(-25%)",
  width: 40,
  aspectRatio: "1 / 1",
  borderRadius: "50%",
  backgroundColor: theme.colors.primary.castletonGreen,
  color: theme.colors.primary.softWhite,
  border: "none",
  display: "flex",
  alignItems: "center",
  justifyContent: "center",
  fontSize: 24,
  cursor: "pointer",
  boxShadow: "rgba(0, 0, 0, 0.2) 0px 3px 5px -1px, rgba(0, 0, 0, 0.14) 0px 6px 10px 0px, rgba(0, 0, 0, 0.12) 0px 1px 18px 0px",
  transitionProperty: "transform, opacity",
  transitionDuration: "180ms",

  ":hover": {
    transform: "translateX(25%) translateY(-25%) scale(1.1)",
  },

  "@media": {
    [breakpoints.tablet]: {
      transform: "translateX(50%) translateY(-50%)",

      ":hover": {
        transform: "translateX(50%) translateY(-50%) scale(1.1)",
      },
    }
  }

});