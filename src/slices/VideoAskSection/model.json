{"id": "video_ask_section", "type": "SharedSlice", "name": "VideoAskSection", "description": "VideoAskSection", "variations": [{"id": "default", "name": "<PERSON><PERSON><PERSON>", "docURL": "...", "version": "initial", "description": "<PERSON><PERSON><PERSON>", "imageUrl": "", "primary": {"title": {"type": "StructuredText", "config": {"label": "Title", "placeholder": "", "allowTargetBlank": true, "multi": "paragraph,preformatted,heading1,heading2,heading3,heading4,heading5,heading6,strong,em,hyperlink,image,embed,list-item,o-list-item,rtl"}}, "description": {"type": "StructuredText", "config": {"label": "Description", "placeholder": "", "allowTargetBlank": true, "multi": "paragraph,preformatted,heading1,heading2,heading3,heading4,heading5,heading6,strong,em,hyperlink,image,embed,list-item,o-list-item,rtl"}}, "videoask_link": {"type": "Text", "config": {"label": "VideoAsk Link", "placeholder": ""}}, "videoask_avatar": {"type": "Image", "config": {"label": "VideoAsk Preview Avatar", "constraint": {}, "thumbnails": []}}}, "items": {"benefit_title": {"type": "Text", "config": {"label": "Benefit Title", "placeholder": ""}}, "benefit_content": {"type": "StructuredText", "config": {"label": "Benefit Content", "placeholder": "", "allowTargetBlank": true, "multi": "paragraph,preformatted,heading1,heading2,heading3,heading4,heading5,heading6,strong,em,hyperlink,image,embed,list-item,o-list-item,rtl"}}}}]}