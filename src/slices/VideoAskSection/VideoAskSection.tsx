"use client";

import Container from "@/components/Container";
import { Content } from "@prismicio/client";
import { SliceComponentProps } from "@prismicio/react";
import * as styles from "./VideoAskSection.css";
import { gridSprinkle } from "@/styles/sprinkles.css";
import Typography from "@/components/Typography";
import classNames from "classnames";
import { PrismicRichText } from "node_modules/@prismicio/react/dist/react-server/PrismicRichText";
import ArrowIcon from "@/assets/icons/ArrowIcon";
import Accordion from "@/components/Accordion";
import { useState } from "react";
import { PrismicNextImage } from "@prismicio/next";
import LearnMorePlayIcon from "@/assets/icons/LearnMorePlayIcon";
import PlayIcon from "@/assets/icons/PlayIcon";
import MemoCloseIcon from "@/assets/icons/CloseIcon";

export type VideoAskSectionProps =
  SliceComponentProps<Content.VideoAskSectionSlice>;

const VideoAskSection = ({ slice }: VideoAskSectionProps): JSX.Element => {
  const [videoAskIsOpen, setVideoAskIsOpen] = useState<boolean>(false);
  const [openedBlocks, setOpenedBlocks] = useState<string[]>([]);

  const toggleOpenBlock = (blockIndex: string) => {
    const blockIsOpen = openedBlocks.includes(blockIndex);

    setOpenedBlocks(blockIsOpen ? openedBlocks.filter(block => block !== blockIndex) : [...openedBlocks, blockIndex]);
  };

  return (
    <Container
      className={styles.container}
      notFullHeight
    >
      <section
        className={styles.root}
        data-slice-type={slice.slice_type}
        data-slice-variation={slice.variation}
      >
        <div
          className={gridSprinkle({ type: "grid" })}
        >
          <div
            className={gridSprinkle({ type: "item", cols: { mobile: 10, tablet: 5 } })}
          >
            <Typography
              className={styles.title}
              variant="h2"
            >
              <PrismicRichText
                field={slice.primary.title}
              />
            </Typography>
            <Typography
              className={styles.description}
              variant="bodySmall"
            >
              <PrismicRichText
                field={slice.primary.description}
              />
            </Typography>
            <div
              className={classNames(styles.videoAskControl, gridSprinkle({ display: { mobile: "none", tablet: "flex" } }))}
            >
              <div
                className={styles.videoAskAvatar}
              >
                <PrismicNextImage
                  fill
                  objectFit="cover"
                  objectPosition="center"
                  field={slice.primary.videoask_avatar}
                />
              </div>
              <button
                className={styles.videoAskButton}
                title="Play VideoAsk"
                onClick={() => setVideoAskIsOpen(true)}
              >
                <LearnMorePlayIcon
                  className={styles.videoAskButtonText}
                />
                <PlayIcon
                  className={styles.videoAskButtonIcon}
                />
              </button>
            </div>
          </div>
          <div
            className={gridSprinkle({ type: "item", cols: { mobile: 10, tablet: 5 } })}
          >
            <div
              className={gridSprinkle({ type: "grid" })}
            >
              <div
                className={classNames(styles.blocks, gridSprinkle({ type: "item", cols: { mobile: 10, tablet: 5 } }))}
              >
                {slice.items.slice(0, slice.items.length / 2).map((item, index) => {
                  const blockIsOpen = openedBlocks.includes(String(item.benefit_title));

                  return (
                    <div
                      onClick={() => toggleOpenBlock(String(item.benefit_title))}
                      className={classNames(styles.block, {
                        [styles.blockIsActive]: blockIsOpen,
                      })}
                      key={index}
                    >
                      <Accordion
                        className={styles.blockBody}
                        isOpen={blockIsOpen}
                      >
                        <div
                          className={styles.blockHeader}
                        >
                          <Typography
                            className={styles.blockTitle}
                            variant="bodySmall"
                          >
                            {item.benefit_title}
                          </Typography>
                          <ArrowIcon
                            className={classNames(styles.blockIndicator, {
                              [styles.blockIndicatorIsActive]: blockIsOpen,
                            })}
                          />
                        </div>
                        <Typography
                          className={styles.blockContentWrapper}
                          variant="bodySmall"
                        >
                          <span
                            className={styles.blockContent}
                          >
                            <PrismicRichText
                              field={item.benefit_content}
                            />                    
                          </span>
                        </Typography>                  
                      </Accordion>
                    </div>
                  );
                })}
              </div>
              <div
                className={classNames(styles.blocks, gridSprinkle({ type: "item", cols: { mobile: 10, tablet: 5 } }))}
              >
                {slice.items.slice(slice.items.length / 2).map((item, index) => {
                  const blockIsOpen = openedBlocks.includes(String(item.benefit_title));

                  return (
                    <div
                      onClick={() => toggleOpenBlock(String(item.benefit_title))}
                      className={classNames(styles.block, {
                        [styles.blockIsActive]: blockIsOpen,
                      })}
                      key={index}
                    >
                      <Accordion
                        className={styles.blockBody}
                        isOpen={blockIsOpen}
                      >
                        <div
                          className={styles.blockHeader}
                        >
                          <Typography
                            className={styles.blockTitle}
                            variant="bodySmall"
                          >
                            {item.benefit_title}
                          </Typography>
                          <ArrowIcon
                            className={classNames(styles.blockIndicator, {
                              [styles.blockIndicatorIsActive]: blockIsOpen,
                            })}
                          />
                        </div>
                        <Typography
                          className={styles.blockContentWrapper}
                          variant="bodySmall"
                        >
                          <span
                            className={styles.blockContent}
                          >
                            <PrismicRichText
                              field={item.benefit_content}
                            />                    
                          </span>
                        </Typography>                  
                      </Accordion>
                    </div>
                  );
                })}
              </div>
            </div>
          </div>
          <div
            className={gridSprinkle({ type: "item", cols: { mobile: 10, tablet: 5 } })}
          >
            <div
              className={classNames(styles.videoAskControl, gridSprinkle({ display: { mobile: "flex", tablet: "none" }, justifyContent: "center" }))}
            >
              <div
                className={styles.videoAskAvatar}
              >
                <PrismicNextImage
                  fill
                  objectFit="cover"
                  objectPosition="center"
                  field={slice.primary.videoask_avatar}
                />
              </div>
              <button
                className={styles.videoAskButton}
                title="Play VideoAsk"
                onClick={() => setVideoAskIsOpen(true)}
              >
                <LearnMorePlayIcon
                  className={styles.videoAskButtonText}
                />
                <PlayIcon
                  className={styles.videoAskButtonIcon}
                />
              </button>
            </div>
          </div>
        </div>
        {videoAskIsOpen && slice.primary.videoask_link && (
          <div
            className={styles.videoAsk}
          >
            <iframe
              className={styles.videoAskFrame}
              src={slice.primary.videoask_link}
              allow="camera *; microphone *; autoplay *; encrypted-media *; fullscreen *; display-capture *;"
            />
            <button
              onClick={() => setVideoAskIsOpen(false)}
              title="Close VideoAsk"
              className={styles.videoAskFrameCloseButton}
            >
              <MemoCloseIcon />
            </button>
          </div>
        )}
      </section>
    </Container>
  );
};

export default VideoAskSection;
