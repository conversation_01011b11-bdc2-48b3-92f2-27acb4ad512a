[{"__TYPE__": "SharedSliceContent", "variation": "default", "primary": {"title": {"__TYPE__": "StructuredTextContent", "value": [{"type": "paragraph", "content": {"text": "Consectetur esse fugiat culpa qui est adipisicing et proident labore. Ut est id elit."}}]}, "description": {"__TYPE__": "StructuredTextContent", "value": [{"type": "paragraph", "content": {"text": "Consectetur et magna consectetur irure sint est commodo."}}]}, "videoask_link": {"__TYPE__": "<PERSON><PERSON><PERSON><PERSON>", "value": "captured", "type": "Text"}, "videoask_avatar": {"__TYPE__": "ImageContent", "origin": {"id": "main", "url": "https://images.unsplash.com/photo-1607582278043-57198ac8da43", "width": 4024, "height": 4024}, "url": "https://images.unsplash.com/photo-1607582278043-57198ac8da43", "width": 4024, "height": 4024, "edit": {"zoom": 1, "crop": {"x": 0, "y": 0}, "background": "transparent"}, "thumbnails": {}}}, "items": [{"__TYPE__": "GroupItemContent", "value": [["benefit_title", {"__TYPE__": "<PERSON><PERSON><PERSON><PERSON>", "value": "route", "type": "Text"}], ["benefit_content", {"__TYPE__": "StructuredTextContent", "value": [{"type": "paragraph", "content": {"text": "Eiusmod id reprehenderit consectetur eiusmod mollit enim ex esse do labore tempor tempor quis culpa deserunt. Sit mollit pariatur excepteur ea ut ad. Aute nisi dolore nostrud cupidatat."}}]}]]}]}]