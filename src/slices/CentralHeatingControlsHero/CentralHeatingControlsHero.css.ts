import { bodyMedium } from "@/components/Typography/Typography.css";
import { breakpoints } from "@/styles/constants.css";
import { theme } from "@/styles/themes.css";
import { style, styleVariants } from "@vanilla-extract/css";


export const root = style({
  position: "relative",
  padding: "16px 10px",
  background: theme.colors.primary.castletonGreen,

  "@media": {
    [breakpoints.tablet]: {
      padding: "25px 20px 20px",
    },
  },
});

export const mainWrapper = style({
  position: "relative",
  padding: "40px 20px 363px",
  borderRadius: 16,
  background: theme.colors.primary.asidGreen,

  "@media": {
    [breakpoints.tablet]: {
      padding: "107px 48px 56px",
    },
  },
});

export const mainContent = style({
  position: "relative",
  zIndex: 1,
});

export const title = style({
  marginBottom: 32,
  fontFamily: theme.fonts.primary,
  color: theme.colors.primary.castletonGreen,
  fontSize: "58px !important",
  lineHeight: "95%",
  letterSpacing: "-1.16px !important",

  "@media": {
    [breakpoints.tablet]: {
      marginBottom: 64,
      maxWidth: 845,
      fontSize: "104px !important",
      letterSpacing: "-2.08px !important",
    },
  },
});


const descriptionDefault = style([bodyMedium]);

const descriptionCallUs = style( {
  fontWeight: 400,
  fontSize: 18,
  "@media": {
    [breakpoints.tablet]: {
      fontSize: 26,
    }
  }
});

const descriptionBase = style({
  marginBottom: 40,
  fontFamily: theme.fonts.secondary,
  color: theme.colors.primary.castletonGreen,

  "@media": {
    [breakpoints.tablet]: {
      marginBottom: 98,
      maxWidth: 567,
    },
  },
});

export const description = styleVariants({
  default: [descriptionBase,descriptionDefault ],
  callUs: [descriptionBase, descriptionCallUs]
});

const buttonBase = style({
  width: "100%",
});

const buttonDefault = style({
  "@media": {
    [breakpoints.tablet]: {
      width: "inherit",
    },
  },

});

const buttonCallUs = style({
  "@media": {
    [breakpoints.tablet]: {
      width: 214
    }
  }
});

export const button = styleVariants({
  default: [buttonBase,buttonDefault ],
  callUs: [buttonBase, buttonCallUs]
});

const imageWrapperBase = style({
  position: "absolute",
  bottom: 0,
  right: 0,
  left: 0,
  top: "50%",
  overflow: "hidden",

  "@media": {
    [breakpoints.tablet]: {
      left: "auto",
      maxWidth: 625,
    },
  },
});

const imageWrapperDefault = style({
  top: "initial",
  minHeight: 353,
  display: "flex",

  "@media": {
    [breakpoints.tablet]: {
      // top: -40,
      zIndex: 9,
      minHeight: "inherit"
    },

    [breakpoints.desktop]: {
      top: "auto"
    }
  },
});
const imageWrapperCallUs = style({
  "@media": {
    [breakpoints.tablet]: {
      top: "-10%",
    },
  },
});

export const imageWrapper = styleVariants({
  default: [imageWrapperBase,imageWrapperDefault ],
  callUs: [imageWrapperBase, imageWrapperCallUs]
});

export const image = style({
  // display: "block",
  // maxWidth: "100%",
  width: "100%",
  height: "100%",
  objectFit: "contain",
  marginTop: "auto",
  
  "@media": {
    [breakpoints.tablet]: {
      // objectFit: "contain",
      // width: "auto",
      // height: "100%",
    },
  },
});


export const buttonInsideWrapper = style({
  display: "flex",
  gap: 8
});

export const container = style({
  padding: 0,
  backgroundColor: theme.colors.primary.castletonGreen
});