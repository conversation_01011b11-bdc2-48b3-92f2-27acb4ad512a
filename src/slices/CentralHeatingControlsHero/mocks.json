[{"__TYPE__": "SharedSliceContent", "variation": "default", "primary": {"title": {"__TYPE__": "StructuredTextContent", "value": [{"type": "paragraph", "content": {"text": "Central Heating Controls Upgrade", "spans": [{"type": "strong", "start": 25, "end": 32}]}, "direction": "ltr"}]}, "description": {"__TYPE__": "StructuredTextContent", "value": [{"type": "paragraph", "content": {"text": "Revitalize your central heating system with a thorough powerflush. Remove sludge and debris, restoring efficiency and prolonging the lifespan of your system.", "spans": []}, "direction": "ltr"}]}, "image": {"origin": {"id": "a19OVaa2rzA", "url": "https://images.unsplash.com/photo-1607990281513-2c110a25bd8c?crop=entropy&cs=srgb&fm=jpg&ixid=M3wzMzc0NjN8MHwxfHNlYXJjaHwyMnx8bWFufGVufDB8fHx8MTcxNzQyMzA5Nnww&ixlib=rb-4.0.3&q=85", "width": 2707, "height": 2477}, "url": "https://images.unsplash.com/photo-1607990281513-2c110a25bd8c?crop=entropy&cs=srgb&fm=jpg&ixid=M3wzMzc0NjN8MHwxfHNlYXJjaHwyMnx8bWFufGVufDB8fHx8MTcxNzQyMzA5Nnww&ixlib=rb-4.0.3&q=85", "width": 2707, "height": 2477, "edit": {"background": "transparent", "zoom": 1, "crop": {"x": 0, "y": 0}}, "credits": null, "alt": "man in black jacket smiling", "__TYPE__": "ImageContent", "thumbnails": {}}}, "items": []}, {"__TYPE__": "SharedSliceContent", "variation": "callUs", "primary": {"title": {"__TYPE__": "StructuredTextContent", "value": [{"type": "paragraph", "content": {"text": "Where trust\nflows like water", "spans": [{"type": "em", "start": 12, "end": 28}, {"type": "strong", "start": 12, "end": 28}]}, "direction": "ltr"}]}, "description": {"__TYPE__": "StructuredTextContent", "value": [{"type": "paragraph", "content": {"text": "Don’t panic. Call the experts!", "spans": []}, "direction": "ltr"}]}, "image": {"origin": {"id": "main", "url": "https://images.unsplash.com/photo-1494173853739-c21f58b16055", "width": 3277, "height": 4092}, "url": "https://images.unsplash.com/photo-1494173853739-c21f58b16055", "width": 3277, "height": 4092, "edit": {"zoom": 1, "crop": {"x": 0, "y": 0}, "background": "transparent"}, "credits": null, "alt": null, "__TYPE__": "ImageContent", "thumbnails": {}}}, "items": [{"__TYPE__": "GroupItemContent", "value": []}]}]