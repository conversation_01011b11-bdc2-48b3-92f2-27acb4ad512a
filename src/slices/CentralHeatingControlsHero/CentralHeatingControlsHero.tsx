import Container from "@/components/Container";
import { Content } from "@prismicio/client";
import { SliceComponentProps } from "@prismicio/react";
import * as styles from "./CentralHeatingControlsHero.css";
import Typography from "@/components/Typography";
import { PrismicRichText } from "node_modules/@prismicio/react/dist/react-server/PrismicRichText";
import Button from "@/components/Button";
import { PrismicNextImage } from "@prismicio/next";
import useStore from "@/hooks/useStore";
import BookServiceButton from "@/components/BookServiceButton/BookServiceButton";
import MemoPhoneIcon from "@/assets/icons/PhoneIcon";

export type CentralHeatingControlsHeroProps =
  SliceComponentProps<Content.CentralHeatingControlsHeroSlice>;

const CentralHeatingControlsHero = ({
  slice,
}: CentralHeatingControlsHeroProps): JSX.Element => {

  const isCallUsVariation = slice.variation === "callUs";

  return (
    
    <section
      data-slice-type={slice.slice_type}
      data-slice-variation={slice.variation}
      className={styles.root}
    >
      <Container
        removeBg
        className={styles.container}
        removeBorderRadius      
      >
        <div
          className={styles.mainWrapper}
        >
          <div
            className={styles.mainContent}
          >
            <Typography
              variant="h1"
              className={styles.title}
            >
              <PrismicRichText
                field={slice.primary.title}
              />
            </Typography>
            <div
              className={styles.description[slice.variation]}
            >
              <PrismicRichText
                field={slice.primary.description}
              />
            </div>
            <BookServiceButton
              color="secondary"
              isAnimated={!isCallUsVariation}
              className={styles.button[slice.variation]}
              alternateHover
            >{isCallUsVariation 
                ? (
                  <div
                    className={styles.buttonInsideWrapper}
                  >
                    <MemoPhoneIcon/>
                    {"Call us!"}
                  </div>
                ) 
                : "Book an Expert"}
            </BookServiceButton>
          </div>
          <div
            className={styles.imageWrapper[slice.variation]}
          >
            <PrismicNextImage
              className={styles.image}
              field={slice.primary.image}
            />
          </div>
        </div>
      </Container>
    </section>
  );
};

export default CentralHeatingControlsHero;
