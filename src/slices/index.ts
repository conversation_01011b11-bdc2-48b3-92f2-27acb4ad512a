// Code generated by Slice Machine. DO NOT EDIT.

import dynamic from "next/dynamic";

export const components = {
  advantages_section: dynamic(() => import("./AdvantagesSection")),
  appliance_section: dynamic(() => import("./ApplianceSection")),
  approach_section: dynamic(() => import("./ApproachSection")),
  arrival_time_section: dynamic(() => import("./ArrivalTimeSection")),
  banner_section: dynamic(() => import("./BannerSection")),
  become_mem_cards: dynamic(() => import("./BecomeMemCards")),
  become_member_section: dynamic(() => import("./BecomeMemberSection")),
  benefits_section: dynamic(() => import("./BenefitsSection")),
  block_with_movies: dynamic(() => import("./BlockWithMovies")),
  block_with_title_and_content: dynamic(
    () => import("./BlockWithTitleAndContent"),
  ),
  blog_section: dynamic(() => import("./BlogSection")),
  boiler_service_hero: dynamic(() => import("./BoilerServiceHero")),
  cards_with_img_on_hover: dynamic(() => import("./CardsWithImgOnHover")),
  central_heating_controls_hero: dynamic(
    () => import("./CentralHeatingControlsHero"),
  ),
  ch_s_upgrades_section: dynamic(() => import("./ChSUpgradesSection")),
  compare_features_section: dynamic(() => import("./CompareFeaturesSection")),
  contact_us: dynamic(() => import("./ContactUs")),
  divider: dynamic(() => import("./Divider")),
  document_with_navigation_and_accordions: dynamic(
    () => import("./DocumentWithNavigationAndAccordions"),
  ),
  document_with_navigation_section: dynamic(
    () => import("./DocumentWithNavigationSection"),
  ),
  faqs_section: dynamic(() => import("./FaqsSection")),
  gas_services_section: dynamic(() => import("./GasServicesSection")),
  heading: dynamic(() => import("./Heading")),
  heat_services_section: dynamic(() => import("./HeatServicesSection")),
  help_and_advice_section: dynamic(() => import("./HelpAndAdviceSection")),
  hero_with_list: dynamic(() => import("./HeroWithList")),
  horizontal_list_section: dynamic(() => import("./HorizontalListSection")),
  image: dynamic(() => import("./Image")),
  list_with_switch: dynamic(() => import("./ListWithSwitch")),
  locate_us_section: dynamic(() => import("./LocateUsSection")),
  london_plumbing_areas: dynamic(
    () => import("./LondonPlumbingAreasHeroSlice"),
  ),
  map_section: dynamic(() => import("./MapSection")),
  marquee_section: dynamic(() => import("./MarqueeSection")),
  paragraph: dynamic(() => import("./Paragraph")),
  partners_section: dynamic(() => import("./PartnersSection")),
  peace_plan_advantages: dynamic(() => import("./PeacePlanAdvantages")),
  peace_plan_cta: dynamic(() => import("./PeacePlanCta")),
  peace_plan_description_with_toggler: dynamic(
    () => import("./PeacePlanDescriptionWithToggler"),
  ),
  peace_plan_subscription_benefits: dynamic(
    () => import("./PeacePlanSubscriptionBenefits"),
  ),
  phone_animation_section: dynamic(() => import("./PhoneAnimationSection")),
  plans_section: dynamic(() => import("./PlansSection")),
  plumbing_services_section: dynamic(() => import("./PlumbingServicesSection")),
  preview_section: dynamic(() => import("./PreviewSection")),
  protecting_households: dynamic(() => import("./ProtectingHouseholds")),
  protecting_households_section: dynamic(
    () => import("./ProtectingHouseholdsSection"),
  ),
  recent_work: dynamic(() => import("./RecentWork")),
  refer_an_engineer_section: dynamic(() => import("./ReferAnEngineerSection")),
  refer_and_earn: dynamic(() => import("./ReferAndEarn")),
  referral_bonus_section: dynamic(() => import("./ReferralBonusSlice")),
  reviews_section: dynamic(() => import("./ReviewsSection")),
  services_section: dynamic(() => import("./ServicesSection")),
  shuffled_cards_list: dynamic(() => import("./ShuffledCardsList")),
  steps_section: dynamic(() => import("./StepsSection")),
  subscribtions_section: dynamic(() => import("./SubscribtionsSection")),
  text_and_image_section: dynamic(() => import("./TextAndImageSection")),
  top_brands_section: dynamic(() => import("./TopBrandsSection")),
  top_services: dynamic(() => import("./TopServices")),
  upgrage_your_ch_c: dynamic(() => import("./UpgrageYourChC")),
  video_ask_section: dynamic(() => import("./VideoAskSection")),
};
