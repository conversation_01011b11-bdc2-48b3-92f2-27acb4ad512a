"use client";
import Container from "@/components/Container";
import Typography from "@/components/Typography";
import { Content } from "@prismicio/client";
import { PrismicRichText, SliceComponentProps } from "@prismicio/react";

import * as styles from "./CardsWithImgOnHover.css";
import { gridSprinkle } from "@/styles/sprinkles.css";
import classNames from "classnames";
import NextArrowIcon from "@/assets/icons/NextArrowIcon";
import { PrismicNextImage } from "@prismicio/next";
import { ChevronIcon } from "@/assets/icons/ChevronIcon";
import IconButton from "@/components/IconButton";
import Divider from "@/components/Divider/Divider";
import { toCamelCase } from "@/utils/helpers";
import useScrollHorList from "@/hooks/useScrollHorList";
import useStore from "@/hooks/useStore";
import BookServiceButton from "@/components/BookServiceButton";
import BookIconButton from "./BookIconButton";

/**
 * Props for `CardsWithImgOnHover`.
 */
export type CardsWithImgOnHoverProps =
  SliceComponentProps<Content.CardsWithImgOnHoverSlice>;

/**
 * Component for "CardsWithImgOnHover" Slices.
 */
const CardsWithImgOnHover = ({
  slice,
}: CardsWithImgOnHoverProps): JSX.Element => {

  const {handleNext,handlePrev,setRef} = useScrollHorList();

  return (
    <Container>
      <section
        id={slice.primary.header_anchor_name ? toCamelCase(slice.primary.header_anchor_name) : ""}
        data-slice-type={slice.slice_type}
        data-slice-variation={slice.variation}
        className={gridSprinkle({type: "grid"})}
      >
        <Typography
          as={"h2"}
          variant="h2"
          fontFamily="primary"
          className={classNames(styles.title, gridSprinkle({ type: "item", cols: 10, justifySelf: "center" }))}
        ><PrismicRichText
            field={slice.primary.title}
        /></Typography>
        <Typography
          variant="subTitleMedium"
          className={classNames(styles.description, gridSprinkle({ type: "item", cols: 10, justifySelf: "center" }))}
        ><PrismicRichText
            field={slice.primary.description}
        /></Typography>
        <ul
          ref={setRef}
          className={classNames(styles.listWrapper, gridSprinkle({type: "item", cols: 10})) }
        >
          {
            slice.items.map(({description,card_img,title}, idx) => {

              return (<li
                key={idx}
                className={styles.listItem}
              >
                <Typography
                  className={styles.listItemTitle }
                  variant="subTitleMedium"
                ><PrismicRichText
                    field={title}
                /></Typography>
                <div
                  className={styles.listItemImgWrapper}
                >
                  <PrismicNextImage
                    className={styles.listItemImg}
                    field={card_img}
                  />
                </div>
                <Typography
                  variant="bodySmall"
                  className={styles.listItemDescription}
                ><PrismicRichText
                    field={description}
                /></Typography>
                <BookIconButton/>
              </li>
              );
            })
          }
        </ul>
        <div
          className={classNames(gridSprinkle({type: "item", cols: 10, justifySelf: "center", display: {tablet: "none"}}), styles.listButtonsWrapper)}
        >
          <IconButton
            onClick={handlePrev}
          > 
            <ChevronIcon
              turn={"left"}
            />
          </IconButton>
          <IconButton
            onClick={handleNext}
          > 
            <ChevronIcon
              turn={"right"}
            />
          </IconButton>
        </div>
        <Divider
          className={gridSprinkle({type: "item", cols: 10, display: {tablet: "none", mobile: "grid"}})}
        />
        <div
          className={classNames(gridSprinkle({type: "item", cols: 10, display: "grid"}), styles.contactUsWrapper)}
        >
          <Typography
            className={styles.footerTitle}
            variant="h4"
            fontFamily="primary"
          >
            <PrismicRichText
              field={slice.primary.footer_title}
            />
          </Typography>
          <div
            className={styles.footerButton}
          >
            <BookServiceButton>Contact Us</BookServiceButton>
          </div>
          <Typography
            className={styles.footerDescription}
            variant="subTitleMedium"
          >
            <PrismicRichText
              field={slice.primary.footer_description}
            />
          </Typography>
        </div>

      </section>
    </Container>
  );
};

export default CardsWithImgOnHover;
