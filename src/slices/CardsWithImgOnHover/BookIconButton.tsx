import useStore from "@/hooks/useStore";
import {listItemButton} from "./CardsWithImgOnHover.css";
import NextArrowIcon from "@/assets/icons/NextArrowIcon";
 
 
const BookIconButton = () => {
  const { landign } = useStore();
  return (
    <button
      type="button"
      className={listItemButton}
      onClick={() => landign.setBookingModalIsOpen(true)}
    ><NextArrowIcon/></button> 
  );
};

export default BookIconButton;

 