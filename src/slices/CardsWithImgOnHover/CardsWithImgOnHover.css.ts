import { breakpoints } from "@/styles/constants.css";
import { theme } from "@/styles/themes.css";
import { globalStyle, style, styleVariants } from "@vanilla-extract/css";


const ANIM_TIME = "400ms";

export const listWrapper = style({
  scrollSnapType: "x mandatory",
  display: "grid",
  gridAutoFlow: "column",
  overflowX: "auto",
  gap: 24,
  listStyle: "none",
  paddingBottom: 80,
  paddingTop: 40,
  paddingInlineStart: 0,
  marginLeft: 10,
  marginRight: 10,
  justifyContent: "start",
  "@media": {
    [breakpoints.tablet]: {
      overflowX: "visible",
      overflowY: "visible",
      gridAutoFlow: "row",
      paddingTop: 56,
      marginLeft: 0,
      marginRight: 0,
      paddingBottom: 0,
    }
  }
});

export const listItem = style({
  display: "grid",
  scrollSnapAlign: "center",
  gridTemplateColumns: "auto auto",
  borderRadius: 24,
  backgroundColor: theme.colors.primary.softWhite,
  columnGap: 16,
  width: "calc(100vw - 40px)",
  
  "@media": {
    [breakpoints.tablet]: {
      transition: `background-color ${ANIM_TIME} ease-in-out`,
      gridAutoFlow: "column",
      gridTemplateColumns: "initial",
      height: 168,
      width: "100%",
    }
  },
  selectors: {
    "&:hover": {
      backgroundColor: theme.colors.primary.asidGreen,
    }
  }

});



export const listItemButton = style({
  backgroundColor: theme.colors.primary.asidGreen,
  color: theme.colors.primary.castletonGreen,
  border: 0,
  padding: "12px 24px",
  borderRadius: 8,
  marginTop: 20,
  marginRight: 20,
  height: "fit-content",
  width: "fit-content",
  justifySelf: "end",
  transition: `all ${ANIM_TIME} ease-in-out`,
  cursor: "pointer",
  gridRowStart: 1,
  gridColumnEnd: 3,
  "@media": {
    [breakpoints.tablet]: {
      gridRowStart: "initial",
      gridColumnEnd: "initial",
      justifySelf: "none",
      marginTop: 40,
      marginRight: 40,
    }
  }


});

export const listItemImgWrapper = style({
  display: "grid",
  alignContent: "center",
  height: "auto",
  gridColumn: "span 2",
  justifyContent: "center",
  transform: "translateY(40px)",
  "@media": {
    [breakpoints.tablet]: {
      transform: "none",
      height: "inherit",
      gridColumn: "initial",
      
    }
  }
});
// const listItemImgWrapperLeft = style({
//   // transform: "rotate(-10deg)"
// });
// const listItemImgWrapperRight = style({
//   // transform: "rotate(10deg)"
// });

// export const listItemImgWrapper = styleVariants({
//   left: [listItemImgWrapperBase, listItemImgWrapperLeft],
//   right: [listItemImgWrapperBase, listItemImgWrapperRight],

// });

export const listItemImg = style({
  width: 160,
  height: 208,
  borderRadius: 16,
  objectFit: "cover",
  "@media": {
    [breakpoints.tablet]: {
      width: 197,
      height: 258,
      transition: `all ${ANIM_TIME} ease-in-out`,
      opacity: 0,
      scale: 0,
      transform: "none"
    }
  }
});

export const listItemTitle = style({
  marginTop: 20,
  marginLeft: 20,
  "@media": {
    [breakpoints.tablet]: {
      marginTop: 40,
      marginLeft: 40,
    }
  }
});

export const listItemDescription = style({
  marginTop: 16,
  marginLeft: 20,
  marginRight: 20,
  gridColumn: "span 2",
  gridRowStart: 2,
  "@media": {
    [breakpoints.tablet]: {
      gridColumn: "initial",
      gridRowStart: "initial",
      marginTop: 40,
      marginBottom: 40,
    }
  }
});

export const title = style({
  marginTop: 40,
  textAlign: "center",
  "@media": {
    [breakpoints.tablet]: {
      marginTop: 66,
    }
  }
});
export const description = style({
  marginTop: 32,
  textAlign: "center",
  fontSize: "16px !important",

  "@media": {
    [breakpoints.tablet]: {
      marginTop: 40,
      maxWidth: 585,
      fontSize: "24px !important",
    }
  }
});


globalStyle(`${listItem}:nth-child(odd):hover ${listItemImg}`,{
  "@media": {
    [breakpoints.tablet]: {
      transform: "rotate(-10deg)",
      scale: 1,
      opacity: 1
    }
  }
});


globalStyle(`${listItem}:nth-child(even):hover ${listItemImg}`,{
  "@media": {
    [breakpoints.tablet]: {
      transform: "rotate(10deg)",
      scale: 1,
      opacity: 1,
    }
  }
});

globalStyle(`${listItem}:nth-child(odd) ${listItemImg}`,{
  transform: "rotate(-10deg)",
  "@media": {
    [breakpoints.tablet]: {
      transform: "none",
    }
  }
});


globalStyle(`${listItem}:nth-child(even) ${listItemImg}`,{
  transform: "rotate(10deg)",
  "@media": {
    [breakpoints.tablet]: {
      transform: "none",
    }
  }
});

globalStyle(`${listItem}:hover ${listItemButton}`, {
  backgroundColor: theme.colors.primary.castletonGreen,
  color: theme.colors.primary.asidGreen,
  
});


export const listButtonsWrapper = style({
  display: "flex",
  gap: 16,
  marginBottom: 40,
  "@media": {
    [breakpoints.tablet]: {
      display: "none"
    }
  }

});

export const contactUsWrapper = style({
  display: "grid",
  rowGap: 32,
  marginTop: 50,
  marginBottom: 40,
  "@media": {
    [breakpoints.tablet]: {
      justifyContent: "space-between",
      gridTemplateColumns: "1fr auto",
      rowGap: 24,
      marginTop: 80,
    }
  }
  
    
});
export const footerTitle = style({
  textAlign: "center",
  "@media": {
    [breakpoints.tablet]: {
      textAlign: "start"
    }
  }
});

export const footerDescription = style({
  textAlign: "center",
  gridRow: 2,
  "@media": {
    [breakpoints.tablet]: {
      gridRow: "auto",
      textAlign: "start"
    }
  }
    
});
export const footerButton = style({
  marginTop: 18,
  alignSelf: "center",
  display: "grid",
  "@media": {
    [breakpoints.tablet]: {
      marginTop: 0,
      gridRow: "span 2",
    }
  }
    
});



