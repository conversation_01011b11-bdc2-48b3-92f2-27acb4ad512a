"use client";

import React, { useState, useEffect } from "react";
import { createPortal } from "react-dom";
import { PrismicText } from "@prismicio/react";
import { useSearchParams } from "next/navigation";
import useStore from "@/hooks/useStore";
import { boroughs } from "@/utils/constants";
import * as styles from "./HeroSection.css";
import CustomMap from "@components/ProtectingHouseholds/Map";
import Modal from "@components/Modal";
import Typography from "@components/Typography";
import Button from "@components/Button";
import { Content } from "@prismicio/client";

type InteractiveHeroProps = {
  slice: Content.LondonPlumbingAreasSlice;
};

type Borough = {
  name: string;
  lat: number;
  lng: number;
  postcode: string;
};

const normalizePostcode = (postcode: string) =>
  postcode.trim().toUpperCase().replace(/\s+/g, "");

const fetchPostcodeCoords = async (postcode: string) => {
  const normalized = postcode.trim().toUpperCase().replace(/\s+/g, "");

  try {
    let response = await fetch(`https://api.postcodes.io/postcodes/${normalized}`);
    let data = await response.json();

    if (response.ok && data.status === 200) {
      return {
        lat: data.result.latitude,
        lng: data.result.longitude,
      };
    }

    response = await fetch(`https://api.postcodes.io/outcodes/${normalized}`);
    data = await response.json();

    if (response.ok && data.status === 200) {
      return {
        lat: data.result.latitude,
        lng: data.result.longitude,
      };
    }

    return null;
  } catch (error) {
    console.error("Error fetching postcode coords:", error);
    return null;
  }
};


const getDistance = (lat1: number, lon1: number, lat2: number, lon2: number) => {
  const toRad = (value: number) => (value * Math.PI) / 180;
  const R = 6371;
  const dLat = toRad(lat2 - lat1);
  const dLon = toRad(lon2 - lon1);
  const a =
    Math.sin(dLat / 2) ** 2 +
    Math.cos(toRad(lat1)) * Math.cos(toRad(lat2)) * Math.sin(dLon / 2) ** 2;
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
  return R * c;
};

const InteractiveHero = ({ slice }: InteractiveHeroProps): JSX.Element => {
  const searchParams = useSearchParams();
  const [postcode, setPostcode] = useState("");
  const [selectedBorough, setSelectedBorough] = useState<Borough | null>(null);
  const [showModal, setShowModal] = useState(false);
  const [modalMessage, setModalMessage] = useState("");
  const [modalType, setModalType] = useState<"success" | "error">("success");
  const [isMounted, setIsMounted] = useState(false);
  const { landign } = useStore();

  const params = new URLSearchParams((searchParams as URLSearchParams | null) ?? {});
  const currentMode = params.get("mode") || "residential";

  useEffect(() => {
    setIsMounted(true);
    document.documentElement.setAttribute("data-mode", currentMode);
  }, [currentMode]);

  const handleSearch = async () => {
    const rawPostcode = postcode.trim();
    if (!rawPostcode) return;

    const normalizedPostcode = normalizePostcode(rawPostcode);
    const coords = await fetchPostcodeCoords(normalizedPostcode);
    if (!coords) {
      setModalMessage("Invalid postcode entered.");
      setModalType("error");
      setSelectedBorough(null);
      setShowModal(true);
      return;
    }

    let closestBorough: Borough | null = null;
    let minDistance = Infinity;

    for (const b of boroughs) {
      const dist = getDistance(coords.lat, coords.lng, b.lat, b.lng);
      if (dist < minDistance) {
        minDistance = dist;
        closestBorough = b;
      }
    }

    if (closestBorough && minDistance < 8) {
      setSelectedBorough(closestBorough);
      setModalMessage(`Great news. We cover ${closestBorough.name}!`);
      setModalType("success");
    } else {
      setSelectedBorough(null);
      setModalMessage("Sorry, it looks like you're out of our range.");
      setModalType("error");
    }

    setShowModal(true);
  };

  const handleCloseModal = () => setShowModal(false);

  const handleBookExpert = () => {
    handleCloseModal();
    landign.setBookingModalIsOpen(true);
  };

  return (
    <>
      <div className={styles.searchContainer}>
        <label className={styles.searchLabel}>
          <PrismicText field={slice.primary.filed_placeholder} />
        </label>
        <div className={styles.searchInputContainer}>
          <input
            type="text"
            placeholder="Enter your postcode"
            className={styles.searchInput}
            value={postcode}
            onChange={(e) => setPostcode(e.target.value)}
            onKeyDown={(e) => e.key === "Enter" && handleSearch()}
          />
          <button className={styles.searchButton} onClick={handleSearch}>
            →
          </button>
        </div>
      </div>

      <CustomMap selectedBorough={selectedBorough} />

      {isMounted &&
        createPortal(
          <Modal open={showModal} onClose={handleCloseModal}>
            <div className={styles.modalContainer}>
              <Typography variant="bodyMedium" className={styles.modalMessage}>
                {modalMessage}
              </Typography>
              <div className={styles.modalButtonsContainer}>
                {modalType === "success" && (
                  <Button onClick={handleBookExpert}>Book an expert</Button>
                )}
                <Button onClick={handleCloseModal}>
                  {modalType === "success" ? "Cancel" : "Close"}
                </Button>
              </div>
            </div>
          </Modal>,
          document.body
        )}
    </>
  );
};

export default InteractiveHero;
