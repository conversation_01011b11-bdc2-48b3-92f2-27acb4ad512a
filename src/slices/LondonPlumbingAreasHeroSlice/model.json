{"id": "london_plumbing_areas", "type": "SharedSlice", "name": "LondonPlumbingAreasHeroSlice", "description": "LondonPlumbingAreas", "variations": [{"id": "default", "name": "<PERSON><PERSON><PERSON>", "docURL": "...", "version": "initial", "description": "<PERSON><PERSON><PERSON>", "imageUrl": "", "primary": {"title": {"type": "StructuredText", "config": {"label": "title", "placeholder": "", "allowTargetBlank": true, "multi": "paragraph,preformatted,heading1,heading2,heading3,heading4,heading5,heading6,strong,em,hyperlink,image,embed,list-item,o-list-item,rtl"}}, "subtitle": {"type": "StructuredText", "config": {"label": "subtitle", "placeholder": "", "allowTargetBlank": true, "multi": "paragraph,preformatted,heading1,heading2,heading3,heading4,heading5,heading6,strong,em,hyperlink,image,embed,list-item,o-list-item,rtl"}}, "field_label": {"type": "Text", "config": {"label": "field_label", "placeholder": ""}}, "filed_placeholder": {"type": "StructuredText", "config": {"label": "filed_placeholder", "placeholder": "", "allowTargetBlank": true, "multi": "paragraph,preformatted,heading1,heading2,heading3,heading4,heading5,heading6,strong,em,hyperlink,image,embed,list-item,o-list-item,rtl"}}, "mobileTitle": {"type": "StructuredText", "config": {"label": "mobile-title", "placeholder": "", "allowTargetBlank": true, "multi": "paragraph,preformatted,heading1,heading2,heading3,heading4,heading5,heading6,strong,em,hyperlink,image,embed,list-item,o-list-item,rtl"}}, "mobileSubtitle": {"type": "StructuredText", "config": {"label": "mobileSubtitle", "placeholder": "", "allowTargetBlank": true, "multi": "paragraph,preformatted,heading1,heading2,heading3,heading4,heading5,heading6,strong,em,hyperlink,image,embed,list-item,o-list-item,rtl"}}, "mobileDescription": {"type": "StructuredText", "config": {"label": "mobileDescription", "placeholder": "", "allowTargetBlank": true, "multi": "paragraph,preformatted,heading1,heading2,heading3,heading4,heading5,heading6,strong,em,hyperlink,image,embed,list-item,o-list-item,rtl"}}}, "items": {"pargraph": {"type": "StructuredText", "config": {"label": "paragraph", "placeholder": "", "allowTargetBlank": true, "multi": "paragraph,preformatted,heading1,heading2,heading3,heading4,heading5,heading6,strong,em,hyperlink,image,embed,list-item,o-list-item,rtl"}}}}]}