"use client";
import { Content } from "@prismicio/client";
import { PrismicRichText, SliceComponentProps } from "@prismicio/react";
import * as styles from "./TopBrandsSection.css";
import Container from "@/components/Container";
import Typography from "@/components/Typography";
import { PrismicNextImage } from "@prismicio/next";
import Divider from "@/components/Divider/Divider";
import Button from "@/components/Button";
import classNames from "classnames";
import { toCamelCase } from "@/utils/helpers";
import useStore from "@/hooks/useStore";

/**
 * Props for `TopBrandsSection`.
 */
export type TopBrandsSectionProps =
  SliceComponentProps<Content.TopBrandsSectionSlice>;

/**
 * Component for "TopBrandsSection" Slices.
 */
const TopBrandsSection = ({ slice }: TopBrandsSectionProps): JSX.Element => {
  const { landign } = useStore();
  return (
    <Container>
      <section
        id={slice.primary.header_anchor_name ? toCamelCase(slice.primary.header_anchor_name) : ""}
        className={styles.root}
        data-slice-type={slice.slice_type}
        data-slice-variation={slice.variation}
      >
        <div
          className={styles.topSection}
        >
          <div
            className={styles.topLeftSection}
          >
            <Typography
              variant="h2"
              as={"h2"}
              isGreenItalic
              fontFamily="primary"
            ><PrismicRichText
                field={slice.primary.title}
            /></Typography>
            <Typography
              className={styles.description}
              variant="bodyMedium"
            ><PrismicRichText
                field={slice.primary.description}
            /></Typography>
          </div>
          <div
            className={styles.topRightSection}
          >
            <Typography
              variant="bodyMedium"
              className={styles.secondDescription}
            ><PrismicRichText
                field={slice.primary.second_description}
            /></Typography>
            <ul
              className={styles.listWrapper}
            >
              {slice.items.map(({logo}, idx, arr) => (
                <li
                  key={logo.id}
                  className={classNames(styles.listItem , {[styles.listItemMobileStretched]: !(idx % 2) && (idx === arr.length - 1)})}
                >
                  <div
                    className={styles.listItemImgWrapper}
                  >
                    <PrismicNextImage
                      className={styles.listItemImg}
                      field={logo}
                    />
                  </div>
                </li>
              ))}
            </ul>
          </div>
        </div>
        <Divider
          withOpacity
        />
        <div
          className={styles.botSection}
        >
          <Typography
            variant="h4"
            isGreenItalic
            fontFamily="primary"
          > 
            <PrismicRichText
              field={slice.primary.footer_text}
            />
          </Typography>
          <Button
            isAnimated
            onClick={() => landign.setBookingModalIsOpen(true)}
          >
              Contact us
          </Button>

        </div>
      </section>
    </Container>
  );
};

export default TopBrandsSection;
