import { breakpoints } from "@/styles/constants.css";
import { theme } from "@/styles/themes.css";
import { globalStyle, style } from "@vanilla-extract/css";


export const root = style({
  borderRadius: 16,
  backgroundColor: theme.colors.primary.castletonGreen,
  color: theme.colors.primary.softWhite,
  padding: "40px 20px",
  display: "grid",
  rowGap: 32,
  "@media": {
    [breakpoints.tablet]: {
      borderRadius: 24,
      padding: "104px 48px 48px 64px",
      rowGap: 54

    }
  }
});

export const topSection = style({
  display:"grid",
  rowGap: 32,
  "@media": {
    [breakpoints.tablet]: {
      gridTemplateColumns: "repeat(2, 1fr)",
      columnGap: 24
    }
  }
});

export const topRightSection = style({
  display: "grid",
  rowGap: 32
});
export const topLeftSection = style({
  display: "grid",
  rowGap: 32,
  alignContent: "space-between"
});

export const botSection = style({
  display:"grid",
  rowGap: 32,
  "@media": {
    [breakpoints.tablet]: {
      justifyContent: "space-between",
      gridAutoFlow: "column",
      alignItems: "center",
    }
  }
});

export const listWrapper = style({
  display: "grid",
  listStyle: "none",
  gridTemplateColumns: "repeat(2, 1fr)",
  padding: 0,
  gap: 12,
  "@media": {
    [breakpoints.tablet]: {
      gridTemplateColumns: "repeat(3, 1fr)",
      gap: 24
    }
  }
});


export const listItem = style({
  borderRadius: 16,
  border: `1px solid ${theme.colors.primary.asidGreen}`,
  padding: 4,
  display: "grid"
});

export const listItemMobileStretched = style({
  gridColumn: "span 2",
  "@media": {
    [breakpoints.tablet]: {
      gridColumn: "auto"
    }
  }
});



export const listItemImgWrapper = style({
  borderRadius: 12,
  backgroundColor: theme.colors.primary.softWhite,
  height: 120,
  padding: "20px 28px"
});
export const listItemImg = style({
  width: "100%",
  height: "100%",
  objectFit: "contain"
});

export const description = style({
  opacity: .8,

  "@media": {
    [breakpoints.tablet]: {
      maxWidth: 419,
    }
  }
});
export const secondDescription = style({
  gridRowStart: 2,
  "@media": {
    [breakpoints.tablet]: {
      gridRowStart: "auto",
    }
  }
});

globalStyle(`${secondDescription} a`, {
  textDecoration: "none",
  color: theme.colors.primary.asidGreen
});

globalStyle(`${secondDescription} em`, {
  color: theme.colors.primary.asidGreen
});