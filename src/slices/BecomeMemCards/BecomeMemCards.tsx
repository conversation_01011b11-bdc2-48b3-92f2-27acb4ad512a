import Container from "@/components/Container";
import Typography from "@/components/Typography";
import { gridSprinkle } from "@/styles/sprinkles.css";
import { Content, RichTextNodeType } from "@prismicio/client";
import { SliceComponentProps } from "@prismicio/react";
import * as styles from "./BecomeMemCards.css";
import classNames from "classnames";
import { PrismicRichText } from "node_modules/@prismicio/react/dist/react-server/PrismicRichText";
import ShrinksList from "@/components/ShrinksList/ShrinksList";
import MemoCheckMarkIcon from "@/assets/icons/CheckMarkIcon";
import Button from "@/components/Button";
import { PrismicNextImage } from "@prismicio/next";
import { toCamelCase } from "@/utils/helpers";
import BecomeMemButton from "@/components/BecomeMemButton";

/**
 * Props for `BecomeMemCards`.
 */
export type BecomeMemCardsProps =
  SliceComponentProps<Content.BecomeMemCardsSlice>;

/**
 * Component for "BecomeMemCards" Slices.
 */
const BecomeMemCards = ({ slice }: BecomeMemCardsProps): JSX.Element => {
  return (
    <Container>
      <section 
        id={slice.primary.header_anchor_name ? toCamelCase(slice.primary.header_anchor_name) : ""}
        data-slice-type={slice.slice_type}
        data-slice-variation={slice.variation}
        className={classNames(styles.root, gridSprinkle({type: "grid"}))}
      >
        <Typography
          className={classNames(gridSprinkle({type: "item", cols: 10, justifySelf: "center"}), styles.titleWrapper)}
          as='h2'
          variant="h2"
          fontFamily="primary"
        ><PrismicRichText
            field={slice.primary.title}
        /></Typography>
        <div
          className={classNames(styles.leftCard, gridSprinkle({type: "item", cols: {tablet: 5, mobile: 10}}))}
        >
          <div
            className={styles.hero}
          >
            <PrismicNextImage
              className={styles.heroImg}
              field={slice.primary.hero}
            />
          </div>
          <Typography
            variant="subTitleMedium"
            className={styles.cardDescription}
          >
            <PrismicRichText
              field={slice.primary.description}
            />
          </Typography>
          <Typography
            className={styles.cardTitle}
            variant="h2"
            fontFamily="primary"
          >
            <PrismicRichText
              
              field={slice.primary.card_title}
            />
          </Typography>
          <div
            className={styles.becomeMemButtonWrapper}
          >
            <BecomeMemButton
              color="secondary"
              alternateHover
              className={styles.becomeMemButton}
            />
          </div>
        </div>
        <ul
          className={classNames(styles.rightCard, gridSprinkle({type: "item", cols: {tablet: 5, mobile: 10}}))}
        >
          {slice.items.map(({ description,title },idx) => {
            return (
              <li
                key={`${idx}_become_mem_list_item`}
                className={styles.listItem}
              >
                <MemoCheckMarkIcon
                  className={styles.listItemIcon}
                />
                <Typography
                  variant="bodyMedium"
                  className={styles.bold}
                >
                  <PrismicRichText
                    field={title}
                  />
                </Typography>
                {description &&   <Typography
                  className={styles.listItemDesc}
                  variant="note"
                                  >
                  {description}
                </Typography> }
              </li>
            );
          })}
        </ul>
      </section>
    </Container>
  );
};

export default BecomeMemCards;