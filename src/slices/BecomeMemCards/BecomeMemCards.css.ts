import { breakpoints } from "@/styles/constants.css";
import { theme } from "@/styles/themes.css";
import { globalStyle, style } from "@vanilla-extract/css";

const cardContainer = style({
  borderRadius: 24,
  padding: 24,
  transition: "transform 0.4s ease-in-out",
  "@media": {
    [breakpoints.tablet]: {
      padding: "56px 48px",
      minHeight: 412
    }
  }
});

export const leftCard = style([cardContainer,{
  backgroundColor: theme.colors.primary.asidGreen,
  position: "relative",
  overflow: "hidden",
  display: "flex",
  flexDirection: "column",
  rowGap: 16,
  zIndex: 1,
  minHeight: 255,
  
  "@media": {
    [breakpoints.tablet]: {
      minHeight: "auto",
      transform: "rotate(-10deg)",
      transformOrigin: "400px -700px",
      rowGap: 24
    }
  }
}]);

export const rightCard = style([cardContainer,{
  backgroundColor: theme.colors.primary.softWhite,
  listStyle: "none",
  
  display: "grid",
  alignContent: "start",
  rowGap: 16,
  "@media": {
    [breakpoints.tablet]: {
      transform: "rotate(10deg)",
      transformOrigin: "200px -500px",
      rowGap: 24,
    }
  }

}]);

export const listItem = style({
  display: "grid",
  gridTemplateColumns: "auto 1fr",
  rowGap: 8,
  columnGap: 10
});

export const listItemDesc = style({
  gridColumn: 2
});

export const listItemIcon = style({
  fontSize: 20 
});

export const titleWrapper = style({
  marginBottom: 32,
  textAlign: "center",
  "@media": {
    [breakpoints.tablet]: {
      marginBottom: 100,
    }
  }
});

export const hero = style({
  bottom: 0,
  right: 0,
  position: "absolute",
  width: "50%",
  height: "100%"
});

export const becomeMemButtonWrapper = style({
  marginTop: "auto",
  marginLeft: -4,
  marginRight: -4,

  "@media": {
    [breakpoints.tablet]: {
      display: "grid",
      justifyItems: "start",
      marginLeft: "initial",
      marginRight: "initial",
    }
  }
});

export const becomeMemButton = style({
  minHeight: "56px !important",
});

export const cardDescription = style({
  zIndex: 1,
  marginRight: "15%",
  fontSize: "16px !important",
  maxWidth: 222,

  "@media": {
    [breakpoints.tablet]: {
      maxWidth: 376,
      fontSize: "24px !important"
    }
  }
});
export const cardTitle = style({
  zIndex: 1,
  fontSize: "36px !important",
  "@media": {
    [breakpoints.tablet]: {
      fontSize: "80px !important"
    }
  }
}); 


export const heroImg = style({
  objectFit: "cover",
  width: "100%",
  height: "100%"
});

export const root = style({
  marginTop: 40,
  "@media": {
    [breakpoints.tablet]: {
      marginTop: 80,
    }
  }
});

globalStyle(`${root}:hover ${leftCard}, ${root}:hover ${rightCard}`, {
  transform: "none"
});

export const bold = style({
  fontWeight: "bold",
});
