"use client";
import Container from "@/components/Container";
import { Content } from "@prismicio/client";
import { PrismicImage, PrismicRichText, SliceComponentProps } from "@prismicio/react";
import classNames from "classnames";
import * as styles from "./ChSUpgradesSection.css";
import Typography from "@/components/Typography";
import IconButton from "@/components/IconButton";
import useScrollHorList from "@/hooks/useScrollHorList";
import { ChevronIcon } from "@/assets/icons/ChevronIcon";
import Button from "@/components/Button";
import BookServiceButton from "@/components/BookServiceButton/BookServiceButton";

/**
 * Props for `ChSUpgradesSection`.
 */
export type ChSUpgradesSectionProps =
  SliceComponentProps<Content.ChSUpgradesSectionSlice>;

/**
 * Component for "ChSUpgradesSection" Slices.
 */
const ChSUpgradesSection = ({
  slice,
}: ChSUpgradesSectionProps): JSX.Element => {

  const { handleNext,handlePrev,setRef } = useScrollHorList();

  return (
    <Container>
      <section
        className={classNames(styles.root)}
        data-slice-type={slice.slice_type}
        data-slice-variation={slice.variation}
      >
     
        <Typography
          as={"h2"}
          variant="h2"
          fontFamily="primary"
          className={styles.title}
        >
          <PrismicRichText
            field={slice.primary.title}
          />
         
        </Typography>
        <div
          className={styles.controlsWrapper}
        >
          <IconButton
            onClick={handlePrev}
          >
            <ChevronIcon
              turn="left"
              className={styles.chevronIcon}
            />
          </IconButton >
          <IconButton
            onClick={handleNext}
          >
            <ChevronIcon
              turn="right"
              className={styles.chevronIcon}
            />
          </IconButton>
        </div>
        <ul
          ref={setRef}
          className={ classNames(styles.listWrapper)}
        >
          {slice.items.map(({ title, image,dark_green_bg },idx) => {
            return (
              <li
                key={`${idx}_upgrade_section_list_item`}
                className={styles.listItemWrapper}
              >
                <div
                  className={styles.listItem[dark_green_bg ? "darkGreen" : "default"]}
                >
                  <div
                    className={styles.listItemImgWrapper}
                  >
                    <PrismicImage
                      className={styles.listItemImg}
                      field={image}
                    />
                  </div>
                  <Typography
                    className={styles.listItemTitleWrapper}
                    variant="subTitleMedium"
                  >
                    <PrismicRichText
                      field={title}
                    />
                  </Typography>
                  <BookServiceButton
                    className={styles.button}
                  />
                </div>
              </li>
            );
          })}
        </ul>
    
      </section>
    </Container>
  );
};

export default ChSUpgradesSection;
