import { breakpoints } from "@/styles/constants.css";
import { theme } from "@/styles/themes.css";
import { style, styleVariants } from "@vanilla-extract/css";

export const root = style({
  display: "grid",
  gap: 32,
  marginTop: 30,
  marginBottom: 40,

  "@media": {
    [breakpoints.tablet]: {
      marginTop: 70,
      marginBottom: 70,
      gap: 72,
      gridTemplateColumns: "1fr auto"
    }
  }
});

export const controlsWrapper = style({
  gridRowStart: 3,
  display: "flex",
  columnGap: 8,
  gridColumn: "span 2",
  justifySelf: "center",

  "@media": {
    [breakpoints.tablet]: {
      alignSelf: "auto",
      gridRowStart: "auto",
      gridColumn: "auto",
      justifySelf: "auto",
    },

    [breakpoints.desktop]: {
      paddingRight: 49,
    }
  }
});

export const title = style({
  gridColumn: "span 2",
  padding: "0 20px",
  fontSize: "36px !important",

  "@media": {
    [breakpoints.tablet]: {
      gridColumn: "auto",
      fontSize: "80px !important",
    },
    [breakpoints.desktop]: {
      paddingLeft: 49,
    }
  },
});


export const listWrapper = style({
  display: "grid",
  gridAutoFlow: "column",
  columnGap: 44,
  listStyle: "none",
  padding: "0 20px",
  overflow: "auto",
  gridColumn: "span 2",
  scrollSnapType: "x mandatory",
  justifyContent: "start",
  // margin: "0 10px",
  "@media": {
    [breakpoints.tablet]: {
      margin: 0,
      columnGap: 24,
      justifyContent: "center",
    },
  }
});


export const listItemWrapper = style({
  height: 418,
  
  display: "grid",
  alignContent: "end",
 
});


const listItemBase = style({
  display: "grid",
  height: 286,
  borderRadius: 24,
  width: "calc(100vw - 20px)",
  "@media": {
    [breakpoints.tablet]: {
      width: "initial"
    },
  }
});

const listItemDefault = style({
  backgroundColor: theme.colors.primary.softWhite,
  color: theme.colors.primary.castletonGreen,
});

const listItemDarkGreenBG = style({
  backgroundColor: theme.colors.primary.castletonGreen,
  color: theme.colors.primary.softWhite,
});

export const listItem = styleVariants({
  default: [listItemBase, listItemDefault],
  darkGreen: [listItemBase, listItemDarkGreenBG],
});


export const listItemImgWrapper = style({
  scrollSnapAlign: "center",
  overflow: "hidden",
  marginTop: "-25%",
  height: 179,
  padding: "0 20px",
  "@media": {
    [breakpoints.tablet]: {
      padding: "0 24px",
  
    }
  },

});

export const listItemImg = style({
  width: "100%",
  height: "100%",
  objectFit: "cover",
  borderRadius: 16,
  "@media": {
    [breakpoints.tablet]: {
      borderRadius: 24,
  
    }
  },
});

export const listItemTitleWrapper = style({
  padding: "32px 32px 8px 32px",
  "@media": {
    [breakpoints.tablet]: {
      padding: "20px 20px 8px 20px",
  
    }
  },
});

export const button = style({
  margin: "0 32px 32px 32px",
  justifySelf: "center",
  alignSelf: "end",
  "@media": {
    [breakpoints.tablet]: {
      justifySelf: "start",
      margin: "0 20px 20px 20px",
    }
  },
});

export const chevronIcon = style({
  width: 24,
  height: 24,

  "@media": {
    [breakpoints.tablet]: {
      width: 26,
      height: 26,
    }
  },
});