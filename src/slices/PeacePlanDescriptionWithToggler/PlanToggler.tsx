"use client";
import Toggler from "@/components/Toggler";
import Typography from "@/components/Typography";
import classNames from "classnames";
import { useCallback, useMemo } from "react";
import { usePathname, useRouter, useSearchParams } from "next/navigation";
import * as styles from "./PlanToggler.css";

const PlanToggler = () => {
  const searchParams = useSearchParams();

  const pathname = usePathname();

  const router = useRouter();

  const onToggleChange = useCallback(
    () => {
      const params = new URLSearchParams((searchParams as URLSearchParams | null) ?? {});

      const currentMode = params.get("mode");

      if (currentMode === "homeowner" || !currentMode) params.set("mode", "landlord");
      else params.delete("mode");

      router.replace(`${pathname}?${params.toString()}`);
    },
    [router, searchParams],
  );

  const isLandlord = useMemo(() => searchParams?.get("mode") === "landlord", [searchParams]);
  return (
    <Toggler
      onChange={onToggleChange}
      size="big"
      variant="filledDark"
      wrapperClassname={styles.modeSwitcher}
      preffix={() => (
        <Typography
          className={classNames(styles.modeSwitcherLabel, { [styles.activeLabel]: !isLandlord })}
          variant="subTitleMedium"
        >
              Homeowner
        </Typography>
      )}
      suffix={() => (
        <Typography
          className={classNames(styles.modeSwitcherLabel, { [styles.activeLabel]: isLandlord })}
          variant="subTitleMedium"
        >
              Landlord
        </Typography>
      )}
      checked={isLandlord}
    />
  );
};

export default PlanToggler;