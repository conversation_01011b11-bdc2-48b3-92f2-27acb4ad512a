import Container from "@/components/Container";
import { Content } from "@prismicio/client";
import { SliceComponentProps } from "@prismicio/react";
import * as styles from "./PeacePlanDescriptionWithToggler.css";
import Typography from "@/components/Typography";
import { PrismicRichText } from "node_modules/@prismicio/react/dist/react-server/PrismicRichText";
import classNames from "classnames";
import { PrismicNextImage } from "@prismicio/next";
import PlanToggler from "./PlanToggler";
import { toCamelCase } from "@/utils/helpers";

export type PeacePlanDescriptionWithTogglerProps =
  SliceComponentProps<Content.PeacePlanDescriptionWithTogglerSlice>;

const PeacePlanDescriptionWithToggler = ({
  slice,
}: PeacePlanDescriptionWithTogglerProps): JSX.Element => {
  return (
    <Container
      removeBorderRadius
      className={styles.container}
    >
      <section
        data-slice-type={slice.slice_type}
        data-slice-variation={slice.variation}
        className={styles.root}
        id={slice.primary.header_anchor_name ? toCamelCase(slice.primary.header_anchor_name) : ""}
      >
        <div
          className={styles.textWrapper}
        >
          <Typography
            className={styles.title}
            variant="h2"
            as={"h1"}
          >
            <PrismicRichText
              field={slice.primary.title}
            />
          </Typography>
          <Typography
            className={styles.description}
            variant="bodyMedium"
          >
            <PrismicRichText
              field={slice.primary.description}
            />
          </Typography>
        </div>
        <PlanToggler />
        <PrismicNextImage
          field={slice.primary.left_image}
          className={classNames(styles.image, styles.leftImage)}
        />
        <PrismicNextImage
          field={slice.primary.right_image}
          className={classNames(styles.image, styles.rightImage)}
        />
      </section>
    </Container>
  );
};

export default PeacePlanDescriptionWithToggler;
