import { breakpoints } from "@/styles/constants.css";
import { theme } from "@/styles/themes.css";
import { style } from "@vanilla-extract/css";

export const container = style({
  paddingTop: 16,
  paddingBottom: 10,
  backgroundColor: theme.colors.primary.castletonGreen,

  "@media": {
    [breakpoints.tablet]: {
      paddingTop: 0,
      paddingBottom: 20,
    }
  },
});

export const root = style({
  position: "relative",
  padding: "40px 20px 297px",
  backgroundColor: theme.colors.primary.asidGreen,
  borderRadius: 16,
  overflow: "hidden",

  "@media": {
    [breakpoints.tablet]: {
      padding: "110px 20px 131px",
      borderRadius: 24,
    }
  },
});

export const textWrapper = style({
  position: "relative",
  display: "flex",
  alignItems: "center",
  flexDirection: "column",
  margin: "0 auto 32px",
  zIndex: 2,

  "@media": {
    [breakpoints.tablet]: {
      maxWidth: 628,
      margin: "0 auto 40px",
    }
  },
});

export const title = style({
  display: "flex",
  justifyContent: "center",
  textAlign: "center",
  fontFamily: theme.fonts.primary,
  fontSize: 58,
  marginBottom: 32,
  color: theme.colors.primary.castleton<PERSON>reen,

  "@media": {
    [breakpoints.tablet]: {
      fontSize: 104,
      marginBottom: 44,
    }
  },
});

export const description = style({
  textAlign: "center",
  fontFamily: theme.fonts.secondary,
  color: theme.colors.primary.castleton<PERSON>reen,

  "@media": {
    [breakpoints.tablet]: {
      maxWidth: 483,
    }
  },
});

export const image = style({
  position: "absolute",
  bottom: 0,

  "@media": {
    [breakpoints.tablet]: {
      width: "100%",
      height: "100%",
      objectFit: "cover",
      pointerEvents: "none",
    }
  },
});

export const leftImage = style({
  width: "228px",
  height: "auto",
  left: 0,
  zIndex: 2,

  "@media": {
    [breakpoints.tablet]: {
      width: 487,
      height: 625,
    }
  },
});

export const rightImage = style({
  width: "247px",
  height: "auto",
  right: 0,
  zIndex: 0,

  "@media": {
    [breakpoints.tablet]: {
      width: 545,
      height: 625,
    }
  }
});
