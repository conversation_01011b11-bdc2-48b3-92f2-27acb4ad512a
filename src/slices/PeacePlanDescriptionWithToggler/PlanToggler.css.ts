import { breakpoints } from "@/styles/constants.css";
import { theme } from "@/styles/themes.css";
import { style } from "@vanilla-extract/css";

export const modeSwitcher = style({
  position: "relative",
  display: "flex",
  alignItems: "center",
  justifyContent: "center",
  gap: 16,
  zIndex: 2,
});

export const modeSwitcherLabel = style({
  fontFamily: theme.fonts.secondary,
  fontSize: 16,
  color: theme.colors.primary.castletonGreen,
  opacity: 0.5,
  fontWeight: 500,
  lineHeight: "130%",

  "@media": {
    [breakpoints.tablet]: {
      fontSize: 24,
      lineHeight: "120%",
      letterSpacing: "-0.24px",
    }
  },
});

export const activeLabel = style({
  opacity: 1,
});