import { Content } from "@prismicio/client";
import { SliceComponentProps } from "@prismicio/react";
import ReferralBonusSection from "@/slices/ReferralBonusSlice/ReferralBonusSection";

/**
 * Props for `ReferralBonusSlice`.
 */
export type ReferralBonusSectionProps =
  SliceComponentProps<Content.ReferralBonusSectionSlice>;

/**
 * Component for "ReferralBonusSlice" Slices.
 */
const ReferralBonusSlice = ({
  slice,
}: ReferralBonusSectionProps): JSX.Element => {
  return (
    <section
      data-slice-type={slice.slice_type}
      data-slice-variation={slice.variation}
    >
      <ReferralBonusSection slice={slice} />
    </section>
  );
};

export default ReferralBonusSlice;
