"use client";
import React from 'react';
import * as styles from './ReferralBonusSection.css';
import Typography from '@components/Typography';
import Button from '@components/Button';
import Image from 'next/image';
import Container from "@components/Container";
import engineer from './engineer.png'
import {PrismicText} from "@prismicio/react";

const ReferralBonusSection = ({ slice }: any) => {
  console.log('slice', slice)
  const handleCopyLink = () => {
    navigator.clipboard.writeText('your-referral-link.com');
    alert('Link copied to clipboard!');
  };

  return (
    <Container >
      <section className={styles.root}>
        <div className={styles.leftPanel}>
          <div className={styles.leftTextWrapper}>
            <Typography variant="h2" className={styles.leftTitle}>
              <span className={styles.sum}>
                {slice.primary.leftbartitlepart_1.text}
              </span>
              <p>{slice.primary.leftbartitlepart_2.text}</p>
            </Typography>
            <Typography className={styles.leftDescription}>
              {slice.primary.description.text}
            </Typography>
          </div>
          <div className={styles.leftImageWrapper}>
            {/*<Image src={engineer.src} alt="Engineer" width={400} height={400} className={styles.engineerImage} />*/}
          </div>
        </div>

        <div className={styles.rightPanel}>
          <div className={styles.breakdownBox}>
            <Typography className={styles.breakdownText}>
              Refer a skilled plumbing or heating engineer to Pleasant Plumbers — and earn up to £1,000 when they join and get to work.
            </Typography>
            <Typography variant="h4" className={styles.breakdownTitle}>
              Here’s the Breakdown:
            </Typography>
            <ul className={styles.breakdownList}>
              <li className={styles.li}>
                <span className={styles.checkIcon}>✔</span>
                <div>
                  <div className={styles.price}>£250</div>
                  <div>when they complete 10 hours</div>
                </div>
              </li>
              <li className={styles.li}>
                <span className={styles.checkIcon}>✔</span>
                <div>
                  <div className={styles.price}>£250</div>
                  <div>when they complete 25 hours</div>
                </div>
              </li>
              <li className={styles.li}>
                <span className={styles.checkIcon}>✔</span>
                <div>
                  <div className={styles.price}>£500</div>
                  <div>when they complete 50 hours</div>
                </div>
              </li>
            </ul>
            <div className={styles.breakdownInfo}>
              <Typography className={styles.breakdownFooter}>
                That’s £1,000 paid directly to your bank in simple, automated stages.
              </Typography>
              <Typography className={styles.breakdownFooter}>
                No admin. No chasing. No limit to how many you can refer.
              </Typography>
            </div>
          </div>

          <div className={styles.shareBox}>
            <Typography variant="h4" className={styles.shareTitle}>
              Share the Sign-Up Link with a Friend
            </Typography>
            <Typography className={styles.shareDescription}>
              Send them the link below so they can apply and enter your name in the referral box. It’s the easiest £1,000 you’ll ever make.
            </Typography>
            <Button className={styles.button} onClick={handleCopyLink} variant='filled'>
              Copy Link
            </Button>
          </div>
        </div>
      </section>
    </Container>
  );
};

export default ReferralBonusSection;
