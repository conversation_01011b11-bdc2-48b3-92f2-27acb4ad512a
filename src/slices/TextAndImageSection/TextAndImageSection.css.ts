import { breakpoints } from "@/styles/constants.css";
import { mode } from "@/styles/functions.css";
import { theme } from "@/styles/themes.css";
import { style } from "@vanilla-extract/css";

export const container = style({
  selectors: {
    [mode("residential")]: {
      background: theme.colors.primary.castletonGreen,
    }
  },
});

export const root = style({
  padding: "40px 20px 20px",
  rowGap: 30,
  background: theme.colors.primary.castletonGreen,
  borderRadius: 24,

  "@media": {
    [breakpoints.tablet]: {
      padding: 48,
    }
  },

  selectors: {
    [mode("residential")]: {
      background: theme.colors.primary.ivory,
    }
  },
});

export const isReversed = style(
  {
    "@media": {
      [breakpoints.tablet]: {
        direction: "rtl",
      }
    },
  }
);

export const title = style({
  fontFamily: theme.fonts.primary,
  marginBottom: "24px",
  color: theme.colors.primary.ivory,

  "@media": {
    [breakpoints.tablet]: {
      marginBottom: "48px",
    }
  },

  selectors: {
    [mode("residential")]: {
      color: theme.colors.primary.castletonGreen,
    }
  },
});

export const description = style({
  marginBottom: "24px",
  color: theme.colors.primary.ivory,
  opacity: 1,
  "@media": {
    [breakpoints.tablet]: {
      marginBottom: "auto",
      maxWidth: 420,
      opacity: 0.8,
    }
  },

  selectors: {
    [mode("residential")]: {
      color: theme.colors.primary.castletonGreen,
    }
  },
});

export const info = style(
  {
    padding: "0 10px",
    direction: "ltr",
    display: "flex",
    flexDirection: "column",
    rowGap: 12,

    selectors: {
      [`${isReversed} &`]: {
        "@media": {
          [breakpoints.tablet]: {
            paddingLeft: 40,
          }        
        }
      }
    },

    "@media": {
      [breakpoints.tablet]: {
        padding: "32px 0 0",
        alignItems: "flex-start",
      }
    },
  }
);

export const imageWrapper = style(
  {
    overflow: "hidden",
    borderRadius: 16,
    position: "relative",
    aspectRatio: "1.12 / 1",

    "@media": {
      [breakpoints.tablet]: {
        borderRadius: 24,
      }
    },
  }
);