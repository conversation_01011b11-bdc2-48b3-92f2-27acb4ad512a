{"id": "text_and_image_section", "type": "SharedSlice", "name": "TextAndImageSection", "description": "TextAndImageSection", "variations": [{"id": "default", "name": "<PERSON><PERSON><PERSON>", "docURL": "...", "version": "initial", "description": "<PERSON><PERSON><PERSON>", "imageUrl": "", "primary": {"mirrored": {"type": "Boolean", "config": {"label": "Mirrored", "default_value": false, "placeholder_true": "true", "placeholder_false": "false"}}, "title": {"type": "StructuredText", "config": {"label": "Title", "placeholder": "", "allowTargetBlank": true, "multi": "paragraph,preformatted,heading1,heading2,heading3,heading4,heading5,heading6,strong,em,hyperlink,image,embed,list-item,o-list-item,rtl"}}, "description": {"type": "StructuredText", "config": {"label": "Description", "placeholder": "", "allowTargetBlank": true, "multi": "paragraph,preformatted,heading1,heading2,heading3,heading4,heading5,heading6,strong,em,hyperlink,image,embed,list-item,o-list-item,rtl"}}, "image": {"type": "Image", "config": {"label": "Image", "constraint": {}, "thumbnails": []}}, "link_text": {"type": "Text", "config": {"label": "Link Text", "placeholder": ""}}, "link_url": {"type": "Link", "config": {"label": "<PERSON>", "placeholder": "", "select": null}}}, "items": {}}]}