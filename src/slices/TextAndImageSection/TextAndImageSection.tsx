import Container from "@/components/Container";
import { Content } from "@prismicio/client";
import { SliceComponentProps } from "@prismicio/react";
import * as styles from "./TextAndImageSection.css";
import classNames from "classnames";
import { gridSprinkle } from "@/styles/sprinkles.css";
import { PrismicNextImage, PrismicNextLink } from "@prismicio/next";
import Typography from "@/components/Typography";
import { PrismicRichText } from "node_modules/@prismicio/react/dist/react-server/PrismicRichText";
import Button from "@/components/Button";

export type TextAndImageSectionProps =
  SliceComponentProps<Content.TextAndImageSectionSlice>;

const TextAndImageSection = (
  {
    slice,
  }: TextAndImageSectionProps
): JSX.Element => {
  return (
    <Container
      className={styles.container}
    >
      <section
        className={classNames(
          styles.root, gridSprinkle(
            { type: "grid" }
          ),
          {
            [styles.isReversed]: slice.primary.mirrored,
          }
        )}
        data-slice-type={slice.slice_type}
        data-slice-variation={slice.variation}
      >
        <div
          className={classNames(
            styles.info, gridSprinkle(
              { type: "item", cols: { mobile: 10, tablet: 5 } }
            )
          )}
        >
          <Typography
            className={styles.title}
            as={"h1"}
            variant={slice.primary.mirrored ? "h3" : "h2"}
          >
            <PrismicRichText
              field={slice.primary.title}
            />
          </Typography>
          <Typography
            className={styles.description}
          >
            <PrismicRichText
              field={slice.primary.description}
            />
          </Typography>
          <Button
            as={PrismicNextLink}
            field={slice.primary.link_url}
            isAnimated
          >
            {slice.primary.link_text}
          </Button>            
        </div>
        <div
          className={classNames(
            styles.imageWrapper, gridSprinkle(
              { type: "item", cols: { mobile: 10, tablet: 5 } }
            )
          )}
        >
          <PrismicNextImage
            field={slice.primary.image}
            fill
            objectFit="cover"
            objectPosition="center"
          />
        </div>
      </section>
    </Container>
  );
};

export default TextAndImageSection;
