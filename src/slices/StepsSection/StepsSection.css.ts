import { breakpoints } from "@/styles/constants.css";
import { mode } from "@/styles/functions.css";
import { theme } from "@/styles/themes.css";
import { style } from "@vanilla-extract/css";

export const container = style({
  selectors: {
    [mode("residential")]: {
      background: theme.colors.primary.castletonGreen,
    }
  },
});

export const root = style(
  {
    padding: "40px 20px",
    borderRadius: 16,

    "@media": {
      [breakpoints.tablet]: {
        padding: "72px 48px",
        borderRadius: 24,
      }
    },
  }
);

export const title = style({
  fontFamily: theme.fonts.primary,
  marginBottom: "24px",
  textAlign: "unset",
  "@media": {
    [breakpoints.tablet]: {
      marginBottom: "68px",
      textAlign: "center",
    }
  },

  selectors: {
    [mode("residential")]: {
      color: theme.colors.primary.softWhite,
    }
  },
});

export const description = style(
  {
    marginBottom: "32px",
    opacity: 0.8,
  }
);

export const steps = style(
  {
    display: "flex",
    flexDirection: "column",
    flexWrap: "wrap",
    gap: 24,

    "@media": {
      [breakpoints.tablet]: {
        flexDirection: "row",
      }
    },
  }
);

export const step = style(
  {
    flex: "1 1",
    backgroundColor: theme.colors.primary.softWhite,
    color: theme.colors.primary.castletonGreen,
    borderRadius: 16,
    display: "flex",
    flexDirection: "column",
    gap: 16,
    padding: 40,

    "@media": {
      [breakpoints.tablet]: {
        gap: 32,
        padding: 56,
      }
    },
  }
);

export const stepTitle = style(
  {
    fontFamily: theme.fonts.primary,
  }
);

export const button = style({
  display: "flex",
  marginTop: 32,
  flexDirection: "column",
  "@media": {
    [breakpoints.tablet]: {
      justifyContent: "center",
      flexDirection: "row",
      marginTop: 56
    }
  },
});