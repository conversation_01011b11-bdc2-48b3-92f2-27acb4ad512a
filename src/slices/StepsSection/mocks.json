[{"__TYPE__": "SharedSliceContent", "variation": "default", "primary": {"title": {"__TYPE__": "StructuredTextContent", "value": [{"type": "paragraph", "content": {"text": "Ut consequat amet non duis ullamco exercitation enim minim irure sit voluptate ipsum. In exercitation qui commodo excepteur nostrud fugiat."}}]}, "description": {"__TYPE__": "StructuredTextContent", "value": [{"type": "paragraph", "content": {"text": "Consequat exercitation cupidatat amet cillum sunt ullamco veniam enim officia occaecat consectetur laboris. Aliquip ullamco aute dolor."}}]}}, "items": [{"__TYPE__": "GroupItemContent", "value": [["step_title", {"__TYPE__": "StructuredTextContent", "value": [{"type": "paragraph", "content": {"text": "Pariatur sint quis pariatur velit aute eu nisi do labore aute anim adipisicing. Qui ipsum reprehenderit ad ipsum."}}]}], ["step_content", {"__TYPE__": "StructuredTextContent", "value": [{"type": "paragraph", "content": {"text": "Occaecat cillum incididunt ex velit in deserunt ut. Occaecat voluptate excepteur id laborum amet amet sit aliqua eu ex eu. Exercitation sint irure nisi tempor do aute proident incididunt do duis ut non excepteur amet."}}]}]]}]}]