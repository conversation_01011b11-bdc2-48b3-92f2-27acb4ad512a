import { Content } from "@prismicio/client";
import { SliceComponentProps } from "@prismicio/react";
import * as styles from "./StepsSection.css";
import Container from "@/components/Container";
import Typography from "@/components/Typography";
import { PrismicRichText } from "node_modules/@prismicio/react/dist/react-server/PrismicRichText";
import classNames from "classnames";
import { gridSprinkle } from "@/styles/sprinkles.css";
import Button from "@/components/Button";
import Link from "next/link";

export type StepsSectionProps = SliceComponentProps<Content.StepsSectionSlice>;

const StepsSection = (
  { slice }: StepsSectionProps
): JSX.Element => {
  return (
    <Container
      className={styles.container}
    >
      <section
        className={styles.root}
        data-slice-type={slice.slice_type}
        data-slice-variation={slice.variation}
      >
        <div
          className={gridSprinkle(
            { type: "grid" }
          )}
        >
          <Typography
            variant="h3"
            as={"h2"}
            className={classNames(
              styles.title, gridSprinkle(
                { type: "item", cols: 10 }
              )
            )}
          >
            <PrismicRichText
              field={slice.primary.title}
            />
          </Typography>
        </div>
        <div
          className={styles.steps}
        >
          {slice.items.map(
            (
              item, index
            ) => (
              <div
                className={styles.step}
                key={index}
              >
                <Typography
                  as={"h3"}
                  variant="h4"
                  className={styles.stepTitle}
                >
                  <PrismicRichText
                    field={item.step_title}
                  />
                </Typography>
                <Typography>
                  <PrismicRichText
                    field={item.step_content}
                  />
                </Typography>
              </div>
            )
          )}
        </div>
        <div
          className={classNames(styles.button)}
        >
          <Button
            as={Link}
            href="/profile"
            isAnimated
          >
          Start making money
          </Button>
        </div>
      </section>
    </Container>
  );
};

export default StepsSection;
