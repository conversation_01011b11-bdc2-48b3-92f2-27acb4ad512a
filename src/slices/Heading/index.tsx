import Typography from "@/components/Typography";
import { Content } from "@prismicio/client";
import { SliceComponentProps } from "@prismicio/react";
import * as styles from "./Heading.css";
import { parseStringToValidId } from "@/utils/helpers";

/**
 * Props for `Heading`.
 */
export type HeadingProps = SliceComponentProps<Content.HeadingSlice>;

/**
 * Component for "Heading" Slices.
 */
const Heading = ({ slice }: HeadingProps): JSX.Element => {
  return (
    <Typography
      as="h2"
      variant="subTitleMedium"
      className={styles.root}
      id={parseStringToValidId(String(slice.primary.content), false)}
      data-type="heading"
      data-type-value={String(slice.primary.content)}
    >
      {slice.primary.content}
    </Typography>
  );
};

export default Heading;
