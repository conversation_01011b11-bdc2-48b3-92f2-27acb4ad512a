[{"__TYPE__": "SharedSliceContent", "variation": "default", "primary": {"price_per": {"__TYPE__": "<PERSON><PERSON><PERSON><PERSON>", "type": "Text", "value": "£20.49/Month"}, "title": {"__TYPE__": "StructuredTextContent", "value": [{"type": "paragraph", "content": {"text": "Save 5% with annual subscription\nfor Peace Plan!", "spans": [{"type": "strong", "start": 0, "end": 7}, {"type": "strong", "start": 37, "end": 48}]}, "direction": "ltr"}]}, "total_price": {"__TYPE__": "<PERSON><PERSON><PERSON><PERSON>", "type": "Text", "value": "£ 234"}, "header_anchor_name": {"__TYPE__": "<PERSON><PERSON><PERSON><PERSON>", "value": "national", "type": "Text"}}, "items": [{"__TYPE__": "GroupItemContent", "value": [["list_item", {"__TYPE__": "<PERSON><PERSON><PERSON><PERSON>", "type": "Text", "value": "Repairs to plumbing system in home our outbuildings"}], ["included", {"__TYPE__": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": true}]]}, {"__TYPE__": "GroupItemContent", "value": [["included", {"__TYPE__": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": true}], ["list_item", {"__TYPE__": "<PERSON><PERSON><PERSON><PERSON>", "type": "Text", "value": "Water supply pipe within boundaries of property"}]]}, {"__TYPE__": "GroupItemContent", "value": [["list_item", {"__TYPE__": "<PERSON><PERSON><PERSON><PERSON>", "type": "Text", "value": "Burst Pipes"}], ["included", {"__TYPE__": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": true}]]}, {"__TYPE__": "GroupItemContent", "value": [["list_item", {"__TYPE__": "<PERSON><PERSON><PERSON><PERSON>", "type": "Text", "value": "Internal/External blockages"}], ["included", {"__TYPE__": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": true}]]}, {"__TYPE__": "GroupItemContent", "value": [["list_item", {"__TYPE__": "<PERSON><PERSON><PERSON><PERSON>", "type": "Text", "value": "Shower, bath and basin waste pipes"}], ["included", {"__TYPE__": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": true}]]}, {"__TYPE__": "GroupItemContent", "value": [["list_item", {"__TYPE__": "<PERSON><PERSON><PERSON><PERSON>", "type": "Text", "value": "Blocked toilets"}], ["included", {"__TYPE__": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": true}]]}, {"__TYPE__": "GroupItemContent", "value": [["list_item", {"__TYPE__": "<PERSON><PERSON><PERSON><PERSON>", "type": "Text", "value": "Radiators & Pipes"}], ["included", {"__TYPE__": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": true}]]}, {"__TYPE__": "GroupItemContent", "value": [["list_item", {"__TYPE__": "<PERSON><PERSON><PERSON><PERSON>", "type": "Text", "value": "Heating system controls, thermostat and programmer"}], ["included", {"__TYPE__": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": true}]]}, {"__TYPE__": "GroupItemContent", "value": [["list_item", {"__TYPE__": "<PERSON><PERSON><PERSON><PERSON>", "type": "Text", "value": "Radiator valves"}], ["included", {"__TYPE__": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": true}]]}, {"__TYPE__": "GroupItemContent", "value": [["list_item", {"__TYPE__": "<PERSON><PERSON><PERSON><PERSON>", "type": "Text", "value": "Boiler Components"}], ["included", {"__TYPE__": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": true}]]}, {"__TYPE__": "GroupItemContent", "value": [["list_item", {"__TYPE__": "<PERSON><PERSON><PERSON><PERSON>", "type": "Text", "value": "Gas Supply Pip"}], ["included", {"__TYPE__": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": true}]]}, {"__TYPE__": "GroupItemContent", "value": [["list_item", {"__TYPE__": "<PERSON><PERSON><PERSON><PERSON>", "type": "Text", "value": "Gas Boiler Flue"}], ["included", {"__TYPE__": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": true}]]}, {"__TYPE__": "GroupItemContent", "value": [["list_item", {"__TYPE__": "<PERSON><PERSON><PERSON><PERSON>", "type": "Text", "value": "Boiler Inspection + Service"}], ["included", {"__TYPE__": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": true}]]}, {"__TYPE__": "GroupItemContent", "value": [["list_item", {"__TYPE__": "<PERSON><PERSON><PERSON><PERSON>", "type": "Text", "value": "Claims within first 14 days of taking cover out"}]]}, {"__TYPE__": "GroupItemContent", "value": [["list_item", {"__TYPE__": "<PERSON><PERSON><PERSON><PERSON>", "type": "Text", "value": "Shower parts, shower pumps, sanitary ware and sealants"}]]}, {"__TYPE__": "GroupItemContent", "value": [["list_item", {"__TYPE__": "<PERSON><PERSON><PERSON><PERSON>", "type": "Text", "value": "Ceramics - Toilets/Basins/Bath/Bidets"}]]}, {"__TYPE__": "GroupItemContent", "value": [["list_item", {"__TYPE__": "<PERSON><PERSON><PERSON><PERSON>", "type": "Text", "value": "Repairs to external pipework/drains/communal drainage pipework"}]]}, {"__TYPE__": "GroupItemContent", "value": [["list_item", {"__TYPE__": "<PERSON><PERSON><PERSON><PERSON>", "type": "Text", "value": "Fridges/Washing Machine appliances"}]]}, {"__TYPE__": "GroupItemContent", "value": [["list_item", {"__TYPE__": "<PERSON><PERSON><PERSON><PERSON>", "type": "Text", "value": "Damages caused by sludge"}]]}, {"__TYPE__": "GroupItemContent", "value": [["list_item", {"__TYPE__": "<PERSON><PERSON><PERSON><PERSON>", "type": "Text", "value": "Damages caused by limescale"}]]}, {"__TYPE__": "GroupItemContent", "value": [["list_item", {"__TYPE__": "<PERSON><PERSON><PERSON><PERSON>", "type": "Text", "value": "Repairing damage caused by third parties"}]]}, {"__TYPE__": "GroupItemContent", "value": [["list_item", {"__TYPE__": "<PERSON><PERSON><PERSON><PERSON>", "type": "Text", "value": "Boiler breakdowns caused by sludge and sediment in the system"}]]}]}]