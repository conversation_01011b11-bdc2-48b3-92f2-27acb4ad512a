"use client";

import * as styles from "./AdditionalAppliances.css";
import Toggler from "@/components/Toggler";
import classNames from "classnames";
import InfoOutlineIcon from "@/assets/icons/InfoOutlineIcon";
import { APPLIANCES } from "@/utils/constants";
import { formatNumToGBP } from "@/utils/helpers";
import { FC, useCallback, useEffect, useRef, useState } from "react";
import Typography from "@/components/Typography";
import IconX from "../../assets/icons/IconX";
import Tooltip from "@/components/Tooltip";
import MemoInfoOutlineIcon from "@/assets/icons/InfoOutlineIcon";

type AdditionalAppliancesProps = {
  onChange: (checked: boolean, value?: string | number | readonly string[] | undefined ) => void
  activeAppliances: string[]
}

const AdditionalAppliances:FC<AdditionalAppliancesProps> = ({activeAppliances,onChange}) => {
  const [showTooltip, setShowTooltip] = useState(false);
  const tooltipRef = useRef<HTMLDivElement>(null);

  const handleShowTooltip = (e: React.MouseEvent<SVGSVGElement, MouseEvent>) => {
    e.preventDefault();
    e.stopPropagation();
    setShowTooltip(!showTooltip);
  };


  const handleCloseTooltip = () => {
    setShowTooltip(false);
  };

  const outsideClick = useCallback((e: MouseEvent) => {
    if (tooltipRef.current && !tooltipRef.current.contains(e.target as Node)) {
      e.preventDefault();
      e.stopPropagation();
      setShowTooltip(false);
    }
  }, []);

  useEffect(() => {
    if (showTooltip) {
      document.addEventListener("click", outsideClick);
    } else {
      document.removeEventListener("click", outsideClick);
    }

    return () => document.removeEventListener("click", outsideClick);
  }, [showTooltip]);

  return (
    <div
      className={styles.root}
    >
      <div
        className={styles.header}
      >
        <div
          className={styles.title}
        >Additional appliances </div>
        <Tooltip
          arrow
          variant="tertiary"
          on={["click", "focus"]}
          trigger={(
            <div
              className={styles.tootlipTrigger}
            >
              <MemoInfoOutlineIcon />
            </div>
          )}
        >
          <Typography
            as={"p"}
            variant="note"
          >
            Additional appliances will<br/>
add additional fee to the<br/>
monthly package cost. <br/>
Each appliance adds <b>£2.50</b><br/>
 onto the monthly plan.
          </Typography>
        </Tooltip>
        {/* <div
          className={styles.iconWrapper}
        >

          <InfoOutlineIcon
            className={styles.infoIcon}
            onClick={handleShowTooltip}
          />
          {showTooltip && (
            <div
              className={styles.tooltip}
              ref={tooltipRef}
            >
              <IconX
                className={styles.closeIcon}
                onClick={handleCloseTooltip}
              />
              <Typography
                variant="note"
              >
                Additional appliances will add additional fee to the monthly package cost.<br />
                Each appliance adds £2.50 onto the monthly plan.
              </Typography>
            </div>
          )}
        </div> */}
      </div>
      <div
        className={styles.items}
      >
        {APPLIANCES.map(({id,name,yearPrice}) => {
          const isActive = activeAppliances.includes(id);
          return (
            <div
              key={id}
              className={styles.item}
            >
              <Toggler
                onChange={onChange}
                value={id}
                variant="filledDifferActiveState"
                checked={isActive}
              />
              <span
                className={styles.text}
              >{name}</span>
              <span
                className={classNames(styles.price, styles.activePrice)}
              >{formatNumToGBP(yearPrice / 100)}</span>
            </div>
          );
        })}
      </div>
    </div>
  );
};

export default AdditionalAppliances;