import { breakpoints } from "@/styles/constants.css";
import { theme } from "@/styles/themes.css";
import { globalStyle, style } from "@vanilla-extract/css";

export const root = style({
  padding: "50px 0 50px",

  "@media": {
    [breakpoints.tablet]: {
      padding: "104px 68px 104px",
    }
  },
});

export const pricePerWrapper = style({
  display: "flex",
  justifyContent: "center",
  marginBottom: 32,
});

export const pricePer = style({
  display: "inline-flex",
  padding: "11px 19px",
  borderRadius: 100,
  border: `1px solid ${theme.colors.primary.castletonGreen}`,
  fontFamily: theme.fonts.secondary,
  color: theme.colors.primary.castletonGreen,
  fontSize: 18,
  fontWeight: 500,
  lineHeight: "120%",
  letterSpacing: "-0.36px",

  "@media": {
    [breakpoints.tablet]: {
      padding: "12.5px 25px",
      fontSize: 24,
      letterSpacing: "0.24px"
    }
  },
});

export const pricePerText = style({
  position: "relative",
  bottom: -2,
});

export const title = style({
  marginBottom: 23,
  marginLeft: 55,
  marginRight: 55,
  textAlign: "center",
  fontFamily: theme.fonts.primary,
  fontSize: "36px !important",
  letterSpacing: "-0.72px !important",

  "@media": {
    [breakpoints.tablet]: {
      marginBottom: 32,
      marginLeft: 0,
      marginRight: 0,
      fontSize: "64px !important",
      letterSpacing: "-1.28px !important",
    }
  },
});

globalStyle(`${title} br`, {
  "@media": {
    [breakpoints.tablet]: {
      content: "",
      height: 24,
      display: "block",
    }
  },
});

export const totalPriceContainer = style({
  display: "flex",
  justifyContent: "center",
  marginBottom: 30,
  fontFamily: theme.fonts.primary,

  "@media": {
    [breakpoints.tablet]: {
      marginBottom: 72,
    }
  },
});

export const totalPriceLabel = style({
  position: "relative",
  display: "inline-flex",
  justifyContent: "center",
  alignItems: "center",
  width: 124,
  height: 44,
  margin: "0 auto",
  borderRadius: 100,
  backgroundColor: theme.colors.primary.asidGreen,

  "@media": {
    [breakpoints.tablet]: {
      width: 207,
      height: 71,
    }
  },
});

export const totalPriceText = style({
  marginBottom: -5,
  fontSize: 36,
  fontWeight: "bold",
  lineHeight: "95%",
  letterSpacing: "-0.72px",
  
  "@media": {
    [breakpoints.tablet]: {
      marginBottom: -7,
      fontSize: 56,
      letterSpacing: "-1.12px",
    }
  },
});

export const benefitsContainer = style({
  position: "relative",
  display: "flex",
  flexWrap: "nowrap",
  overflowX: "scroll",
  scrollSnapType: "x mandatory",
  scrollBehavior: "smooth",
  maxWidth: "calc(100vw - 30px)",
  gap: 10,

  "@media": {
    [breakpoints.tablet]: {
      scrollSnapType: "none",
      maxWidth: "100%",
      gap: 0,
    }
  },
});

export const includedBenefits = style({
  minWidth: "calc(100vw - 30px)",
  scrollSnapAlign: "center",
  padding: 32,
  borderRadius: 16,
  backgroundColor: theme.colors.primary.castletonGreen,
  color: theme.colors.primary.softWhite,

  "@media": {
    [breakpoints.tablet]: {
      position: "relative",
      zIndex: 1,
      padding: "40px 56px 40px 40px",
      scrollSnapAlign: "none",
      minWidth: 640,
      marginBottom: 48,
    },
  },
});

export const includedBenefitsAdditionalAppliances = style({
  "@media": {
    [breakpoints.tablet]: {
      paddingBottom: 56,
      marginBottom: 155,
    }
  },
});

export const excludedBenefits = style({
  minWidth: "calc(100vw - 20px)",
  scrollSnapAlign: "center",
  padding: 32,
  borderRadius: 16,
  backgroundColor: theme.colors.primary.softWhite,
  color: theme.colors.primary.castletonGreen,

  "@media": {
    [breakpoints.tablet]: {
      padding: "40px 64px 40px 112px",
      minWidth: "unset",
      maxWidth: 720,
      marginLeft: "-56px",
      marginTop: 116,
      scrollSnapAlign: "none",
    },
    [breakpoints.desktop]: {
      // boxSizing: "border-box",
      // minWidth: "fit-content",
      minHeight: 447,
      height: "fit-content",
    }
  },
});

export const benefitsTitle = style({
  marginBottom: 20,

  "@media": {
    [breakpoints.tablet]: {
      marginBottom: 24,
    }
  },
});

export const benefitsList = style({
  display: "flex",
  flexDirection: "column",
  gap: 12,

  "@media": {
    [breakpoints.tablet]: {
      gap: 8,
    }
  },
});

export const excludedBenefitsList = style({
  opacity: 0.5,
});

export const includedBenefit = style({
  position: "relative",
  display: "flex",
  alignItems: "flex-start",
  gap: 10,
});

export const checkIcon = style({
  minWidth: "20px !important",
  width: 20,
  height: 20,
  color: theme.colors.primary.asidGreen,
});

export const xIcon = style({
  minWidth: 20,
  width: 20,
  height: 20,
});

export const benefitText = style({
  fontFamily: theme.fonts.secondary,
  fontSize: 16,
  fontWeight: 500,
  lineHeight: "130%",
  maxWidth: 514,

  "@media": {
    [breakpoints.tablet]: {
      fontSize: 18,
    }
  },
});

export const benefitsButtons = style({
  display: "flex",
  justifyContent: "center",
  gap: 16,
  marginTop: 20,

  "@media": {
    [breakpoints.tablet]: {
      display: "none",
    }
  },
});

export const planActionWrapper = style({
  display: "flex",
  justifyContent: "center",
  marginTop: 30,

  "@media": {
    [breakpoints.tablet]: {
      marginTop: 56,
    }
  },
});

export const planActionWrapperAdditionalAppliances = style({
  "@media": {
    [breakpoints.tablet]: {
      marginTop: -56,
    }
  },
});


export const controlButton = style({
  fontSize: 26
});

export const getCoverButton = style({
  minWidth: "315px",

  "@media": {
    [breakpoints.tablet]: {
      minWidth: "inherit",
    }
  },
});