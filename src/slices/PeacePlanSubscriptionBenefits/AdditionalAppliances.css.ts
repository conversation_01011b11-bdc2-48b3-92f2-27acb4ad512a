import { breakpoints } from "@/styles/constants.css";
import { mode } from "@/styles/functions.css";
import { theme } from "@/styles/themes.css";
import { style } from "@vanilla-extract/css";

export const root = style({
  display: "none",
  position: "absolute",
  top: "100%",
  left: 32,
  minWidth: 292,
  marginTop: -35,
  padding: 24,
  border: `2px solid ${theme.colors.primary.asidGreen}`,
  borderRadius: 16,
  backgroundColor: theme.colors.primary.castletonGreen,

  "@media": {
    [breakpoints.tablet]: {
      display: "block",
    }
  },
});

export const header = style({
  display: "flex",
  alignItems: "center",
  gap: 4,
  marginBottom: 8,
});

export const title = style({
  fontFamily: theme.fonts.secondary,
  fontSize: 18,
  fontWeight: 500,
  lineHeight: "120%",
  color: theme.colors.primary.softWhite,
});


export const tootlipTrigger =  style({
  color: theme.colors.primary.asidGreen,
  cursor: "help",
  fontSize: 18,
  display: "flex",
  // selectors: {
  //   [mode("residential")]: {
  //     color: theme.colors.primary.castletonGreen,
  //   }
  // },
});

export const items = style({
  display: "flex",
  flexDirection: "column",
  gap: 6,
});

export const item = style({
  display: "flex",
  alignItems: "center",
  gap: 16,
  height: 32,
});

export const text = style({
  fontFamily: theme.fonts.secondary,
  fontSize: 16,
  fontWeight: 500,
  lineHeight: "120%",
  letterSpacing: "-0.32px",
  color: theme.colors.primary.softWhite,
  marginRight: "auto",
});

export const price = style({
  marginLeft: "auto",
  fontFamily: theme.fonts.secondary,
  fontSize: 16,
  fontWeight: 500,
  lineHeight: "120%",
  letterSpacing: "-0.32px",
  color: theme.colors.primary.softWhite,
  opacity: 0.8,
});

export const activePrice = style({
  color: theme.colors.primary.asidGreen,
  opacity: 1,
});

export const infoIcon = style({
  color: theme.colors.primary.asidGreen,
  width: 18,
  height: 18,
});

export const iconWrapper = style({
  position: "relative",
});

export const tooltip = style({
  position: "absolute",
  top: "100%",
  left: "50%",
  marginTop: 10,
  transform: "translateX(-50%)",
  display: "flex",
  flexDirection: "column",
  gap: 10,
  backgroundColor: theme.colors.primary.softWhite,
  padding: "20px 36px 20px 20px",
  borderRadius: 8,
  minWidth: 262,
  color: theme.colors.primary.castletonGreen,

  "::before": {
    content: "''",
    position: "absolute",
    top: -20,
    left: "50%",
    transform: "translateX(-50%)",
    width: 0,
    height: 0,
    borderLeft: "10px solid transparent",
    borderRight: "10px solid transparent",
    borderTop: "10px solid transparent",
    borderBottom: "10px solid white",
  },
});

export const closeIcon = style({
  position: "absolute",
  top: 12,
  right: 12,
  width: 16,
  height: 16,
  cursor: "pointer",
});