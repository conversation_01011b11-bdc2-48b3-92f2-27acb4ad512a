{"id": "peace_plan_subscription_benefits", "type": "SharedSlice", "name": "PeacePlanSubscriptionBenefits", "description": "PeacePlanSubscriptionBenefits", "variations": [{"id": "default", "name": "<PERSON><PERSON><PERSON>", "docURL": "...", "version": "initial", "description": "<PERSON><PERSON><PERSON>", "imageUrl": "", "primary": {"price_per": {"type": "Text", "config": {"label": "Price per", "placeholder": ""}}, "title": {"type": "StructuredText", "config": {"label": "Title", "placeholder": "", "allowTargetBlank": true, "multi": "paragraph,preformatted,heading1,heading2,heading3,heading4,heading5,heading6,strong,em,hyperlink,image,embed,list-item,o-list-item,rtl"}}, "total_price": {"type": "Text", "config": {"label": "Total price", "placeholder": ""}}, "header_anchor_name": {"type": "Text", "config": {"label": "Header anchor name", "placeholder": ""}}}, "items": {"list_item": {"type": "Text", "config": {"label": "List item", "placeholder": ""}}, "included": {"type": "Boolean", "config": {"label": "Included", "default_value": false, "placeholder_true": "true", "placeholder_false": "false"}}}}]}