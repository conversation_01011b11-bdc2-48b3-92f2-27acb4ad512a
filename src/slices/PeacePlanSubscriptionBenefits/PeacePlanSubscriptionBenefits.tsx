"use client";

import Container from "@/components/Container";
import { Content } from "@prismicio/client";
import { SliceComponentProps } from "@prismicio/react";
import * as styles from "./PeacePlanSubscriptionBenefits.css";
import Typography from "@/components/Typography";
import { PrismicRichText } from "node_modules/@prismicio/react/dist/react-server/PrismicRichText";
import { useCallback, useMemo, useRef, useState } from "react";
import classNames from "classnames";
import IconButton from "@/components/IconButton";
import IconX from "../../assets/icons/IconX";
import AdditionalAppliances from "./AdditionalAppliances";
import CheckMarkIcon from "@/assets/icons/CheckMarkIcon";
import ActionButton from "./ActionButton";
import { useSearchParams } from "next/navigation";
import GetCoverButton from "@/components/GetCoverButton/GetCoverButton";
import { toCamelCase } from "@/utils/helpers";
import { ChevronIcon } from "@/assets/icons/ChevronIcon";
import { fetchSlice } from "../PlansSection/actions";
import { SlicesContextData } from "@/types/common";


export type PeacePlanSubscriptionBenefitsProps =
  SliceComponentProps<Content.PeacePlanSubscriptionBenefitsSlice> & {context: SlicesContextData};





const PeacePlanSubscriptionBenefits = ({
  slice, context
}: PeacePlanSubscriptionBenefitsProps): JSX.Element => {
  
  const { plans } = context[slice.slice_type];

  const scrollContainerRef = useRef<HTMLDivElement>(null);
  const searchParams = useSearchParams();
  const [activeAppliances, setActiveAppliances] = useState<string[]>([]);
  
  const handlePrev = () => {
    if (!scrollContainerRef.current) return;

    scrollContainerRef.current.scrollTo({
      left:
        scrollContainerRef.current.scrollLeft -
        scrollContainerRef.current.clientWidth,
    });
  };

  const handleNext = () => {
    if (!scrollContainerRef.current) return;

    scrollContainerRef.current.scrollTo({
      left:
        scrollContainerRef.current.scrollLeft +
        scrollContainerRef.current.clientWidth,
    });
  };

  const includedBenefits = useMemo(() => slice.items.filter((item) => item.included), [slice]);
  const excludedBenefits = useMemo(() => slice.items.filter((item) => !item.included), [slice]);
  
  const planType = useMemo(() => searchParams?.get("mode") || "homeowner", [searchParams]);
  const showAdditionalAppliances = useMemo(() => planType === "landlord", [planType]);
  const serverPlan = useMemo(() => plans?.data?.prices.find(({id, product}) => (product.metadata.plan_name === `peace_${planType}`)), [planType, plans]);

  const onChangeAppliances = useCallback(
    (checked: boolean, value?: string | number | readonly string[] | undefined ) => {
      setActiveAppliances(prev => checked ? [...prev, value as string] : prev.filter(prevApp => prevApp !== value));
    },
    [],
  );

  

  return (
    <Container
      removeBorderRadius
    >
      <section
        id={slice.primary.header_anchor_name ? toCamelCase(slice.primary.header_anchor_name) : ""}
        data-slice-type={slice.slice_type}
        data-slice-variation={slice.variation}
        className={styles.root}
      >
        <div
          className={styles.pricePerWrapper}
        >
          <div
            className={styles.pricePer}
          >
            <span
              className={styles.pricePerText}
            >{slice.primary.price_per}</span>
          </div>
        </div>
        <Typography
          className={styles.title}
          variant="h3"
        >
          <PrismicRichText
            field={slice.primary.title}
          />
        </Typography>
        <div
          className={styles.totalPriceContainer}
        >
          <div
            className={styles.totalPriceLabel}
          >
            <span
              className={styles.totalPriceText}
            >
              {slice.primary.total_price}
            </span>
          </div>
        </div>

        <div
          className={styles.benefitsContainer}
          ref={scrollContainerRef}
        >
          <div
            className={classNames(styles.includedBenefits, {
              [styles.includedBenefitsAdditionalAppliances]: showAdditionalAppliances,
            })}
          >
            <Typography
              variant="subTitleMedium"
              className={classNames(styles.benefitsTitle)}
            >
              Included:
            </Typography>
            <div
              className={styles.benefitsList}
            >
              {includedBenefits.map((benefit, index) => (
                <div
                  key={index}
                  className={styles.includedBenefit}
                >
                  <CheckMarkIcon
                    className={styles.checkIcon}
                  />
                  <span
                    className={styles.benefitText}
                  >{benefit.list_item}</span>
                </div>
              ))}
            </div>
            {showAdditionalAppliances && <AdditionalAppliances
              onChange={onChangeAppliances}
              activeAppliances={activeAppliances}
                                         />}
          </div>

          <div
            className={styles.excludedBenefits}
          >
            <Typography
              variant="subTitleMedium"
              className={classNames(styles.benefitsTitle)}
            >
              Excluded:
            </Typography>
            <div
              className={classNames(styles.benefitsList, styles.excludedBenefitsList)}
            >
              {excludedBenefits.map((benefit, index) => (
                <div
                  key={index}
                  className={styles.includedBenefit}
                >
                  <IconX
                    className={styles.xIcon}
                  />
                  <span
                    className={styles.benefitText}
                  >{benefit.list_item}</span>
                </div>
              ))}
            </div>
          </div>
        </div>

        <div
          className={styles.benefitsButtons}
        >
          <IconButton
            onClick={handlePrev}
            title="Last Reviews"
            className={styles.controlButton}
          >
            <ChevronIcon
              turn="left"
            />
          </IconButton>

          <IconButton
            onClick={handleNext}
            title="Included Benefits"
            className={styles.controlButton}
          >
            <ChevronIcon
              turn="right"
            />
          </IconButton>
        </div>

        <div
          className={classNames(styles.planActionWrapper, {
            [styles.planActionWrapperAdditionalAppliances]: showAdditionalAppliances,
          })}
        >
          <GetCoverButton
            recurringInterval="year"
            planId={serverPlan?.id}
            activeAppliances={activeAppliances}
            planName="peace"
            className={styles.getCoverButton}
          />
        </div>
      </section>
    </Container>
  );
};

export default PeacePlanSubscriptionBenefits;
