"use client";

import Button from "@/components/Button";
import * as styles from "./ActionButton.css";

const ActionButton = () => {
  const handleGetPlan = async () => {
    // const serverPlanByPlanType = serverPlans.prices.find(({recurring}) => recurring.inverval === planType);

    // if (!serverPlanByPlanType) return;

    // let basePlanUrl = `/payment/${serverPlanByPlanType.id}?plan=${plan.name}&type=${planType}`;

    // if (selectedAppliances.length) {
    //   basePlanUrl += `&appliances=${selectedAppliances.map(appl => appl.name).join(",")}`;
    // }

    // router.push(basePlanUrl);
  };

  return (
    <Button
      onClick={handleGetPlan}
      className={styles.planAction}
      isAnimated
    >
          Get cover
    </Button>
  );
};

export default ActionButton;