"use client";

import TimeIcon from "@/assets/icons/TimeIcon";
import Button from "@/components/Button";
import DropdownInput from "@/components/DropdownInput";
import Typography from "@/components/Typography";
import { gridSprinkle } from "@/styles/sprinkles.css";
import { Content } from "@prismicio/client";
import { PrismicNextImage } from "@prismicio/next";
import classNames from "classnames";
import dayjs from "dayjs";
import Link from "next/link";
import { useCallback, useEffect, useMemo, useState } from "react";
import * as styles from "./BlogSection.css";
import { getArticles } from "./actions";
import ModeProvider from "@/components/ModeProvider";
import { usePathname, useRouter, useSearchParams } from "next/navigation";
import { ArticlesType } from "@/types/common";
import { BlogTagsDocument } from "prismicio-types";
import { generateSlug } from "@/utils/generateSlugFromTitle";

interface Props {
  tags: BlogTagsDocument<string>[]
  articles: ArticlesType
}

const pageSize = 4;

const Articles = ({ tags, articles }: Props): JSX.Element => {

  const [clientArticles, setClientArticles] = useState<ArticlesType["results"]>(articles.results);
  // const [selectedTag, setSelectedTag] = useState("All");
  const [moreArticles, setMoreArticles] = useState(Boolean(articles.next_page));
  const searchParams = useSearchParams();
  const router = useRouter();
  const pathname = usePathname();
  useEffect(() => {
    setClientArticles(articles.results);
    setMoreArticles(Boolean(articles.next_page));
  }, [articles.results]);

  const selectedTag = useMemo(() => searchParams?.get("tag") || null, [searchParams]);
  const pageMode = useMemo(() => searchParams?.get("mode") || undefined, [searchParams]);
  const isCommercial = useMemo(() => pageMode === "commercial", [pageMode]);

  const setSelectedTag = useCallback(
    (tag: string | null) => {
      const newSearchParams = new URLSearchParams((searchParams as URLSearchParams | null) ?? {});
      if(!tag) {
        newSearchParams.delete("tag");
      }
      else {
        newSearchParams.set("tag", tag);
      }
      router.push(`${pathname}?${newSearchParams}`);
    },
    [pathname, searchParams, router],
  );




  useEffect(() => {
    // getArticles(selectedTag === "All" ? tags : [selectedTag], pageSize).then(res => {
    //   setClientArticles(res.results);
    //   });
    // setMoreArticles(Boolean(res.next_page));
  }, [selectedTag,tags]);

  const loadMore = async () => {
    const articles = await getArticles(selectedTag, pageMode , pageSize, Math.ceil(clientArticles.length / pageSize) + 1);

    setClientArticles([...clientArticles, ...articles.results]);
    setMoreArticles(Boolean(articles.next_page));
  };



  return (
    <>
      <div
        className={classNames(styles.tags, gridSprinkle({ display: { mobile: "none", tablet: "flex" } }))}
      >
        <ModeProvider>
          {(mode) => (
            <Button
              noMinWidth
              onClick={() => setSelectedTag(null)}
              variant={!selectedTag ? "filled" : "outlined"}
              shape="rect"
              color={mode === "residential" ? "secondaryInverted" : "secondary"}
              className={!selectedTag ? styles.activeTagBtnDarkMode : undefined}
            >
                All
            </Button>
          )}
        </ModeProvider>
        {tags.map((tag) => (
          <ModeProvider
            key={tag.id + "mode"}
          >
            {(mode) => (
              <Button
                noMinWidth
                onClick={() => setSelectedTag(tag.uid)}
                key={tag.id}
                color={mode === "residential" ? "secondaryInverted" : "secondary"}
                shape="rect"
                variant={selectedTag === tag.uid ? "filled" : "outlined"}
                className={selectedTag === tag.uid ? styles.activeTagBtnDarkMode : undefined}
              >
                {tag.data.name}
              </Button>
            )}
          </ModeProvider>
        ))}
      </div>
      <div
        className={classNames(styles.tags, gridSprinkle({ display: { mobile: "flex", tablet: "none" } }))}
      >
        <DropdownInput
          value={{
            value: selectedTag || null,
            label: selectedTag || "All",
          }}
          onChange={(option) => {
            if (!option?.value) return;

            setSelectedTag(String(option.value));
          }}
          options={[
            {
              value: null,
              label: "All",
            },
            ...tags.map((tag) => ({
              value: tag.uid,
              label: tag.data.name as string,
            }))
          ]}
          enableDarkMode
        />
      </div>
      <div
        className={styles.articles}
      >
        {clientArticles.map(article => {
          const slug = generateSlug(article.data.title as string);
          return (
          <article
            className={styles.article}
            key={article.id}
          >
            <div
              className={styles.articleImage}
            >
              <PrismicNextImage
                fill
                objectFit="cover"
                objectPosition="center"
                field={isCommercial ? article.data.image_commercial : article.data.image_residential}
              />
            </div>
            <Typography
              className={styles.articleTitle}
              variant="subTitleMedium"
            >
              {isCommercial ? article.data.title_commercial : article.data.title_residential}
            </Typography>
            <Typography
              className={styles.articleContent}
              variant="bodySmall"
            >
              {isCommercial ? article.data.description_commercial : article.data.description_residential}
            </Typography>
            <div
              className={styles.tagsList}
            >
              {article.data.tags_group.map(({tag}) => {
                // @ts-ignore
                // Can't type this because of 'fetchLinks' in the request (from actions)
                const {uid, name} = tag.data;
                return (
                  <div
                    key={uid}
                    className={classNames(styles.articleTag, gridSprinkle({ display: { mobile: "inline-flex", tablet: "none" } }))}
                  >
                    <Typography
                      variant="subTitleSmall"
                    >
                      {name}
                    </Typography>
                  </div>
                );})}
            </div>
            <footer
              className={styles.articleFooter}
            >
              <div
                className={styles.articleTags}
              >
                {article.data.tags_group.map(({tag} ) => {
                  // @ts-ignore
                  // Can't type this because of 'fetchLinks' in the request (from actions)
                  const {uid, name} = tag.data;
                  return (
                    <div
                      key={uid}
                      className={classNames(styles.articleTag, gridSprinkle({ display: { mobile: "none", tablet: "flex" } }))}
                    >
                      <Typography
                        variant="subTitleSmall"
                      >
                        {name}
                      </Typography>
                    </div>
                  );}
                )}
              </div>
              {/* <Typography
                variant="subTitleSmall"
              >
                {dayjs(article.last_publication_date).format("MMMM D, YYYY")}
              </Typography> */}
              {/* | */}
              <Typography
                variant="subTitleSmall"
                className={styles.articleTime}
              >
                <TimeIcon />
                {article.data.reading_time_minutes} min read
              </Typography>
            </footer>
            <Link
              href={`/learn/${slug}${searchParams ? "?" + searchParams.toString() : ""}`}
              className={styles.articleLink}
            />
          </article>
        )})}
      </div>
      <div
        className={gridSprinkle({ display: "flex", justifyContent: "center" })}
      >
        {moreArticles && (
          <Button
            onClick={loadMore}
          >
            Load More
          </Button>
        )}
      </div>
    </>
  );
};

export default Articles;
