"use server";

import { createClient } from "@/prismicio";
import { ArticlesType } from "@/types/common";
import { filter } from "@prismicio/client";
import { headers } from "next/headers";
import { NextRequest } from "next/server";
import { BlogPageDocument } from "prismicio-types";

export const getTags = async () => {
  const client = createClient();

  const tags = await client.getByType("blog_tags");

  return tags.results;
  // const tags = await client.getTags();

  // return tags.filter((tag) => !["residential", "commercial"].includes(tag));
};

const client = createClient();
export const getArticles = async (tag: string | null, mode = "residential", pageSize = 4, pageNumber = 1, ): Promise<ArticlesType> => {

  const tagObj = tag ? await client.get({filters:[filter.at("my.blog_tags.uid",tag)]}) : null;
  const tagId = !!tagObj?.results.length ? tagObj?.results[0].id : null;
    
    
  
  const paginationOptions = {
    pageSize,
    page: pageNumber
  };
  
  
  const blog = await client.get({
    filters: [
      // Filter all blog_page pages
      filter.at("document.type", "blog_page"),
      // Filter blog_page by page mode 
      filter.not("my.blog_page.page_mode", mode === "residential" ? "commercial" : "residential"),
      // Filter blog_page by tags
      ...(tagId ? [filter.at("my.blog_page.tags_group.tag", tagId)] : []),
    ], 
    // Get name and uid of tags by using 'Content Relationship'
    fetchLinks: [
      "blog_tags.name",
      "blog_tags.uid",
    ],
    ...paginationOptions,
  });

  return blog as ArticlesType;
  

 

  
  

};

export const fetchSlice = async (searchParams: Record<string,string>) => {

  const tags = await getTags();

  const articles = await getArticles(searchParams.tag ? searchParams.tag : null,searchParams.mode, 4);

  return {
    articles,
    tags,
  };
};