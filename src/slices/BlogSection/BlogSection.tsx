import Container from "@/components/Container";
import { Content } from "@prismicio/client";
import { SliceComponentProps } from "@prismicio/react";
import Articles from "./Articles";
import * as styles from "./BlogSection.css";
import { SlicesContextData } from "@/types/common";

export type BlogSectionProps = SliceComponentProps<Content.BlogSectionSlice> & {context: SlicesContextData};

const BlogSection = ({ slice, context }: BlogSectionProps): JSX.Element => {
  const {articles, tags} = context[slice.slice_type];
  return (
    <Container
      className={styles.container}
    >
      <section
        data-slice-type={slice.slice_type}
        data-slice-variation={slice.variation}
        className={styles.root}
      >
        <Articles
          articles={articles}
          tags={tags}
        />
      </section>
    </Container>
  );
};

export default BlogSection;
