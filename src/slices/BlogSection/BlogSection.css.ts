import { breakpoints } from "@/styles/constants.css";
import { mode } from "@/styles/functions.css";
import { theme } from "@/styles/themes.css";
import { style } from "@vanilla-extract/css";

export const container = style({
  selectors: {
    [mode("residential")]: {
      backgroundColor: theme.colors.primary.castletonGreen,
    }
  },
});

export const root = style({
  padding: "40px 20px",

  "@media": {
    [breakpoints.tablet]: {
      padding: "72px 48px",
    },
  }
});

export const articles = style({
  display: "flex",
  flexWrap: "wrap",
  gap: 24,
  marginBottom: 40,

  "@media": {
    [breakpoints.tablet]: {
      gap: 32,
    },
  }
});


export const article = style({
  position: "relative",
  flex: "1 1",
  padding: 16,
  borderRadius: 16,
  backgroundColor: theme.colors.primary.softWhite,
  transition: "background-color 50ms, transform 350ms",
  cursor: "pointer",

  ":active": {
    backgroundColor: theme.colors.primary.softWhitePressed,
    color: theme.colors.primary.castletonGreenPressed,
    transform: "scale(0.97)",
  },

  "@media": {
    [breakpoints.tablet]: {
      padding: 24,
      minWidth: "40%",
      maxWidth: 640,
    },
  }
});

export const articleImage = style({
  display: "block",
  backgroundColor: theme.colors.primary.ivory,
  position: "relative",
  borderRadius: 16,
  overflow: "hidden",
  aspectRatio: "1.6/1",
  marginBottom: 24,
});

export const articleTitle = style({
  WebkitBoxOrient: "vertical",
  WebkitLineClamp: 2,
  display: "-webkit-box",
  overflow: "hidden",
  textOverflow: "ellipsis",
  marginBottom: 24,
});

export const articleContent = style({
  WebkitBoxOrient: "vertical",
  WebkitLineClamp: 2,
  display: "-webkit-box",
  overflow: "hidden",
  textOverflow: "ellipsis",
  marginBottom: 24,
});

export const articleFooter = style({
  display: "flex",
  alignItems: "center",
  gap: 16,
  color: theme.colors.grayscale[200],
  marginTop: 16,

  "@media": {
    [breakpoints.tablet]: {
      marginTop: 0,
    },
  }
});

export const articleTags = style({
  display: "flex",
  alignItems: "center",
  gap: 12,
  marginLeft: -12,

  "@media": {
    [breakpoints.tablet]: {
      marginLeft: 0,
      marginRight: "auto",    
    }
  }
});

export const articleTag = style({
  padding: "0 12px",
  alignItems: "center",
  minHeight: 32,
  borderRadius: 32,
  border: `1px solid ${theme.colors.grayscale[100]}`,
  color: theme.colors.primary.castletonGreen,
});

export const articleTime = style({
  display: "flex",
  gap: 6,
});

export const articleLink = style({
  position: "absolute",
  top: 0,
  left: 0,
  right: 0,
  bottom: 0,
  opacity: 0,
});

export const tags = style({
  flexWrap: "wrap",
  gap: 16,
  marginBottom: 24,

  "@media": {
    [breakpoints.tablet]: {
      marginBottom: 40,
    }
  }
});

export const tagsList = style({
  display: "flex",
  alignItems: "center",
  flexWrap: "wrap",
  gap: 8,
});

export const activeTagBtnDarkMode = style({
  selectors: {
    [mode("residential")]: {
      backgroundColor: theme.colors.primary.asidGreen,
      color: theme.colors.primary.castletonGreen,
    }
  },
});