import { breakpoints } from "@/styles/constants.css";
import { mode, modeGlobal } from "@/styles/functions.css";
import { theme } from "@/styles/themes.css";
import { globalStyle, style } from "@vanilla-extract/css";

export const container = style({
  scrollSnapAlign: "start",

  selectors: {
    [mode("residential")]: {
      backgroundColor: theme.colors.primary.castletonGreen,
    },
  },
});

export const root = style({
  scrollSnapAlign: "center",
  padding: "40px 20px",
  display: "flex",
  flexWrap: "wrap",
  gap: 32,

  "@media": {
    [breakpoints.tablet]: {
      padding: "72px 48px",
      gap: "58px 32px",
    }
  }
});

export const title = style({
  fontFamily: theme.fonts.primary,
  position: "relative",

  "@media": {
    [breakpoints.tablet]: {
    },
  },

  selectors: {
    [mode("residential")]: {
      color: theme.colors.primary.softWhite,
    },
  },
});

globalStyle(`${title} p`, {
  marginBottom: 0,
});

globalStyle(`${modeGlobal("residential")} ${title} em`, {
  color: theme.colors.primary.asidGreen,
});

export const card = style({
  minWidth: 200,
  flex: "1 1",

  "@media": {
    [breakpoints.tablet]: {
      minWidth: 380,
    }
  },
});

export const cardIcon = style({
  position: "relative",
  minWidth: 18,
  height: 18,

  "@media": {
    [breakpoints.tablet]: {
      minWidth: 24,
      height: 24,
    }
  },

  selectors: {
    [mode("residential")]: {
      backgroundColor: theme.colors.primary.castletonGreen,
    },
  },
});

globalStyle(`${modeGlobal("residential")} ${cardIcon} img`, {
  display: "none",
});

export const cardTitle = style({
  fontWeight: 500,
  fontSize: 18,
  lineHeight: 1.2,

  "@media": {
    [breakpoints.tablet]: {
      fontSize: 24,
      minHeight: 58,
    }
  },

  selectors: {
    [mode("residential")]: {
      color: theme.colors.primary.castletonGreen,
    },
  },
});

export const cardHeader = style({
  position: "relative",
  padding: "20px 28px 38px",
  display: "flex",
  gap: 12,
  zIndex: 1,
  color: theme.colors.primary.softWhite,

  "@media": {
    [breakpoints.tablet]: {
      gap: 16,
      padding: "30px 48px 46px",
    }
  },

  ":before": {
    content: "",
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    bottom: -30,
    borderRadius: 16,
    backgroundColor: theme.colors.primary.castletonGreen,
    zIndex: -1,
  }
});

globalStyle(`${modeGlobal("residential")} ${cardHeader}:before`, {
  backgroundColor: theme.colors.primary.asidGreen,
});

export const cardAction = style({
  marginLeft: "auto",
  minWidth: 70,

  "@media": {
    [breakpoints.tablet]: {
      minWidth: "auto",
    }
  },

  selectors: {
    [mode("residential")]: {
      color: theme.colors.primary.castletonGreen,
    },

    [`${mode("residential")}:hover`]: {
      borderColor: theme.colors.primary.castletonGreen,
    }
  },
});

export const cardBody = style({
  position: "relative",
  zIndex: 1,
  margin: "0 28px",

  ":before": {
    content: "",
    position: "absolute",
    top: -16,
    left: -16,
    right: -16,
    bottom: -16,
    borderRadius: 16,
    backgroundColor: theme.colors.primary.softWhite,
    zIndex: -1,
  },

  "@media": {
    [breakpoints.tablet]: {
      margin: "0 48px",

      ":before": {
        top: -24,
        left: -24,
        right: -24,
        bottom: -24,
      },
    }
  },
});