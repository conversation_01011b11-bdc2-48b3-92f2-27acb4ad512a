"use client";

import Container from "@/components/Container";
import { Content } from "@prismicio/client";
import { SliceComponentProps } from "@prismicio/react";
import * as styles from "./TopServices.css";
import Typography from "@/components/Typography";
import { PrismicRichText } from "node_modules/@prismicio/react/dist/react-server/PrismicRichText";
import IconButton from "@/components/IconButton";
import { PrismicNextImage, PrismicNextLink } from "@prismicio/next";
import Link from "@/components/Link";
import NextArrowIcon from "@/assets/icons/NextArrowIcon";
import ModeProvider from "@/components/ModeProvider";

export type TopServicesProps = SliceComponentProps<Content.TopServicesSlice>;

const TopServices = ({ slice }: TopServicesProps): JSX.Element => {
  return (
    <Container
      className={styles.container}
      notFullHeight
    >
      <section
        data-slice-type={slice.slice_type}
        data-slice-variation={slice.variation}
        className={styles.root}
      >
        <div
          className={styles.card}
        >
          <Typography
            variant="h2"
            className={styles.title}
          >
            <PrismicRichText
              field={slice.primary.title}
            />
          </Typography>        
        </div>
        {slice.items.map((item, index) => (
          <div
            className={styles.card}
            key={index}
          >
            <div
              className={styles.cardHeader}
            >
              <div
                className={styles.cardIcon}
                style={{
                  maskImage: `url(${item.icon.url})`,
                  WebkitMaskImage: `url(${item.icon.url})`,
                }}
              >
                <PrismicNextImage
                  fill
                  field={item.icon}
                />              
              </div>
              <div
                className={styles.cardTitle}
              >
                {item.title}
              </div>
              <ModeProvider>
                {(mode) => (
                  <IconButton
                    as={Link}
                    field={item.link}
                    className={styles.cardAction}
                    title={item.title}
                    size="small"
                    type="submit"
                    variant="outlined"
                    color={mode === "residential" ? "primary" : "secondary"}
                    shape="rect"
                  >
                    <NextArrowIcon />
                  </IconButton>
                )}
              </ModeProvider>
            </div>
            <div
              className={styles.cardBody}
            >
              <Typography
                variant="bodySmall"
              >
                <PrismicRichText
                  field={item.content}
                />
              </Typography>            
            </div>
          </div>
        ))}
      </section>
    </Container>
  );
};

export default TopServices;
