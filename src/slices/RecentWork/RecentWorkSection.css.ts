import { breakpoints } from "@/styles/constants.css";
import { theme } from "@/styles/themes.css";
import { style } from "@vanilla-extract/css";
import { mode } from "@/styles/functions.css";

export const wrapper = style({
  backgroundColor: theme.colors.primary.castletonGreen,
  padding: "60px 20px",
  width: "100%",

  "@media": {
    [breakpoints.tablet]: {
      padding: "80px 68px",
    }
  },

  selectors: {
    [mode("commercial")]: {
      backgroundColor: theme.colors.primary.ivory,
    }
  }
});

export const container = style({
  maxWidth: "100%",
  margin: "0 auto",
  position: "relative",
});

export const title = style({
  fontFamily: theme.fonts.primary,
  fontSize: "52px",
  lineHeight: "95%",
  letterSpacing: "-1.04px",
  color: theme.colors.primary.castletonGreen,
  fontWeight: 400,
  marginBottom: "32px",

  "@media": {
    [breakpoints.tablet]: {
      fontSize: "80px",
      marginBottom: 98,
    },
  },

  selectors: {
    [mode("residential")]: {
      color: theme.colors.primary.softWhite,
    }
  }
});

export const highlight = style({
  fontStyle: "italic",
  fontWeight: 700,
  selectors: {
    [mode("commercial")]: {
      color: theme.colors.primary.castletonGreen,
    },
    [mode("residential")]: {
      color: theme.colors.primary.asidGreen,
    }
  }
});

export const sliderContainer = style({
  width: "100%",
  overflow: "hidden",
  position: "relative",
  marginBottom: "20px",
});

export const slider = style({
  display: "flex",
  transition: "transform 0.5s ease",
  gap: "16px",

  "@media": {
    [breakpoints.tablet]: {
      gap: "20px",
    }
  }
});

export const slide = style({
  position: "relative",
  flex: "0 0 auto",
  height: "100%",
  width: "227",

  "@media": {
    [breakpoints.tablet]: {
      width: "calc((100% - 20px) / 2.5)",
    }
  }
});

export const workCard = style({
  height: "100%",
  borderRadius: "16px",
  overflow: "hidden",
});

export const imageContainer = style({
  position: "relative",
  width: "100%",
  paddingBottom: "75%", // 4:3 aspect ratio
  borderRadius: "16px",
  overflow: "hidden",

  "@media": {
    [breakpoints.tablet]: {
      width: "100%",
      height: "auto",
      paddingBottom: "75%", // Maintain aspect ratio on tablet
    }
  }
});

export const workImage = style({
  objectFit: "cover",
  objectPosition: "center",
});

export const workName = style({
  position: "absolute",
  bottom: "16px",
  left: "16px",
  color: "white",
  fontSize: "16px",
  fontWeight: 500,
  textShadow: "0 1px 2px rgba(0,0,0,0.5)",
});

export const controls = style({
  display: "flex",
  justifyContent: "center",
  gap: "16px",
  marginTop: "24px",

  "@media": {
    [breakpoints.tablet]: {
      justifyContent: "flex-start",
      gap: "8px",
      marginTop: 0,
    }
  }
});

export const mobileControls = style({
  display: "flex",
  justifyContent: "center",
  gap: "16px",
  marginTop: "24px",

  "@media": {
    [breakpoints.tablet]: {
      display: "none",
    }
  }
});

export const controlButton = style({
  width: "48px",
  height: "48px",
  borderRadius: "4px",
  backgroundColor: theme.colors.primary.castletonGreen,
  color: "white",
  display: "flex",
  alignItems: "center",
  justifyContent: "center",
  border: "none",
  cursor: "pointer",
  transition: "background-color 0.2s ease",

  selectors: {
    "&:hover": {
      backgroundColor: theme.colors.primary.castletonGreen,
    },
    "&:disabled": {
      opacity: 0.5,
      cursor: "not-allowed",
    },
    [mode("commercial")]: {
      backgroundColor: theme.colors.primary.castletonGreen,
      color: theme.colors.primary.ivory,
    },
    [`${mode("commercial")}:hover`]: {
      backgroundColor: theme.colors.primary.asidGreen,
    },
    [mode("residential")]: {
      backgroundColor: theme.colors.primary.softWhite,
      color: theme.colors.primary.castletonGreen,
    },
    [`${mode("residential")}:hover`]: {
      backgroundColor: theme.colors.primary.asidGreen,
    },
    [`${mode("residential")}:disabled`]: {
      opacity: 0.5,
      backgroundColor: theme.colors.primary.softWhite,
      color: "rgba(0, 61, 35, 0.3)",
    }
  }
});

export const header = style({
  display: "flex",
  flexDirection: "column",

  "@media": {
    [breakpoints.tablet]: {
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
    }
  }
});
