"use client";

import React, { useState, useEffect, useRef } from "react";
import { Content } from "@prismicio/client";
import Image from "next/image";
import * as styles from "./RecentWorkSection.css";
import ModeProvider from "@components/ModeProvider";
import {PrismicText} from "@prismicio/react";

// Define the type for the items prop
type RecentWorkSliderProps = {
  items: Content.RecentWorkSlice["items"];
  primary: Content.RecentWorkSlice["primary"];
};

const RecentWorkSlider = ({ items, primary }: RecentWorkSliderProps): JSX.Element => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isMobile, setIsMobile] = useState(false);
  const sliderRef = useRef<HTMLDivElement>(null);

  // Effect to check for mobile view on the client
  useEffect(() => {
    const checkIfMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };
    checkIfMobile();
    window.addEventListener('resize', checkIfMobile);
    return () => window.removeEventListener('resize', checkIfMobile);
  }, []);

  const itemsToShow = isMobile ? 1 : 2.5;
  const maxIndex = Math.max(0, items.length - Math.floor(itemsToShow));

  const handlePrev = () => {
    setCurrentIndex(prev => Math.max(0, prev - 1));
  };

  const handleNext = () => {
    setCurrentIndex(prev => Math.min(maxIndex, prev + 1));
  };

  // Mobile view: a simple image display with controls
  if (isMobile) {
    return (
      <ModeProvider>
        {(mode) => (
          <div>
            <div className={styles.header}>
              <h2 className={styles.title}>
                <PrismicText field={primary.title_1}/>
                <span className={styles.highlight}>
                          <PrismicText field={primary.title_2}/>
                        </span>
                <br/>
                <PrismicText field={primary.title_3}/>
              </h2>
            </div>
            <div className={styles.imageContainer}>
              <Image
                src={items[currentIndex].image.url || ""}
                alt={items[currentIndex].name || ""}
                fill
                className={styles.workImage}
                sizes="100vw"
                priority
              />
              <div className={styles.workName}>
                {items[currentIndex].name}, {items[currentIndex].location}
              </div>
            </div>
            <div className={styles.mobileControls}>
              <button className={styles.controlButton} onClick={handlePrev} disabled={currentIndex === 0} aria-label="Previous">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M15.41 7.41L14 6L8 12L14 18L15.41 16.59L10.83 12L15.41 7.41Z" fill="currentColor"/></svg>
              </button>
              <button className={styles.controlButton} onClick={handleNext} disabled={currentIndex >= maxIndex} aria-label="Next">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M10 6L8.59 7.41L13.17 12L8.59 16.59L10 18L16 12L10 6Z" fill="currentColor"/></svg>
              </button>
            </div>
          </div>
        )}
      </ModeProvider>
    );
  }

  // Desktop view: a draggable-style slider
  return (
    <ModeProvider>
      {(mode) => (
        <>
          <div className={styles.header}>
            <h2 className={styles.title}>
              <PrismicText field={primary.title_1}/>
              <span className={styles.highlight}>
                          <PrismicText field={primary.title_2}/>
                        </span>
              <br/>
              <PrismicText field={primary.title_3}/>
            </h2>

            <div className={styles.controls}>
              <button
                className={styles.controlButton}
                onClick={handlePrev}
                disabled={currentIndex === 0}
                aria-label="Previous slide"
              >
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none"
                     xmlns="http://www.w3.org/2000/svg">
                  <path d="M15.41 7.41L14 6L8 12L14 18L15.41 16.59L10.83 12L15.41 7.41Z"
                        fill="currentColor"/>
                </svg>
              </button>
              <button
                className={styles.controlButton}
                onClick={handleNext}
                disabled={currentIndex >= maxIndex}
                aria-label="Next slide"
              >
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none"
                     xmlns="http://www.w3.org/2000/svg">
                  <path d="M10 6L8.59 7.41L13.17 12L8.59 16.59L10 18L16 12L10 6Z"
                        fill="currentColor"/>
                </svg>
              </button>
            </div>
          </div>
          <div className={styles.container}>
            <div className={styles.sliderContainer}>
              <div
                ref={sliderRef}
                className={styles.slider}
                style={{
                  transform: `translateX(-${currentIndex * (100 / itemsToShow)}%)`
                }}
              >
                {items.map((work) => (
                  <div key={work.image_id} className={styles.slide}>
                    <div className={styles.workCard}>
                      <div className={styles.imageContainer}>
                        <Image
                          src={work.image.url || ""}
                          alt={work.name || ""}
                          fill
                          className={styles.workImage}
                          sizes="(max-width: 768px) 100vw, 33vw"
                        />
                        <div className={styles.workName}>
                          {work.name}, {work.location}
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </>
      )}
    </ModeProvider>
  );
};

export default RecentWorkSlider;