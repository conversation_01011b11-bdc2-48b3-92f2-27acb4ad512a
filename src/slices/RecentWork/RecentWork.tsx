import { Content } from "@prismicio/client";
import { PrismicText, SliceComponentProps } from "@prismicio/react";
import * as styles from "@/slices/RecentWork/RecentWorkSection.css";
import RecentWorkSlider from "./RecentWorkSlider";
import React from "react";

export type RecentWorkProps = SliceComponentProps<Content.RecentWorkSlice>;

/**
 * Component for "RecentWork" Slices.
 */
const RecentWork = ({ slice }: RecentWorkProps): JSX.Element => {
  return (
    <section
      data-slice-type={slice.slice_type}
      data-slice-variation={slice.variation}
    >
      <div className={styles.wrapper}>
        <RecentWorkSlider items={slice.items} primary={slice.primary} />
      </div>
    </section>
  );
};

export default RecentWork;
