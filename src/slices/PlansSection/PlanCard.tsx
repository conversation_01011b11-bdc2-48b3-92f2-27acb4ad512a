"use client";

import MemoCheckMarkIcon from "@/assets/icons/CheckMarkIcon";
import MemoCloseIcon from "@/assets/icons/CloseIcon";
import Accordion from "@/components/Accordion";
import Divider from "@/components/Divider/Divider";
import Toggler from "@/components/Toggler";
import Typography from "@/components/Typography";
import { gridSprinkle } from "@/styles/sprinkles.css";
import { formatNumToGBP } from "@/utils/helpers";
import classNames from "classnames";
import { observer } from "mobx-react-lite";
import { FC, useEffect, useMemo, useState } from "react";
import * as styles from "./PlansSection.css";
import { PeacePlanTypes, Plan, PlanAppliances, SubIntervalTypes } from "@/types/plans";
import { ClientPlanType } from "@/utils/constants";
import GetCoverButton from "@/components/GetCoverButton/GetCoverButton";
import Star from "@/components/Star";
import ModeProvider from "@/components/ModeProvider";
import Tooltip from "@/components/Tooltip";
import MemoInfoOutlineIcon from "@/assets/icons/InfoOutlineIcon";


interface Props {
  serverPlans: Plan
  planType: SubIntervalTypes
  plan: ClientPlanType
}

const PlanCard: FC<Props> = observer(({
  serverPlans,
  plan,
  planType,
}) => {

  const {additionalAppliances: appliances,prices} = serverPlans;


  const [showExclusions, setShowExclusions] = useState(false);

  const [type, setType] = useState<PeacePlanTypes>("homeowner");

  const [selectedAppliances, setSelectedAppliances] = useState<Array<PlanAppliances>>([]);

  const planPrice = () => {
    const basePrice = planType === "month" ? plan.monthPrice : plan.yearPrice;

    if (type === "landlord") {
      return basePrice + selectedAppliances.reduce((accum, appliance) => {
        return accum + appliance.unit_amount;
      }, 0);
    }

    return basePrice;
  };


  const serverPlanByPlanType = useMemo(() => {
    const currentRecurring = planType; 
    return prices.find(({product, recurring}) => {
      const isCorrectPlanType = product.metadata.plan_name === (plan.appliances ? [plan.id, type].join("_") : plan.id);
      const isCorrectRecurring = recurring.inverval === currentRecurring;
      return isCorrectPlanType && isCorrectRecurring;
    });
  }, [planType, prices,type]) ;
  
  // const handleGetPlan = async () => {
  //   // const matchedServerPlans = serverPlans.prices.filter((pl) => pl.product.metadata.plan_name === (appliances ? [plan.id, type].join("_") : plan.id));

  //   const serverPlanByPlanType = prices.find(({recurring}) => recurring.inverval === planType);

  //   if (!serverPlanByPlanType) return;

  //   let basePlanUrl = `/payment/${serverPlanByPlanType.id}?plan=${plan.name}&type=${planType}`;

  //   if (selectedAppliances.length) {
  //     basePlanUrl += `&appliances=${selectedAppliances.map(appl => appl.name).join(",")}`;
  //   }

  //   router.push(basePlanUrl);
  // };

  useEffect(() => {
    setSelectedAppliances([]);
  }, [planType]);

  return (
    <section
      key={plan.name}
      className={classNames(styles.plan, {
        [styles.isHighlighted]: plan.isHighlighted,
      })}
    >
      {!!appliances?.length && !!plan.appliances && (
        <ModeProvider>
          {(mode) => (
            <Toggler
              className={styles.cardToggler}
              wrapperClassname={styles.planBadge}
              onChange={() => setType(type === "homeowner" ? "landlord" : "homeowner")}
              variant={mode === "residential" ? "filledDark" : "filled"}
              checked={type === "landlord"}
              preffix={() => ( 
                <div
                  className={classNames(styles.label, styles.cardLabel, {
                    [styles.activeLabel]: type === "homeowner",
                    [styles.cardActiveLabel]: type === "homeowner"
                  })}
                >
                  <b>Homeowner</b>
                </div>
              )}
              suffix={() => (<div
                className={classNames(styles.label, styles.cardLabel, {
                  [styles.activeLabel]: type === "landlord",
                  [styles.cardActiveLabel]: type === "landlord"
                })}
                             >
                <b>Landlord</b>
              </div>
              )}
            />
          )}
        </ModeProvider>
      )}
      <Typography
        className={styles.planTitle}
        variant="h5"
      >
        {plan.name}
      </Typography>
      <div
        className={styles.planPrice}
      >
        <Typography
          variant="subTitleMedium"
        >
          <b>{formatNumToGBP(planPrice() / 100)}</b>
          <Typography
            as="span"
          >
            / per {planType}
          </Typography>
          {plan.isHighlighted && (
            <Typography
              className={styles.planNote}
              variant="note"
            >
              <Star
                filled
              />
              Most Popular & Best Value
            </Typography>
          )}
        </Typography>
      </div>
      <ModeProvider>
        {(mode) => (
          <GetCoverButton
            planName={plan.id}
            recurringInterval={serverPlanByPlanType?.recurring.inverval}
            activeAppliances={selectedAppliances.map(({name}) => name )}
            planId={serverPlanByPlanType?.id}
            className={styles.planAction}
            color={mode === "residential" && plan.isHighlighted ? "secondary" : "primary"}
            alternateHover
          />
        )}
      </ModeProvider>
      <Divider
        className={styles.planDivider}
      />
      {appliances && (
        <Accordion
          isOpen={type === "landlord"}
        >
          <Typography
            variant="bodySmall"
            className={styles.planSubTitle}
          >
            <b>Additional appliances</b>
            <Tooltip
              arrow
              variant="tertiary"
              on={["click", "focus"]}
              trigger={(
                <div
                  className={styles.tootlipTrigger}
                >
                  <MemoInfoOutlineIcon />
                </div>
              )}
            >
              <Typography
                as={"p"}
                variant="note"
              >
            Additional appliances will<br/>
add additional fee to the<br/>
monthly package cost. <br/>
Each appliance adds <b>£2.50</b><br/>
 onto the monthly plan.
              </Typography>
            </Tooltip>
          
          </Typography>
          <ul
            className={styles.planList}
          >
            {appliances.filter((appliance) => appliance.recurring_interval === planType).map((appliance) => {
              const isActive = selectedAppliances.includes(appliance);
              return (
                <li
                  key={appliance.name}
                  className={classNames(styles.planListItem, gridSprinkle({ alignItems: "center" }))}
                >
                  <Toggler
                    variant="filledDifferActiveState"
                    checked={isActive}
                    onChange={() => {
                      if (isActive) setSelectedAppliances(selectedAppliances.filter((appl) => appl !== appliance));
                      else setSelectedAppliances([...selectedAppliances, appliance]);
                    }}
                  />
                  <Typography
                    variant="buttonSmall"
                  >
                    {appliance.name.replaceAll("_", " ")}
                  </Typography>
                  <Typography
                    className={classNames(styles.planAppliancePrice, {
                      [styles.isActive]: isActive
                    })}
                    variant="buttonSmall"
                  >
                  + {formatNumToGBP(appliance.unit_amount / 100)}
                  </Typography>
                </li>
              );})}
          </ul>
        </Accordion>
      )}
      <ul
        className={styles.planList}
      >
        {plan.features.map((feature) => (
          <li
            key={feature}
            className={styles.planListItem}
          >
            <MemoCheckMarkIcon />
            <Typography
              variant="buttonSmall"
            >
              {feature}
            </Typography>
          </li>
        ))}
      </ul>
      <Accordion
        isOpen={showExclusions}
      >
        <ul
          className={classNames(styles.planList, styles.isDisabled)}
        >
          {plan.exclusions.map((exclusion) => (
            <li
              key={exclusion}
              className={styles.planListItem}
            >
              <MemoCloseIcon />
              <Typography
                variant="buttonSmall"
              >
                {exclusion}
              </Typography>
            </li>
          ))}
        </ul>
      </Accordion>
      <button
        onClick={() => setShowExclusions(!showExclusions)}
        className={styles.planLink}
      >
        {showExclusions ? "Hide" : "Show"} Exclusions
      </button>
    </section>
  );
});
  

export default PlanCard;