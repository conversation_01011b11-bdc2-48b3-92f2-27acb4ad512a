"use client";

import Container from "@/components/Container";
import Toggler from "@/components/Toggler";
import Typography from "@/components/Typography";
import classNames from "classnames";
import { PrismicRichText } from "node_modules/@prismicio/react/dist/react-server/PrismicRichText";
import { FC, useCallback, useMemo, useState } from "react";
import PlanCard from "./PlanCard";
import {  PlansSectionProps } from "./PlansSection";
import * as styles from "./PlansSection.css";
import {  Plan, SubIntervalTypes } from "@/types/plans";
import { PLANS } from "@/utils/constants";
import ModeProvider from "@/components/ModeProvider";
import { SliceComponentProps } from "@prismicio/react";
import { Content } from "@prismicio/client";

type PlansProps = {
  plans: Plan 
} & Omit<SliceComponentProps<Content.PlansSectionSlice>, "context">

export const Plans: FC<PlansProps> = ({
  plans: serverPlans,
  slice
}) => {
  const [type, setType] = useState<SubIntervalTypes>("month");


  const isYearly = useMemo(() => type === "year" , [type]);

  const onIntervalChange = useCallback(
    (checked: boolean) => {
      setType( checked ? "year" : "month"); 
    },
    [],
  );
  


  return (
    <Container
      notFullHeight
      className={styles.container}
    > 
      <section
        className={styles.root}
        data-slice-type={slice.slice_type}
        data-slice-variation={slice.variation}
      >
        <div
          className={styles.info}
        >
          <Typography
            className={styles.title}
            variant="h2"
          >
            <PrismicRichText
              field={slice.primary.title}
            />
          </Typography>              
          <Typography
            className={styles.description}
            variant="bodySmall"
          >
            <PrismicRichText
              field={slice.primary.description}
            />
          </Typography> 
         
           
          <ModeProvider>
            {(mode) => (
              <Toggler
                className={styles.plansToggler}
                size="big"
                variant={mode === "residential" ? "residential" : "filledDarkDifferActiveState"}
                color={mode === "residential" ? "primary" : "secondary"}
                checked={isYearly}
                wrapperClassname={styles.plansToggle}
                onChange={onIntervalChange}
                preffix={(isActive) => (
                  <Typography
                    variant="bodySmall"
                    className={classNames(styles.label, {
                      [styles.activeLabel]: !isActive
                    })}
                  >
                    <b
                      className={styles.labelText}
                    >Pay Monthly</b>
                  </Typography>
                )}
                suffix={(isActive) => (
                  <Typography
                    variant="bodySmall"
                    className={classNames(styles.label, {
                      [styles.activeLabel]: isActive
                    })}
                  >
                    <b
                      className={styles.labelText}
                    >Pay Yearly</b>
                    <div
                      className={styles.labelBadge}
                    >
                      <Typography
                        variant="note"
                      >
                    5% Off
                      </Typography>              
                    </div>
                  </Typography>
                )}
               
              />
            )}
          </ModeProvider>
          
        </div>
        <div
          className={styles.plans}
        >
          {PLANS.map((plan) => (
            <PlanCard
              key={plan.name}
              serverPlans={serverPlans}
              planType={type}
              plan={plan}
            />
          ))}
        </div>
      </section>
    </Container>
  );
};

export default Plans;