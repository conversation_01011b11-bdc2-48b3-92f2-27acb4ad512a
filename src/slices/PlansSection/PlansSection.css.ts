import { opacity } from "@/components/Divider/Divider.css";
import { breakpoints } from "@/styles/constants.css";
import { mode, modeGlobal } from "@/styles/functions.css";
import { theme } from "@/styles/themes.css";
import { globalStyle, style } from "@vanilla-extract/css";

export const container = style({
  scrollSnapAlign: "start",

  selectors: {
    [mode("residential")]: {
      backgroundColor: theme.colors.primary.castletonGreen,
    }
  },
});

export const root = style(
  {
    padding: "40px 20px 20px",

    "@media": {
      [breakpoints.tablet]: {
        padding: "72px 48px 0",
      }
    },
  }
);

export const title = style({
  fontFamily: theme.fonts.primary,
  marginBottom: "24px",

  "@media": {
    [breakpoints.tablet]: {
      marginBottom: 48,
    }
  },

  selectors: {
    [mode("residential")]: {
      color: theme.colors.primary.softWhite,
    }
  },
});

globalStyle(`${title} p`, {
  margin: 0,
});

export const description = style({
  marginBottom: "45px",
  flex: "1 1",
  maxWidth: 420,

  "@media": {
    [breakpoints.tablet]: {
      marginBottom: 48,
    }
  },

  selectors: {
    [mode("residential")]: {
      color: theme.colors.primary.softWhite,
      opacity: 0.8,
    }
  },
});

export const info = style(
  {
    display: "flex",
    flexDirection: "column",
    alignItems: "center",
    textAlign: "center",
  }
);

export const plansToggle = style({
  display: "flex",
  alignItems: "center",
  gap: 24,
  marginBottom: 32,

  "@media": {
    [breakpoints.tablet]: {
      marginBottom: 48,
    }
  }
});

export const plans = style({
  display: "flex",
  flexWrap: "wrap",
  gap: 24,
  alignItems: "flex-start",
});

export const isHighlighted = style({

});

export const plan = style({
  position: "relative",
  display: "flex",
  alignItems: "center",
  flexDirection: "column",
  backgroundColor: theme.colors.primary.softWhite,
  borderRadius: 16,
  flex: "1 1",
  padding: "40px 20px",

  selectors: {
    [`${isHighlighted}&`]: {
      marginTop: 16,
      backgroundColor: theme.colors.primary.castletonGreen,
      color: theme.colors.primary.softWhite,
    },
    [`${modeGlobal("residential")} ${isHighlighted}&`]: {
      backgroundColor: theme.colors.primary.asidGreen,
      color: theme.colors.primary.castletonGreen,
    }
  },

  "@media": {
    [breakpoints.tablet]: {
      marginTop: "0 !important",
      alignItems: "stretch",
      padding: "48px 32px",
    }
  },
});

export const planTitle = style({
  fontFamily: theme.fonts.primary,
  marginBottom: 16,

  "@media": {
    [breakpoints.tablet]: {
      marginBottom: 24,
    }
  },
});

export const planPrice = style({
  marginBottom: 24,
  position: "relative",

  selectors: {
    [`${isHighlighted} &`]: {
      marginBottom: 48,
    },
  },

  "@media": {
    [breakpoints.tablet]: {
      marginBottom: 48,
    }
  },
});

export const planAction = style({
  width: "100%",
});

export const planDivider = style({
  margin: "24px 0",

  selectors: {
    [`${isHighlighted} &`]: {
      opacity: 0.2,
    }
  },

  "@media": {
    [breakpoints.tablet]: {
      margin: "32px 0",
    }
  },
});

export const isDisabled = style({

});

export const planList = style({
  display: "flex",
  flexDirection: "column",
  gap: 12,
  padding: 0,
  listStyle: "none",
  marginBottom: 32,

  selectors: {
    [`${isDisabled}&`]: {
      color: theme.colors.grayscale[200],
    },
    [mode("residential")]: {
      color: theme.colors.primary.castletonGreen,
    },
    [`${modeGlobal("residential")} ${isDisabled}&`]: {
      color: theme.colors.primary.castletonGreen,
      opacity: 0.5,
    },
  },

  "@media": {
    [breakpoints.tablet]: {
      gap: 14,
    }
  },
});

export const planListItem = style({
  textTransform: "capitalize",
  display: "flex",
  gap: 10,
  margin: 0,
});

export const planNote = style({
  whiteSpace: "nowrap",
  fontWeight: 400,
  display: "flex",
  gap: 4,
  color: theme.colors.primary.asidGreen,
  position: "absolute",
  top: "calc(100% + 12px)",
  left: 0,
  right: 0,

  selectors: {
    [mode("residential")]: {
      color: theme.colors.primary.castletonGreen,
    }
  },
});

export const planLink = style({
  cursor: "pointer",
  backgroundColor: "transparent",
  color: "inherit",
  border: "none",
  font: "inherit",
  fontSize: 18,
  borderBottom: "1px solid currentColor",
  width: "max-content",
  padding: 0,
  margin: 0,
  fontWeight: 500,
  marginRight: "auto",
});

export const planSubTitle = style({
  marginBottom: 8,
  display: "flex",
  columnGap: 8,
  alignItems: "center"
});

export const planBadge = style({
  cursor: "pointer",
  userSelect: "none",
  borderRadius: 30,
  backgroundColor: theme.colors.primary.castletonGreenPressed,
  minHeight: 44,
  padding: "0 18px",
  display: "flex",
  alignItems: "center",
  justifyContent: "center",
  gap: 10,
  position: "absolute",
  top: 0,
  left: 32,
  right: 32,
  transform: "translateY(-50%)",

  selectors: {
    [mode("residential")]: {
      backgroundColor: theme.colors.primary.asidGreen,
      border: `2px solid ${theme.colors.primary.castletonGreen}`,
    }
  },
});

export const label = style({
  position: "relative",
  color: theme.colors.grayscale[200],

  selectors: {
    [mode("residential")]: {
      color: theme.colors.primary.softWhite,
    }
  },
});

globalStyle(`${label} b, ${planPrice} b`, {
  fontWeight: 500,
});

export const labelText = style({
  selectors: {
    [mode("residential")]: {
      color: theme.colors.primary.softWhite,
      opacity: 0.6,
    }
  },
});

export const activeLabel = style({
  color: "currentColor",

  selectors: {
    [mode("residential")]: {
      color: theme.colors.primary.softWhite,
      opacity: 1,
    }
  },
});

globalStyle(`${modeGlobal("residential")} ${activeLabel} ${labelText}`, {
  opacity: 1,
});

export const isActive = style({});

export const planAppliancePrice = style({
  marginLeft: "auto",
  opacity: 0.8,

  selectors: {
    [`${isActive}&`]: {
      color: theme.colors.primary.asidGreen,
      opacity: 1,
    },
    [`${modeGlobal("residential")} ${isActive}&`]: {
      color: theme.colors.primary.castletonGreen,
      opacity: 1,
    }
  }
});

export const labelBadge = style({
  minHeight: 26,
  padding: "0 10px",
  borderRadius: 26,
  backgroundColor: theme.colors.primary.asidGreen,
  color: theme.colors.primary.castletonGreenPressed,
  position: "absolute",
  left: "calc(100% - 16px)",
  top: "calc(-100% - 8px)",
  whiteSpace: "nowrap",
  display: "flex",
  alignItems: "center",

  "@media": {
    [breakpoints.tablet]: {
      left: "calc(100% + 8px)",
      top: 0,
      bottom: 0,
      margin: "auto 0",
    }
  },
});

export const cardToggler = style({
  selectors: {
    [mode("residential")]: {
      backgroundColor: "rgba(0, 61, 35, 0.16)",
      color: theme.colors.primary.castletonGreen,
    }
  },
});

export const cardLabel = style({
  selectors: {
    [mode("residential")]: {
      color: theme.colors.primary.castletonGreen,
      opacity: 0.6,
    }
  },
});

export const cardActiveLabel = style({
  selectors: {
    [mode("residential")]: {
      color: theme.colors.primary.castletonGreen,
      opacity: 1,
    }
  },
});

export const plansToggler = style({
  selectors: {
    [mode("residential")]: {
      color: theme.colors.primary.softWhite,
    }
  },
});


export const tootlipTrigger =  style({
  color: theme.colors.primary.asidGreen,
  cursor: "help",
  fontSize: 18,
  display: "flex",
  selectors: {
    [mode("residential")]: {
      color: theme.colors.primary.castletonGreen,
    }
  },
});