{"id": "plans_section", "type": "SharedSlice", "name": "PlansSection", "description": "PlansSection", "variations": [{"id": "default", "name": "<PERSON><PERSON><PERSON>", "docURL": "...", "version": "initial", "description": "<PERSON><PERSON><PERSON>", "imageUrl": "", "primary": {"title": {"type": "StructuredText", "config": {"label": "Title", "placeholder": "", "allowTargetBlank": true, "multi": "paragraph,preformatted,heading1,heading2,heading3,heading4,heading5,heading6,strong,em,hyperlink,image,embed,list-item,o-list-item,rtl"}}, "description": {"type": "StructuredText", "config": {"label": "Description", "placeholder": "", "allowTargetBlank": true, "multi": "paragraph,preformatted,heading1,heading2,heading3,heading4,heading5,heading6,strong,em,hyperlink,image,embed,list-item,o-list-item,rtl"}}}, "items": {"plan_name": {"type": "Text", "config": {"label": "Plan Name", "placeholder": ""}}, "plan_price": {"type": "StructuredText", "config": {"label": "Plan Price", "placeholder": "", "allowTargetBlank": true, "multi": "paragraph,preformatted,heading1,heading2,heading3,heading4,heading5,heading6,strong,em,hyperlink,image,embed,list-item,o-list-item,rtl"}}, "plan_benefits": {"type": "Text", "config": {"label": "Plan Benefits", "placeholder": ""}}, "plan_link": {"type": "Link", "config": {"label": "Plan Link", "placeholder": "", "select": null}}, "plan_link_text": {"type": "Text", "config": {"label": "Plan Link Text", "placeholder": ""}}}}]}