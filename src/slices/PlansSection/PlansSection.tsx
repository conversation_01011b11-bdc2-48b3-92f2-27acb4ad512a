import { Content } from "@prismicio/client";
import { SliceComponentProps } from "@prismicio/react";
import Plans from "./Plans";
import { SlicesContextData } from "@/types/common";

export type PlansSectionProps =
  SliceComponentProps<Content.PlansSectionSlice> & {
    context: SlicesContextData;
  };

const PlansSection = ({
  context,
  ...props
}: PlansSectionProps): JSX.Element => {
  const { plans } = context[props.slice.slice_type];
  if (!plans.data) return <></>;

  return (
    <>
      <Plans
        plans={plans.data}
        {...props}
      />
    </>
  );
};

export default PlansSection;
