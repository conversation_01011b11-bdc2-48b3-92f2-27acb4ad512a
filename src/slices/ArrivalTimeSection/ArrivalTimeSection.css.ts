import { bodyMedium, bodySmall } from "@/components/Typography/Typography.css";
import { breakpoints } from "@/styles/constants.css";
import { mode, modeGlobal } from "@/styles/functions.css";
import { theme } from "@/styles/themes.css";
import { globalStyle, style, styleVariants } from "@vanilla-extract/css";

export const container = style({
  selectors: {
    [mode("residential")]: {
      backgroundColor: theme.colors.primary.castletonGreen,
    }
  },
});

export const root = style({
  borderRadius: 24,
  padding: "40px 20px 56px 20px",
  "@media": {
    [breakpoints.tablet]: {
      padding: "72px 20px",
    }
  },
  background: theme.colors.primary.castletonGreen,
  display: "grid",
  justifyContent: "center",
  justifyItems: "center",
  height: "max-content",

  selectors: {
    [mode("residential")]: {
      background: theme.colors.primary.ivory,
    }
  },
});

export const inputWrapper = style({
  display: "flex",
  columnGap: 2,
  marginTop: 72,
  maxWidth: 652,
  width: "100%",
});

export const searchButtonIcon = style({
  fontSize: 24,
});

export const title = style({
  color: theme.colors.primary.ivory,
  textAlign: "center",

  selectors: {
    [mode("residential")]: {
      color: theme.colors.primary.castletonGreen,
    }
  },
});

globalStyle(`${title} em`, {
  color: theme.colors.primary.asidGreen,
});

globalStyle(`${modeGlobal("residential")} ${title} em`, {
  color: theme.colors.primary.castletonGreen,
});

export const divLine = style({
  borderLeft: `1px solid ${theme.colors.grayscale[100]}`,
  display: "none",
  "@media": {
    [breakpoints.tablet]: {
      display: "block"
    }
  }
});

export const timeContainer = style({
  display: "flex",
  columnGap: 24,
  rowGap: 18,
  marginTop: 40,
  flexDirection: "column",
  "@media": {
    [breakpoints.tablet]: {
      flexDirection: "row",
    }
  }
  
});

const timeWrapperBase = style({
  display: "flex",
  alignItems: "center",
  rowGap: 14,
  columnGap: 8,
  flexDirection: "row",
  "@media": {
    [breakpoints.tablet]: {
      flexDirection: "column",
    }
  }
});

export const timeWrapper = styleVariants({
  members: [timeWrapperBase, {
    color: theme.colors.primary.asidGreen,
    [mode("residential")]: {
      color: theme.colors.primary.castletonGreen,
    }
  }],
  nonMembers: [timeWrapperBase, {
    color: theme.colors.primary.ivory,
    [mode("residential")]: {
      color: theme.colors.grayscale[200],
    }
  }],

});

export const timeTitle = style([bodyMedium,{
  display: "flex",  
  justifyContent: "center",
  columnGap: 8,

}]);

export const memberButton = style({
  marginTop: 56,
  width: "100%",
  display: "grid",
  "@media": {
    [breakpoints.tablet]: {
      width: "auto",
    }
  }

});
export const buttonDesc = style({
  color: theme.colors.primary.ivory,
  marginTop: 16,

  selectors: {
    [mode("residential")]: {
      color: theme.colors.primary.castletonGreen,
    }
  },
});

export const input = style([bodySmall, {
  borderRadius: 8,
  padding: "20px 21.5px",
  width: "100%",
  border: 0,
  background: theme.colors.primary.ivory,
  selectors: {
    ["&:placeholder"]: {
      color: theme.colors.grayscale[200]
    },
    ["&:focus-visible"]: {
      outline: 0
    },
  },
}]);

export const inputResidentialMode = style({
  selectors: {
    [mode("residential")]: {
      border: "1px solid " + theme.colors.grayscale[200],
      borderRadius: 8,
    }
  },
});

export const loaderWrapper = style({
  color: theme.colors.primary.ivory,
  fontSize: 120,
  margin: "72px auto 0 auto",
  [mode("residential")]: {
    color: theme.colors.primary.castletonGreen,
  }
});