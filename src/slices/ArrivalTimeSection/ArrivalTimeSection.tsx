"use client";
import Container from "@/components/Container";
import { Content } from "@prismicio/client";
import { SliceComponentProps } from "@prismicio/react";
import classNames from "classnames";

import MemoSearchIcon from "@/assets/icons/SearchIcon";
import MemoSquareTimeIcon from "@/assets/icons/SquareTimeIcon";
import Button from "@/components/Button";
import IconButton from "@/components/IconButton";
import Loader from "@/components/Loader";
import TextInput from "@/components/TextInput";
import Typography from "@/components/Typography";
import { POST_CODE_INPUT_PROPS } from "@/utils/constants";
import Link from "next/link";
import { PrismicRichText } from "node_modules/@prismicio/react/dist/react-server/PrismicRichText";
import React, { FormEventHandler, useCallback, useState } from "react";
import * as styles from "./ArrivalTimeSection.css";

export type ArrivalTimeSectionProps =
  SliceComponentProps<Content.ArrivalTimeSectionSlice>;

const isDisabledForm = (value: string) => value.length < 3 || value.length > 7;

const ArrivalTimeSection = ({
  slice,
}: ArrivalTimeSectionProps): JSX.Element => {

  const [isInitialized, setIsInitialized] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [inputValue, setInputValue] = useState("");
  
  
  const onInputChange = useCallback<React.ChangeEventHandler<HTMLInputElement>>(
    (e) => {
      setInputValue(e.currentTarget.value);
    },
    [],
  );
      
      
  const onSubmit = useCallback<FormEventHandler<HTMLFormElement> >(
    (e) => {
      e.preventDefault();
      if(isDisabledForm(inputValue)) return;
      setIsLoading(true);
      setTimeout(() => {
        setIsLoading(false);
      }, (slice.primary.wait_time || 1000) as number);
      if(!isInitialized) {
        setIsInitialized(true);
      }
    },
    [isInitialized, inputValue],
  );
  

  return (
    <Container
      className={styles.container}
    >
      <section
        className={classNames(styles.root)}
        data-slice-type={slice.slice_type}
        data-slice-variation={slice.variation}
      >
        <Typography
          variant="h3"
          className={styles.title}
          fontFamily="primary"
        >
          <PrismicRichText
            field={slice.primary.title}
          />
        </Typography>
        <form
          noValidate
          onSubmit={onSubmit}
          className={styles.inputWrapper}
        >
          <TextInput
            {...POST_CODE_INPUT_PROPS}
            label=""
            variant="filled"
            value={inputValue}
            onChange={onInputChange}
            inputClassname={styles.inputResidentialMode}
          />
          <IconButton
            disabled={isDisabledForm(inputValue)}
            color="secondary"
            shape="box"
            type="submit"
          >
            <MemoSearchIcon
              className={styles.searchButtonIcon}
            />
          </IconButton>
        </form>
        {isLoading ? (
          <div
            className={styles.loaderWrapper}
          >
            <Loader/>
          </div>
        ) : isInitialized && (<>
          <div
            className={styles.timeContainer}
          >
            <div
              className={styles.timeWrapper.members}
            >
              <div
                className={styles.timeTitle}
              >
                <MemoSquareTimeIcon/>
            Members wait time:
              </div>
              <Typography
                variant="subTitleMedium"
              >{slice.primary.member_wait_time}</Typography>
            </div>
            <div
              className={styles.divLine}
            />
            <div
              className={styles.timeWrapper.nonMembers}
            >
              <div
                className={styles.timeTitle}
              >
                <MemoSquareTimeIcon/>
                Non-members wait time:
              </div>
              <Typography
                variant="subTitleMedium"
              >{slice.primary.non_member_wait_time}</Typography>
            </div>
          </div>
          <div
            className={styles.memberButton}
          >
            <Button
              isAnimated
              as={Link}
              href="/register"
            >
            Become a member
            </Button>
          </div>
          <Typography
            className={styles.buttonDesc}
            variant="note"
          >
          Becoming a member is free and easy!
          </Typography>
        </>)}
      </section>
    </Container>
  );
};

export default ArrivalTimeSection;
