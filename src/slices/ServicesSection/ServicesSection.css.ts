import { breakpoints } from "@/styles/constants.css";
import { theme } from "@/styles/themes.css";
import { style } from "@vanilla-extract/css";
import { mode } from "@/styles/functions.css";

export const wrapper = style({
  backgroundColor: theme.colors.primary.castletonGreen,
  padding: "20px",
  display: "flex",
  justifyContent: "center",

  "@media": {
    [breakpoints.tablet]: {
      padding: "40px",
    },
    [breakpoints.desktop]: {
      padding: "72px 48px",
    }
  },

  selectors: {
    [mode("commercial")]: {
      backgroundColor: theme.colors.primary.ivory,
    }
  }
});

export const grid = style({
  display: "grid",
  gridTemplateColumns: "1fr",
  gap: "16px",
  maxWidth: "1200px",
  margin: "0 auto",
  width: "100%",
  justifyContent: "center",

  "@media": {
    [breakpoints.tablet]: {
      gridTemplateColumns: "1fr 1fr",
      gap: "20px",
      maxWidth: "800px",
    },
    [breakpoints.desktop]: {
      gridTemplateColumns: "repeat(4, 1fr)",
      gap: "24px",
      maxWidth: "1200px",
    }
  }
});

export const card = style({
  backgroundColor: theme.colors.primary.softWhite,
  borderRadius: "16px",
  padding: "16px",
  display: "flex",
  flexDirection: "column",
  height: "100%",
  width: "100%",
  margin: "0 auto",

  "@media": {
    [breakpoints.tablet]: {
      padding: "20px",
      maxWidth: "340px",
    },
    [breakpoints.desktop]: {
      padding: "auto",
      maxWidth: "auto",
    }
  },

  selectors: {
    [mode("commercial")]: {
      backgroundColor: theme.colors.primary.softWhite,
    }
  }
});

export const cardIcon = style({
  color: theme.colors.primary.castletonGreen,
  marginRight: "8px",
  width: "24px",
  height: "24px",
  flexShrink: 0,
});

export const titleWrapper = style({
  display: "flex",
  marginBottom: "16px",
});

export const cardTitle = style({
  color: theme.colors.primary.castletonGreen,
  fontWeight: 600,
  fontSize: "24px",
  marginBottom: "8px",
});

export const cardDescription = style({
  color: theme.colors.primary.castletonGreen,
  fontSize: "16px",
  lineHeight: 1.2,
  marginBottom: "18px",
  fontWeight: 400,
  flexGrow: 1,
});

export const cardButton = style({
  backgroundColor: theme.colors.primary.asidGreen,
  color: theme.colors.primary.castletonGreen,
  borderRadius: "100px",
  padding: "12px 24px",
  textAlign: "center",
  fontWeight: 500,
  border: "none",
  cursor: "pointer",
  textDecoration: "none",
  display: "flex",
  alignItems: "center",
  justifyContent: "center",
  height: "48px",
  width: "80%", // Reduced width to allow centering
  margin: "0 auto", // Center the button horizontally
  transition: "background-color 0.2s ease",
  fontSize: "18px",

  ":hover": {
    backgroundColor: theme.colors.primary.asidGreen,
  },

  "@media": {
    [breakpoints.desktop]: {
      fontSize: "20px",
      padding: "24px",
      width: "260px",
      height: "56px"
    }
  }

});
