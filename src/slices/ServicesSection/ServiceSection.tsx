"use client"
import { Content } from "@prismicio/client";
import { SliceComponentProps } from "@prismicio/react";
import ModeProvider from "@components/ModeProvider";
import * as styles from "./ServicesSection.css";
import BookServiceButton from "@components/BookServiceButton";
import Link from "next/link";
import React from "react";
import {HEADER_PHONE_NUMBER} from "@/utils/constants";
import Button from "@components/Button";

/**
 * Props for `ServicesSection`.
 */
export type ServicesSectionProps =
  SliceComponentProps<Content.ServicesSectionSlice>;

const ServicesSection = ({ slice }: ServicesSectionProps): JSX.Element => {
  return (
    <section
      data-slice-type={slice.slice_type}
      data-slice-variation={slice.variation}
    >
      <ModeProvider>
        {(mode) => (
          <div className={styles.wrapper}>
            <div className={styles.grid}>
              {slice.items.map((service) => (
                <div key={service.service_id} className={styles.card}>
                  <div className={styles.titleWrapper}>
                    <svg className={styles.cardIcon} width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M12 2L4 5V11.09C4 16.14 7.41 20.85 12 22C16.59 20.85 20 16.14 20 11.09V5L12 2ZM18 11.09C18 15.09 15.45 18.79 12 19.92C8.55 18.79 6 15.1 6 11.09V6.39L12 4.14L18 6.39V11.09Z" fill="currentColor"/>
                      <path d="M12 13L9 10L7.59 11.41L12 15.83L16.41 11.41L15 10L12 13Z" fill="currentColor"/>
                    </svg>
                    <h3 className={styles.cardTitle}>{service.title}</h3>
                  </div>
                  <p className={styles.cardDescription}>{service.description}</p>
                  {service.service_id === 'heat-pumps' || service.service_id === 'boiler-service' ? (
                    <BookServiceButton isAnimated={false} className={styles.cardButton}>
                      {service.buttontext}
                    </BookServiceButton>
                  ) : (
                    <Button
                      as={Link}
                      href={service.service_id === "emergencies"
                        ? `tel:${HEADER_PHONE_NUMBER}`
                        :service.buttonlink || ""
                      }
                      className={styles.cardButton}
                    >
                      {service.buttontext}
                    </Button>
                  )}
                </div>
              ))}
            </div>
          </div>
        )}
      </ModeProvider>
    </section>
  );
};

export default ServicesSection;
