import { Content } from "@prismicio/client";
import { SliceComponentProps } from "@prismicio/react";

/**
 * Props for `BlockWithTitleAndContent`.
 */
export type BlockWithTitleAndContentProps =
  SliceComponentProps<Content.BlockWithTitleAndContentSlice>;

/**
 * Component for "BlockWithTitleAndContent" Slices.
 */
const BlockWithTitleAndContent = ({
  slice,
}: BlockWithTitleAndContentProps): JSX.Element => {
  return (
    <section
      data-slice-type={slice.slice_type}
      data-slice-variation={slice.variation}
    >
      Placeholder component for block_with_title_and_content (variation:{" "}
      {slice.slice_type}) Slices
    </section>
  );
};

export default BlockWithTitleAndContent;
