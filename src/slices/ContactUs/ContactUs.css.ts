import { breakpoints } from "@/styles/constants.css";
import { theme } from "@/styles/themes.css";
import { style } from "@vanilla-extract/css";

export const container = style({
  position: "relative",
  overflow: "hidden",
  marginBottom: -10,

  "@media": {
    [breakpoints.tablet]: {
      paddingTop: 122,
      marginBottom: -20,
    },
  },
});

export const root = style({
  display: "flex",
  flexDirection: "column",
  alignItems: "center",
  paddingBottom: 10,
  paddingTop: 24,

  "@media": {
    [breakpoints.tablet]: {
      paddingBottom: 140,
    },
  },
});

export const title = style({
  marginBottom: 32,

  "@media": {
    [breakpoints.tablet]: {
      marginBottom: 40,
      maxHeight: "56px",
    },
  },
});

export const description = style({
  marginBottom: 32,

  "@media": {
    [breakpoints.tablet]: {
      marginBottom: 48,
      maxWidth: 589,
      textAlign: "center",
    },
  },
});

export const form = style({
  position: "relative",
  zIndex: 2,
  display: "flex",
  flexDirection: "column",
  gap: 24,
  maxWidth: "100%",
  width: "100%",
  padding: "24px 20px",
  backgroundColor: theme.colors.primary.softWhite,
  borderRadius: 16,

  "@media": {
    [breakpoints.tablet]: {
      zIndex: "initial",
      borderRadius: 24,
      padding: "56px 48px",
      width: "100%",
      maxWidth: 640,
      gap: 32,
    },
  },
});

export const fieldsContainer = style({
  display: "flex",
  flexDirection: "column",
  rowGap: 30,
  columnGap: 14,
  "@media": {
    [breakpoints.tablet]: {
      position: "relative",
      zIndex: 5,
      columnGap: 16,
      rowGap: 30,
    },
  },
});

export const field = style({
  position: "relative",
  zIndex: 1,
});

export const mobileAddressContainer = style({
  display: "flex",
  flexDirection: "column",
  columnGap: 14,
  rowGap: 28,

  "@media": {
    [breakpoints.tablet]: {
      flexDirection: "row",
      rowGap: 24,
    },
  },
});

export const submitButton = style({
  position: "relative",
  zIndex: 3,
  width: "100%",
});

const imageWr = style({
  position: "absolute",
  overflow: "hidden",
  zIndex: 0,

  "@media": {
    [breakpoints.tablet]: {
      zIndex: 2,
    },
  },
});

export const leftImageWr = style([
  imageWr,
  {
    position: "relative",
    maxWidth: "100%",
    width: "100%",
    height: 536,
    marginBottom: -123,

    "@media": {
      [breakpoints.tablet]: {
        position: "absolute",
        maxWidth: "initial",
        width: 888,
        height: 888,
        left: -267,
        bottom: -35,
        marginBottom: "initial",
      },
    },
  },
]);

export const rightImageWr = style([
  imageWr,
  {
    display: "none",

    "@media": {
      [breakpoints.tablet]: {
        display: "block",
        width: 425,
        height: 848,
        right: 0,
        bottom: 0,
      },
    },
  },
]);

const image = style({
  display: "block",
  width: "100%",
  height: "100%",
  objectFit: "cover",
});

export const leftImage = style([
  image,
  {
    position: "absolute",
    width: "100%",
    height: "100%",

    "@media": {
      [breakpoints.tablet]: {
        width: 888,
        height: 888,
      },
    },
  },
]);

export const rightImage = style([image]);

export const successModal = style({
  width: "100%",
  maxWidth: 476,
});

export const successContent = style({
  display: "grid",
  rowGap: 40,
  width: "100%",
  maxWidth: 476,
  boxSizing: "border-box",

  "@media": {
    [breakpoints.tablet]: {
      minWidth: 380,
    },
  },
});

export const okButton = style({
  maxWidth: 163,
  marginTop: 48,
});
