"use client";

import Container from "@/components/Container";
import { Content } from "@prismicio/client";
import { PrismicImage, SliceComponentProps } from "@prismicio/react";
import * as styles from "./ContactUs.css";
import Typography from "@/components/Typography";
import { PrismicRichText } from "node_modules/@prismicio/react/dist/react-server/PrismicRichText";
import TextInput from "@/components/TextInput";
import { EMAIL_INPUT_PROPS, FULL_NAME_INPUT_PROPS, PHONE_NUMBER_INPUT_PROPS, PHONE_NUMBER_CONTROLLER_RULES, FULL_NAME_CONTROLLER_RULES, EMAIL_CONTROLLER_RULES, ADDRESS_INPUT_PROPS, ADDRESS_CONTROLLER_RULES, ONLY_UPPERCASE_AND_LOWERCASE_CONTROLLER_RULES } from "@/utils/constants";
import Button from "@/components/Button";
import { Controller, useForm } from "react-hook-form";
import { ContactUsFormValuesType } from "./ContactUs.types";
import { useCallback, useState } from "react";
import { createClient } from "@/utils/supabase/client";
import Modal from "@/components/Modal";
import useStore from "@/hooks/useStore";
import { PatternFormat } from "react-number-format";

export type ContactUsProps = SliceComponentProps<Content.ContactUsSlice>;

const ContactUs = ({ slice }: ContactUsProps): JSX.Element => {
  const { alert } = useStore();
  const [successModalOpened, setSuccessModalOpened] = useState(false);

  const form = useForm<ContactUsFormValuesType>({
    mode: "onBlur",
    defaultValues: {
      fullName: "",
      Email: "",
      mobileNumber: "",
      address: "",
      businessName: "",
      businessNature: "",
      buildingType: "",
    }
  });

  const onSubmit = useCallback(
    async (body: ContactUsFormValuesType) => {
      try {
        const supabase = createClient();

        const response = await supabase.functions.invoke(
          "zoho_api/commercial/contact-us", {
            method: "POST",
            body,
          }
        );
  
        if (response.error) throw response.error.message;
  
        setSuccessModalOpened(true);
  
        form.reset();
      } catch (error) {
        throw new Error("Something went wrong");
      }
    },
    [],
  );

  return (
    <Container
      className={styles.container}
      removeBorderRadius
    >
      <section
        data-slice-type={slice.slice_type}
        data-slice-variation={slice.variation}
        className={styles.root}
      >
        <Typography
          variant="h2"
          className={styles.title}
        >
          <PrismicRichText
            field={slice.primary.title}
          />
        </Typography>
        <Typography
          variant="bodySmall"
          className={styles.description}
        >
          <PrismicRichText
            field={slice.primary.description}
          />
        </Typography>
        <div
          className={styles.leftImageWr}
        >
          <PrismicImage
            field={slice.primary.left_image}
            className={styles.leftImage}
          />
        </div>
        <div
          className={styles.rightImageWr}
        >
          <PrismicImage
            field={slice.primary.right_image}
            className={styles.rightImage}
          />
        </div>
        <form
          className={styles.form}
          onSubmit={form.handleSubmit(
            onSubmit
          )}
        >
          <div
            className={styles.fieldsContainer}
          >
            <Controller
              name="fullName"
              rules={{
                required: "Required!",
                ...(FULL_NAME_CONTROLLER_RULES),
              }}
              control={form.control}
              render={(
                { field, fieldState }
              ) => (
                <TextInput
                  {...FULL_NAME_INPUT_PROPS}
                  {...field}
                  className={styles.field}
                  error={fieldState.error?.message}
                />
              )}
            />
            <Controller
              name="Email"
              rules={{
                required: "Required!",
                ...(EMAIL_CONTROLLER_RULES),
              }}
              control={form.control}
              render={(
                { field, fieldState }
              ) => (
                <TextInput
                  {...EMAIL_INPUT_PROPS}
                  {...field}
                  className={styles.field}
                  error={fieldState.error?.message}
                />
              )}
            />
            <div
              className={styles.mobileAddressContainer}
            >
              <Controller
                name="mobileNumber"
                rules={{
                  required: "Required!",
                  ...(PHONE_NUMBER_CONTROLLER_RULES),
                }}
                control={form.control}
                render={(
                  { field, fieldState }
                ) => (
                  <PatternFormat
                    {...PHONE_NUMBER_INPUT_PROPS}
                    inputMode="tel"
                    className={styles.field}
                    error={fieldState.error?.message}
                    {...field}
                    customInput={TextInput}
                  />
                  // <TextInput
                  //   {...PHONE_NUMBER_INPUT_PROPS}
                  //   {...field}
                  //   className={styles.field}
                  //   error={fieldState.error?.message}
                  // />
                )}
              />
              <Controller
                name="address"
                rules={{
                  required: "Required!",
                  ...(ADDRESS_CONTROLLER_RULES),
                }}
                control={form.control}
                render={(
                  { field, fieldState }
                ) => (
                  <TextInput
                    {...ADDRESS_INPUT_PROPS}
                    {...field}
                    className={styles.field}
                    error={fieldState.error?.message}
                  />
                )}
              />
            </div>
            <Controller
              name="businessName"
              rules={{
                required: "Required!",
                ...(ONLY_UPPERCASE_AND_LOWERCASE_CONTROLLER_RULES),
              }}
              control={form.control}
              render={(
                { field, fieldState }
              ) => (
                <TextInput
                  label="Business Name"
                  placeholder="Enter business name"
                  {...field}
                  className={styles.field}
                  error={fieldState.error?.message}
                />
              )}
            />
            <Controller
              name="businessNature"
              rules={{
                required: "Required!",
                ...(ONLY_UPPERCASE_AND_LOWERCASE_CONTROLLER_RULES),
              }}
              control={form.control}
              render={(
                { field, fieldState }
              ) => (
                <TextInput
                  label="Nature of Business"
                  placeholder="Enter nature of business"
                  {...field}
                  className={styles.field}
                  error={fieldState.error?.message}
                />
              )}
            />
            <Controller
              name="buildingType"
              rules={{
                required: "Required!",
                ...(ONLY_UPPERCASE_AND_LOWERCASE_CONTROLLER_RULES),
              }}
              control={form.control}
              render={(
                { field, fieldState }
              ) => (
                <TextInput
                  label="Building Type"
                  placeholder="Enter building type"
                  {...field}
                  className={styles.field}
                  error={fieldState.error?.message}
                />
              )}
            />
          </div>

          <Button
            type="submit"
            color="primary"
            isAnimated
            alternateHover
            className={styles.submitButton}
          >
            Send form
          </Button>
        </form>
      </section>
      <Modal
        open={successModalOpened}
        onClose={() => setSuccessModalOpened(false)}
      >
        <div
          className={styles.successContent}
        >
          <Typography
            fontFamily="primary"
            variant="h4"
          >
              Success!
          </Typography>
          <Typography
            variant="bodyMedium"
          >
              Your changes have been saved!
          </Typography>
          <Button
            onClick={() => setSuccessModalOpened(false)}
            className={styles.okButton}
            alternateHover
          >
              Ok
          </Button>
        </div>
      </Modal>
    </Container>
  );
};

export default ContactUs;
