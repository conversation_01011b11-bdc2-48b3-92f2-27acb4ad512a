import { bodyMedium, buttonMedium } from "@/components/Typography/Typography.css";
import { breakpoints } from "@/styles/constants.css";
import { mode, modeGlobal } from "@/styles/functions.css";
import { theme } from "@/styles/themes.css";
import { globalStyle, style, styleVariants } from "@vanilla-extract/css";

export const container = style({

  selectors: {
    [mode("residential")]: {
      backgroundColor: theme.colors.primary.castletonGreen,
      color: theme.colors.primary.softWhite,
    }
  },
});

export const root = style({
  padding: "0 20px",
  marginTop: 40,
  scrollSnapAlign: "center",
  "@media": {
    [breakpoints.tablet]: {
      padding: "0 48px",
      marginTop: 24
    }
  }


});

// export const tabsWrapper = style({
//   columnGap: 16,
//   marginBottom: 88,
//   marginTop: 20,
//   display: "none",
//   "@media": {
//     [breakpoints.tablet]: {
//       display: "flex"
//     }
//   }
// });

// const tabBase = style({
//   cursor: "pointer",
//   borderRadius: 8,
//   padding: "16px 20px",
// });

// const tabUnactive = style({
//   border: `1px solid ${theme.colors.primary.castletonGreen}`,
//   color: theme.colors.primary.castletonGreen,
//   background: "transparent",
// });

// const tabActive = style({
//   border: "1px solid transparent",
//   color: theme.colors.primary.ivory,
//   background: theme.colors.primary.castletonGreen,
// });

// export const tab = styleVariants({
//   base: [tabBase,tabUnactive],
//   active: [tabBase, tabActive]
// });

const listWrapperBase = style({
  marginTop: 56,
  "@media": {
    [breakpoints.tablet]: {
      gridTemplateColumns: "repeat(3,1fr)",
    }
  },
  overflow: "hidden",
});

export const listWrapperDarkTheme = style({
  selectors: {
    [mode("residential")]: {
      color: theme.colors.primary.softWhite,
    }
  },
});

globalStyle(`${modeGlobal("residential")} ${listWrapperBase} button`, {
  color: theme.colors.primary.softWhite,
});

export const listDarkTheme = style({
  selectors: {
    [mode("residential")]: {
      color: theme.colors.primary.softWhite,
    }
  },
});

const listWrapperOpen = style({
  height: "auto",
});
const listWrapperClosed = style({
  height: 300,
  "@media": {
    [breakpoints.tablet]: {
      height: "auto",
    }
  }
});

export const listWrapper = styleVariants({
  open: [listWrapperBase, listWrapperOpen],
  closed: [listWrapperBase, listWrapperClosed],

});

// bodyMedium here to make icon inherit font styles (color and size)
const listBase = style([bodyMedium,{
  rowGap: 14,
  paddingInlineStart: 0,
  display: "grid",
  gridTemplateColumns: "repeat(3,1fr)",
  alignItems: "center",

}]);

const listVisible = style({
  display: "grid",
});

const listHidden = style({
  display: "none",
  "@media": {
    [breakpoints.tablet]: {
      display: "grid",
    }
  }
});



export const list = styleVariants({
  visible: [listBase, listVisible],
  hidden: [listBase, listHidden],
});



export const description = style({
  maxWidth: 498,
  textAlign: "center",
  opacity: .8,
  marginTop: 32


});

export const button = style({
  marginTop: 24,
  marginBottom: 24,
  width: "100%",

  "@media": {
    [breakpoints.tablet]: {
      marginBottom: 72,
      marginTop: 72,
      maxWidth: 234,
    }
  },
  
});

export const dropdownWrapper = style({
  display: "flex",
  marginBottom: 48,
  marginTop: 10,
  "@media": {
    [breakpoints.tablet]: {
      display: "none"
    }
  }
});





