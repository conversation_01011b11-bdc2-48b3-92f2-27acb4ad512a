"use client";

import { Content } from "@prismicio/client";
import { PrismicRichText, SliceComponentProps } from "@prismicio/react";
import * as styles from "./ListWithSwitch.css";
import classNames from "classnames";
import { gridSprinkle } from "@/styles/sprinkles.css";
import {  useState } from "react";
import Typography from "@/components/Typography";
import Button from "@/components/Button";
import Container from "@/components/Container";
import ShrinksList from "@/components/ShrinksList/ShrinksList";
import Link from "next/link";
import BookServiceButton from "@/components/BookServiceButton";

/**
 * Props for `ListWithSwitch`.
 */
export type ListWithSwitchProps =
  SliceComponentProps<Content.ListWithSwitchSlice>;

// type SliceDataType = {
//     name: string;
//     title: string;
//     description: string;
//     list1: string[],
//     list2: string[],
//     list3: string[],
// }

/**
 * Component for "ListWithSwitch" Slices.
 */
const ListWithSwitch = ({ slice }: ListWithSwitchProps): JSX.Element => {
  
  // const sliceData: SliceDataType[] = slice.items.map(({ item }) => JSON.parse(item as string));
  
  const [showMore, setShowMore] = useState(false);

  // const [activeTab, setActiveTab] = useState(sliceData[0].name);

  // const onActiveTabClick = useCallback(
  //   (tab: string) => () => {
  //     setActiveTab(tab); 
  //   },
  //   [],
  // );

  // const onSelectChange = useCallback(
  //   (selected: Option) => {
  //     if(!selected) return;
  //     setActiveTab(selected.value as string);
  //   },
  //   [],
  // );
  

  // const options = sliceData.map(({name}) => ({value: name, label: name}));
  
  return (
    <Container
      notFullHeight
      className={styles.container}
    >
      <section
        className={classNames(styles.root, gridSprinkle({type: "grid"}), "plumbing-section")}
        data-slice-type={slice.slice_type}
        data-slice-variation={slice.variation}
      >
        {/* <Typography
          className={gridSprinkle({type: "item", cols: 10, justifySelf: "center"})}
          variant="bodySmall"
        >
          <PrismicRichText
            field={slice.primary.title}
          />
        </Typography> */}
        {/* <div
          className={classNames(styles.dropdownWrapper,  gridSprinkle({type: "item",cols: 10, justifySelf: "stretch"}))}
        >
          <DropdownInput
            options={options}
            onChange={onSelectChange}
            value={{value: activeTab, label: activeTab}}
          />
        </div>
        <div
          className={classNames( styles.tabsWrapper, gridSprinkle({type: "item",cols: 10, justifySelf: "center"}))}
        >
       
          {sliceData.map(({name}) =>{
            return (<button
              className={styles.tab[name === activeTab ? "active" : "base"]}
              onClick={onActiveTabClick(name)}
              key={name}
              type="button"
                    >
              <Typography
                variant="bodyMedium"
              >
                {name}
              </Typography>
            </button>
            );})}
          
        </div> */}
        {/* {
          sliceData.map(({title,list1,list2,list3,description, name}) => {
            if(name !== activeTab) return null;
            const lists = [list1, list2, list3];
            return(
              <Fragment
                key={title}
              >
                <Typography
                  className={classNames(gridSprinkle({type: "item", cols: 10, justifySelf: "center"}))}
                  variant="h3"
                  fontFamily="primary"
                >{title}</Typography>
                <Typography
                  className={classNames(styles.description, gridSprinkle({type: "item", cols: 10, justifySelf: "center"}))}
                  variant="bodyMedium"
                >{description}</Typography>
                <div
                  className={classNames(styles.listWrapper["open"], gridSprinkle({type: "item", cols: 10}))}
                >
                  {lists.map((list,idx) => (
                    <ul
                      key={`${idx}`}
                      className={classNames(styles.list[showMore || idx === 0 ? "visible" : "hidden"],)}
                    >{list.map(listDesc => (
                        <li
                          key={listDesc}
                          className={styles.listItem}
                        >
                          <div><MemoCheckMarkIcon/></div>
                          {listDesc}
                        </li>
                      ))
                      }
                    </ul>))}
                </div>
              </Fragment>
            );})
        } */}
      
        <Typography
          className={classNames(gridSprinkle({type: "item", cols: 10, justifySelf: "center"}))}
          variant="h3"
          as={"h2"}
          fontFamily="primary"
        >
          <PrismicRichText
            field={slice.primary.title}
          />
        </Typography>
        {!!slice.primary.description && <Typography
          className={classNames(styles.description, gridSprinkle({type: "item", cols: 10, justifySelf: "center"}))}
          variant="bodySmall"
        >{slice.primary.description}</Typography>}
        <ShrinksList
          data={slice.items.map(({item}) =>item)}
          cols={3}
          className={classNames(styles.listWrapper.open, gridSprinkle({type: "item", cols: 10}), styles.listWrapperDarkTheme)}
          listClassName={styles.listDarkTheme}
        />      
        <BookServiceButton
          className={classNames(styles.button, gridSprinkle({type: "item", cols: 10, justifySelf: "center"}))}
        />
      </section>
    </Container>
  );
};

export default ListWithSwitch;


/* 
{"name":"For home owners","title":"Plumbing","description":"Lorem ipsum dolor sit amet consectetur adipiscing eli mattis sit phasellus mollis sit aliquam sit nullam.","list1":["Airlocks","Ball valves-bidets","Blocked toilets","Blocked sinks","Blocked pipes & drains","Burst pipes hot cylinder installations","Hot water problems"],"list2":["Immersion heaters leaks overflows","Pipework replacement","Plumbing installations","Pumps radiator & valves repairs","Saniflo installs","Showers & pressure","Adjustments stopcocks"],"list3":["Tap repairs & installations","Thermostat problems","Toilet repairs & installs unvented & vented cylinders","Waste disposals water heater repairs & installs","Water tank installs"]}
*/