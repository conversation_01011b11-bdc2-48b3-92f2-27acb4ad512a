import { breakpoints } from "@/styles/constants.css";
import { theme } from "@/styles/themes.css";
import { globalStyle, style } from "@vanilla-extract/css";

export const root = style({
  position: "relative",
  padding: "40px 20px 20px",
  borderRadius: 16,
  backgroundColor: theme.colors.primary.castletonGreen,

  "@media": {
    [breakpoints.tablet]: {
      padding: "104px 51px 104px",
      backgroundColor: "transparent",

      selectors: {
        "&::before": {
          content: "''",
          position: "absolute",
          top: 0,
          left: 0,
          width: "100%",
          height: "703px",
          borderRadius: 16,
          backgroundColor: theme.colors.primary.castletonGreen,
        }
      }
    }
  },
});

export const title = style({
  marginBottom: 32,
  fontFamily: theme.fonts.primary,
  fontSize: 40,
  fontWeight: 400,
  lineHeight: "95%",
  letterSpacing: "-0.8px",
  color: theme.colors.primary.ivory,
  textAlign: "center",

  "@media": {
    [breakpoints.tablet]: {
      position: "relative",
      zIndex: 1,
      maxHeight: 138,
      marginBottom: 72,
      fontSize: 88,
      letterSpacing: "-1.76px",
    }
  },
});

globalStyle(`${title} strong`, {
  color: theme.colors.primary.asidGreen,
});

export const content = style({
  position: "relative",
  maxWidth: "100%",
});

export const image = style({
  display: "block",
  maxWidth: "100%",
  height: "auto",
  marginBottom: 32,
  borderRadius: 16,

  "@media": {
    [breakpoints.tablet]: {
      marginBottom: 0,
      borderRadius: 24,
    }
  },
});

export const cards = style({
  display: "flex",
  flexDirection: "column",
  gap: 9,

  "@media": {
    [breakpoints.tablet]: {
      position: "absolute",
      bottom: 0,
      left: 0,
      right: 0,
      margin: "0 auto 20px",
      flexDirection: "row",
      justifyContent: "center"
    }
  },
});

export const card = style({
  maxWidth: 413,
  fontFamily: theme.fonts.secondary,
  padding: 24,
  borderRadius: 16,
  color: theme.colors.primary.castletonGreen,
  backgroundColor: theme.colors.primary.softWhite,

  "@media": {
    [breakpoints.tablet]: {
      display: "flex",
      flexDirection: "column",
      minHeight: 267,
      padding: "32px 40px",
    }
  },
});

export const cardIcon = style({
  width: 24,
  height: 24,

  "@media": {
    [breakpoints.tablet]: {
      width: 32,
      height: 32,
    }
  },
});

export const cardTitle = style({
  margin: "12px 0",
  fontSize: "18px !important",

  "@media": {
    [breakpoints.tablet]: {
      margin: "16px 0 auto",
      fontSize: "24px !important",
    }
  },
});

export const cardDescription = style({
  marginTop: "auto",
  fontSize: 16,

  "@media": {
    [breakpoints.tablet]: {
      fontSize: 18,
    }
  },
});