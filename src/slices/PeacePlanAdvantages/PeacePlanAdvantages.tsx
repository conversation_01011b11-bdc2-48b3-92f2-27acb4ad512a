import Container from "@/components/Container";
import { Content } from "@prismicio/client";
import { SliceComponentProps } from "@prismicio/react";
import * as styles from "./PeacePlanAdvantages.css";
import { PrismicRichText } from "node_modules/@prismicio/react/dist/react-server/PrismicRichText";
import { PrismicNextImage } from "@prismicio/next";
import Typography from "@/components/Typography";
import { toCamelCase } from "@/utils/helpers";

export type PeacePlanAdvantagesProps =
  SliceComponentProps<Content.PeacePlanAdvantagesSlice>;

const PeacePlanAdvantages = ({
  slice,
}: PeacePlanAdvantagesProps): JSX.Element => {
  return (
    <Container
      removeBorderRadius
    >
      <section
        id={slice.primary.header_anchor_name ? toCamelCase(slice.primary.header_anchor_name) : ""}
        data-slice-type={slice.slice_type}
        data-slice-variation={slice.variation}
        className={styles.root}
      >
        <h3
          className={styles.title}
        >
          <PrismicRichText
            field={slice.primary.main_title}
          />
        </h3>
        <div
          className={styles.content}
        >
          <PrismicNextImage
            field={slice.primary.image}
            className={styles.image}
          />
          <div
            className={styles.cards}
          >
            {slice.items.map((item) => (
              <div
                className={styles.card}
                key={item.advantage_title}
              >
                <PrismicNextImage
                  field={item.advantage_icon}
                  className={styles.cardIcon}
                />
                <Typography
                  variant="subTitleMedium"
                  className={styles.cardTitle}
                >
                  {item.advantage_title}
                </Typography>
                <div
                  className={styles.cardDescription}
                >
                  <PrismicRichText
                    field={item.advantage_description}
                  />
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>
    </Container>
  );
};

export default PeacePlanAdvantages;
