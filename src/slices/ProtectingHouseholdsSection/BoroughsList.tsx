"use client";

import React, { useState, useEffect } from "react";
import * as styles from "./ProtectingHouseholdsSection.css";
import { Content } from "@prismicio/client";

// Define the type for the items prop, which is an array of boroughs
type BoroughsListProps = {
  items: Content.ProtectingHouseholdsSectionSlice["items"];
};

const BoroughsList = ({ items }: BoroughsListProps): JSX.Element => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [isDesktop, setIsDesktop] = useState(false);

  // This effect runs only on the client to check the window size
  useEffect(() => {
    const checkIfDesktop = () => {
      setIsDesktop(window.innerWidth >= 768); // Tablet breakpoint
    };

    checkIfDesktop();
    window.addEventListener('resize', checkIfDesktop);

    return () => {
      window.removeEventListener('resize', checkIfDesktop);
    };
  }, []);

  const itemsToShowCollapsed = 10;
  const itemsPerColumn = Math.ceil(items.length / 2);

  // Logic to determine which items to show based on state
  const leftColumnItems = isDesktop || isExpanded
    ? items.slice(0, itemsPerColumn)
    : items.slice(0, Math.min(itemsToShowCollapsed, itemsPerColumn));

  const rightColumnItems = isDesktop || isExpanded
    ? items.slice(itemsPerColumn)
    // Fix for right column calculation when collapsed
    : items.slice(itemsPerColumn, itemsPerColumn + Math.min(itemsToShowCollapsed - leftColumnItems.length, items.length - itemsPerColumn));

  return (
    <>
      <div className={styles.boroughsContainer}>
        <ul className={styles.boroughsList}>
          {leftColumnItems.map((borough, index) => (
            <li key={`borough-left-${index}`} className={styles.boroughItem}>
              <svg className={styles.checkIcon} width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M9 16.17L4.83 12L3.41 13.41L9 19L21 7L19.59 5.59L9 16.17Z" fill="currentColor"/>
              </svg>
              <span className={styles.boroughText}>{borough.name}</span>
            </li>
          ))}
        </ul>

        <ul className={styles.boroughsList}>
          {rightColumnItems.map((borough, index) => (
            <li key={`borough-right-${index}`} className={styles.boroughItem}>
              <svg className={styles.checkIcon} width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M9 16.17L4.83 12L3.41 13.41L9 19L21 7L19.59 5.59L9 16.17Z" fill="currentColor"/>
              </svg>
              <span className={styles.boroughText}>{borough.name}</span>
            </li>
          ))}
        </ul>
      </div>

      {/* The "View All" button is only rendered on mobile */}
      {!isDesktop && (
        <div className={styles.viewAllContainer}>
          <button onClick={() => setIsExpanded(!isExpanded)} className={styles.viewAllLink}>
            View All
            <svg className={`${styles.viewAllIcon} ${isExpanded ? styles.expanded : ""}`} width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M7.41 8.59L12 13.17L16.59 8.59L18 10L12 16L6 10L7.41 8.59Z" fill="currentColor"/>
            </svg>
          </button>
        </div>
      )}
    </>
  );
};

export default BoroughsList;