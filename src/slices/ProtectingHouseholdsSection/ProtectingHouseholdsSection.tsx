import { Content } from "@prismicio/client";
import { PrismicText, SliceComponentProps } from "@prismicio/react";
import React from "react";
import * as styles from "@/slices/ProtectingHouseholdsSection/ProtectingHouseholdsSection.css";
import Link from "next/link";
import PhoneIcon from "@/assets/icons/PhoneIcon";
import Image from "next/image";
import BoroughsList from "@/slices/ProtectingHouseholdsSection/BoroughsList";

/**
 * Props for `ProtectingHouseholdsSection`.
 */
export type ProtectingHouseholdsSectionProps =
  SliceComponentProps<Content.ProtectingHouseholdsSectionSlice>;

/**
 * Component for "ProtectingHouseholdsSection" Slices.
 */
const ProtectingHouseholdsSection = ({
  slice,
}: ProtectingHouseholdsSectionProps): JSX.Element => {
  return (
    <section
      data-slice-type={slice.slice_type}
      data-slice-variation={slice.variation}
    >
      <div className={styles.wrapper}>
        <section className={styles.root}>
          <div className={styles.container}>
            <div className={styles.content}>
              <h2 className={styles.title}>
                <span className={styles.highlight}>
                  <PrismicText field={slice.primary.title_1} />
                </span>
                <PrismicText field={slice.primary.title_2} />
                <br />
                <PrismicText field={slice.primary.title_3} />
              </h2>

              <p className={styles.description}>
                <span className={styles.trustedText}>
                  <PrismicText field={slice.primary.description} />
                </span>
                <PrismicText field={slice.primary.description_2} />
                <span className={styles.needExpert}>
                  <PrismicText field={slice.primary.description_3} />
                </span>
                <PrismicText field={slice.primary.description_4} />
              </p>

              <div className={styles.mobileCallButton}>
                <Link href="tel:08000461000" className={styles.callButton}>
                  <PhoneIcon className={styles.phoneIcon} />
                  Call Us
                </Link>
              </div>

              <div className={styles.imageAndBoroughs}>
                <div className={styles.imageContainer}>
                  <Image
                    src={slice.primary.sectionimage.url || ""}
                    alt={slice.primary.sectionimage.alt || "London cityscape"}
                    fill
                    className={styles.image}
                    priority
                  />
                </div>

                {/* The interactive list is now handled by the client component */}
                <BoroughsList items={slice.items} />
              </div>
            </div>
          </div>
        </section>
      </div>
    </section>
  );
};

export default ProtectingHouseholdsSection;
