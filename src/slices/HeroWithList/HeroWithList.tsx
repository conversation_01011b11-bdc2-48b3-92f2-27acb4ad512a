"use client";

import Button from "@/components/Button";
import Container from "@/components/Container";
import ShrinksList from "@/components/ShrinksList/ShrinksList";
import Typography from "@/components/Typography";
import { gridSprinkle } from "@/styles/sprinkles.css";
import { Content } from "@prismicio/client";
import { PrismicNextImage } from "@prismicio/next";
import { SliceComponentProps } from "@prismicio/react";
import classNames from "classnames";
import { PrismicRichText } from "node_modules/@prismicio/react/dist/react-server/PrismicRichText";
import * as styles from "./HeroWithList.css";
import Link from "next/link";
import ModeProvider from "@/components/ModeProvider";


/**
 * Props for `HeroWithList`.
 */
export type HeroWithListProps = SliceComponentProps<Content.HeroWithListSlice>;

/**
 * Component for "HeroWithList" Slices.
 */
const HeroWithList = ({ slice }: HeroWithListProps): JSX.Element => {


  return (
    <Container
      notFullHeight
      className={styles.container}
    >
      <section
        className={classNames(styles.root[slice.variation],
          gridSprinkle({ type: "grid", justifyItems: "stretch" }))}
        data-slice-type={slice.slice_type}
        data-slice-variation={slice.variation}
      >
        <div
          className={classNames(styles.hero[slice.variation],
            gridSprinkle({
              type: "item",
              cols: {
                mobile: 10,
                tablet: 5,
              },
            })
          )}
        >
          <PrismicNextImage
            className={styles.image}
            field={slice.primary.hero}
            fill
            objectFit={(slice.variation === "inverted" || slice.variation === "default") ? "cover" : "contain"}
            objectPosition={(slice.variation === "inverted" || slice.variation === "default") ? "center" : "bottom center"}
          />
        </div>
        <div
          className={classNames(styles.rightSection[slice.variation],
            gridSprinkle({ type: "item", cols: { mobile: 10, tablet: 5 } })
          )}
        >
          <Typography
            fontFamily="primary"
            className={classNames(styles.title[slice.variation])}
            variant="h3"
            as={"h2"}
          >
            <PrismicRichText
              field={slice.primary.title}
            />
          </Typography>
          {!!slice.primary.description && !!slice.primary.description.length && <Typography 
            variant="bodySmall"
            className={classNames(styles.description[slice.variation])}
          >
            <PrismicRichText
              field={slice.primary.description}
            />
          </Typography>}
          <ModeProvider>
            {(mode) => {
              const initialVariant = slice.variation === "default" || slice.variation === "inverted" ? "primary": "secondary";

              return (
                <ShrinksList
                  data={slice.items.map(({item}) => item)}
                  variant={mode === "residential" ? "primary" : initialVariant}
                  cols={2}
                  className={classNames(styles.shrinksList, styles.shrinksListDarkMode)}
                  listClassName={classNames({
                    [styles.listClassName]: mode === "residential" && initialVariant === "secondary",
                    [styles.listClassNameWhite]: mode === "residential" && initialVariant === "primary",
                  })}
                />
              );
            }}
          </ModeProvider>
          <div
            className={classNames(styles.button[slice.variation])}
          >
            <ModeProvider>
              {(mode) => (
                <Button
                  isAnimated
                  as={Link}
                  href="#booking-form"
                  color={mode === "residential" && slice.variation === "alternativeColors" ? "secondary" : "primary"}
                >Book an Expert</Button>
              )}
            </ModeProvider>
          </div>
        </div>
      
      </section>
    </Container>
  );
};

export default HeroWithList;
