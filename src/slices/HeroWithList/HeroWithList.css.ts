import { breakpoints } from "@/styles/constants.css";
import { mode, modeGlobal } from "@/styles/functions.css";
import { theme } from "@/styles/themes.css";
import { globalStyle, style, styleVariants } from "@vanilla-extract/css";

export const container = style({
  scrollSnapAlign: "start",
  overflow: "hidden",

  selectors: {
    [mode("residential")]: {
      background: theme.colors.primary.castletonGreen,
    }
  },
});

export const rootBase = style({
});

export const mainRoot = style({
  padding: 0,
  "@media": {
    [breakpoints.tablet]: {
      padding: 48,
    }
  },
});
export const alternativeRoot = style({
  padding: 0,
  background: theme.colors.primary.castletonGreen,
  borderRadius: 16,

  "@media": {
    [breakpoints.tablet]: {
      borderRadius: 24,
    }
  }
});

const mainRootCommercialMode = style({
  selectors: {
    [mode("residential")]: {
      background: theme.colors.primary.castletonGreen,
    }
  }
});


const alternativeRootCommercialMode = style({
  selectors: {
    [mode("residential")]: {
      background: theme.colors.primary.ivory,
    }
  }
});

export const root = styleVariants({
  default: [rootBase, mainRoot, mainRootCommercialMode],
  alternativeColors: [rootBase, alternativeRoot, alternativeRootCommercialMode],
  inverted:[rootBase, mainRoot, mainRootCommercialMode],
  invertedAlternative: [rootBase, alternativeRoot, alternativeRootCommercialMode],
});

const heroBase = style({
  position: "relative",
  overflow: "hidden",
  order: 1,
  minHeight: "unset",
  alignSelf: "end",
  height: "100%",
  "@media": {
    [breakpoints.tablet]: {
      minHeight:450,
    }
  }
  
});
const heroDefault = style({
  marginRight: 0,
  "@media": {
    [breakpoints.tablet]: {
      marginRight: 40,
      // minHeight: 617,
    }
  }
});

const heroMain = style({
  order: 1,
  borderRadius: 20,
  aspectRatio: "9 / 10",
  width: "auto",
  "@media": {
    [breakpoints.tablet]: {
      order: 0,
      aspectRatio: "auto",  
      // minHeight: 485,
    }
  }
});
const heroAlternative = style({
  order: 1,

  aspectRatio: "11 / 9",
  width: "100%",
  "@media": {
    [breakpoints.tablet]: {
      aspectRatio: "auto",
      // minHeight: 747,
    }
  }
});
const heroInverted = style({
  aspectRatio: "11 / 9",

  "@media": {
    [breakpoints.tablet]: {
      order: 1,
    }
  }
});
const heroInvertedAlternative = style({
  order: 1,
  aspectRatio: "9 / 11",
  "@media": {
    [breakpoints.tablet]: {
      order: -1,
    }
  },
});

export const hero = styleVariants({
  default: [heroBase, heroMain,heroDefault],
  alternativeColors: [heroBase, heroAlternative],
  inverted: [heroBase, heroMain, heroInverted],
  invertedAlternative: [heroBase,heroAlternative, heroInvertedAlternative],
});


export const rightSectionBase = style({
  display: "flex",
  flexDirection: "column",
  padding: 20,
  "@media": {
    [breakpoints.tablet]: {
      padding: 0,
      marginRight:20,
    }
  }
});

const rightSectionMain = style({

});
const rightSectionAlternative = style({
  marginLeft: 0,
  "@media": {
    [breakpoints.tablet]: {
      marginLeft: 48,
    }
  }
});


export const rightSection = styleVariants({
  default: [rightSectionBase, rightSectionMain],
  alternativeColors: [rightSectionBase, rightSectionAlternative],
  inverted:[rightSectionBase, rightSectionMain],
  invertedAlternative: [rightSectionBase, rightSectionAlternative],

});




export const listBase = style({
  marginBlockStart: 0,
  marginBlockEnd: 0,
  paddingInlineStart: 0,
  marginBottom: 48,
  display: "grid",
  rowGap: 12,
  listStyleType: "none",
});

const mainList = style({});
const alternativeList = style({
  gridTemplateColumns: "unset",
  color: theme.colors.primary.ivory,
  "@media": {
    [breakpoints.tablet]: {
      gridTemplateColumns: "repeat(2, 1fr)",
    }
  },
});

export const list = styleVariants({
  default: [listBase, mainList],
  alternativeColors: [listBase, alternativeList],
  inverted: [listBase, mainList],
  invertedAlternative: [listBase, alternativeList],
});


export const listItemBase = style({
  display: "flex",
  columnGap: "1ch",

});

const mainListItem = style({});
const alternativeListItem = style({
  // selectors: {
  //   ["&::marker"]: {
  //     color: theme.colors.primary.asidGreen
  //   }
  // }
});

export const listItem = styleVariants({
  default: [listItemBase, mainListItem],
  alternativeColors: [listItemBase, alternativeListItem ],
  inverted: [listItemBase, mainListItem],
  invertedAlternative: [listItemBase, alternativeListItem ],
});

const  listItemIconBase = style({});
const  mainlistItemIcon = style({});
const  alternativelistItemIcon = style({
  color: theme.colors.primary.asidGreen
});

export const listItemIcon = styleVariants({
  default: [listItemIconBase, mainlistItemIcon],
  alternativeColors: [listItemIconBase, alternativelistItemIcon ],
  inverted: [listItemIconBase, mainlistItemIcon],
  invertedAlternative: [listItemIconBase, alternativelistItemIcon ],
  

});

const titleBase = style({
  marginBottom: 32,
  marginTop: 20,
  "@media": {
    [breakpoints.tablet]: {
      marginTop: 44,
    }
  }
});

const mainTitle = style({

});
const alternativeTitle = style({
  color: theme.colors.primary.ivory,
  marginTop: 20,
  "@media": {
    [breakpoints.tablet]: {
      marginTop: 72,
    }
  }
});

const mainTitleCommercialMode = style({
  selectors: {
    [mode("residential")]: {
      color: theme.colors.primary.softWhite,
    }
  }
});

const alternativeTitleCommercialMode = style({
  selectors: {
    [mode("residential")]: {
      color: theme.colors.primary.castletonGreen,
    }
  }
});

export const title = styleVariants({
  default: [titleBase, mainTitle, mainTitleCommercialMode],
  alternativeColors: [titleBase, alternativeTitle, alternativeTitleCommercialMode],
  inverted: [titleBase, mainTitle, mainTitleCommercialMode],
  invertedAlternative: [titleBase, alternativeTitle, alternativeTitleCommercialMode],
});

const descriptionBase = style({
  marginBottom: 26,
  opacity: .8,
  "@media": {
    [breakpoints.tablet]: {
      marginBottom: 48,
    }
  }
});

const mainDescription = style({});
const alternativeDescription = style({
  color: theme.colors.primary.ivory,

});

const mainDescriptionCommercialMode = style({
  selectors: {
    [mode("residential")]: {
      color: theme.colors.primary.softWhite,
    }
  }
});

const alternativeDescriptionCommercialMode = style({
  selectors: {
    [mode("residential")]: {
      color: theme.colors.primary.castletonGreen,
    }
  }
});

export const description = styleVariants({
  default: [descriptionBase, mainDescription, mainDescriptionCommercialMode],
  alternativeColors: [descriptionBase, alternativeDescription, alternativeDescriptionCommercialMode],
  inverted: [descriptionBase, mainDescription, mainDescriptionCommercialMode],
  invertedAlternative: [descriptionBase, alternativeDescription, alternativeDescriptionCommercialMode]
});

export const buttonBase = style({
  display: "grid",
  justifyItems: "unset",
  alignItems: "unset",
  paddingTop: 32,
  "@media": {
    [breakpoints.tablet]: {
      justifyItems: "flex-start",
      marginTop: "auto",
      paddingTop: 72,
    }
  }
});

export const mainButton = style({
  marginBottom: 0,
  "@media": {
    [breakpoints.tablet]: {
      marginBottom: 44,
    }
  }
});
export const alternativeButton = style({
  marginBottom: 0,
  "@media": {
    [breakpoints.tablet]: {
      marginBottom: 72,
    }
  }
});

export const button = styleVariants({
  default: [buttonBase, mainButton],
  alternativeColors: [buttonBase, alternativeButton ],
  inverted: [buttonBase, mainButton],
  invertedAlternative: [buttonBase, alternativeButton ],
});

export const image = style({
  selectors: {
    [`${root.invertedAlternative} &`]: {
      "@media": {
        [breakpoints.tablet]: {
          paddingTop: 72,
        }
      }
    }
  }
});

export const shrinksList = style({
  selectors: {
    [mode("residential")]: {
      color: theme.colors.primary.castletonGreen,
    }
  },
});

export const listClassName = style({
  selectors: {
    [mode("residential")]: {
      color: theme.colors.primary.castletonGreen,
    },
  },
});

export const listClassNameWhite = style({
  selectors: {
    [mode("residential")]: {
      color: theme.colors.primary.softWhite,
    }
  },
});

export const shrinksListDarkMode = style({
  selectors: {
    [mode("residential")]: {
      color: theme.colors.primary.softWhite,
    }
  },
});

globalStyle(`${modeGlobal("residential")} ${shrinksListDarkMode} button`, {
  color: theme.colors.primary.castletonGreen,
});
