"use client";

import Container from "@/components/Container";
import Divider from "@/components/Divider/Divider";
import IconButton from "@/components/IconButton";
import Typography from "@/components/Typography";
import Video from "@/components/Video";
import { gridSprinkle } from "@/styles/sprinkles.css";
import { Content } from "@prismicio/client";
import { PrismicNextImage } from "@prismicio/next";
import { SliceComponentProps } from "@prismicio/react";
import { PrismicRichText } from "node_modules/@prismicio/react/dist/react-server/PrismicRichText";
import { Fragment, useCallback, useRef, useState } from "react";
import * as styles from "./BlockWithMovies.css";
import useObserveIntoView from "@/hooks/useObserveIntoView";

export type BlockWithMoviesProps =
  SliceComponentProps<Content.BlockWithMoviesSlice>;

const BlockWithMovies = ({ slice }: BlockWithMoviesProps): JSX.Element => {
  const sliderRef = useRef<HTMLDivElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  const [containerInView, setContainerInView] = useState(false);
  const [activeSlideIndex, setActiveSlideIndex] = useState(0);

  const onInView = useCallback(
    (target?: HTMLElement) => {
      setContainerInView(true);
    },
    []
  );

  useObserveIntoView(containerRef, { onInView, threshold: 0.2 });

  const handlePrev = () => {
    if (!sliderRef.current) return;

    sliderRef.current.scrollTo({
      left:
        sliderRef.current.scrollLeft -
        sliderRef.current.clientWidth,
    });
  };

  const handleNext = () => {
    if (!sliderRef.current) return;

    sliderRef.current.scrollTo({
      left:
        sliderRef.current.scrollLeft +
        sliderRef.current.clientWidth,
    });
  };

  return (
    <div
      className={styles.wrapper}
    >
      <Container
        className={styles.container}
        notFullHeight
      >
        <section
          ref={containerRef}
          className={styles.root}
          data-slice-type={slice.slice_type}
          data-slice-variation={slice.variation}
        >
          <div
            className={styles.content}
          >
            <Typography
              variant="h3"
              className={styles.title}
            >
              <PrismicRichText
                field={slice.primary.title}
              />
            </Typography>
            <Typography
              variant="bodySmall"
              className={styles.subtitle}
            >
              <PrismicRichText
                field={slice.primary.description}
              />
            </Typography>
          </div>
          <div
            ref={sliderRef}
            className={styles.slider}
            onScroll={(event) => {
              const currentTargetRect = event.currentTarget.getBoundingClientRect();

              const deeperElement = document.elementFromPoint(
                currentTargetRect.left + currentTargetRect.width / 2,
                currentTargetRect.top + currentTargetRect.height / 2
              );

              if (!deeperElement) return;

              const closestSlide = deeperElement.closest(`.${styles.slide}`);

              if (!closestSlide) return;
            
              const closestSlideIndex = closestSlide.getAttribute("data-slide-index");

              if (closestSlideIndex === null) return;

              setActiveSlideIndex(Number(closestSlideIndex));
            }}
          >
            {slice.items.map((item, index) => (
              <Fragment
                key={index}
              >
                <div
                  className={styles.slide}
                  data-slide-index={index}
                >
                  <Video
                    className={styles.video}
                    preview={(
                      <PrismicNextImage
                        className={styles.videoPreview}
                        field={item.movie_preview}
                        fill
                      />
                    )}
                  >
                    <source
                      src={(item.movie as any)?.url}
                      type="video/webm"
                    />
                  </Video>
                  <Typography
                    variant="bodySmall"
                    className={styles.slideDescription}
                  >
                    <PrismicRichText
                      field={item.movie_description}
                    />
                  </Typography>
                  <div
                    className={styles.control}
                  >
                    <Typography
                      className={styles.slideTitle}
                      variant="subTitleMedium"
                    >
                      {item.movie_title}
                    </Typography>
                    <Divider
                      className={gridSprinkle({ display: { mobile: "block", tablet: "none" } })}
                    />
                    {slice.items.length > 1 && (
                      <div
                        className={styles.slideControl}
                      >
                        <Typography
                          variant="bodySmall"
                        >
                          {index + 1}/{slice.items.length}
                        </Typography>
                        <div
                          className={styles.slideControlButtons}
                        >
                          <IconButton
                            title="Prev"
                            onClick={handlePrev}
                          >
                            <svg
                              width="26"
                              height="26"
                              viewBox="0 0 26 26"
                              fill="none"
                              xmlns="http://www.w3.org/2000/svg"
                            >
                              <path
                                fillRule="evenodd"
                                clipRule="evenodd"
                                d="M16.2353 7.80964C17.0918 6.7017 17.9401 5.28369 18.6663 3.85734H16.7761C13.5143 7.60699 10.0901 10.4222 6.50046 12.2886V13.0371L6.50046 13.0471L6.50046 13.0472L6.50046 13.8298C10.0901 15.6961 13.5143 18.5113 16.7761 22.261H18.6663C17.9401 20.8346 17.0918 19.4166 16.2353 18.3087C15.3625 17.1795 14.1179 15.8852 13.1105 14.8381L13.0935 14.8204C12.5061 14.2099 11.9903 13.6738 11.7011 13.3226L11.4742 13.0472L11.7011 12.7717C11.9918 12.4187 12.5108 11.883 13.1006 11.274L13.1184 11.2557C14.1248 10.2167 15.3651 8.93551 16.2353 7.80964Z"
                                fill="currentColor"
                              />
                            </svg>
                          </IconButton>
                          <IconButton
                            title="Next"
                            onClick={handleNext}
                          >
                            <svg
                              width="24"
                              height="24"
                              viewBox="0 0 24 24"
                              fill="none"
                              xmlns="http://www.w3.org/2000/svg"
                            >
                              <path
                                fillRule="evenodd"
                                clipRule="evenodd"
                                d="M9.43287 7.52887C8.69381 6.57274 7.96167 5.34901 7.33496 4.1181H8.96621C11.7811 7.35398 14.7361 9.7835 17.8339 11.3941L17.8339 12.0488V12.7241C14.7361 14.3347 11.7811 16.7643 8.96621 20.0001H7.33497C7.96167 18.7692 8.69381 17.5455 9.43287 16.5894C10.1861 15.6149 11.2602 14.4979 12.1296 13.5943L12.1442 13.5791L12.1442 13.5791C12.6512 13.0522 13.0963 12.5895 13.3458 12.2865L13.5416 12.0488L13.3458 11.811C13.095 11.5064 12.6471 11.0441 12.138 10.5185L12.138 10.5185L12.1228 10.5028C11.2542 9.60611 10.1839 8.50047 9.43287 7.52887Z"
                                fill="currentColor"
                              />
                            </svg>
                          </IconButton>
                        </div>
                      </div>                    
                    )}
                  </div>
                </div>
                {index < slice.items.length - 1 && (
                  <Divider
                    className={styles.slideDivider}
                    direction="vertical"
                  />
                )}
              </Fragment>
            ))}

          </div>
        </section>      
      </Container>
    </div>
  );
};

export default BlockWithMovies;
