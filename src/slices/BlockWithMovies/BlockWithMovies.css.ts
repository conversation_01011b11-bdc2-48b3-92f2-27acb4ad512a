import { breakpoints } from "@/styles/constants.css";
import { mode, modeGlobal } from "@/styles/functions.css";
import { theme } from "@/styles/themes.css";
import { globalStyle, style } from "@vanilla-extract/css";

export const wrapper = style({
  position: "relative",
  zIndex: 1,
  backgroundColor: theme.colors.primary.ivory,

  selectors: {
    [mode("residential")]: {
      backgroundColor: theme.colors.primary.castletonGreen,
    }
  },
});

export const root = style({
  position: "relative",
  display: "flex",
  flexDirection: "column",
  overflow: "hidden",
  minHeight: "max-content",
  borderRadius: 16,
  backgroundColor: theme.colors.primary.castletonGreen,
  color: theme.colors.primary.ivory,

  "@media": {
    [breakpoints.tablet]: {
      flexDirection: "row",
      borderRadius: 24,
    },
  },

  selectors: {
    [mode("residential")]: {
      backgroundColor: theme.colors.primary.ivory,
    }
  },
});

export const container = style({
  scrollSnapAlign: "center !important",
  paddingTop: 0,
  paddingBottom: 0,

  selectors: {
    [mode("residential")]: {
      backgroundColor: theme.colors.primary.castletonGreen,
    }
  },
});

export const content = style({
  display: "flex",
  flexDirection: "column",
  padding: "40px 20px 32px",

  "@media": {
    [breakpoints.tablet]: {
      width: "50%",
      padding: "72px 48px 48px",
    },
  },
});

export const title = style({
  fontFamily: theme.fonts.primary,
  marginBottom: "24px",

  "@media": {
    [breakpoints.tablet]: {
      marginBottom: "40px",
    },
  },

  selectors: {
    [mode("residential")]: {
      color: theme.colors.primary.castletonGreen,
    }
  },
});

export const subtitle = style({
  marginTop: "auto",
  maxWidth: 320,
  fontWeight: 500,
  selectors: {
    [mode("residential")]: {
      color: theme.colors.primary.castletonGreen,
    }
  },
});

globalStyle(`${title} em`, {
  color: theme.colors.primary.asidGreen,
});

globalStyle(`${modeGlobal("residential")} ${title} em`, {
  color: theme.colors.primary.castletonGreen,
});

export const slider = style({
  scrollBehavior: "smooth",
  scrollSnapType: "x mandatory",
  display: "flex",
  overflowY: "hidden",
  overflowX: "auto",
  backgroundColor: theme.colors.primary.softWhite,
  color: theme.colors.primary.castletonGreen,
  borderRadius: "inherit",

  "@media": {
    [breakpoints.tablet]: {
      width: "50%",
    },
  },
});

export const videoPreview = style({
  objectFit: "cover",
  objectPosition: "center",
});

export const slide = style({
  scrollSnapAlign: "start",
  width: "100%",
  minWidth: "100%",
  padding: 20,

  "@media": {
    [breakpoints.tablet]: {
      padding: 32,
    },
  },
});

export const slideDivider = style({
  margin: "56px 0",
  height: "auto",
});

export const video = style({
  marginBottom: 16,

  "@media": {
    [breakpoints.tablet]: {
      marginBottom: 24,
    },
  },
});

export const slideDescription = style({
  marginBottom: 24,

  "@media": {
    [breakpoints.tablet]: {
      marginBottom: 60,
    },
  },
});

export const slideTitle = style({
  marginRight: "auto",
});

export const control = style({
  display: "flex",
  alignItems: "center",
  flexDirection: "column",
  gap: 24,

  "@media": {
    [breakpoints.tablet]: {
      gap: 16,
      flexDirection: "row",
    },
  },
});

export const slideControl = style({
  display: "flex",
  alignItems: "center",
  flexDirection: "column-reverse",
  gap: 24,

  "@media": {
    [breakpoints.tablet]: {
      flexDirection: "row",
    },
  },
});

export const slideControlButtons = style({
  display: "flex",
  alignItems: "center",
  gap: 16,
});