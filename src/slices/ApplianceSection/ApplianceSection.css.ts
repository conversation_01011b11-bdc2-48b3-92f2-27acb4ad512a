import { breakpoints } from "@/styles/constants.css";
import { mode } from "@/styles/functions.css";
import { theme } from "@/styles/themes.css";
import { style } from "@vanilla-extract/css";

export const container = style({
  backgroundColor: theme.colors.primary.castletonGreen,
});

export const root = style({
  borderRadius: 24,
  background: theme.colors.primary.castletonGreen,
  padding: "40px 20px",
  gap: 20,
  "@media": {
    [breakpoints.tablet]: {
      padding: "40px 46px",
    },
    [breakpoints.desktop]: {
      padding: "42px 72px",
    },
  },

  selectors: {
    [mode("residential")]: {
      background: theme.colors.primary.ivory,
    }
  },
});

export const title = style({
  color: theme.colors.primary.ivory,

  selectors: {
    [mode("residential")]: {
      color: theme.colors.primary.castletonGreen,
    }
  },
});

export const description = style({
  color: theme.colors.primary.ivory,
  opacity: 0.8,

  selectors: {
    [mode("residential")]: {
      color: theme.colors.primary.castleton<PERSON>reen,
    }
  },
});

export const appliancesItem = style({
  display: "flex",
  columnGap: 8,
  listStyleType: "none",
  paddingInlineStart: "1ch",
  background: theme.colors.primary.ivory,
  marginBottom: 0,
  borderRadius: 8,
  padding: "20px 16px",

  "@media": {
    [breakpoints.tablet]: {
      padding: 24,
      columnGap: 10,
      borderRadius: 16,
    }
  },

  selectors: {
    [mode("residential")]: {
      background: theme.colors.primary.softWhite,
      color: theme.colors.primary.castletonGreen,
    }
  },
});

export const appliancesItemIcon = style({
  fontSize: 20,
});

export const appliancesItemText = style({
  fontWeight: 500,
});

export const appliancesList = style({
  display: "grid",
  gridTemplateColumns: "repeat(1, 1fr)",
  gap: 12,
  marginTop: 10,
  paddingInlineStart: "0",
  "@media": {
    [breakpoints.tablet]: {
      gridTemplateColumns: "repeat(2, 1fr)",
      gap: 16,
      marginTop: 20,
    },
    [breakpoints.desktop]: {
      gridTemplateColumns: "repeat(3, 1fr)",
      gap: 24,
      marginTop: 44,
    },
  },
});

export const bookButton = style({
  marginTop: 12,
  "@media": {
    [breakpoints.tablet]: {
      marginTop: 18,
    },
    [breakpoints.desktop]: {
      marginTop: 36,
    },
  },
});
