import Container from "@/components/Container";
import { Content } from "@prismicio/client";
import { PrismicRichText, SliceComponentProps } from "@prismicio/react";
import * as styles from "./ApplianceSection.css";
import classNames from "classnames";
import { gridSprinkle } from "@/styles/sprinkles.css";
import Typography from "@/components/Typography";
import Button from "@/components/Button";
import Link from "next/link";
import MemoCheckMarkIcon from "@/assets/icons/CheckMarkIcon";

/**
 * Props for `ApplianceSection`.
 */
export type ApplianceSectionProps =
  SliceComponentProps<Content.ApplianceSectionSlice>;

/**
 * Component for "ApplianceSection" Slices.
 */
const ApplianceSection = ({ slice }: ApplianceSectionProps): JSX.Element => {
  return (
    <Container
      className={styles.container}
    >
      <section
        className={classNames(styles.root, gridSprinkle({ type: "grid" }))}
        data-slice-type={slice.slice_type}
        data-slice-variation={slice.variation}
      >
        <Typography
          variant="h3"
          as={"h2"}
          fontFamily="primary"
          className={classNames(
            styles.title,
            gridSprinkle({
              type: "item",
              alignSelf: "center",
              cols: { desktop: 7, tablet: 5, mobile: 10 },
            })
          )}
        >
          <PrismicRichText
            field={slice.primary.title}
          />
        </Typography>
        <Typography
          className={classNames(
            styles.description,
            gridSprinkle({
              type: "item",
              cols: { desktop: 3, tablet: 5, mobile: 10 },
              justifySelf: "end",
            })
          )}
        >
          <PrismicRichText
            field={slice.primary.description}
          />
        </Typography>
        <ul
          className={classNames(
            styles.appliancesList,
            gridSprinkle({
              type: "item",
              cols: 10,
            })
          )}
        >
          {slice.items.map(({ appliance_item },idx) => {
            return (
              <li
                className={classNames(styles.appliancesItem)}
                key={`${idx}`}
              >
                <MemoCheckMarkIcon
                  className={styles.appliancesItemIcon}
                />
                <Typography
                  className={styles.appliancesItemText}
                >
                  <PrismicRichText
                    field={appliance_item}
                  />
                </Typography>
              </li>
            );
          })}
        </ul>
        <Button
          as={Link}
          href="#booking-form"
          isAnimated
          className={classNames(
            styles.bookButton,
            gridSprinkle({
              type: "item",
              alignSelf: "center",
              justifySelf: {
                mobile: "stretch",
                tablet: "center",
              },
              cols: {
                desktop: 10,
                mobile: 10,
                tablet: 10,
              },
            })
          )}
        >
          Book an Expert
        </Button>
      </section>
    </Container>
  );
};

export default ApplianceSection;
