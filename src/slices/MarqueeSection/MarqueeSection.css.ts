import { breakpointValues } from "@/styles/constants.css";
import { theme } from "@/styles/themes.css";
import { style, styleVariants } from "@vanilla-extract/css";

const marqueeDefault = style({
  backgroundColor: theme.colors.primary.castletonGreen
});
const marqueeDark = style({

});
const marqueeBase = style({
  margin: "0 auto"
});

export const marquee = styleVariants({
  default: [marqueeBase,marqueeDefault],
  dark: [marqueeBase,marqueeDark],
});

export const movingTextDividerImg = style({
  width: 86,
  height: "min-content"
});

const movingTextWrapperBase = style({
  display: "flex",
  gap: "16px",
  width: "100%",
  height: 100,
  alignItems: "center"
});
const movingTextWrapperDefault = style({
  color: theme.colors.primary.softWhite
});
const movingTextWrapperDark = style({
 
});

export const movingTextWrapper = styleVariants({
  default: [movingTextWrapperBase, movingTextWrapperDefault],
  dark: [movingTextWrapperBase, movingTextWrapperDark],
});

export const root = style({
  backgroundColor: theme.colors.primary.castletonGreen
});