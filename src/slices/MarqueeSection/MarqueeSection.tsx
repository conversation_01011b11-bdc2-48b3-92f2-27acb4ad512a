import Marquee from "@/components/Marquee";
import { Content } from "@prismicio/client";
import { SliceComponentProps } from "@prismicio/react";
import * as styles from "./MarqueeSection.css";
import { PrismicNextImage } from "@prismicio/next";
import Typography from "@/components/Typography";
import { PrismicRichText } from "node_modules/@prismicio/react/dist/react-server/PrismicRichText";

/**
 * Props for `MarqueeSection`.
 */
export type MarqueeSectionProps =
  SliceComponentProps<Content.MarqueeSectionSlice>;

/**
 * Component for "MarqueeSection" Slices.
 */
const MarqueeSection = ({ slice }: MarqueeSectionProps): JSX.Element => {
  return (
    <div
      className={styles.root}
    >
      <Marquee
        className={styles.marquee[slice.variation]}
      > 
        <div
          className={styles.movingTextWrapper[slice.variation]}
        >
          <PrismicNextImage
            className={styles.movingTextDividerImg}
            field={slice.primary.text_divider}
          />
          <Typography
            isGreenItalic={slice.variation === "default"}
            fontFamily="primary"
            as={"div"}
            variant="h1"
          >
            <PrismicRichText
              field={slice.primary.title}
            />
          </Typography>
        </div>
      </Marquee>
    </div>
  );
};

export default MarqueeSection;
