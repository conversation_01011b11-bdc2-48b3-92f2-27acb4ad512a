import { Content } from "@prismicio/client";
import { SliceComponentProps } from "@prismicio/react";
import * as styles from "./SubscribtionsSection.css";
import Container from "@/components/Container";
import Typography from "@/components/Typography";
import { PrismicRichText } from "node_modules/@prismicio/react/dist/react-server/PrismicRichText";
import Button from "@/components/Button";
import { PrismicNextLink } from "@prismicio/next";
import classNames from "classnames";
import { gridSprinkle } from "@/styles/sprinkles.css";

export type SubscribtionsSectionProps =
  SliceComponentProps<Content.SubscribtionsSectionSlice>;

const SubscribtionsSection = (
  {
    slice,
  }: SubscribtionsSectionProps
): JSX.Element => {
  return (
    <Container>
      <section
        className={styles.root}
        data-slice-type={slice.slice_type}
        data-slice-variation={slice.variation}
      >
        <div
          className={classNames(
            styles.info, gridSprinkle(
              { type: "grid" }
            )
          )}
        >
          <Typography
            className={classNames(
              styles.title, gridSprinkle(
                { type: "item", cols: { mobile: 10, tablet: 6 } }
              )
            )}
            variant="h3"
          >
            <PrismicRichText
              field={slice.primary.title}
            />
          </Typography>              
          <Typography
            className={classNames(
              styles.description, gridSprinkle(
                { type: "item", cols: { mobile: 10, tablet: 4 } }
              )
            )}
            variant="bodySmall"
          >
            <PrismicRichText
              field={slice.primary.description}
            />
          </Typography> 
        </div>
        <div
          className={styles.plansList}
        >
          {slice.items.map(
            (
              item, index
            ) => (
              <div
                className={classNames(
                  styles.planSection, gridSprinkle(
                    { type: "grid" }
                  )
                )}
                key={index}
              >
                <div
                  className={classNames(
                    styles.planInfo, gridSprinkle(
                      { type: "item", cols: { mobile: 10, tablet: 6 } }
                    )
                  )}
                >
                  <Typography
                    className={styles.planName}
                    variant="h4"
                  >
                    {item.plan_title}
                  </Typography> 
                  <Typography
                    className={styles.planPrice}
                    variant="subTitleMedium"
                  >
                    <PrismicRichText
                      field={item.plan_price}
                    />
                  </Typography>
                  <Button
                    as={PrismicNextLink}
                    field={item.plan_link}
                    isAnimated
                  >
                    {item.plan_link_text}
                  </Button>            
                  <div
                    className={styles.planDivider}
                  />
                </div>
                <div
                  className={gridSprinkle(
                    { type: "item", cols: { mobile: 10, tablet: 4 } }
                  )}
                >
                  <div
                    className={styles.planBenefits}
                  >
                    {item.plan_benefits?.split(
                      "|"
                    ).map(
                      (
                        benefit
                      ) => (
                        <div
                          className={styles.planBenefit}
                          key={benefit}
                        >
                          <svg
                            className={styles.planBenefitIcon}
                            width="20"
                            height="20"
                            viewBox="0 0 20 20"
                            fill="none"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <path
                              fillRule="evenodd"
                              clipRule="evenodd"
                              d="M2.26367 9.40601C2.26367 9.40601 3.97167 8.33281 4.52727 8.13401C4.76687 8.57001 6.55167 11.4304 6.82927 12.1032C7.70407 11.2696 13.2617 5.03641 18.2637 3.96561C17.8273 4.67961 17.5497 4.99801 17.5497 4.99801C17.5497 4.99801 8.93527 10.556 7.70407 16.0344C6.59247 16.0344 6.03647 16.0344 6.03647 16.0344C6.03647 16.0344 4.20887 11.9452 2.26367 9.40601Z"
                              fill="currentColor"
                            />
                          </svg>
                          <Typography
                            variant="bodySmall"
                            className={styles.planBenefitText}
                          >
                            {benefit}
                          </Typography>
                        </div>
                      )
                    )}
                  </div>
                </div>
              </div>
            )
          )}
        </div>
      </section>
    </Container>
  );
};

export default SubscribtionsSection;
