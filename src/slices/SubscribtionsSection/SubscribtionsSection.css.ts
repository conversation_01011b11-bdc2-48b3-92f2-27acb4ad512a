import { breakpoints } from "@/styles/constants.css";
import { theme } from "@/styles/themes.css";
import { globalStyle, style } from "@vanilla-extract/css";

export const root = style(
  {
    padding: "40px 20px",
    backgroundColor: theme.colors.primary.castletonGreen,
    color: theme.colors.primary.ivory,
    borderRadius: 24,

    "@media": {
      [breakpoints.tablet]: {
        padding: "72px 48px",
      }
    },
  }
);

export const title = style(
  {
    fontFamily: theme.fonts.primary,
    marginBottom: "24px",

    "@media": {
      [breakpoints.tablet]: {
        marginBottom: "100px",
      }
    },
  }
);

export const description = style(
  {
    marginBottom: "24px",
    flex: "1 1",
    maxWidth: 420,
  }
);

export const info = style(
  {
  }
);

export const plansList = style(
  {
    display: "flex",
    flexDirection: "column",
    gap: 10,

    "@media": {
      [breakpoints.tablet]: {
        gap: 24 ,
      }
    },
  }
);

export const planInfo = style(
  {
    display: "flex",
    flexDirection: "column",
    alignItems: "center",
    textAlign: "center",

    "@media": {
      [breakpoints.tablet]: {
        textAlign: "left",
        alignItems: "flex-start",
      }
    },
  }
);

export const planSection = style(
  {
    padding: "40px 20px",
    borderRadius: 16,
    backgroundColor: theme.colors.primary.ivory,
    color: theme.colors.primary.castletonGreen,

    "@media": {
      [breakpoints.tablet]: {
        padding: 56,
      }
    },
  }
);

export const planName = style(
  {
    fontFamily: theme.fonts.primary,
    marginBottom: 10,

    "@media": {
      [breakpoints.tablet]: {
        marginBottom: 32,
      }
    },
  }
);

export const planPrice = style(
  {
    fontWeight: "400 !important",
    marginBottom: 32,

    "@media": {
      [breakpoints.tablet]: {
        marginBottom: 48,
      }
    },
  }
);

globalStyle(`${planPrice} b`, {
  fontWeight: 500,
});

export const planBenefits = style(
  {
    display: "flex",
    flexDirection: "column",
    gap: 14,
  }
);

export const planBenefit = style(
  {
    display: "flex",
    gap: 10,
  }
);

export const planBenefitIcon = style(
  {
  }
);

export const planBenefitText = style(
  {
    fontWeight: 500,
  }
);

export const planDivider = style(
  {
    height: 1,
    width: "100%",
    margin: "24px 0",
    backgroundColor: theme.colors.grayscale[100],
    opacity: 0.2,

    "@media": {
      [breakpoints.tablet]: {
        display: "none",
      }
    },
  }
);
