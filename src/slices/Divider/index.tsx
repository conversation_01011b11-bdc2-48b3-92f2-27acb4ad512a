import Container from "@/components/Container";
import Divider from "@/components/Divider/Divider";
import { Content } from "@prismicio/client";
import { SliceComponentProps } from "@prismicio/react";
import * as styles from "./Divider.css";

/**
 * Props for `Divider`.
 */
export type DividerProps = SliceComponentProps<Content.DividerSlice>;

/**
 * Component for "Divider" Slices.
 */
const DividerSection = ({ slice }: DividerProps): JSX.Element => {
  return (
    <Container
      notFullHeight
      className={styles.container}
    >
      <section
        data-slice-type={slice.slice_type}
        data-slice-variation={slice.variation}
      >
        <Divider />
      </section>
    </Container>
  );
};

export default DividerSection;
