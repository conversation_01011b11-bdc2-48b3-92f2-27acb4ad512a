[{"__TYPE__": "SharedSliceContent", "variation": "default", "primary": {"title": {"__TYPE__": "StructuredTextContent", "value": [{"type": "paragraph", "content": {"text": "Fugiat enim aliqua velit aliqua irure aliquip excepteur id est in qui incididunt labore enim quis. Aliqua nisi non ullamco."}}]}, "last_update": {"__TYPE__": "<PERSON><PERSON><PERSON><PERSON>", "type": "Date", "value": "2020-01-12"}, "description": {"__TYPE__": "StructuredTextContent", "value": [{"type": "paragraph", "content": {"text": "In in sint fugiat id. Eu tempor in sit enim excepteur in nostrud cillum est mollit quis nisi culpa est cupidatat. Incididunt elit dolor elit laboris dolore minim."}}]}}, "items": [{"__TYPE__": "GroupItemContent", "value": [["block_title", {"__TYPE__": "<PERSON><PERSON><PERSON><PERSON>", "value": "captain", "type": "Text"}], ["block_content", {"__TYPE__": "StructuredTextContent", "value": [{"type": "paragraph", "content": {"text": "Nostrud exercitation culpa proident anim dolor tempor tempor laborum voluptate reprehenderit ex ipsum. Deserunt ad irure laborum quis sunt aliquip veniam nulla id dolore dolore aliquip proident eiusmod eiusmod. Fugiat consequat consequat nulla excepteur esse sit cillum id ullamco."}}]}]]}]}]