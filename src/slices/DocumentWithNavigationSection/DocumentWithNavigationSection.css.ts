import { breakpoints } from "@/styles/constants.css";
import { mode } from "@/styles/functions.css";
import { theme } from "@/styles/themes.css";
import { globalStyle, style, styleVariants } from "@vanilla-extract/css";

export const container = style({
  selectors: {
    [mode("residential")]: {
      backgroundColor: theme.colors.primary.castletonGreen,
    }
  },
});

export const root = style({
  padding: "40px 0 0",
  
  "@media": {
    [breakpoints.tablet]: {
      padding: "80px 0",
    }
  }
});

export const title = style({
  fontFamily: theme.fonts.primary,
  marginBottom: "24px",

  "@media": {
    [breakpoints.tablet]: {
      marginBottom: 0,
    },
  },

  selectors: {
    [mode("residential")]: {
      color: theme.colors.primary.softWhite,
    }
  },
});

export const description = style({
  selectors: {
    [mode("residential")]: {
      color: theme.colors.primary.softWhite,
      opacity: 0.8,
    }
  },
});


export const paper = style({
  marginTop: 32,
  backgroundColor: theme.colors.primary.softWhite,
  borderRadius: 16,
  padding: 20,
  display: "grid",
  rowGap: 56,

  "@media": {
    [breakpoints.tablet]: {
      marginTop: 100,    
      borderRadius: 24,
      padding: 40,
    }
  }
});

export const blockTitle = style({
  fontFamily: theme.fonts.primary,
  marginBottom: 24,

  "@media": {
    [breakpoints.tablet]: {
      marginBottom: 32,
    }
  }
});

export const block = style({
  scrollMarginTop: 100,
  whiteSpace: "break-spaces",

  "@media": {
    [breakpoints.tablet]: {
      
    }
  }
});

export const blockContent = style({
  ":first-child": {
    marginTop: 72,
  }
});

globalStyle(`${blockContent} p`, {
  marginBottom: 24,

  "@media": {
    [breakpoints.tablet]: {
      marginBottom: 32,
    }
  }
});

globalStyle(`${blockContent} p:not(:first-of-type)`, {
  fontSize: "16px !important",

  "@media": {
    [breakpoints.tablet]: {
      fontSize: "18px !important",
    }
  }
});

globalStyle(`${blockContent} ul`, {
  marginBottom: 16,
  paddingLeft: 24,

  "@media": {
    [breakpoints.tablet]: {
      marginBottom: 24,
    }
  }
});

globalStyle(`${blockContent} li`, {
  marginBottom: 8,
  fontSize: "16px !important",


  "@media": {
    [breakpoints.tablet]: {
      marginBottom: 16,
      fontSize: "18px !important",
    }
  }
});

globalStyle(`${blockContent} h5`, {
  marginBottom: 16,
  fontSize: "22px",
  letterSpacing: "-0.01em",
  fontWeight: 500,
  lineHeight: "120%",

  "@media": {
    [breakpoints.tablet]: {
      fontSize: "24px",
      marginBottom: 24,
    },
  },
});