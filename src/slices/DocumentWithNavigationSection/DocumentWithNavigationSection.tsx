"use client";

import Container from "@/components/Container";
import Typography from "@/components/Typography";
import { gridSprinkle } from "@/styles/sprinkles.css";
import { Content } from "@prismicio/client";
import { SliceComponentProps } from "@prismicio/react";
import classNames from "classnames";
import { PrismicRichText } from "node_modules/@prismicio/react/dist/react-server/PrismicRichText";
import * as styles from "./DocumentWithNavigationSection.css";
import dayjs from "dayjs";
import { useEffect, useLayoutEffect, useMemo, useRef, useState } from "react";
import NavList from "@/components/NavList";
import { parseStringToValidId } from "@/utils/helpers";
import ModeProvider from "@/components/ModeProvider";

export type DocumentWithNavigationSectionProps =
  SliceComponentProps<Content.DocumentWithNavigationSectionSlice>;

const DocumentWithNavigationSection = ({
  slice,
}: DocumentWithNavigationSectionProps): JSX.Element => {
  const documentRef = useRef<HTMLDivElement>(null);
  const [activeSection, setActiveSection] = useState(String(slice.items[0].block_title));

  useEffect(() => {
    const handleScroll = () => {
      if (!documentRef.current) return;

      const documentRect = documentRef.current.getBoundingClientRect();

      const deeperElement = document.elementFromPoint(documentRect.left + documentRect.width / 2, 100);

      if (!deeperElement) return;

      const closestDocumentBlock = deeperElement.closest(`.${styles.block}`);

      if (!closestDocumentBlock) return;

      setActiveSection(closestDocumentBlock.getAttribute("id") ?? parseStringToValidId(String(slice.items[0].block_title)));
    };

    window.addEventListener("scroll", handleScroll);

    return () => {
      window.removeEventListener("scroll", handleScroll);
    };
  }, []);

  useLayoutEffect(() => {
    if(!window || !documentRef.current || !window.location.hash) return;
    const activeBlock = documentRef.current.querySelector(window.location.hash);
    if(!activeBlock) return;
    activeBlock?.scrollIntoView();
  }, []);

  const navOptions = useMemo(() => {
    return slice.items.map((item) =>{
      const label = `${item.block_title}`;
      return ({href: parseStringToValidId(label, true), label});});
  } , []);

  return (
    <Container
      className={styles.container}
    >
      <section
        className={classNames(styles.root, gridSprinkle({ type: "grid" }))}
        data-slice-type={slice.slice_type}
        data-slice-variation={slice.variation}
      >
        {!!slice.primary.title.length && <Typography
          variant="h2"
          as={"h1"}
          className={classNames(styles.title, gridSprinkle({ type: "item", cols: { mobile: 10, tablet: 6 } }))}
        >
          <PrismicRichText
            field={slice.primary.title}
          />
        </Typography>}
        <Typography
          className={classNames(gridSprinkle({ type: "item", cols: { mobile: 10, tablet: 4 }, justifySelf: { mobile: "start", tablet: "end" }, alignSelf: "center" }), styles.description)}
        >
          Last updated on:
          {" "}
          {dayjs(slice.primary.last_update).format("D/MM/YYYY")}
        </Typography>

        <ModeProvider>
          {(mode) => (
            <NavList
              options={navOptions}
              active={activeSection}
              checkValue={({href}) => href.replace("#", "")}
              className={gridSprinkle({ type: "item", cols: { mobile: 10, tablet: 3 }, display: { mobile: "none", tablet: "flex" } })}
              darkMode={mode === "residential"}
            />
          )}
        </ModeProvider>

        <div
          ref={documentRef}
          className={classNames(styles.paper, gridSprinkle({ type: "item", cols: { mobile: 10, tablet: 7 } }))}
        >
          {!!slice.primary.description.length && (
            <Typography
              className={styles.blockContent}
            >
              <PrismicRichText
                field={slice.primary.description}
              />              
            </Typography>          
          )}
          {slice.items.map((item, index) => (
            <div
              className={styles.block}
              id={parseStringToValidId(String(item.block_title))}
              key={index}
            >
              <Typography
                className={styles.blockTitle}
                variant="h4"
              >
                {item.block_title}
              </Typography>
              <Typography
                className={styles.blockContent}
              >
                <PrismicRichText
                  field={item.block_content}
                />              
              </Typography>
            </div>          
          ))}
        </div>
      </section>
    </Container>
  );
};

export default DocumentWithNavigationSection;
