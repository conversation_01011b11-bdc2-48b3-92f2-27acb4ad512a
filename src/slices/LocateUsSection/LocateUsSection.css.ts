import { breakpoints } from "@/styles/constants.css";
import { theme } from "@/styles/themes.css";
import { style } from "@vanilla-extract/css";

export const title = style({

});

export const root = style({
  display: "grid",
  rowGap: 40,
  marginTop: 80,
  marginBottom: 80,
});

export const markerContainer = style({
  position: "relative",
  borderRadius: "50%",
  backgroundColor: theme.colors.primary.asidGreen,
  width: 74,
  height: 74,
  overflow: "hidden"
});

export const smallMarker = style({
  position: "absolute",
  bottom: -12,
  right: -12,
  width: 48,
  height: 48,
  fontSize: 24,
  borderRadius: "50%",
  display: "grid",
  placeItems: "center",
  border: `1.5px solid ${theme.colors.primary.softWhite}`,
  backgroundColor: theme.colors.primary.castletonGreen,
  color: theme.colors.primary.softWhite
});

export const imageWrapper = style({
  
});

export const imageMarker = style({
  width: "100%",
  height: "100%",
  objectFit: "cover",
});

export const overlayContainer = style({
  backgroundColor: theme.colors.primary.castletonGreen,
  display: "grid",
  rowGap: 16,
  position: "relative",
  transform: "translate(-50%, 20%)",
  borderRadius: 16,
  padding: 8,
  minWidth: 280,
  "@media": {
    [breakpoints.tablet]: {
      minWidth: 320,
      transform: "translate(20%, -50%)",
      borderRadius: 24,
      width: 400,
      padding: 24,
      
    }
  }

});

export const img = style({
  width: "100%",
  height: 122,
  objectFit: "cover",
  borderRadius: 16
});

export const textWrapper = style({
  display: "grid",
  gridTemplateColumns: "auto 1fr",
  columnGap: 16,
  fontSize: 24,
  color: theme.colors.primary.ivory
});

export const iconWrapper = style({
  color: theme.colors.primary.asidGreen,
  fontSize: 24
});

export const popoverText = style({
  color: theme.colors.primary.softWhite
});

export const popoverArrow = style({
  position: "absolute",
  borderTop: "12px solid transparent",
  borderBottom: "12px solid transparent",
  borderRight: `18px solid ${theme.colors.primary.castletonGreen}`,
  width: 18,
  top: -18,
  left:"50%",
  transform: "rotate(90deg) translateY(50%)",
  "@media": {
    [breakpoints.tablet]: {
      transform: "none",
      left: -18,
      top: "50%",

    }
  }
});

export const mapLocationLink = style({
  display: "contents"
});

export const phoneNumber = style({
  color: theme.colors.primary.asidGreen
});

export const mapContainer = style({
  width: "100%",
  height: "calc(100vh - 120px)",
  "@media": {
    [breakpoints.tablet]: {
      height: "600px",
      borderRadius: "24px",

    }
  }
});