"use client";


import { Content, GroupField } from "@prismicio/client";
import { PrismicRichText, SliceComponentProps } from "@prismicio/react";
import { GoogleMap, InfoWindow, OverlayView, useJsApiLoader,  } from "@react-google-maps/api";
import { useEffect, useLayoutEffect, useMemo, useRef, useState } from "react";
import mapStyles from "./mapStyles.json";
import Typography from "@/components/Typography";
import { getNumbersFromString, getURLToGoogleMapByPlaceId, throttle, toCamelCase } from "@/utils/helpers";
import * as styles from "./LocateUsSection.css";
import {AdvancedMarker, useAdvancedMarkerRef} from "@/components/AdvancedMarker/AdvancedMarker";
import { EmergencyLocationsDocumentDataLocationsItem, Simplify } from "prismicio-types";
import { useParams } from "next/navigation";
import MemoMarkerLocationIcon from "@/assets/icons/MarkerLocationIcon";
import { PrismicNextImage, PrismicNextLink } from "@prismicio/next";
import Container from "@/components/Container";
import Divider from "@/components/Divider/Divider";
import TimeIcon from "@/assets/icons/TimeIcon";
import MemoPhoneIcon from "@/assets/icons/PhoneIcon";
import Link from "next/link";
import { EmergenciesByCityContextProps } from "@/components/EmergenciesByCityPage/EmergenciesByCityPage.types";
import { breakpointValues } from "@/styles/constants.css";

/**
 * Props for `LocateUsSection`.
 */
export type LocateUsSectionProps = {context: EmergenciesByCityContextProps} &
  SliceComponentProps<Content.LocateUsSectionSlice>; 

  
// const center = {
//   lat: 46.483357100651816,
//   lng: 30.730532719888537,
// };

const libraries: any = ["marker"];

/**
 * Component for "LocateUsSection" Slices.
 */
const LocateUsSection = ({ slice, context: {locations,} }: LocateUsSectionProps): JSX.Element => {
  const params = useParams<{uid: string}>();

  const isMobile = useRef(false);

  
  // const [markerRef, marker] = useAdvancedMarkerRef();
  // const [zoom, setZoom] = useState(17);
  const [map, setMap] = useState<google.maps.Map | null>(null);
  const [popoverIsOpen, setPopoverIsOpen] = useState(true);

  const emergencyLocation = useMemo(() => params?.uid && locations ? locations.find(({uuid}) => params.uid === uuid) : null , [params?.uid, locations]);

  const markerPosition  = useMemo(() => (emergencyLocation ?{lat: emergencyLocation?.location.latitude, lng: emergencyLocation?.location.longitude}  : null) , [emergencyLocation]);

  const { isLoaded } = useJsApiLoader({
    id: "google-map-script",
    libraries ,
    googleMapsApiKey: process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY ?? "",

  });

  useLayoutEffect(() => {

    // This effect was made to move map center a little bit to the top
    // because if marker was directly in the center we can't see full popup (OverlayView)

    if(!markerPosition || !map) {
      return;
    };

    const resizeEventCallback = (e: UIEvent) => {
      if(!e.currentTarget) return;
      //@ts-ignore
      const width = e.currentTarget.innerWidth ;

      if(width >= breakpointValues.md && isMobile.current) {
        isMobile.current = false;
        map?.setCenter(markerPosition);
      }
      if(width < breakpointValues.md && !isMobile.current) {
        isMobile.current = true;
        map?.setCenter({lat: markerPosition.lat - 0.001, lng: markerPosition.lng });
      }
    };

    // @ts-ignore
    resizeEventCallback({currentTarget:{innerWidth: window.innerWidth}});

    window.addEventListener("resize", throttle(resizeEventCallback, 3000));
    return () => {
      window.removeEventListener("resize", resizeEventCallback);
      
    };
  }, [markerPosition, map]);
  

  const onMarkerClick = () => {
    setPopoverIsOpen(prev => !prev);};

  return (
    <Container>
      <section
        id={slice.primary.header_anchor_name ? toCamelCase(slice.primary.header_anchor_name) : ""}
        data-slice-type={slice.slice_type}
        data-slice-variation={slice.variation}
        className={styles.root}
      >
        <Typography
          as={"h2"}
          variant="h2"
          fontFamily="primary"
          className={styles.title}
        >
          <PrismicRichText
            field={slice.primary.title}
          />
        </Typography>
        {isLoaded && markerPosition && (
          <GoogleMap
            options={{
              // Cannot use styles with mapId
              // To change styles we need to use 
              // styles: mapStyles,
              disableDefaultUI: true,
              mapId: process.env.NEXT_PUBLIC_GOOGLE_MAP_ID
            }}
            mapContainerClassName={styles.mapContainer}
            center={markerPosition}
            zoom={17}
            onLoad={(map) => setMap(map)}
            onUnmount={() => setMap(null)}
          >
            {markerPosition && (
              <AdvancedMarker
                // ref={markerRef}
                map={map}
                position={markerPosition}
                onClick={onMarkerClick}
                clickable
              >
                <div
                  className={styles.markerContainer}
                >
                
                  <PrismicNextImage
                    className={styles.imageMarker}
                    field={emergencyLocation?.marker_image}
                  />
                </div>
                <div
                  className={styles.smallMarker}
                >
                  <MemoMarkerLocationIcon/>
                </div>
             
              </AdvancedMarker>)}
            {markerPosition && popoverIsOpen && !!emergencyLocation && (
              <OverlayView
                mapPaneName="floatPane"
                position={markerPosition}
                zIndex={99999}

                // onCloseClick={onMarkerClick}
                // anchor={marker}
              >
                <div
                  className={styles.overlayContainer}
                >
                  <div
                    className={styles.popoverArrow}
                  />
                  <Link
                    className={styles.mapLocationLink}
                    target="_blank"
                    href={getURLToGoogleMapByPlaceId(emergencyLocation.place_id as string, emergencyLocation.location_name as string)}
                    // field={emergencyLocation.google_map_location}
                  >
                    <div>
                      <PrismicNextImage
                        className={styles.img}
                        field={emergencyLocation?.location_image}
                      />
                    </div>
                    <div
                      className={styles.textWrapper}
                    >
                      <div
                        className={styles.iconWrapper}
                      >
                        <MemoMarkerLocationIcon/>
                      </div>
                      <Typography
                        variant="bodySmall"
                      >
                        {emergencyLocation.location_name}
                      </Typography>
                    </div>
                    <div
                      className={styles.textWrapper}
                    >
                      <div
                        className={styles.iconWrapper}
                      >
                        <TimeIcon/>
                      </div>
                      <Typography
                        variant="bodySmall"
                      >
                        {emergencyLocation.work_time}
                      </Typography>
                    </div>
                  </Link>
                  <Divider
                    withOpacity
                  />
                  <div
                    className={styles.textWrapper}
                  >
                    <div
                      className={styles.iconWrapper}
                    >
                      <MemoPhoneIcon/>
                    </div>
                    <Typography
                      className={styles.phoneNumber}
                      prefetch={false}
                      as={Link}
                      href={`tel:${emergencyLocation.phone_number ? getNumbersFromString(emergencyLocation.phone_number) : ""}`}
                      variant="bodyMedium"
                    >
                      {emergencyLocation?.phone_number}
                    </Typography>
                  </div>
                </div>
              </OverlayView>
            )}
          </GoogleMap>
        )}
      </section>
    </Container>
  );
};

export default LocateUsSection;
