"use client";

import Container from "@/components/Container";
import { Content } from "@prismicio/client";
import { SliceComponentProps } from "@prismicio/react";
import { GoogleMap, Marker, useJsApiLoader } from "@react-google-maps/api";
import { memo, useState } from "react";
import { mapMarkerSvg } from "./markerSvg";

export type MapSectionProps = SliceComponentProps<Content.MapSectionSlice>;

const containerStyle = {
  width: "100%",
  borderRadius: "24px",
  height: "600px",
};

const center = {
  lat: 46.483357100651816,
  lng: 30.730532719888537,
};

const MapSection = ({ slice }: MapSectionProps): JSX.Element => {
  //const [zoom, setZoom] = useState(17);
  //const [map, setMap] = useState<google.maps.Map | null>(null);

  //const { isLoaded } = useJsApiLoader({
  //  id: "google-map-script",
  //  //googleMapsApiKey: "",
  //});

  //return (
  //  <Container>
  //    <section
  //      data-slice-type={slice.slice_type}
  //      data-slice-variation={slice.variation}
  //    >
  //      {isLoaded && (
  //        <GoogleMap
  //          options={{
  //            styles: mapStyles,
  //            disableDefaultUI: true,
  //          }}
  //          mapContainerStyle={containerStyle}
  //          center={center}
  //          zoom={zoom}
  //          onLoad={(map) => setMap(map)}
  //          onUnmount={() => setMap(null)}
  //        >
  //          <Marker
  //            animation={google.maps.Animation.DROP}
  //            icon={{
  //              url: "data:image/svg+xml;charset=UTF-8," + encodeURIComponent(mapMarkerSvg), scaledSize: new google.maps.Size(90, 98) 
  //            }}
  //            title="Pleasant Plumbers"
  //            position={center}
  //          />
  //        </GoogleMap>
  //      )}
  //    </section>
  //  </Container>
  //);
  return <></>;
};

export default memo(MapSection);
