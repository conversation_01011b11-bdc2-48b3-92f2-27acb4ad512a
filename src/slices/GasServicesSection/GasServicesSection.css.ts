import { breakpoints } from "@/styles/constants.css";
import { theme } from "@/styles/themes.css";
import { style } from "@vanilla-extract/css";

export const root = style({
  display: "grid",
  rowGap: 40,
  margin: "80px 0",

  "@media": {
    [breakpoints.tablet]: {
      rowGap: 125,
      margin: "95px 0",
    }
  }
});

export const listContainer = style({
  borderRadius: 24,
  padding: 8,
  backgroundColor: theme.colors.primary.castletonGreen,
  color: theme.colors.primary.softWhite,
  zIndex: 1,
  position: "relative",
  "@media": {
    [breakpoints.tablet]: {
      justifySelf: "center",
    }
  }
  
});

export const listWrapper = style({
  display: "grid",
  listStyle: "none",
  borderRadius: 20,
  border: `1.5px solid ${theme.colors.primary.asidGreen}`,
  padding: "40px 24px",
  "@media": {
    [breakpoints.tablet]: {
      
      margin: "auto",
      padding: "58px 48px 38px 48px"
    }
  }
});

export const image = style({
  width: "100%",
  height: "100%",
  objectFit: "cover",
  
});

const imageWrapperBase = style({
  width: 470,
  height:495,
  position: "absolute",
  top: -32,
  borderRadius: 24,
  overflow: "hidden",
  display: "none",
  backgroundColor: "#C3E0CB",
  "@media": {
    [breakpoints.tablet]: {
      display: "block"
    }
  }
});

export const leftImageWrapper = style([imageWrapperBase,{
  left: 0,
  transform: "rotate(-10deg)"
}]);
export const rightImageWrapper = style([imageWrapperBase,{
  right: 0,
  transform: "rotate(10deg)"
}]);

export const listAdditionalWrapper = style({
  display: "contents"
});
export const list  = style({
  columnGap: "48px !important",
});

export const button = style({
  marginTop: 40,
  "@media": {
    [breakpoints.tablet]: {
      marginTop: 48,
      marginLeft: "auto",
      marginRight: "auto",
    }
  }

});

export const title = style({
  margin: "auto"
});

export const content = style({
  position: "relative",
  display: "grid"
});