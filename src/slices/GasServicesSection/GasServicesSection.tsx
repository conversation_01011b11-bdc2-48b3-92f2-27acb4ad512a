import Container from "@/components/Container";
import { Content } from "@prismicio/client";
import { PrismicRichText, SliceComponentProps } from "@prismicio/react";

import * as styles from "./GasServicesSection.css";
import ShrinksList from "@/components/ShrinksList/ShrinksList";
import BookServiceButton from "@/components/BookServiceButton/BookServiceButton";
import { PrismicNextImage } from "@prismicio/next";
import Typography from "@/components/Typography";
import { toCamelCase } from "@/utils/helpers";

/**
 * Props for `GasServicesSection`.
 */
export type GasServicesSectionProps =
  SliceComponentProps<Content.GasServicesSectionSlice>;

/**
 * Component for "GasServicesSection" Slices.
 */
const GasServicesSection = ({
  slice,
}: GasServicesSectionProps): JSX.Element => {



  return (
    <Container>
      <section
        id={slice.primary.header_anchor_name ? toCamelCase(slice.primary.header_anchor_name) : ""}
        className={styles.root}
        data-slice-type={slice.slice_type}
        data-slice-variation={slice.variation}
      >
        <Typography
          fontFamily="primary"
          className={styles.title}
          variant="h1"
          as={"h2"}
        >
          <PrismicRichText
            field={slice.primary.title}
          />
        </Typography>
        <div
          className={styles.content}
        >
          <div
            className={styles.leftImageWrapper}
          >
            <PrismicNextImage
              className={styles.image}
              field={slice.primary.left_image}
            />
          </div>
          <div
            className={styles.listContainer}
          >
            <div
              className={styles.listWrapper}
            >
              <ShrinksList
                className={styles.listAdditionalWrapper}
                listClassName={styles.list}
                variant="secondary"
                cols={2}
                maxAmount={9}
                data={slice.items.map(({service}) => service)}
              />
              <BookServiceButton
                className={styles.button}
              />
            </div>
          </div>
          <div
            className={styles.rightImageWrapper}
          >
            <PrismicNextImage
              className={styles.image}
              field={slice.primary.right_image}
            />
          </div>
        </div>
      </section>
    </Container>
  );
};

export default GasServicesSection;
