"use client";
import { Content } from "@prismicio/client";
import { PrismicImage, PrismicLink, PrismicRichText, SliceComponentProps } from "@prismicio/react";
import * as styles from "./HelpAndAdviceSection.css";
import Container from "@/components/Container";
import classNames from "classnames";
import Typography from "@/components/Typography";
import IconButton from "@/components/IconButton";
import { ChevronIcon } from "@/assets/icons/ChevronIcon";
import useScrollHorList from "@/hooks/useScrollHorList";
import NextArrowIcon from "@/assets/icons/NextArrowIcon";
import { toCamelCase } from "@/utils/helpers";
import Button from "@/components/Button";
import Link from "next/link";

/**
 * Props for `HelpAndAdviceSection`.
 */
export type HelpAndAdviceSectionProps =
  SliceComponentProps<Content.HelpAndAdviceSectionSlice>;

/**
 * Component for "HelpAndAdviceSection" Slices.
 */
const HelpAndAdviceSection = ({
  slice,
}: HelpAndAdviceSectionProps): JSX.Element => {

  const { handleNext,handlePrev,setRef } = useScrollHorList();

  return (
    <Container>
      <section
        id={slice.primary.header_anchor_name ? toCamelCase(slice.primary.header_anchor_name) : ""}
        className={classNames(styles.root,)}
        data-slice-type={slice.slice_type}
        data-slice-variation={slice.variation}
      >
     
        <Typography
          as={"h2"}
          variant="h2"
          fontFamily="primary"
          className={styles.title}
        >
          <PrismicRichText
            field={slice.primary.title}
          />
         
        </Typography>
        <div
          className={styles.controlsWrapper}
        >
          <IconButton
            onClick={handlePrev}
          >
            <ChevronIcon
              turn="left"
              className={styles.controlsIcon}
            />
          </IconButton >
          <IconButton
            onClick={handleNext}
          >
            <ChevronIcon
              turn="right"
              className={styles.controlsIcon}
            />
          </IconButton>
        </div>
        <ul
          ref={setRef}
          className={ classNames(styles.listWrapper)}
        >
          {slice.items.map(({ title, link_to,tags, hero }, idx) => {
            return (
              <li
                key={idx}
                className={styles.listItemWrapper}
              >
                <PrismicLink
                  className={styles.listItem}
                  field={link_to}
                >
                  <div
                    className={styles.listItemImgWrapper}
                  >
                    <PrismicImage
                      className={styles.listItemImg}
                      field={hero}
                    />
                  </div>
                  <div
                    className={styles.tagsWrapper}
                  >
                    {tags?.split(",").map(tag => (
                      <Typography
                        key={tag}
                        className={styles.tagItem}
                        variant="subTitleSmall"
                      >
                        {tag}
                      </Typography>)
                    )}
                  </div>
                  <Typography
                    className={styles.listItemDescriptionWrapper}
                    variant="buttonMedium"
                  >
                    <PrismicRichText
                      field={title}
                    />
                  </Typography>
                  <IconButton
                    shape="rect"
                    variant="outlined"
                    size="small"
                    className={styles.listItemButton}
                  >
                    <NextArrowIcon/>
                  </IconButton>
                </PrismicLink>
              </li>
            );
          })}
        </ul>
        <div
          className={styles.learnMoreButton}
        >
          <Button
            isAnimated
            as={Link}
            href="/learn"
          >Learn More</Button>
        </div>
      </section>
    </Container>
  );
};

export default HelpAndAdviceSection;
