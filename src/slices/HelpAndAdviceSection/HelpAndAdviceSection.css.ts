import { breakpoints } from "@/styles/constants.css";
import { theme } from "@/styles/themes.css";
import { style } from "@vanilla-extract/css";

export const root = style({
  display: "grid",
  gap: 32,
  marginTop: 60,
  marginBottom: 40,

  "@media": {
    [breakpoints.tablet]: {
      marginTop: 70,
      marginBottom: 70,
      gap: 72,
      gridTemplateColumns: "1fr auto"
    }
  }
});

export const controlsWrapper = style({
  gridRowStart: 3,
  display: "flex",
  columnGap: 8,
  gridColumn: "span 2",
  justifySelf: "center",
  "@media": {
    [breakpoints.tablet]: {
      alignSelf: "center",
      gridRowStart: "auto",
      gridColumn: "auto",
      justifySelf: "auto",
    }
  }
});

export const controlsIcon = style({
  width: 26,
  height: 26,
});

export const title = style({
  gridColumn: "span 2",
  fontSize: "36px !important",

  "@media": {
    [breakpoints.tablet]: {
      gridColumn: "auto",
      fontSize: "64px !important",
    }
  }

});


export const listWrapper = style({
  display: "grid",
  gridAutoFlow: "column",
  columnGap: 44,
  listStyle: "none",
  padding: "0 20px",
  overflow: "auto",
  gridColumn: "span 2",
  scrollSnapType: "x mandatory",
  justifyContent: "start",
  // margin: "0 10px",
  "@media": {
    [breakpoints.tablet]: {
      margin: 0,
    }
  }
});

export const listItemWrapper = style({
  height: 372,
  alignContent: "end",
  "@media": {
    [breakpoints.tablet]: {
      height: "auto",
      display: "grid",
      alignContent: "normal"
    }
  },
});

export const listItem = style({
  scrollSnapAlign: "center",
  textDecoration: "none",
  display: "grid",
  borderRadius: 16,
  backgroundColor: theme.colors.primary.softWhite,
  width: "calc(100vw - 20px)",
  padding: "0 20px 20px 20px",
  height: 275,
  gap: 24,
  alignContent: "end",

  "@media": {
    [breakpoints.tablet]: {
      height: "auto",
      alignContent: "normal",
      width: 487,
      padding: "20px 20px 20px 0",
      gridTemplateColumns: "auto 1fr",
     
    }
  },
});

export const listItemImgWrapper = style({
  
  borderRadius: 16,
  overflow: "hidden",
  marginTop: "-25%",
  height: 136,
  "@media": {
    [breakpoints.tablet]: {
      height: 196,
      width: 196,
      marginLeft: -20,
      marginTop: 0,
      gridRow: "span 3",
    }
  },

});

export const listItemImg = style({
  width: "100%",
  height: "100%",
  objectFit: "cover"
});

export const tagsWrapper = style({
  display: "flex",
  gap: 8
});

export const tagItem = style({
  borderRadius: 24,
  border: `1px solid ${theme.colors.grayscale[100]}`,
  height: 32,
  padding: "0 20px",
  display: "grid",
  placeItems: "center",

});

export const listItemDescriptionWrapper = style({
  alignTracks: "end",

  "@media": {
    [breakpoints.tablet]: {
      marginTop: "auto",
      alignSelf: "end",
    }
  }
});

export const listItemButton = style({
  "@media": {
    [breakpoints.tablet]: {
      alignSelf: "end",
    }
  }
});

export const learnMoreButton = style({
  display: "grid",
  gridColumn: "span 2",
  maxWidth: 312,
  width: "100%",
  margin: "auto",
  "@media": {
    [breakpoints.tablet]: {
      justifyItems: "center",

    }
  }
});