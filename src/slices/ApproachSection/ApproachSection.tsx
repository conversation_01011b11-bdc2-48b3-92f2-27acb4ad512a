"use client";

import Container from "@/components/Container";
import Typography from "@/components/Typography";
import { Content } from "@prismicio/client";
import { PrismicNextImage } from "@prismicio/next";
import { PrismicRichText, SliceComponentProps } from "@prismicio/react";
import * as styles from "./ApproachSection.css";
import classNames from "classnames";
import { gridSprinkle } from "@/styles/sprinkles.css";
import useObserveIntoView from "@/hooks/useObserveIntoView";
import { useCallback, useContext, useRef } from "react";
import useIsAnimationExist from "@/hooks/useIsAnimationExist";

export type ApproachSectionProps =
  SliceComponentProps<Content.ApproachSectionSlice>;

const ANIM_APPROACH_LIST_ID = "anim--aproach_list";
const ANIM_TITLE_ID = "anim--title";

const ApproachSection = ({ slice }: ApproachSectionProps): JSX.Element => {
  const containerRef = useRef<HTMLDivElement>(null);
  const isWithAnimation = useIsAnimationExist();

  const onInView = useCallback(
    (target?: HTMLElement) => {
      if (!target || !isWithAnimation) return;
      const approachList = target.querySelector(`#${ANIM_APPROACH_LIST_ID}`);
      const title = target.querySelector(`#${ANIM_TITLE_ID}`);

      approachList?.classList.add(styles.approachList.animate);

      title?.classList.add(styles.title.animate);
    },
    [isWithAnimation]
  );

  useObserveIntoView(containerRef, { onInView, isDisabled: !isWithAnimation, threshold: 0.3 });

  return (
    <Container
      ref={containerRef}
      className={styles.container}
    >
      <section
        className={classNames(
          styles.root,
          gridSprinkle({
            type: "grid",
          })
        )}
        data-slice-type={slice.slice_type}
        data-slice-variation={slice.variation}
      >
        <div
          className={classNames(styles.content, gridSprinkle({
            type: "item",
            cols: { mobile: 10, tablet: 6 },
          }))}
        >
          <Typography
            id={ANIM_TITLE_ID}
            className={classNames(styles.title.base)}
            variant="h2"
          >
            <PrismicRichText
              field={slice.primary.title}
            />
          </Typography>
          <div
            id={ANIM_APPROACH_LIST_ID}
            className={styles.approachList.base}
          >
            {slice.items.map((item, index) => (
              <div
                key={index}
                className={styles.paper}
              >
                <Typography
                  variant="subTitleMedium"
                  className={styles.approachTitle}
                >
                  <PrismicNextImage
                    field={item.approachIcon}
                    objectFit="cover"
                    objectPosition="center bottom"
                  />
                  {item.approachtitle}
                </Typography>
                <Typography
                  variant="bodySmall"
                >
                  <PrismicRichText
                    field={item.approachdescription}
                  />
                </Typography>
              </div>
            ))}
          </div>
        </div>
        <div
          className={classNames(
            styles.previewWrapper,
            gridSprinkle({
              type: "item",
              cols: { mobile: 10, tablet: 4 },
            })
          )}
        >
          <PrismicNextImage
            className={styles.preview}
            field={slice.primary.preview}
          />
        </div>
      </section>
    </Container>
  );
};

export default ApproachSection;
