import { breakpoints } from "@/styles/constants.css";
import { mode, modeGlobal } from "@/styles/functions.css";
import { theme } from "@/styles/themes.css";
import { createVar, globalStyle, keyframes, style, styleVariants } from "@vanilla-extract/css";

export const container = style({
  selectors: {
    [mode("residential")]: {
      background: theme.colors.primary.castletonGreen,
    }
  },
});

export const root = style({
  // aspectRatio: '16 / 9',
  padding: "0 20px",
  paddingTop: "40px",
  height: "100%",
  background: theme.colors.primary.ivory,
  willChange: "transform",
  "@media": {
    [breakpoints.tablet]: {
      padding: 0,
    },
  },

  selectors: {
    [mode("residential")]: {
      background: theme.colors.primary.castletonGreen,
    }
  },
});

export const content = style({
  padding: 0,
  display: "flex",
  flexDirection: "column",

  "@media": {
    [breakpoints.tablet]: {
      padding: "72px 48px",
    },
  },
});

export const titleBase = style({
  opacity: 0,
  fontFamily: theme.fonts.primary,
  marginBottom: 56,
  willChange: "opacity, transform",

  "@media": {
    [breakpoints.tablet]: {
      marginBottom: 64,
    },
  },

  selectors: {
    [mode("residential")]: {
      color: theme.colors.primary.softWhite,
    }
  },
});

globalStyle(`${modeGlobal("residential")} ${titleBase} strong`, {
  color: theme.colors.primary.asidGreen,
});

const titleSlide = keyframes({
  from: {
    opacity: 0,
    transform: "translateY(50px)",
  },
  to: {
    opacity: 1,
    transform: "translateY(0px)",
  },
});

export const title = styleVariants({
  base: [titleBase],
  animate: {
    animation: `1s ${titleSlide} forwards`,
  },
});

const approachListBase = style({
  display: "flex",
  gap: 16,
  flexWrap: "wrap",

  "@media": {
    [breakpoints.tablet]: {
      paddingRight: 40,
      gap: 24,
    },
  },
});

export const approachTitle = style({
  display: "flex",
  gap: 8,
  marginBottom: 12,

  "@media": {
    [breakpoints.tablet]: {
      marginBottom: 16,
    },
  },
});

export const previewWrapper = style({
  display: "flex",
  aspectRatio: "9 / 11",
  marginBottom: -10,
  padding: "0 30px",

  "@media": {
    [breakpoints.tablet]: {
      aspectRatio: "auto",
      marginBottom: -20,
    },
  },
});

export const preview = style({
  marginTop: "auto",
  width: "100% !important",
  height: "auto !important",
  objectFit: "contain",
  objectPosition: "center bottom"
});

const slideFromTopKeyFrame = keyframes({
  from: {
    transform: "translateY(0)",
  },
  to: {
    transform: "translateY(15%)",
  },
});

export const slideFromTop = style({
  willChange: "transform",
  animation: `1s ${slideFromTopKeyFrame}`,
});

const initialPaperMargin = createVar();

const approachListSlide = keyframes({
  from: {
    opacity: 0,
    transform: `translateY(${initialPaperMargin})`,
  },
  to: {
    opacity: 1,
    transform: "translateY(0px)",
  },
});

export const approachList = styleVariants({
  base: [approachListBase],
  animate: {
    willChange: "opacity, transform",
  },
});

export const paper = style({
  opacity: 0,
  backgroundColor: theme.colors.primary.softWhite,
  borderRadius: 16,
  padding: 20,
  flex: "1 1",
  minWidth: "100%",

  "@media": {
    [breakpoints.tablet]: {
      padding: 24,
      minWidth: "40%",
    }
  },

  selectors: {
    "&:nth-of-type(1)": {
      vars: {
        [initialPaperMargin]: "100px",
      }
    },
    "&:nth-of-type(2)": {
      vars: {
        [initialPaperMargin]: "150px",
      }
    },
    "&:nth-of-type(3)": {
      vars: {
        [initialPaperMargin]: "200px",
      }
    },
    "&:nth-of-type(4)": {
      vars: {
        [initialPaperMargin]: "250px",
      }
    },
    "&:nth-of-type(5)": {
      vars: {
        [initialPaperMargin]: "300px",
      }
    },
    "&:nth-of-type(6)": {
      vars: {
        [initialPaperMargin]: "350px",
      }
    },
    [`${approachList.animate} &`]: {
      animation: `1s ${approachListSlide} forwards`,
    }
  }
});
