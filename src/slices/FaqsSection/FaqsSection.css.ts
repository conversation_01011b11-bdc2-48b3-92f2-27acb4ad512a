import { breakpoints } from "@/styles/constants.css";
import { mode } from "@/styles/functions.css";
import { theme } from "@/styles/themes.css";
import { globalStyle, style, styleVariants } from "@vanilla-extract/css";

export const container = style({
  selectors: {
    [mode("residential")]: {
      backgroundColor: theme.colors.primary.castletonGreen,
    }
  },
});

export const root = style({
  padding: "40px 20px",

  "@media": {
    [breakpoints.tablet]: {
      padding: "72px 48px",
    }
  }
});

export const title = style({
  fontFamily: theme.fonts.primary,
  marginBottom: "24px",

  "@media": {
    [breakpoints.tablet]: {
      marginBottom: "48px",
    }
  },

  selectors: {
    [mode("residential")]: {
      color: theme.colors.primary.softWhite,
    }
  },
});

export const description = style({
  marginBottom: 32,
  maxWidth: 500,

  "@media": {
    [breakpoints.tablet]: {
      marginBottom: "auto",
    }
  },

  selectors: {
    [mode("residential")]: {
      color: theme.colors.primary.softWhite,
    }
  },
});

globalStyle(`${description} p`, {
  marginBottom: 16,
});

globalStyle(`${description} p:not(:first-child)`, {
  opacity: 0.8,
});

globalStyle(`${description} strong`, {
  fontSize: "18px !important",
  fontWeight: 500,

  "@media": {
    [breakpoints.tablet]: {
      fontSize: "24px !important",
    }
  }
});

export const info = style({
  display: "flex",
  flexDirection: "column",
});

export const action = style({
  width: "100%",
  marginTop: 40,
});

export const items = style({
  display: "flex",
  flexDirection: "column",
  gap: 10,

  "@media": {
    [breakpoints.tablet]: {
      gap: 16,
    }
  },
});

export const itemContainer = style({
  borderRadius: 16,
  backgroundColor: theme.colors.primary.softWhite,
  transition: "background-color 50ms, transform 350ms",

  ":active": {
    backgroundColor: theme.colors.primary.softWhitePressed,
    color: theme.colors.primary.castletonGreenPressed,
    transform: "scale(0.95)",
  },

  "@media": {
    [breakpoints.tablet]: {
      borderRadius: 24,
    }
  }
});

export const itemHeader = style({
  cursor: "pointer",
  width: "100%",
  padding: "20px",
  minHeight: "52px",
  display: "flex",
  alignItems: "center",
  justifyContent: "space-between",
  border: "none",
  backgroundColor: "transparent",
  textAlign: "left",
  color: "inherit",
  font: "inherit",

  "@media": {
    [breakpoints.tablet]: {
      padding: "0 40px",
      minHeight: "110px",
    }
  }
});

export const itemBody = style({
  padding: "0 20px 20px",

  "@media": {
    [breakpoints.tablet]: {
      padding: "0 40px 40px",
    }
  }
});

export const itemIndicatorBase = style({
  minWidth: 30,
  width: 30,
  aspectRatio: "1 / 1",
  transitionProperty: "transform",
  transitionDuration: "200ms",
  transitionTimingFunction: "ease",
});

export const itemIndicator = styleVariants({
  isActive: [itemIndicatorBase],
  isInactive: [itemIndicatorBase, {
    transform: "rotateZ(180deg)",
  }],
});