"use client";

import Container from "@/components/Container";
import { Content, asHTML } from "@prismicio/client";
import { SliceComponentProps } from "@prismicio/react";
import * as styles from "./FaqsSection.css";
import Typography from "@/components/Typography";
import { PrismicRichText } from "node_modules/@prismicio/react/dist/react-server/PrismicRichText";
import { gridSprinkle } from "@/styles/sprinkles.css";
import { useMemo, useState } from "react";
import Accordion from "@/components/Accordion";
import classNames from "classnames";
import Button from "@/components/Button";
import { observer } from "mobx-react-lite";
import useStore from "@/hooks/useStore";
import { toCamelCase } from "@/utils/helpers";
import BookServiceButton from "@/components/BookServiceButton/BookServiceButton";
import { faqJsonLd } from "@/utils/metadata";

export type FaqsSectionProps = SliceComponentProps<Content.FaqsSectionSlice>;

const FaqsSection = observer((
  { slice }: FaqsSectionProps
): JSX.Element => {
  const { landign } = useStore();

  const [expandedAnswer, setExpandedAnswer] = useState<number | null>(
    null
  );

  const metadataJsonLd = useMemo(() => (
    {...faqJsonLd, 
      mainEntity: slice.items.map(({question,answer}) => (
        {
          "@type": "Question",
          "name": question,
          "acceptedAnswer": {
            "@type": "Answer",
            "text": asHTML(answer)} 
        }
      ))
    }
  ) , []);

  return (
    <Container
      className={styles.container}
    >
      <section
        id={slice.primary.header_anchor_name ? toCamelCase(slice.primary.header_anchor_name) : ""}
        className={styles.root}
        data-slice-type={slice.slice_type}
        data-slice-variation={slice.variation}
        
      >
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{ __html: JSON.stringify(metadataJsonLd) }}
        />
        <div
          className={gridSprinkle(
            { type: "grid" }
          )}
        >
          <div
            className={classNames(
              styles.info, gridSprinkle(
                { type: "item", cols: { mobile: 10, tablet: 5 } }
              )
            )}
          >
            <Typography
              className={styles.title}
              variant="h2"
            >
              <PrismicRichText
                field={slice.primary.title}
              />
            </Typography>              
            <Typography
              className={styles.description}
            >
              <PrismicRichText
                field={slice.primary.description}
              />
            </Typography> 
            <div
              className={gridSprinkle(
                { display: { mobile: "none", tablet: "flex" } }
              )}
            >
              <Button
                isAnimated
                onClick={() => landign.setBookingModalIsOpen(true)}
              >
                Book an Expert
              </Button>            
            </div>
          </div>
          <div
            className={gridSprinkle(
              { type: "item", cols: { mobile: 10, tablet: 5 } }
            )}
          >
            <div
              className={styles.items}
            >
              {slice.items.map(
                (
                  item, index
                ) => (
                  <div
                    key={index}
                    className={styles.itemContainer}
                  >
                    <button
                      className={styles.itemHeader}
                      onClick={(
                      ) => setExpandedAnswer(
                        expandedAnswer === index ? null : index
                      )}
                    >
                      <Typography
                        variant="subTitleMedium"
                      >
                        {item.question}
                      </Typography>
                      <svg
                        className={expandedAnswer === index ? styles.itemIndicator.isActive : styles.itemIndicator.isInactive}
                        viewBox="0 0 30 30"
                        fill="none"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          fillRule="evenodd"
                          clipRule="evenodd"
                          d="M20.9904 18.7326C22.2688 19.7208 23.905 20.6997 25.5508 21.5376V19.3566C21.2243 15.593 17.9759 11.642 15.8224 7.50004H14.9478H14.9471H14.0447C11.8913 11.642 8.64292 15.593 4.31641 19.3566V21.5376C5.96219 20.6997 7.59836 19.7208 8.87676 18.7326C10.1796 17.7255 11.6731 16.2894 12.8813 15.127L12.9016 15.1075C13.6059 14.4298 14.2244 13.8348 14.6296 13.5011L14.9474 13.2392L15.2653 13.5011C15.6726 13.8365 16.2906 14.4352 16.9931 15.1156L17.0142 15.1361C18.2131 16.2974 19.6914 17.7285 20.9904 18.7326Z"
                          fill="#003D23"
                        />
                      </svg>
                    </button>
                    <Accordion
                      duration={350}
                      isOpen={expandedAnswer === index}
                    >
                      <div
                        className={styles.itemBody}
                      >
                        <Typography
                          variant="bodySmall"
                        >
                          <PrismicRichText
                            field={item.answer}
                          />
                        </Typography>
                      </div>                  
                    </Accordion>
                  </div>
                )
              )}            
            </div>
          </div>
        </div>
        <div
          className={gridSprinkle(
            { display: { mobile: "flex", tablet: "none" } }
          )}
        >
          <BookServiceButton
            className={styles.action}
          >Book an Expert</BookServiceButton>
        </div>
      </section>
    </Container>
  );
});

export default FaqsSection;
