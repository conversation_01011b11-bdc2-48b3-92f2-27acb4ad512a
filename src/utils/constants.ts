import { Option } from "@/components/DropdownInput/DropdownInput.types";
import {
  BOILER_FUEL_SOURCES,
  PROPERTY_TYPES,
  RESIDENTIAL_STATUSES,
} from "./api/types/common";
import { convertStrArrToOptions } from "./helpers";

export const DARK_MODE_PATHNAMES = [
  "/",
  "/emergencies",
  "/repair",
  "/protect",
  "/install",
  "/earn",
  "/careers",
  "/learn",
  "/support",
  "/privacy-policy",
  "/terms-of-use",
  "/london-plumbing-areas",
  "/subcontractors",
];

export const DYNAMIC_DARK_MODE_PATH_PATTERNS = [
  /^\/blog\/.+$/, // Dynamic path for pages of type /blog/ZipOxhAAAEqcBtXx
];

export const TYPE_OF_EMPLOYMENT_OPTIONS = [
  {
    value: "Sub-contractor",
    label: "Sub-contractor",
  },
  {
    value: "Full-time employment",
    label: "Full-time employment",
  },
];

export const QUALIFICATIONS = [
  {
    value: "Level 1 NVQ Plumbing",
    label: "Level 1 NVQ Plumbing",
  },
  {
    value: "Level 2 NVQ Plumbing",
    label: "Level 2 NVQ Plumbing",
  },
  {
    value: "Level 3 NVQ Plumbing",
    label: "Level 3 NVQ Plumbing",
  },
];

export const AREA_OF_WORK_OPTIONS = [
  {
    value: "Boiler installations",
    label: "Boiler installations",
  },
  {
    value: "Boiler service and repair",
    label: "Boiler service and repair",
  },
  {
    value: "Reactive maintenance",
    label: "Reactive maintenance",
  },
  {
    value: "General plumbing",
    label: "General plumbing",
  },
  {
    value: "Commercial plumbing",
    label: "Commercial plumbing",
  },
  {
    value: "Bathroom installations",
    label: "Bathroom installations",
  },
  {
    value: "Commercial heating",
    label: "Commercial heating",
  },
  {
    value: "Underfloor heating systems",
    label: "Underfloor heating systems",
  },
  {
    value: "Heating system controls and wiring",
    label: "Heating system controls and wiring",
  },
  {
    value: "LPG Systems",
    label: "LPG Systems",
  },
];

export const RESIDENTIAL_STATUSES_OPTIONS: Option[] =
  convertStrArrToOptions(RESIDENTIAL_STATUSES);
export const PROPERTY_TYPES_OPTIONS: Option[] =
  convertStrArrToOptions(PROPERTY_TYPES);
export const BOILER_FUEL_SOURCES_OPTIONS: Option[] =
  convertStrArrToOptions(BOILER_FUEL_SOURCES);

export const WORK_ONLY_VARIANTS = ["Residential", "Commercial", "Both"];

export const YES_NO_VARIANTS = ["Yes", "No"];

export const PHONE_NUMBER_INPUT_PROPS = {
  placeholder: "Enter your mobile number",
  format: "###############",
  patternChar: "#",
  label: "Mobile number",
};

export const PHONE_NUMBER_CONTROLLER_RULES = {
  validate: (value: string) => {
    // Check if the number starts with +
    // if (!value.startsWith('+')) {
    //   return "Phone number must start with + symbol";
    // }
    
    // Remove all non-digit characters except + for validation
    const cleanedValue = value.replace(/[^\d+]/g, '');
    
    // Check if there's at least one digit after +
    if (cleanedValue.length < 2) {
      return "Please enter digits after +";
    }
    
    // Check that the total length is reasonable
    if (cleanedValue.length < 8) {
      return "Phone number is too short";
    }
    
    if (cleanedValue.length > 16) {
      return "Phone number is too long";
    }
    
    return true;
  },
};

export const FULL_NAME_INPUT_PROPS = {
  placeholder: "Enter your name",
  label: "Full name",
};

export const FULL_NAME_CONTROLLER_RULES = {
  minLength: {
    value: 3,
    message: "At least 3 characters required",
  },
  maxLength: {
    value: 50,
    message: "Max 50 characters",
  },
  validate: (value: string) => {
    return true;
    // if (!/^\w+\s\w+$/.test(value)) return "Should be two words";
  },
};

export const ADDRESS_INPUT_PROPS = {
  placeholder: "Enter your address",
  label: "Address",
};

export const ADDRESS_CONTROLLER_RULES = {
  minLength: {
    value: 3,
    message: "At least 3 characters required",
  },
  validate: (value: string) => {
    const regex = /^[A-Za-z0-9\s.,#'\/\-]+$/;
    if (!regex.test(value)) {
      return "Allows letters, numbers 0-9, and special symbols ., #, ', /, -";
    }
  },
};

export const ONLY_UPPERCASE_AND_LOWERCASE_CONTROLLER_RULES = {
  validate: (value: string) => {
    const regex = /^[A-Za-z\s]+$/;
    if (!regex.test(value)) {
      return "Allows only uppercase and lowercase letters";
    }
  },
};

export const POST_CODE_INPUT_PROPS = {
  placeholder: "Enter your post code",
  label: "Post code",
  maxLength: 10,
};

export const POST_CODE_CONTROLLER_RULES = {
  minLength: {
    value: 2,
    message: "Min number to enter - 2",
  },
  maxLength: {
    value: 10,
    message: "Max number to enter - 10",
  },
  validate: (value: string) => {
    if (!value.trim()) {
      return "Post code is required";
    }
    
    // Simple validation - just check for minimum length
    if (value.trim().length >= 2) {
      return true;
    }
    
    return "Please enter a valid post code";
  },
};

export const EMAIL_INPUT_PROPS = {
  placeholder: "Enter your email",
  label: "Email",
  type: "email",
};

export const EMAIL_CONTROLLER_RULES = {
  validate: (value: string) => {
    if (
      value.trim() &&
      !/^(([^<>()[\]\.,;:\s@\"]+(\.[^<>()[\]\.,;:\s@\"]+)*)|(\".+\"))@(([^<>()[\]\.,;:\s@\"]+\.)+[^<>()[\]\.,;:\s@\"]{2,})$/i.test(
        value
      )
    )
      return "Incorrect email";
  },
};

export const APPLIANCES = [
  {
    id: "gas_cooker_hob",
    name: "Gas Cooker/Hob",
    monthPrice: 250,
    yearPrice: 2850,
  },
  {
    id: "gas_fire",
    name: "Gas Fire",
    monthPrice: 250,
    yearPrice: 2850,
  },
  {
    id: "gas_oven",
    name: "Gas Oven",
    monthPrice: 250,
    yearPrice: 2850,
  },
];

export const PLANS = [
  {
    id: "basic",
    name: "Basic",
    monthPrice: 1049,
    yearPrice: 12000,
    expires: null as number | null,
    features: [
      "Repairs to plumbing system in home or outbuildings",
      "Water supply pipe within boundaries of property",
      "Burst pipes",
    ],
    exclusions: [
      "Claims within first 14 days of taking cover out",
      "Shower parts, shower pumps, sanitary ware and sealants",
      "Faults/Repairs caused by 3rd parties",
    ],
  },
  {
    id: "standard",
    name: "Standard",
    monthPrice: 1249,
    yearPrice: 14200,
    expires: null as number | null,
    features: [
      "Repairs to plumbing system in home or outbuildings",
      "Water supply pipe within boundaries of property",
      "Burst pipes",
      "Internal/External blockages",
      "Shower, bath and basin waste pipes",
      "Blocked toilets",
    ],
    exclusions: [
      "Claims within first 14 days of taking cover out",
      "Shower parts, shower pumps, sanitary ware and sealants",
      "Ceramics - Toilets/Basins/Bath/Bidets",
      "Repairs to external pipework/drains/communal drainage pipework",
      "Fridges/Washing Machine appliances",
    ],
  },
  {
    id: "premium",
    name: "Premium",
    monthPrice: 1549,
    yearPrice: 17700,
    expires: null as number | null,
    features: [
      "Repairs to plumbing system in home or outbuildings",
      "Water supply pipe within boundaries of property",
      "Burst pipes",
      "Internal/External blockages",
      "Shower, bath and basin waste pipes",
      "Blocked toilets",
      "Radiators & Pipes",
      "Heating system controls, thermostat and programmer",
      "Radiator valves",
    ],
    exclusions: [
      "Claims within first 14 days of taking cover out",
      "Shower parts, shower pumps, sanitary ware and sealants",
      "Ceramics - Toilets/Basins/Bath/Bidets",
      "Repairs to external pipework/drains/communal drainage pipework",
      "Fridges/Washing Machine appliances",
      "Damages caused by sludge",
      "Damages caused by limescale",
      "Repairing damage caused by third parties",
    ],
  },
  {
    id: "peace",
    name: "Peace",
    isHighlighted: true,
    monthPrice: 2049,
    yearPrice: 23400,
    expires: null as number | null,
    features: [
      "Repairs to plumbing system in home or outbuildings",
      "Water supply pipe within boundaries of property",
      "Burst pipes",
      "Internal/External blockages",
      "Shower, bath and basin waste pipes",
      "Blocked toilets",
      "Radiators & Pipes",
      "Heating system controls, thermostat and programmer",
      "Radiator valves",
      "Boiler Components",
      "Gas Supply Pip",
      "Gas Boiler Flue",
      "Boiler Inspection + Service",
    ],
    exclusions: [
      "Claims within first 14 days of taking cover out",
      "Shower parts, shower pumps, sanitary ware and sealants",
      "Ceramics - Toilets/Basins/Bath/Bidets",
      "Repairs to external pipework/drains/communal drainage pipework",
      "Fridges/Washing Machine appliances",
      "Damages caused by sludge",
      "Damages caused by limescale",
      "Repairing damage caused by third parties",
      "Boiler breakdowns caused by sludge and sediment in the system",
    ],
    appliances: APPLIANCES,
  },
];

export const WHATS_UP_NUMBER = "448000461000";
export const HEADER_PHONE_NUMBER = "0800 046 1000";

export const INFO_EMAIL = "<EMAIL>";
export const COMPANY_MAIN_OFFICE_ADDRESS = {
  streetAddress: "122 Leadenhall Street",
  addressLocality: "London",
  postalCode: "EC3V 4AB",
  addressCountry: "UK",
};

export const COMPANY_NAME = "Pleasant Plumbers";

export type ClientPlanType = (typeof PLANS)[number];

export const LOWEST_AND_HIGHEST_PLAN_PRICE = (() => {
  const lowestPrice =
    PLANS.reduce(
      (prev, accum) => {
        const accumPrice = accum.monthPrice;
        if (!prev) return accumPrice;
        if (prev < accumPrice) {
          return prev;
        }
        return accumPrice;
      },
      null as number | null
    ) || 0;

  const highestPrice =
    PLANS.reduce(
      (prev, accum) => {
        let accumPlanPrice = accum.yearPrice;
        if (accum.appliances) {
          accumPlanPrice =
            accumPlanPrice +
            accum.appliances.reduce((prev, accum) => prev + accum.yearPrice, 0);
        }
        if (!prev) {
          return accumPlanPrice;
        }

        let prevPlanPrice = prev;

        if (prevPlanPrice > accumPlanPrice) {
          return prev;
        }
        return accumPlanPrice;
      },
      null as number | null
    ) || 0;

  return {
    lowestPrice,
    highestPrice,
  };
})();

export const boroughs = [
  { name: "Barking & Dagenham", lat: 51.5441, lng: 0.1505, postcode: "IG11" },
  { name: "Barnet", lat: 51.6252, lng: -0.1517, postcode: "EN5" },
  { name: "Bexley", lat: 51.4566, lng: 0.1420, postcode: "DA5" },
  { name: "Brent", lat: 51.5673, lng: -0.2711, postcode: "NW10" },
  { name: "Bromley", lat: 51.4050, lng: 0.0148, postcode: "BR1" },
  { name: "Camden", lat: 51.5416, lng: -0.1433, postcode: "NW1" },
  { name: "City of London", lat: 51.5128, lng: -0.0918, postcode: "EC2V" },
  { name: "Cobham", lat: 51.3293, lng: -0.4120, postcode: "KT11" },
  { name: "Croydon", lat: 51.3762, lng: -0.0982, postcode: "CR0" },
  { name: "Ealing", lat: 51.5136, lng: -0.3084, postcode: "W5" },
  { name: "Enfield", lat: 51.6521, lng: -0.0804, postcode: "EN1" },
  { name: "Greenwich", lat: 51.4892, lng: 0.0648, postcode: "SE10" },
  { name: "Hackney", lat: 51.5450, lng: -0.0553, postcode: "E8" },
  { name: "Hammersmith & Fulham", lat: 51.4927, lng: -0.2339, postcode: "W6" },
  { name: "Haringey", lat: 51.5906, lng: -0.1040, postcode: "N8" },
  { name: "Harrow", lat: 51.5784, lng: -0.3424, postcode: "HA1" },
  { name: "Havering", lat: 51.5761, lng: 0.2165, postcode: "RM1" },
  { name: "Hillingdon", lat: 51.5352, lng: -0.4486, postcode: "UB8" },
  { name: "Hounslow", lat: 51.4676, lng: -0.3618, postcode: "TW3" },
  { name: "Islington", lat: 51.5380, lng: -0.1022, postcode: "N1" },
  { name: "Kensington & Chelsea", lat: 51.4990, lng: -0.1939, postcode: "SW7" },
  { name: "Kingston upon Thames", lat: 51.4123, lng: -0.3007, postcode: "KT1" },
  { name: "Lambeth", lat: 51.4570, lng: -0.1167, postcode: "SW9" },
  { name: "Lewisham", lat: 51.4452, lng: -0.0209, postcode: "SE13" },
  { name: "Merton", lat: 51.4098, lng: -0.2104, postcode: "SW19" },
  { name: "Newham", lat: 51.5255, lng: 0.0352, postcode: "E13" },
  { name: "Redbridge", lat: 51.5761, lng: 0.0454, postcode: "IG4" },
  { name: "Richmond on Thames", lat: 51.4479, lng: -0.3260, postcode: "TW9" },
  { name: "Southwark", lat: 51.5035, lng: -0.0819, postcode: "SE1" },
  { name: "Sutton", lat: 51.3618, lng: -0.1945, postcode: "SM1" },
  { name: "Tower Hamlets", lat: 51.5091, lng: -0.0206, postcode: "E1" },
  { name: "Waltham Forest", lat: 51.5898, lng: -0.0118, postcode: "E17" },
  { name: "Walton-on-Thames", lat: 51.3862, lng: -0.4173, postcode: "KT12" },
  { name: "Wandsworth", lat: 51.4566, lng: -0.1922, postcode: "SW18" },
  { name: "Westminster", lat: 51.4975, lng: -0.1357, postcode: "SW1A" },
  { name: "Weybridge", lat: 51.3720, lng: -0.4574, postcode: "KT13" },
];
