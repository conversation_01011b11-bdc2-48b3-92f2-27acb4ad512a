import { COMPANY_MAIN_OFFICE_ADDRESS, COMPANY_NAME, HEADER_PHONE_NUMBER, INFO_EMAIL, LOWEST_AND_HIGHEST_PLAN_PRICE } from "./constants";
import { formatNumToGBP } from "./helpers";


const PRICE_RANGE = `${formatNumToGBP(LOWEST_AND_HIGHEST_PLAN_PRICE.lowestPrice / 100)}-${formatNumToGBP(LOWEST_AND_HIGHEST_PLAN_PRICE.highestPrice / 100)}`;
const ORIGIN_URL = process.env.NEXT_PUBLIC_CURRENT_SITE_URL;

export const organizationJsonLd = {
  
  "@context": "http://www.schema.org",
  "@type": "Organization",
  "name": COMPANY_NAME,
  "logo": "../assets/images/logo.png",
  "url": process.env.NEXT_PUBLIC_CURRENT_SITE_URL,
  "description": `Looking for a reliable plumber nearby? ${COMPANY_NAME} offers 24/7 emergency plumbing services, fast response times, and qualified technicians. Contact us today!`,
  "address": {
    "@type": "PostalAddress",
    ...COMPANY_MAIN_OFFICE_ADDRESS
  },
  "telephone": HEADER_PHONE_NUMBER,
  "email": INFO_EMAIL,
  
};

export const localBusinessJsonLd = {
  "@type": "LocalBusiness",
  "priceRange": PRICE_RANGE
};

export const plumberJsonLd = {
  "@context": "http://www.schema.org",
  "@type": "Plumber",
  "name": COMPANY_NAME,
  "logo": "../assets/images/logo.png",
  "url": process.env.NEXT_PUBLIC_CURRENT_SITE_URL,
  "description": `Looking for a reliable plumber nearby? ${COMPANY_NAME} offers 24/7 emergency plumbing services, fast response times, and qualified technicians. Contact us today! `,
  "address": {
    "@type": "PostalAddress",
    ...COMPANY_MAIN_OFFICE_ADDRESS
  },
  "hasMap": "https://www.google.com/maps/place/Pleasant+Plumbers/@8.9907345,-110.2038456,3z/data=!4m10!1m2!2m1!1sPleasant+Plumbers!3m6!1s0x48761b4df01b6b0f:0xcd143211773a8c7d!8m2!3d51.5138619!4d-0.0823131!15sChFQbGVhc2FudCBQbHVtYmVyc-ABAA!16s%2Fg%2F11jsb0r67_?entry=ttu",
  "telephone": HEADER_PHONE_NUMBER,
};


export const faqJsonLd = {
  
  "@context": "https:\/\/schema.org",
  "@type": "FAQPage",
  // "mainEntity": [ {
  //   "@type": "Question",
  //   "name": "How quickly can you respond to emergencies?",
  //   "acceptedAnswer": {
  //     "@type": "Answer",
  //     "text": "<p>We strive to respond promptly to all emergency calls. Our team aims to be at your location with in 90 minutes to address the issue.<\/p>\n"} 
  //   },
  //    {
  //   "@type": "Question",
  //   "name": "Are emergency plumbing services available outside of regular business hours?",
  //   "acceptedAnswer": {
  //     "@type": "Answer",
  //     "text": "<p>Yes, we understand that emergencies don't always happen during typical business hours. That's why our emergency plumbing services are available 24/7, including weekends and holidays.<\/p>\n"} }, {
  //   "@type": "Question",
  //   "name": "Is there an additional fee for emergency plumbing services?",
  //   "acceptedAnswer": {
  //     "@type": "Answer",
  //     "text":"<p>Yes, emergency plumbing services may incur additional charges due to the urgency and specialised attention required. However, we always strive to keep our pricing fair and transparent.<\/p>\n"} }, {
  //   "@type": "Question",
  //   "name": "How can I minimise damage while waiting for the plumber to arrive during an emergency?",
  //   "acceptedAnswer": {
  //     "@type": "Answer",
  //     "text": "<p>If it's safe to do so, try to locate and shut off the main water supply to your property. Additionally, move valuable items away from any water or potential damage areas. Our team will guide you through these steps over the phone if needed.<\/p>\n"} }, {
  //   "@type": "Question",
  //   "name": "Do you offer assistance with water damage restoration after emergencies?",
  //   "acceptedAnswer": {
  //     "@type": "Answer",
  //     "text": "<p>Yes, we provide comprehensive services that include water damage restoration. Our team can help assess the extent of the damage and take appropriate steps to restore your property to its perfect condition.<\/p>\n"} }
  // ]
    
    
};

export const createArticleJsonLd = ({
  pathname,
  headline,
  image,
  // authorName,
  datePublished
}: {
  pathname: string,
  headline: string,
  image: string,
  // authorName: string,
  datePublished: string
}) =>{ 
  return {
    "@context": "https://schema.org",
    "@type": "Article",
    "mainEntityOfPage": {
      "@type": "WebPage",
      "@id": `${ORIGIN_URL}${pathname}`
    },
    "headline": headline,
    "image": image,  
    "author": {
      "@type": "Person",
      "name": COMPANY_NAME 
    },  
    "publisher": {
      "@type": "Organization",
      "name": COMPANY_NAME,
      "logo": {
        "@type": "ImageObject",
        "url": "../assets/images/logo.png",
      }
    },
    "datePublished": datePublished
  };};

export const createBreadcrumbJsonLd  = ({
  title,pathname
}: {
  title: string
pathname: string
}) => {
  return {
    "@context": "https://schema.org/", 
    "@type": "BreadcrumbList", 
    "itemListElement": [{
      "@type": "ListItem", 
      "position": 1, 
      "name": "Home Page",
      "item": ORIGIN_URL  
    },{
      "@type": "ListItem", 
      "position": 2, 
      "name": "Blog",
      "item":  `${ORIGIN_URL}blog`
    },{
      "@type": "ListItem", 
      "position": 3, 
      "name": title,
      "item": pathname  
    }]
  };};

export const createVideoJsonLd = ({name,description, url, uploadDate, min,  sec}: {name: string,description: string, url: string, uploadDate: string, min: string | number, sec: string | number}) => {
  return {
    "@context": "https://schema.org",
    "@type": "VideoObject",
    "name": name,
    "description":description,
    "thumbnailUrl": url,
    "uploadDate": uploadDate,
    "duration": `PT${min}M${sec}S`
  };
    
};
