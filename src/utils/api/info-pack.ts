export interface InfoPackRequestData {
  email: string;
}

export interface InfoPackRequestResponse {
  message: string;
}

export interface InfoPackRequestError {
  error: string;
}

export async function requestInfoPack(data: InfoPackRequestData): Promise<InfoPackRequestResponse> {
  const response = await fetch('/api/info-pack-request', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(data),
  });

  const result = await response.json();

  if (!response.ok) {
    throw new Error(result.error || 'Failed to request info pack');
  }

  return result;
}
