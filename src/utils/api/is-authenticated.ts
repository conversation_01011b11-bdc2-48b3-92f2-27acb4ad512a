import { SupabaseClient } from "@supabase/supabase-js";
import { ServiceResponse } from "./types/common";

// Validates if user is either unauthenticated of banned/deleted

export type isAuthenticatedData = {
  id: string;
  email?: string;
};

export const isAuthenticated = async (
  supabase: SupabaseClient
): Promise<ServiceResponse<isAuthenticatedData>> => {
  const { data: sessionData, error: getSessionError } =
    await supabase.auth.getSession();

  if (getSessionError) {
    console.log("Get session error", getSessionError);

    return {
      error: {
        message: "Unauthorized",
        details: getSessionError,
      },
    };
  }

  const { session } = sessionData;

  if (!session) {
    console.log("Session is missing", sessionData);

    return {
      error: {
        message: "Session is missing",
        details: {
          session,
        },
      },
    };
  }

  const { id, email } = session.user;

  const { data: profile, error: getProfileError } = await supabase
    .from("profiles")
    .select("status")
    .match({ id })
    .single();

  if (getProfileError) {
    return {
      error: {
        message: "Failed to get authenticated user's profile",
        details: getProfileError,
      },
    };
  }

  const { status } = profile;

  switch (status) {
    case "deleted":
      return {
        error: {
          message: "User is deleted",
          details: {
            action: "sign-out",
          },
        },
      };
  }

  return {
    data: {
      id,
      email,
    },
  };
};
