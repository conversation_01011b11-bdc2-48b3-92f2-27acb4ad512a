import { z } from 'zod';
import { RESIDENTIAL_STATUSES } from '../common';

export const SignUpDtoSchema = z.object({
  email: z.string().email(),
  password: z.string(),
  full_name: z.string(),
  date_of_birth: z.string(),
  mobile_number: z.string(),
  post_code: z.string(),
  address_details: z.string(),
  residential_status: z.enum(RESIDENTIAL_STATUSES),
});

export type SignUpDto = z.infer<typeof SignUpDtoSchema>;
