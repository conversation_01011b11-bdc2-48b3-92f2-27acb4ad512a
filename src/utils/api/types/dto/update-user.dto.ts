import { z } from "zod";
import {
  BOILER_FUEL_SOURCES,
  HOME_OWNERSHIP_STATUSES,
  PROPERTY_TYPES,
  RESIDENTIAL_STATUSES,
  YES_NO_VARIANTS,
} from "../common";

export const UpdateUserDtoSchema = z.object({
  // Personal info (without email, and password. They have separate change endpoint):
  full_name: z
    .string()
    .trim()
    .min(3)
    .max(50)
    .optional(),

  date_of_birth: z.string().optional(),
  mobile_number: z.string().optional(),
  post_code: z.string().optional(),
  address_details: z.string().optional(),
  residential_status: z.enum(RESIDENTIAL_STATUSES).optional(),

  // Home info
  home_ownership_status: z.enum(HOME_OWNERSHIP_STATUSES).optional(),
  property_type: z.enum(PROPERTY_TYPES).optional(),
  bathrooms_count: z.number().optional(),
  boiler_fuel_source: z.enum(BOILER_FUEL_SOURCES).optional(),
  boiler_age: z.string().optional(),
  airing_cupboard_cylinder: z.enum(YES_NO_VARIANTS).optional(),
  tanks_in_loft: z.enum(YES_NO_VARIANTS).optional(),
  radiators_count: z.number().optional(),
  smart_thermostat: z.enum(YES_NO_VARIANTS).optional(),
  thermostat_radiators_valves: z.enum(YES_NO_VARIANTS).optional(),
  boiler_serviced_recently: z.enum(YES_NO_VARIANTS).optional(),
  carbon_monoxide_alarm: z.enum(YES_NO_VARIANTS).optional(),
  any_cover: z.enum(YES_NO_VARIANTS).optional(),
  power_flush_carried_out: z.enum(YES_NO_VARIANTS).optional(),
});

export type UpdateUserDto = z.infer<typeof UpdateUserDtoSchema>;
