import { Option } from "@/components/DropdownInput/DropdownInput.types";
import { PageMode, SlicesContextData, SlicesFetchType } from "@/types/common";
import { AnyRegularField, SharedSlice, Slice, SliceZone } from "@prismicio/client";
import { PLANS } from "./constants";
import { ReadonlyURLSearchParams } from "next/navigation";

export const createDateIntl = () => {
  // Output example:  February 15, 2024
  const options = {
    year: "numeric",
    month: "long",
    day: "numeric",
    // hour: "numeric",
    // minute: "numeric",
    // second: "numeric",
    // hour12: true,
  } as const;

  return new Intl.DateTimeFormat("en-gb", options);
};

export const onlyNumInStr = (value: string) =>
  value ? /^\d+$/.test(value) : true;

export const onlyFloatNumInStr = (inputString: string) => {
  // Check if the string is empty
  if (inputString.trim() === "") {
    return true;
  }

  // Check if the string contains a number or has only one dot
  return /^[0-9]*[,.]?[0-9]*$/.test(inputString);
};

export const debounce = <F extends (...args: any[]) => any>(
  func: F,
  waitFor = 250
) => {
  let timeout: NodeJS.Timeout | null = null;

  const debounced = (...args: any) =>
    new Promise((resolve) => {
      if (timeout) {
        clearTimeout(timeout);
      }
      timeout = setTimeout(
        () => void Promise.resolve(func.apply(this, [...args])).then(resolve),
        waitFor
      );
    });

  return debounced as (...args: Parameters<F>) => Promise<ReturnType<F>>;
};


export const isChromeBrowser = () => {
  if(typeof window === "undefined") return false;
  // @ts-ignore
  const isChrome = window.chrome;
  const userAgent = navigator.userAgent.toLowerCase();
  return isChrome && userAgent.indexOf("chrome") > -1;
};

export const isMobileOrTablet = () => {
  if(typeof window === "undefined") return false;
  const userAgent = navigator.userAgent.toLowerCase();
  return /android|webos|iphone|ipad|ipod|blackberry|iemobile|opera mini/i.test(userAgent);
};

export const parseStringToValidId = (text: string, withHash?: boolean) => {
  return `${withHash ? "#" : ""}${text.replaceAll(/[^a-zA-Z]/g,"")}`;
};

const EURInstance = new Intl.NumberFormat("en-GB", {style: "currency", currency: "GBP"});

export const formatNumToGBP = EURInstance.format;


export const convertStrArrToOptions = (values: string[] | readonly string[]): Option[] => values.map((value) => ({label: value, value}));

export const toCamelCase = (value: string) =>  value.toLowerCase().replaceAll(" ","_");

export const getNumbersFromString = (string: string) => string.replace(/\D/g, "");

export const getSliceDataObject = async (slices: SliceZone<Slice<string, Record<string, AnyRegularField>, Record<string, AnyRegularField>> | SharedSlice>,slicesFetch: SlicesFetchType,searchParams: Record<string,string>) => {
  let slicesData = {};
  for (let i = 0; i < slices.length; i++) {
    const slice = slices[i];
    if(!slicesFetch[slice.slice_type as keyof typeof slicesFetch]) continue;
    const data = await slicesFetch[slice.slice_type as keyof typeof slicesFetch]?.(searchParams);
    slicesData =  {...slicesData, [slice.slice_type]: data};
  }
  return slicesData as SlicesContextData;
};

export const throttle = <F extends (...args: any[]) => any>(func: F, waitFor: number) => {
  const now = () => new Date().getTime();
  const resetStartTime = () => startTime = now();
  let timeout: NodeJS.Timeout;
  let startTime: number = now() - waitFor;

  return (...args: Parameters<F>): Promise<ReturnType<F>> =>
    new Promise((resolve) => {
      const timeLeft = (startTime + waitFor) - now();
      if (timeout) {
        clearTimeout(timeout);
      }
      if (startTime + waitFor <= now()) {
        resetStartTime();
        resolve(func(...args));
      } else {
        timeout = setTimeout(() => {
          resetStartTime();
          resolve(func(...args));
        }, timeLeft);
      }
    });
};

type ReturnMetaDataRobotsParams = {noIndex: boolean, noFollow: boolean,}

export const returnMetaDataRobots = ({noIndex, noFollow}:ReturnMetaDataRobotsParams) => {
  if(noIndex && noFollow) {
    return "noindex,nofollow";
  }
  if(noIndex) {
    return "index,nofollow";
  }
  if(noFollow) {
    return "noindex,follow";
  }
  return "index,follow";
};


export const getURLToGoogleMapByPlaceId = (placeId: string, fallbackAddress = "") => {
  return encodeURI(`https://www.google.com/maps/search/?api=1&query=${fallbackAddress}&query_place_id=${placeId}`);

};


// Only NULL and UNDEFINED
export const cleanObjectFromFalsyValues = <T extends Record<string, any>,>(obj: T) => {
  const newObj = {...obj};
  Object.keys(newObj).forEach(key => {
    if (newObj[key] === undefined || newObj[key] === null) {
      delete newObj[key];
    }
  });

  return newObj;
};



// Get a new searchParams string by merging the current
// searchParams with a provided key/value pair
export const createQueryString =
  (name: string, value: string, searchParams: ReadonlyURLSearchParams | null ) => {
    if(!searchParams) return;
    const params = new URLSearchParams(searchParams.toString());
    params.set(name, value);

    return params.toString();
  };


// Shallow routing workaround
// Problem thread: https://github.com/vercel/next.js/discussions/48110
// Next docs: https://nextjs.org/docs/app/building-your-application/routing/linking-and-navigating#using-the-native-history-api
export const setNewParamWithoutRefresh = (params?: string) => {
  if(!params) return;
  const queryParams = "?" + params;
  history.replaceState({}, "", queryParams);
};
