import { SlicesContextData } from "@/types/common";
import { GroupField, SliceZone } from "@prismicio/client";
import { EmergencyLocationsDocumentDataLocationsItem, FooterDocument, HeaderDocument, LandingPageDocumentDataSlicesSlice, Simplify } from "prismicio-types";

type HomePage = {
  header: HeaderDocument<string>
  footer: FooterDocument<string>
  slices: SliceZone<LandingPageDocumentDataSlicesSlice>;
  slicesData: SlicesContextData
  emergencyLocations:  GroupField<Simplify<EmergencyLocationsDocumentDataLocationsItem>>;
};

export default HomePage;
