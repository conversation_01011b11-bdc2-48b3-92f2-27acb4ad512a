import { breakpoints } from "@/styles/constants.css";
import { mode } from "@/styles/functions.css";
import { theme } from "@/styles/themes.css";
import { style, styleVariants } from "@vanilla-extract/css";

export const mainContainer = style({
  display: "flex",
  flexDirection: "column",
});

export const root = style({
  //display: "flex",
  //flexDirection: "column",
  paddingTop: 130,

  "@media": {
    [breakpoints.tablet]: {
      paddingTop: 150,
    }
  },

  selectors: {
    [mode("residential")]: {
      backgroundColor: theme.colors.primary.castletonGreen,
    }
  },
});

export const main = style({
  display: "flex",
  flexDirection: "column",
  gap: 10,
  flex: "1 1",
});


const scrollSnapContainerBase = style({
  // scrollSnapType: "y mandatory",
  // scrollBehavior: "revert",
  height: "100%",
  overflowY: "auto",
  flex: "1 1",
  position: "relative",
  // selectors: {
  //   "&": {
  //     scrollbarWidth: "none" /* Firefox */,
  //     msOverflowStyle: "none" /* Internet Explorer 10+ */,
  //   },
  //   "&::-webkit-scrollbar": {
  //     /* WebKit */
  //     width: 0,
  //     height: 0,
  //   },
  // },
});

export const scrollSnapContainer = styleVariants({
  base: [scrollSnapContainerBase],
  withScrollSnap: {
    scrollSnapType: "y mandatory",
  }
});

// export const border = style({
//   pointerEvents: "none",
//   position: "absolute",
//   height: "60px !important",
//   bottom: 10,
//   // 1px - Strange line on sides fix
//   boxShadow: `0px 20px 0px 1px ${theme.colors.primary.ivory}`,
//   left: 10,
//   right: 10,
//   background: "transparent !important",
//   borderBottomLeftRadius: 20,
//   borderBottomRightRadius: 20,
//   borderTopRightRadius: 0,
//   borderTopLeftRadius: 0,
//   padding: "0 !important",
//   "@media": {
//     [breakpoints.tablet]: {
//       left: 20,
//       right: 20,
//       height: "60px !important",
//       bottom: 20,
//     },
//     [breakpoints.desktop]: {
//       left: 0,
//       right: 0,
//     },
//   },
// });

// export const borderBg = style({
//   pointerEvents: "none",
//   padding: "0 !important",
//   position: "absolute",
//   height: "10px !important",
//   // Strange line on sides fix
//   scale: 1.01,
//   width: "100%",
//   // top: 0,
//   left: 10,
//   right: 10,
//   bottom: 0,
//   borderRadius: "0px !important",
//   "@media": {
//     [breakpoints.tablet]: {
//       left: 20,
//       right: 20,
//       height: "20px !important",
//     },
//     [breakpoints.desktop]: {
//       left: 0,
//       right: 0,
//     },
//   },
//   // margin: "0 auto",
//   // background: theme.colors.primary.ivory,
// });
