import { components } from "@/slices";
import FloatingContactWidgets from "../FloatingContactWidgets";
import Footer from "../Footer";
import Header from "../Header";
import * as styles from "./HomePage.css";
import HomePageProps from "./HomePage.types";
import { WHATS_UP_NUMBER } from "@/utils/constants";
import { SliceZone } from "@prismicio/react";
import ProtectingHouseholds from "@/slices/ProtectingHouseholds/ProtectingHouseholds";

const HomePage = ({ slices, header, footer, slicesData, emergencyLocations }: HomePageProps) => {
  // Create a mock slice object for ProtectingHouseholds
  const protectingHouseholdsSlice = {
    id: "protecting-households-static",
    slice_type: "protecting_households",
    primary: {
      header_anchor_name: "protecting-households",
      image: {
        url: "",
        alt: "London Big Ben",
        dimensions: { width: 800, height: 600 }
      }
    },
    items: [
      { borough: "Barnet" },
      { borough: "Bexley" },
      { borough: "Bromley" },
      { borough: "Camden" },
      { borough: "Croydon" },
      { borough: "Greenwich" },
      { borough: "Hackney" },
      { borough: "Harrow" },
      { borough: "Kensington" },
      { borough: "Lambeth" },
      { borough: "Merton" },
      { borough: "Redbridge" },
      { borough: "Sutton" },
      { borough: "Wandsworth" },
      { borough: "Westminster" }
    ],
    variation: "default"
  };

  return (
    <>
      <div className={styles.root}>
        <Header
          emergencyLocations={emergencyLocations}
          header={header}
        />

        <SliceZone
          slices={slices}
          context={slicesData}
          components={components}
        />

        {/* Add ProtectingHouseholds component directly before Footer */}
        <ProtectingHouseholds slice={protectingHouseholdsSlice} />

        <Footer
          footer={footer}
        />
      </div>
      <FloatingContactWidgets
        phoneNumber={String(header.data.phone_number)}
        whatsAppNumber={WHATS_UP_NUMBER}
      />
    </>
  );
};

export default HomePage;
