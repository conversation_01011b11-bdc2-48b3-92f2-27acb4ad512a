"use client";

import classNames from "classnames";
import { useCallback, useContext } from "react";
import { Controller, useForm } from "react-hook-form";
import { PatternFormat } from "react-number-format";

import { EMAIL_CONTROLLER_RULES, EMAIL_INPUT_PROPS, FULL_NAME_CONTROLLER_RULES, FULL_NAME_INPUT_PROPS, PHONE_NUMBER_CONTROLLER_RULES, PHONE_NUMBER_INPUT_PROPS, POST_CODE_CONTROLLER_RULES, POST_CODE_INPUT_PROPS, RESIDENTIAL_STATUSES_OPTIONS } from "@/utils/constants";
import Button from "../Button";
import DatePicker from "../DatePicker";
import DropdownInput from "../DropdownInput";
import TextInput from "../TextInput";
import Typography from "../Typography";
import * as styles from "./PersonalInfoPage.css";
import { PersonalInfoFormValuesType } from "./PersonalInfoPage.types";
// import { headers } from "next/headers";
import useStore from "@/hooks/useStore";
import dayjs from "dayjs";
import { observer } from "mobx-react-lite";
import Divider from "../Divider/Divider";


const defaultValues:PersonalInfoFormValuesType = {
  email: "",
  address_details: "",
  date_of_birth: null,
  full_name: "",
  mobile_number: "",
  post_code: "",
  residential_status: null
};

// full_name
// date_of_birth
// mobile_number
// post_code
// address_details
// residential_status


const PersonalInfoPage = observer(() => {
  const { auth, alert } = useStore();

  const emailForm = useForm<{ email: string }>({
    mode: "onBlur",
    defaultValues: {
      email: auth.user?.email ?? "",
    },
  });

  const form = useForm<PersonalInfoFormValuesType>(
    {
      mode: "onBlur",
      defaultValues: auth.user ? {
        email: auth.user.email,
        address_details: auth.user.address_details,
        date_of_birth: auth.user.date_of_birth ? new Date(auth.user.date_of_birth) : null ,
        full_name: auth.user.full_name,
        mobile_number: auth.user.mobile_number,
        post_code: auth.user.post_code,
        residential_status: auth.user.residential_status ? {value: auth.user.residential_status, label: auth.user.residential_status} : null
      } : defaultValues,
    }
  );

  const onSubmit = useCallback(
    async (payload: PersonalInfoFormValuesType) => {
      if(!payload.residential_status || !payload.residential_status.value) return;

      try {
        await auth.updatePersonalInfo({
          address_details: payload.address_details,
          date_of_birth: dayjs(payload.date_of_birth).format("YYYY-MM-DD"),
          full_name: payload.full_name,
          mobile_number: payload.mobile_number,
          post_code: payload.post_code,
          residential_status: payload.residential_status.value as any
        });

        alert.addAlert({ content: "Personal Info changed success!" });

        form.reset({
          address_details: payload.address_details,
          date_of_birth: new Date(dayjs(payload.date_of_birth).format("YYYY-MM-DD")),
          full_name: payload.full_name,
          mobile_number: payload.mobile_number,
          post_code: payload.post_code,
          residential_status: payload.residential_status,
        });
      } catch (error) {
        alert.addAlert({ content: String(error), type: "error" });
      }
    },
    [],
  );
  

  return (
    <div
      className={styles.container}
    >
      <form
        onSubmit={emailForm.handleSubmit(async ({ email }) => {
          try {
            await auth.resetEmail(email);

            alert.addAlert({ content: "Email verification letter was sent to your new inbox!" });
          } catch (error) {
            alert.addAlert({ content: String(error), type: "error" });
          }
        })}
        className={styles.changeEmailWrapper}
      >
        <Controller
          name="email"
          rules={{
            required: "Required!",
            ...(EMAIL_CONTROLLER_RULES),
          }}
          control={emailForm.control}
          render={(
            { field, fieldState }
          ) => (
            <TextInput
              {...EMAIL_INPUT_PROPS}
              className={styles.field}
              {...field}
              error={fieldState.error?.message}
            />
          )}
        />
        <Button
          type="submit"
          color="secondary"
          variant="outlined"
          isLoading={emailForm.formState.isSubmitting}
          disabled={!emailForm.formState.isDirty || !emailForm.formState.isValid}
        >
          Change
        </Button>
      </form>
      <Divider />
      <form
        noValidate
        className={styles.personalInfoForm}
        onSubmit={form.handleSubmit(
          onSubmit
        )}
      >
        <Controller
          name="full_name"
          rules={{
            required: "Required!",
            ...(FULL_NAME_CONTROLLER_RULES),
          }}
          control={form.control}
          render={(
            { field, fieldState }
          ) => (
            <TextInput
              {...FULL_NAME_INPUT_PROPS}
              className={styles.stretchToTwoCols}
              {...field}
              error={fieldState.error?.message}
            />
          )}
        />
        <Controller
          name="date_of_birth"
          rules={{
            required: "Required!",
          }}
          control={form.control}
          render={(
            { field, fieldState }
          ) => (
            <DatePicker
              {...field}
              label={"Date of birth"}
              error={fieldState.error?.message}
              maxDate={new Date()}
            />
          )}
        />
        <Controller
          name="mobile_number"
          rules={{
            required: "Required!",
            ...PHONE_NUMBER_CONTROLLER_RULES,
          }}
          control={form.control}
          render={(
            { field: {ref ,...restField}, fieldState }
          ) => (
            <PatternFormat
              {...PHONE_NUMBER_INPUT_PROPS}
              containerRef={ref}
              inputMode="tel"
              error={fieldState.error?.message}
              {...restField}
              customInput={TextInput}
            />
          )}
        />
        <Controller
          name="post_code"
          rules={{
            required: "Required!",
            ...(POST_CODE_CONTROLLER_RULES),
          }}
          control={form.control}
          render={(
            { field, fieldState }
          ) => (
            <TextInput
              {...POST_CODE_INPUT_PROPS}
              {...field}
              error={fieldState.error?.message}
            />
          )}
        />
        <Controller
          name="address_details"
          rules={{
            required: "Required!",
          }}
          control={form.control}
          render={(
            { field, fieldState }
          ) => (
            <TextInput
              {...field}
              error={fieldState.error?.message}
              label="Address details"
              placeholder="Enter your address details"
            />
          )}
        />
        <Controller
        
          name="residential_status"
          rules={{
            required: "Required!",
          }}
          control={form.control}
          render={(
            { field, fieldState }
          ) => (
            <div
              className={styles.stretchToTwoCols}
            >
              
              <DropdownInput
                options={RESIDENTIAL_STATUSES_OPTIONS}
                {...field}
                onChange={(option) => {
                  field.onChange(option);
                  field.onBlur();
                }}
                error={fieldState.error?.message}
                label="Residential status"
                placeholder="Enter your residential status"
              />
            </div>
          )}
        />
        <Button
          disabled={form.formState.disabled || !form.formState.isDirty || !form.formState.isValid}
          type="submit"
          className={classNames(styles.submitButton, styles.stretchToTwoCols)}
          isLoading={form.formState.isSubmitting}
        >
          Save changes
        </Button>
      </form>
      <Divider />
      <div
        className={styles.deleteAccountWrapper}
      >
        <Typography
          variant="bodySmall"
        >
          Permanently delete account with all your data:
        </Typography>
        <Button
          size="small"
          type="button"
          color="error"
          variant="outlined"
          onClick={() => auth.setDeleteAccountConfirmationModalIsOpen(true)}
        >
          Delete account
        </Button>
      </div>
    </div>
  );
});

export default PersonalInfoPage;