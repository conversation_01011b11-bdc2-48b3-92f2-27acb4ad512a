import { breakpoints } from "@/styles/constants.css";
import { theme } from "@/styles/themes.css";
import { style } from "@vanilla-extract/css";


export const container = style({
  display: "grid"
});


export const deleteAccountWrapper = style({
  display: "grid",
  
  gridAutoFlow: "row",
  justifyContent: "center",
  justifyItems: "center",
  alignItems: "center",
  columnGap: 8,
  rowGap: 16,
  paddingTop: 40,
  textAlign: "center",
  "@media": {
    [breakpoints.tablet]: {
      textAlign: "left",
      gridAutoFlow: "column",
      justifyContent: "space-between",
    }
  }
});

export const changeEmailWrapper = style({
  display: "flex",
  flexDirection: "column",
  gap: 16,
  marginBottom: 32,

  "@media": {
    [breakpoints.tablet]: {
      gap: 24,
      flexDirection: "row",
      alignItems: "flex-end",
    }
  }
});

export const personalInfoForm = style({
  display: "grid",
  columnGap: 24,
  rowGap: 24,
  marginTop: 32,
  marginBottom: 24,
  "@media": {
    [breakpoints.tablet]: {
      gridTemplateColumns: "repeat(2, 1fr)",
      rowGap: 32,
      marginBottom: 56,
    }
  }
});

export const stretchToTwoCols = style({
  // gridColumn: '1 / -1',
  "@media": {
    [breakpoints.tablet]: {
      gridColumn: "1 / -1",
    }
  }
});

export const submitButton = style({
  marginTop: 8
});

export const field = style({
  marginBottom: 12,

  "@media": {
    [breakpoints.tablet]: {
      marginBottom: 0,
    }
  }
});