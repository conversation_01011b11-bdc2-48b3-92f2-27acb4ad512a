import { useCallback, useMemo } from "react";
import NavListProps from "./NavList.types";
import Link from "next/link";
import Typography from "../Typography";
import * as styles from "./NavList.css";
import classNames from "classnames";
import useStore from "@/hooks/useStore";
import { observer } from "mobx-react-lite";

const NavList = observer( ({className, options, active, checkValue = ({label}) => label, logOutButton, darkMode = false,}: NavListProps) => {

  const { auth, landign: {headerIsExpanded}  } = useStore();

  const elementsList = useMemo(() => {
    return options.map(({href,label, openInNewTab, disabled}) => (
      <Typography
        target={openInNewTab ? "_blank" : undefined}
        key={href}
        title={label}
        className={classNames({
          [styles.menuItem.base]: checkValue({href, label}) !== active && !darkMode,
          [styles.menuItem.active]: checkValue({href, label}) === active && !darkMode,
          [styles.menuItem.darkMode]: darkMode && checkValue({href, label}) !== active,
          [styles.menuItem.darkModeActive]: darkMode && checkValue({href, label}) === active
        })}
        onClick={disabled ? (e) => e.preventDefault(): undefined }
        as={Link}
        href={href}
      >
        <span>{label}</span>
      </Typography>
    ));
  }, [active, options, darkMode]);

  const onLogOutClick = useCallback(
    () => {
      auth.logout();
    },
    [],
  );
  

  return (
    <nav
      className={classNames(styles.menu[headerIsExpanded ? "expanded" : "default"], className)}
    >
      {elementsList}
      {logOutButton &&  <>
        <div
          className={styles.divider}
        />
        <button
          className={styles.logoutButton}
          type="button"
          onClick={onLogOutClick}
        >
          <Typography
            as={"span"}
            title={"Log out"}
            className={styles.menuItem.base}
          >
            {"Log out"}
          </Typography>
    
        </button>
      </>}
    </nav>
  );
});


export default NavList;