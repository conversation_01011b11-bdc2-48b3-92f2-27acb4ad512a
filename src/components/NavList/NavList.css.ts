import { theme } from "@/styles/themes.css";
import { style, styleVariants } from "@vanilla-extract/css";

const menuBase = style({
  transition: "400ms top ease",
  flexDirection: "column",
  gap: 8,
  position: "sticky",
  overflowY:"auto",
  marginTop: 36
});

const menuDefault = style({
  height: "calc(100dvh - 100px)",
  top: 100,
});
const menuExpanded = style({
  height: "calc(100dvh - 163px)",
  top: 163,

});

export const menu = styleVariants({
  default: [menuBase,menuDefault],
  expanded: [menuBase,menuExpanded]
});



export const menuItemBase = style({
  borderRadius: 8,
  paddingTop: 14,
  paddingBottom: 10,
  paddingLeft: 20,
  paddingRight: 20,
  // width: "max-content",
  transitionProperty: "background-color, color",
  transitionDuration: "100ms",
});

export const menuItem = styleVariants({
  base: [menuItemBase, {
    ":hover": {
      backgroundColor: theme.colors.primary.softWhite,
    },
    ":active": {
      backgroundColor: theme.colors.primary.softWhitePressed,    
      color: theme.colors.primary.castletonGreenPressed,
    }
  }],
  active: [menuItemBase, {
    backgroundColor: theme.colors.primary.castletonGreen,
    color: theme.colors.primary.ivory,
    fontWeight: 500,
  }],
  darkMode: [menuItemBase, {
    color: theme.colors.primary.ivory,
    opacity: 0.8,
    ":hover": {
      backgroundColor: theme.colors.primary.asidGreen,
      color: theme.colors.primary.castletonGreen,
    },
    ":active": {
      backgroundColor: theme.colors.primary.asidGreenPressed,    
      color: theme.colors.primary.castletonGreenPressed,
    }
  }],
  darkModeActive: [menuItemBase, {
    backgroundColor: theme.colors.primary.asidGreen,
    color: theme.colors.primary.castletonGreen,
    fontWeight: 500,
    opacity: 1,
  }],
});

export const logoutButton = style({
  background: "transparent",
  color: "inherit",
  border: 0,
  cursor: "pointer",
  textAlign: "start",
  padding: 0,
  display: "grid"
});


export const divider = style({
  borderTop: `1px solid ${theme.colors.grayscale[100]}`,
  margin: "4px 0",
});