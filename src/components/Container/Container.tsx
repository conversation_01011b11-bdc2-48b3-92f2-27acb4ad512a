import classNames from "classnames";
import { forwardRef } from "react";
import * as styles from "./Container.css";
import Props from "./Container.types";

const Container = forwardRef<HTMLDivElement, Props>(
  (
    {
      className,
      children,
      notFullHeight,
      withAnimation,
      removeBg,
      removeBorderRadius,
      ...restProps
    },
    ref
  ) => {

    return (
      <div
        ref={ref}
        {...restProps}
        className={classNames(styles.root.base, className, {
          [styles.root.notFullHeight]: notFullHeight,
          [styles.root.borderRadius]: !removeBorderRadius,
          [styles.root.noAnimation]: !withAnimation,
          [styles.root.removeBg]: removeBg,
        })}
      >
        {children}
      </div>
    );
  }
);

export default Container;
