import { breakpoints } from "@/styles/constants.css";
import { theme } from "@/styles/themes.css";
import {
  createVar,
  style,
  styleVariants,
} from "@vanilla-extract/css";

const gap = createVar();

export const rootBase = style({
  justifyItems: "stretch",
  vars: {
    [gap]: "10px",
  },
  display: "grid",
  flex: 1,
  // minWidth: "200px",
  maxWidth: "100vw",
  width: "100%",
  //minHeight: "100%",
  // padding: 24,
  // padding: "0 24px",
  // margin: "18px auto",
  // margin: "0 auto",
  marginLeft: "auto",
  marginRight: "auto",
  background: theme.colors.primary.ivory,

  "@media": {
    [breakpoints.tablet]: {
      maxWidth: "1440px",
      vars: {
        [gap]: "20px",
      },
    },
  },
});

const animContainerBase = style({
  display: "flex",
  overflow: "hidden",
  // position: "sticky",
  // top: 0,
  //scrollSnapAlign: "end",
  //scrollSnapStop: "always",
  minHeight: "100%",
  // margin: 20,
  padding: 10,

  "@media": {
    [breakpoints.tablet]: {
      padding: 20,
    },
  },
  // selectors: {
  //   // To prevent footer and last element to overlap
  //   "&:last-of-type, &:nth-last-child(-n + 2)": {
  //     position: "relative",
  //     top: "unset",
  //   },
  // },
});

export const animContainer = styleVariants({
  base: [animContainerBase],
  bg: {
    background: theme.colors.primary.ivory,
  },
});

export const root = styleVariants({
  base: [rootBase],
  notFullHeight: {
    height: "max-content",
    minHeight: "max-content",
  },
  borderRadius: {
    borderRadius: 24,
  },
  noAnimation: {
    paddingLeft: gap,
    paddingRight: gap,

    "@media": {
      [breakpoints.tablet]: {
        vars: {
          [gap]: "20px",
        }
      }
    }
  },
  removeBg: {
    backgroundColor: "unset"
  }
  // bg: {
  //   background: theme.colors.primary.ivory,
  // },
});
