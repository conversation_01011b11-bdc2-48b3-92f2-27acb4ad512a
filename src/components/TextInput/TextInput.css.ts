import { createVar, style, styleVariants } from "@vanilla-extract/css";
import { bodySmall, noteMedium } from "../Typography/Typography.css";
import { theme } from "@/styles/themes.css";
import { breakpoints } from "@/styles/constants.css";

const textInputFillColor = createVar(
);
const textInputContentColor = createVar(
);

export const container = style(
  {
    vars: {
      [textInputContentColor]: theme.colors.primary.castletonGreen,
      [textInputFillColor]: theme.colors.grayscale[100],
    },
    userSelect: "none",
    position: "relative",
    display: "flex",
    flexDirection: "column",
    width: "100%",
    rowGap: 4,
  }
);

export const outlined = style(
  {
    selectors: {
      "&:hover, &:focus-within": {
        vars: {
          [textInputFillColor]: textInputContentColor,
        }
      }    
    }
  }
);

export const filled = style(
  {
    vars: {
      [textInputContentColor]: theme.colors.primary.castletonGreen,
      [textInputFillColor]: theme.colors.primary.ivory,
    },
  }
);

export const inputContainer = styleVariants(
  {
    error: {
      vars: {
        [textInputContentColor]: theme.colors.primary.error,
        [textInputFillColor]: theme.colors.primary.error,
      },
    },
    disabled: {
      vars: {
        [textInputContentColor]: theme.colors.grayscale[200],
        [textInputFillColor]: theme.colors.grayscale[100]
      },
    },
  }
);

export const inputContainerBase = style(
  {
    color: textInputContentColor,
    display: "flex",
    gridColumn: "span 2",
    borderRadius: 8,

    ":focus-within": {
      boxShadow: `0px 0px 0px 2px ${theme.colors.primary.castletonGreen20}`,
    },

    selectors: {
      [`${outlined} &`]: {
        border: `1px solid ${textInputFillColor}`,
      },
      [`${filled} &`]: {
        backgroundColor: textInputFillColor,
      },
      [`${filled}${inputContainer.error} &`]: {
        backgroundColor: theme.colors.primary.softWhite,
        boxShadow: `inset 0 0 0 1px ${theme.colors.primary.error}`,
      },
    },
  }
);

export const input = style(
  [
    bodySmall,
    {
      width: "100%",
      flex: 1,
      paddingTop: 5,
      paddingLeft: 20,
      paddingRight: 20,
      minHeight: 48,
      fontFamily: "inherit",
      backgroundColor: "transparent",
      border: 0,
      margin: 0,
      color: "inherit",
      selectors: {
        "&:focus-visible": {
          outline: 0,
        },
        "&:placeholder": {
          color: theme.colors.grayscale[200],
        },
        // Hide number input arrows
        /* Chrome, Safari, Edge, Opera */
        "&::-webkit-outer-spin-button, &::-webkit-inner-spin-button": {
          WebkitAppearance: "none",
          margin: 0,
        },
        /* Firefox */
        "&[type=\"number\"]": {
          MozAppearance: "textfield",
        },
      },

      "@media": {
        [breakpoints.tablet]: {
          paddingTop: 3,
          paddingLeft: 24,
          paddingRight: 24,
          minHeight: 56,
        }
      }
    },
  ]
);

export const error = style(
  {
    position: "absolute",
    top: "calc(100% + 6px)",
    color: theme.colors.primary.error,
    left: 0,
    right: 0,
  }
);

export const label = style(
  [noteMedium, {
    selectors: {
      [`${inputContainer.error}:not(${filled}) &`]: {
        color: textInputFillColor,
      }
    }
  }],
);

export const description = style(
  {
    gridColumn: "span 2",
    opacity: 0.7,
  }
);

export const inputIcon = style({
  width: 24,
  aspectRatio: "1 / 1",
  marginRight: 24,
  cursor: "pointer",
});