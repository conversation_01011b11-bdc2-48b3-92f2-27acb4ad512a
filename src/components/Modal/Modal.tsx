"use client";
import Popup from "reactjs-popup";

import MemoCloseIcon from "@/assets/icons/CloseIcon";
import classNames from "classnames";
import * as styles from "./Modal.css";
import ModalProps from "./Modal.types";

const Modal = (
  { children, fullWidth, fullHeight, stickToBottom, open, ...modalProps }: ModalProps
) => {
  return (
    <Popup
      {...modalProps}
      contentStyle={{ width: fullWidth ? "100%" : "auto", height: fullHeight ? "100%" : "auto" }}
      nested
      modal
      open={open}
      closeOnEscape
      closeOnDocumentClick
      className={classNames( styles.contentContainer, {
        [styles.stickToBottom]: stickToBottom,
        [styles.isFullSize]: fullWidth && fullHeight,
      })}
    >
      <div
        className={classNames(styles.content, {
          [styles.isFullSize]: fullWidth && fullHeight,
        })}
      >
        <button
          type="button"
          onClick={modalProps.onClose}
          className={styles.closeIconWrapper}
        >
          <MemoCloseIcon />
        </button>
        {children}
      </div>
    </Popup>
  );
};

export default Modal;
