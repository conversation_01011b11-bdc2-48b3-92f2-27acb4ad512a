import { breakpoints } from "@/styles/constants.css";
import { theme } from "@/styles/themes.css";
import { globalStyle, keyframes, style } from "@vanilla-extract/css";

export const contentContainer = style(
  {
  // maxWidth: 690,
  // width: "100%",
  // padding: "0 16px",
  }
);

export const stickToBottom = style({
  
});

globalStyle(
  `${stickToBottom}-content`, {
    marginBottom: "0 !important",
    "@media": {
      [breakpoints.tablet]: {
        marginBottom: "auto !important",
      },
    },
  }
);

const contentAppear = keyframes({
  from: {
    opacity: 0,
    transform: "scale(0.9) translateY(15%)",
  },
  to: {
    opacity: 1,
    transform: "scale(1) translateY(0%)",
  }
});

globalStyle(
  `${contentContainer}-content`, {
    padding: "16px",
    maxWidth: "100%",
    animation: `${contentAppear} 250ms ease-out forwards`,

    "@media": {
      [breakpoints.tablet]: {
        maxWidth: 760,
      }
    },
  }
);

export const isFullSize = style({
  height: "100%",
  width: "100%",
  borderRadius: 0,
});

globalStyle(
  `${isFullSize}-content${contentContainer}-content`, {
    padding: 0,
    maxWidth: "none",

    "@media": {
      [breakpoints.tablet]: {
        padding: "48px 32px",
        borderRadius: 24,
      },
    },
  }
);

const overlayAppear = keyframes({
  from: {
    backgroundColor: "transparent",
  },
  to: {
    backgroundColor: "#00211B99",
  }
});

globalStyle(
  `${contentContainer}-overlay`, {
    overflow: "auto",
    animation: `${overlayAppear} 350ms linear forwards`,
  }
);

export const content = style(
  {
    position: "relative",
    borderRadius: 16,
    background: theme.colors.primary.softWhite,
    color: theme.colors.primary.castletonGreen,
    padding: "32px 20px",
    "@media": {
      [breakpoints.tablet]: {
        padding: "48px 32px",
        borderRadius: 24,
      },
      [breakpoints.desktop]: {
        padding: "64px 48px",
      },
    },
    boxSizing: "border-box",
    
    selectors: {
      [`&${isFullSize}`]: {
        borderRadius: 0,
        padding: 0,
        color: theme.colors.primary.softWhite,

        "@media": {
          [breakpoints.tablet]: {
            borderRadius: 24,
          },
        },
      }
    }
  },
);

export const closeIconWrapper = style(
  {
    display: "flex",
    cursor: "pointer",
    position: "absolute",
    top: 16,
    right: 16,
    border: 0,
    background: "none",
    fontSize: 24,
    padding: 4,
    color: "currentColor",
    zIndex: 10,
    transition: "background-color 50ms, transform 350ms",
    borderRadius: "50%",

    ":hover": {
      backgroundColor: theme.colors.primary.ivory,
      color: theme.colors.primary.castletonGreen,
    },

    ":active": {
      backgroundColor: theme.colors.grayscale[100],
      color: theme.colors.primary.castletonGreenPressed,
      transform: "scale(0.9)",
    },
  }
);
