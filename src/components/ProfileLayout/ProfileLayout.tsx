"use client";

import Container from "../Container";

import useStore from "@/hooks/useStore";
import { gridSprinkle } from "@/styles/sprinkles.css";
import classNames from "classnames";
import { observer } from "mobx-react-lite";
import { redirect, RedirectType, usePathname, useRouter } from "next/navigation";
import { useCallback, useEffect, useMemo, useState } from "react";
import BalanceCard from "../BalanceCard";
import { Option } from "../DropdownInput/DropdownInput.types";
import Header from "../Header";
import NavList from "../NavList";
import { NavListOption } from "../NavList/NavList.types";
import ProfileHeader from "../ProfileHeader";
import UserCard from "../UserCard";
import * as styles from "./ProfileLayout.css";
import ProfileLayoutProps from "./ProfileLayout.types";
import Loader from "../Loader";
import FloatingContactWidgets from "../FloatingContactWidgets";
import { WHATS_UP_NUMBER } from "@/utils/constants";
import { createClient } from "@/utils/supabase/client";


const NAV_LINKS: NavListOption[] = [
  {
    label: "Earn program",
    href: "/profile",
  },
  {
    label: "Plans",
    href: "/profile/plans",
  },
  {
    label: "Personal info",
    href: "/profile/personal-info",
  },
  {
    label: "Billing info",
    href: "/profile/billing-info",
    openInNewTab: true,

  },
  {
    label: "Home info",
    href: "/profile/home-info",
  },
  // Uncomment if password tab neede in the future
  // It will appear in the profile page nav tab
  // {
  //   label: "Password",
  //   href: "/profile/password",
  // },
];

// const supabase = createClient();


const ProfileLayout = observer(({ children, header, emergencyLocations }: ProfileLayoutProps) => {
  const { auth} = useStore();

  useEffect(() => {
    if (!auth.isAuthorizationInProgress && !auth.isAuthorized) {
      redirect("/login", RedirectType.replace);
    }
  }, [auth.isAuthorizationInProgress, auth.isAuthorized]);
  




  const pathname = usePathname();
  const router = useRouter();

  // useEffect(() => {
  //   (async function getPortalLink () {
  //     const response  = await supabase.functions.invoke<{ redirectUrl: string}>("stripe/customers/portal", {method: "GET"});
  //     if(!response.data) return;
  //     setNavLinks(prev => prev.map((nav) => (!!nav.href ? nav : ({...nav, href: response.data.redirectUrl, disabled: false}))));
  //   })();
  // }, []);

  const selected = useMemo<Option>(() => {
    const currentTab = NAV_LINKS.find(({href}) => href === pathname );
    if(!currentTab) return null;
    return {
      label: currentTab.label,
      value: currentTab.href,
    };

  } , [pathname]);

  const options = useMemo<Option<string>[]>(() => NAV_LINKS.map(({href,label}) => ({label, value: href})), []);

  const onSelectChange = useCallback(
    (selected: Option) => {
      if(!selected || !selected.value) return;
      router.push(`${selected.value}`);
    },
    [router],
  );    

  // FIXME?: We have a problem with a scroll on the profile page
  // When I scrolled down a little on a home page and the went to the profile page (on mobile)
  // scroll appears to be in the middle of the page
  // Idk why this happening but 'scroll' prop in the 'next/link' doesn't work
  // So I added this hacky soluiton
  useEffect(() => window.document.scrollingElement?.scrollTo({top: 0,
    behavior: "instant"
  }), []);

  const isSplitted = useMemo(() => selected?.value === NAV_LINKS[0].href , [selected]);
  const isForm = useMemo(() => [NAV_LINKS[2].href, NAV_LINKS[3].href,NAV_LINKS[4].href].includes(`${selected?.value}`), [selected]);


  // FIXME: href.includes("plans") - is a temp solution for a nested routes
  // it was made to make nav item to highlight
  // here should be better approach

  return (
    <Container>
      <div
        className={styles.mainContainer}
      >
        <Header
          emergencyLocations={emergencyLocations}
          header={header}
        />
        <main
          className={classNames(styles.main, gridSprinkle({type: "grid"}))}
        >
          <NavList
            logOutButton
            checkValue={({href}) => href.includes("plans") ? "plans" : href}
            options={NAV_LINKS}
            active={pathname?.includes("plans") ? "plans" : pathname}
            className={classNames(styles.navList, gridSprinkle({type: "item", cols: {
              mobile: 10,
              tablet: 2,
            },
            display: {
              tablet: "flex",
              mobile: "none",
            }}))}
          />
          {auth.userIsLoading ? (
            <div
              className={classNames(styles.loaderWrapper, gridSprinkle({ type: "item", cols: { mobile: 10, tablet: 8 } }))}
            >
              <Loader />
            </div>
          ) : (
            <section
              className={classNames(styles.section[isSplitted ? "default" : "removeGap"], gridSprinkle({type: "item", cols: {
                mobile: 10,
                tablet: 8,
                desktop: 8
              }}),
              {[styles.section.form]: isForm})}
            >
              <div
                className={styles.paper[!isSplitted ? "removeBottomBorderRadius" : "default"]}
              >
                <ProfileHeader
                  email={auth.user?.email}
                  fullName={auth.user?.full_name}
                  onSelectChange={onSelectChange}
                  options={options}
                  selected={selected}
                />
                {isSplitted && (
                  <div
                    className={styles.userInfoWrapper}
                  >
                    <BalanceCard/>
                    <UserCard />
                  </div>
                )}
              </div>
              <div
                className={styles.paper[!isSplitted ? "removeTopBorderRadius" : "default"]}
              >
                {children}
              </div>
            </section>            
          )}
        </main>
      </div>
      <FloatingContactWidgets
        phoneNumber={String(header.data.phone_number)}
        whatsAppNumber={WHATS_UP_NUMBER}
      />
    </Container>
  );
});

export default ProfileLayout;