import { GroupField } from "@prismicio/client";
import { EmergencyLocationsDocumentDataLocationsItem, HeaderDocument, Simplify } from "prismicio-types";
import { ReactNode } from "react";


type ProfileLayoutProps = {
  children: ReactNode | ReactNode[];
  header: HeaderDocument<string>;
  emergencyLocations:  GroupField<Simplify<EmergencyLocationsDocumentDataLocationsItem>>;
}

export default ProfileLayoutProps;