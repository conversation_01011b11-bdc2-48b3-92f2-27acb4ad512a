import { breakpoints } from "@/styles/constants.css";
import { theme } from "@/styles/themes.css";
import { style, styleVariants } from "@vanilla-extract/css";

export const mainContainer = style({
  minHeight: "100dvh",
  display: "flex",
  flexDirection: "column",
  paddingTop: 130,

  "@media": {
    [breakpoints.tablet]: {
      paddingTop: 150,
    }
  }
});

export const main = style({

  flex: "1 1",
  position: "relative",
  // display: "grid",
  // columnGap: 20,
  // gridTemplateColumns: "314px 1fr"
});

export const nav = style({
  display: "flex",
  flexDirection: "column",
});

const sectionBase = style({
  display: "flex",
  flexDirection: "column",
  rowGap: 10,
  marginBottom: 10,
  "@media": {
    [breakpoints.tablet]: {
      rowGap: 20,
      marginBottom: 46,
    },
    [breakpoints.desktop]: {
      marginLeft: 70,
    },
  }
  
});

export const section = styleVariants({
  default: [sectionBase],
  removeGap: [sectionBase, {
    rowGap: 10,
    "@media": {
      [breakpoints.tablet]: {
        rowGap: 0,
      }
    }
  }],
  form: [{maxWidth: 690}]
});

const paperBase = style({
  borderRadius: 16,
  background: theme.colors.primary.softWhite,
  padding: 20,
  "@media": {
    [breakpoints.tablet]: {
      borderRadius: 24,
      padding: 48,
    }
  }
});

export const paper = styleVariants({
  default: [paperBase],
  removeBottomBorderRadius: [paperBase, {
    "@media": {
      [breakpoints.tablet]: {
        borderBottomLeftRadius: 0,
        borderBottomRightRadius: 0,
        paddingBottom: 0,

      }
    }
  }],
  removeTopBorderRadius: [paperBase, {
    "@media": {
      [breakpoints.tablet]: {
        borderTopLeftRadius: 0,
        borderTopRightRadius: 0,
        paddingTop: 0,
      }
    }
  }],
});

export const userInfoWrapper = style({
  display: "grid",
  columnGap: 24,
  rowGap: 20,
  alignItems: "baseline",
  "@media": {
    [breakpoints.tablet]: {
      gridTemplateColumns: "repeat(2, 1fr)",
    }
  }
});

export const navList  = style({
  
  "@media": {
    [breakpoints.desktop]: {
      marginLeft: 68,
    }
  },
  maxWidth: 170,
});

export const loaderWrapper = style({
  height: "100%",
  display: "flex",
  justifyContent: "center",
  alignItems: "center",
  color: "currentColor",
  fontSize: 120,
});