"use client";
import { FC } from "react";
import ConfirmModal from "../ConfirmModal";
import CallUsModalProps from "./CallUsModal.types";


const CallUsModal:FC<CallUsModalProps> = ({ phoneNumber, ...props}) => {

  return (
    <ConfirmModal
      title={`Have
a question?`}
      hideAcceptButton
      cancelButtonProps={{
        children: "Ok",
        onClick: props.onClose,
        
      }}
      {...props}
    >
      {"You can reach us at "}<b>{phoneNumber}</b>
    </ConfirmModal>
  );
};

export default CallUsModal;