import { FC, createElement } from "react";
import * as styles from "./Typography.css";
import Props from "./Typography.types";
import classNames from "classnames";

const variantTagsMap: {
  [K in NonNullable<Props<any>["variant"]>]: NonNullable<Props<any>["as"]>;
} = {
  h1: "h1",
  h2: "h2",
  h3: "h3",
  h4: "h4",
  h4It: "h4",
  h5: "h5",
  subTitleMedium: "div",
  subTitleSmall: "div",
  buttonMedium: "span",
  buttonSmall: "span",
  noteMedium: "div",
  note: "div",
  bodyMedium: "div",
  bodySmall: "div",
};

function Typography<P extends {}>({
  as,
  variant = "bodyMedium",
  className,
  children,
  fontFamily = "secondary",
  isGreenItalic,
  ...restProps
}: Props<P> & Omit<P, keyof Props<P>>) {
  return createElement(
    as || (variant ? variantTagsMap[variant] : "p"),
    {
      className: classNames(styles[variant], className, {
        [styles.primaryFont]: fontFamily === "primary",
        [styles.greenEm]: isGreenItalic,
        [styles.buttonReset]: as === "button"
      }),
      ...restProps,
    },
    children
  );
}

export default Typography;
