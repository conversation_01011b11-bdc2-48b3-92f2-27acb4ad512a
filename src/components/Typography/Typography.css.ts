import { breakpoints } from "@/styles/constants.css";
import { theme } from "@/styles/themes.css";
import { globalStyle, style } from "@vanilla-extract/css";

export const base = style(
  {
    textDecoration: "none",
  }
);

export const primaryFont = style(
  {
    fontFamily: theme.fonts.primary,
  }
);

export const headingText = style(
  [
    base,
    {
      fontWeight: 400,
      lineHeight: "95%",
      letterSpacing: "-0.02em",
    },
  ]
);

export const h1 = style(
  [
    headingText,
    {
      fontSize: "58px",
      "@media": {
        [breakpoints.tablet]: {
          fontSize: "88px",
        },
      },
    },
  ]
);

export const h2 = style(
  [
    headingText,
    {
      fontSize: "52px",
      "@media": {
        [breakpoints.tablet]: {
          fontSize: "80px",
        },
      },
    },
  ]
);

export const h3 = style(
  [
    headingText,
    {
      fontSize: "44px",
      "@media": {
        [breakpoints.tablet]: {
          fontSize: "64px",
        },
      },
    },
  ]
);

export const h4 = style(
  [
    headingText,
    {
      fontSize: "36px",
      "@media": {
        [breakpoints.tablet]: {
          fontSize: "56px",
        },
      },
    },
  ]
);
export const h4It = style(
  [
    headingText,
    {
      fontSize: "36px",
      fontWeight: 500,
      "@media": {
        [breakpoints.tablet]: {
          fontSize: "56px",
        },
      },
    },
  ]
);

export const h5 = style(
  [
    headingText,
    {
      fontSize: "40px",
      "@media": {
        [breakpoints.tablet]: {
          fontSize: "40px",
        },
      },
    },
  ]
);

export const subTitleText = style(
  [
    base,
    {
      lineHeight: "120%",
    },
  ]
);

export const subTitleMedium = style(
  [
    subTitleText,
    {
      fontSize: "22px",
      letterSpacing: "-0.01em",
      fontWeight: 500,
      "@media": {
        [breakpoints.tablet]: {
          fontSize: "24px",
        },
      },
    },
  ]
);

export const subTitleSmall = style(
  [
    subTitleText,
    {
      fontSize: "15px",
      fontWeight: 500,
      "@media": {
        [breakpoints.tablet]: {
          fontSize: "16px",
        },
      },
    },
  ]
);

export const buttonMedium = style(
  [
    subTitleText,
    {
      fontSize: "18px",
      letterSpacing: "-0.02em",
      fontWeight: 500,
      "@media": {
        [breakpoints.tablet]: {
          fontSize: "20px",
        },
      },
    },
  ]
);

export const buttonSmall = style(
  [
    subTitleText,
    {
      fontSize: "14px",
      letterSpacing: "-0.02em",
      fontWeight: 500,
      "@media": {
        [breakpoints.tablet]: {
          fontSize: "16px",
        },
      },
    },
  ]
);

export const noteMedium = style(
  [
    subTitleText,
    {
      fontSize: "14px",
      letterSpacing: "-0.01em",
      fontWeight: 500,
      "@media": {
        [breakpoints.tablet]: {
          fontSize: "16px",
        },
      },
    },
  ]
);

export const note = style(
  [
    subTitleText,
    {
      fontSize: "14px",
      letterSpacing: "-0.01em",
      margin: 0,
      "@media": {
        [breakpoints.tablet]: {
          fontSize: "16px",
        },
      },
    },
  ]
);

export const bodyText = style(
  [
    base,
    {
      lineHeight: "120%",
      fontWeight: 400,
    },
  ]
);

export const bodyMedium = style(
  [
    bodyText,
    {
      fontSize: "18px",
      "@media": {
        [breakpoints.tablet]: {
          fontSize: "20px",
        },
      },
    },
  ]
);

export const bodySmall = style(
  [
    bodyText,
    {
      fontSize: "16px",
      "@media": {
        [breakpoints.tablet]: {
          fontSize: "18px",
          lineHeight: "130%",
        },
      },
    },
  ]
);


export const greenEm = style({});

export const buttonReset = style({
  border: 0,
  padding: 0,
  backgroundColor: "transparent",
  color: "inherit",
  cursor: "pointer"

});

globalStyle(`${greenEm} em`, {
  color: theme.colors.primary.asidGreen
});