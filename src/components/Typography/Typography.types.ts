import {
  DetailedHTMLProps,
  FunctionComponent,
  HTMLAttributes,
} from "react";

interface Props<P>
  extends DetailedHTMLProps<HTMLAttributes<HTMLDivElement>, HTMLDivElement> {
  as?:
    | "h1"
    | "h2"
    | "h3"
    | "h4"
    | "h5"
    | "h6"
    | "p"
    | "div"
    | "span"
    | "button"
    | FunctionComponent<P>;
  variant?:
    | "h1"
    | "h2"
    | "h3"
    | "h4"
    | "h5"
    | "h4It"
    | "subTitleMedium"
    | "subTitleSmall"
    | "buttonMedium"
    | "buttonSmall"
    | "noteMedium"
    | "note"
    | "bodyMedium"
    | "bodySmall";
  fontFamily?: "primary" | "secondary";
  isGreenItalic?: boolean
}

export default Props;
