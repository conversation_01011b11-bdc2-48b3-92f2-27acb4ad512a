import { GroupField } from "@prismicio/client";
import { EmergencyLocationsDocumentDataLocationsItem, HeaderDocument, Simplify } from "prismicio-types";

export type HeaderVariants = "primary" | "secondary"

export type AnchorLinksType = {
  name: string;
  id: string;
}

type HeaderProps = {
  variant?: HeaderVariants
  header: HeaderDocument<string>
  emergencyLocations?:  GroupField<Simplify<EmergencyLocationsDocumentDataLocationsItem>>;
  anchorLinks?: AnchorLinksType[]
  showModeToggle?: boolean
}

export default HeaderProps;