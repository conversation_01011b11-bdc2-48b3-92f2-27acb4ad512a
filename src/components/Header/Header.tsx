"use client";

import MemoCloseIcon from "@/assets/icons/CloseIcon";
import MemoPhoneIcon from "@/assets/icons/PhoneIcon";
import UserIcon from "@/assets/icons/UserIcon";
import useStore from "@/hooks/useStore";
import { gridSprinkle } from "@/styles/sprinkles.css";
import PrismicLink from "@components/Link";
import classNames from "classnames";
import { observer } from "mobx-react-lite";
import Link from "next/link";
import { usePathname, useRouter, useSearchParams } from "next/navigation";
import {
  FC,
  Fragment,
  Suspense,
  useCallback,
  useEffect,
  useLayoutEffect,
  useMemo,
  useRef,
  useState,
} from "react";
import Popup from "reactjs-popup";
import Accordion from "../Accordion";
import BookingModal from "../BookingModal";
import Button from "../Button";
import Container from "../Container";
import DeleteAccountModal from "../DeleteAccountModal";
import Divider from "../Divider/Divider";
import FloatingMenu from "../FloatingMenu";
import IconButton from "../IconButton";
import Logo from "../Logo/Logo";
import LogoutConfirmationModal from "../LogoutConfirmationModal";
import OpportunitiesModal from "../OpportunitiesModal";
import Toggler from "../Toggler";
import Typography from "../Typography";
import * as styles from "./Header.css";
import HeaderProps from "./Header.types";
import MemoMenuIcon from "@/assets/icons/MenuIcon";
import ModeProvider from "../ModeProvider";
import {
  DARK_MODE_PATHNAMES,
  DYNAMIC_DARK_MODE_PATH_PATTERNS,
} from "@/utils/constants";
import CallUsModal from "../CallUsModal";
import BookServiceButton from "../BookServiceButton";
import { ChevronIcon } from "@/assets/icons/ChevronIcon";
import { useHidePopupOnScroll } from "@/hooks/useHidePopupOnScroll";
import { debounce, throttle } from "@/utils/helpers";
import { getInitials } from "@/utils/getInitials";

const PROFILE_HEADER_LINKS = [
  {
    label: "Earn Program",
    url: "/profile",
  },
  {
    label: "Plans",
    url: "/profile/plans",
  },
  {
    label: "Personal info",
    url: "/profile/personal-info",
  },
  {
    label: "Home info",
    url: "/profile/home-info",
  },
  // {
  //   label: "Password",
  //   url: "/profile/password",
  // },
];

function isPathnameExcluded(pathname: string) {
  // Check static paths
  if (DARK_MODE_PATHNAMES.includes(pathname)) {
    return true;
  }

  // Check dynamic paths
  for (const pattern of DYNAMIC_DARK_MODE_PATH_PATTERNS) {
    if (pattern.test(pathname)) {
      return true;
    }
  }

  return false;
}

const HeaderElement: FC<HeaderProps> = observer(
  ({
    header,
    variant = "primary",
    anchorLinks,
    emergencyLocations = [],
    showModeToggle = true,
  }) => {
    const { auth, landign } = useStore();

    const searchParams = useSearchParams();

    const pathname = usePathname();

    const router = useRouter();

    const [openedSubLinks, setOpenedSubLinks] = useState<string | null>(null);

    const { setRef: setPopupRef } = useHidePopupOnScroll();

    useEffect(() => {
      if (landign.headerMenuIsOpen) {
        document.documentElement.style.overflow = "hidden";
        document.documentElement.style.overflow = "hidden";
      } else {
        document.body.style.removeProperty("overflow");
        document.documentElement.style.removeProperty("overflow");
      }
    }, [landign.headerMenuIsOpen]);

    useEffect(() => {
      // Fix of bug when we come from another scrolled page back to home page
      // header is not expanded at the top of the page
      if (window.scrollY <= 140) {
        landign.setHeaderIsExpanded(true);
      }
    }, []);

    useLayoutEffect(() => {
      let lastScroll = window.scrollY;
      let isGoingUp = true;

      function handleScroll() {
        const deeperElement = document.elementFromPoint(
          window.innerWidth / 2,
          window.innerHeight / 2
        );

        if (!deeperElement) return;

        const closestPreviewBlock = deeperElement.closest(
          "[data-slice-type=\"preview_section\"]"
        );

        const previewItems = Number(
          closestPreviewBlock?.getAttribute("data-slice-slides-length") ?? 0
        );

        if (closestPreviewBlock && previewItems > 1) {
          document.documentElement.style.scrollSnapType = "y mandatory";

          return;
        } else {
          document.documentElement.style.removeProperty("scroll-snap-type");
        }

        // Check if we are on top of the page
        if (window.scrollY <= 140) {
          landign.setHeaderIsExpanded(true);
        } else {
          if (lastScroll < window.scrollY && isGoingUp) {
            isGoingUp = false;
            landign.setHeaderIsExpanded(isGoingUp);
          } else if (lastScroll > window.scrollY && !isGoingUp) {
            isGoingUp = true;
            landign.setHeaderIsExpanded(isGoingUp);
          }
        }

        lastScroll = window.scrollY;
      }

      const throttledHandleScroll = throttle(handleScroll, 250);

      window.addEventListener("scroll", throttledHandleScroll);

      return () => {
        window.removeEventListener("scroll", throttledHandleScroll);
      };
    }, []);

    useEffect(() => {
      landign.setBookingModalIsOpen(false);
    }, []);

    useEffect(() => {
      if (pathname && !isPathnameExcluded(pathname)) {
        document.documentElement.removeAttribute("data-mode");
        return;
      }

      const params = new URLSearchParams(
        (searchParams as URLSearchParams | null) ?? {}
      );

      const currentMode = params.get("mode");

      if (currentMode === "residential" || !currentMode) {
        document.documentElement.setAttribute("data-mode", "residential");
      } else {
        document.documentElement.setAttribute("data-mode", "commercial");
      }
    }, [searchParams]);

    const onToggleChange = useCallback(() => {
      const params = new URLSearchParams(
        (searchParams as URLSearchParams | null) ?? {}
      );

      const currentMode = params.get("mode");

      if (currentMode === "residential" || !currentMode) {
        params.set("mode", "commercial");
      } else {
        params.delete("mode");
      }

      router.replace(`${pathname}?${params.toString()}`);
    }, [router, searchParams]);

    const isCommercial = useMemo(
      () => searchParams?.get("mode") === "commercial",
      [searchParams]
    );

    const isPrimaryStyle = useMemo(() => variant === "primary", [variant]);

    const isProfile = useMemo(
      () => pathname?.startsWith("/profile"),
      [pathname]
    );

    return (
      <header
        className={styles.root[variant]}
        onClick={() => landign.setHeaderMenuIsOpen(false)}
      >
        <Accordion
          isOpen={landign.headerIsExpanded}
        >
          <div
            className={styles.firstPart[variant]}
          >
            <Container
              notFullHeight
              className={classNames(
                styles.container,
                gridSprinkle({
                  type: "grid",
                })
              )}
            >
              <div
                className={gridSprinkle({
                  type: "item",
                  cols: 3,
                })}
              />
              <div
                className={gridSprinkle({
                  type: "item",
                  cols: 4,
                  justifySelf: "center",
                })}
              >
                {isPrimaryStyle && showModeToggle && (
                  <Toggler
                    onChange={onToggleChange}
                    wrapperClassname={classNames(
                      styles.modeSwitcher,
                      styles.linkWithIcon
                    )}
                    className={styles.modeSwitcherDarkMode}
                    preffix={() => (
                      <Typography
                        variant="note"
                      >Residential</Typography>
                    )}
                    suffix={() => (
                      <Typography
                        variant="note"
                      >Commercial</Typography>
                    )}
                    title={`Switch to ${searchParams?.get("mode") === "commercial" ? "Residential" : "Commercial"}`}
                    checked={isCommercial}
                    // variant={searchParams?.get("mode") !== "commercial" ? "residential" : "filledDark"}
                    variant={
                      searchParams?.get("mode") !== "commercial"
                        ? "outlined"
                        : "filledDark"
                    }
                  />
                )}
              </div>
              <div
                className={gridSprinkle({
                  type: "item",
                  cols: isPrimaryStyle ? 3 : 10,
                  justifySelf: isPrimaryStyle ? "end" : "center",
                  display: { mobile: "none", tablet: "flex" },
                })}
              >
                <Typography
                  variant="subTitleSmall"
                  as={Link}
                  prefetch={false}
                  href={`tel:${(header.data.phone_number || "").replaceAll(" ", "")}`}
                  className={classNames(
                    styles.linkWithIcon[variant],
                    styles.hideOnTablet
                  )}
                >
                  <span
                    className={styles.phoneIconWrapper}
                  >
                    <MemoPhoneIcon
                      className={styles.phoneIcon}
                    />
                  </span>
                  {header.data.phone_number}
                </Typography>
                <button
                  className={styles.callUsButton}
                  onClick={() => landign.setCallUsModalIsOpen(true)}
                >
                  <Typography
                    variant="subTitleSmall"
                    className={classNames(
                      styles.linkWithIcon[variant],
                      styles.hideOnMobile,
                      styles.desktopPhoneContainer
                    )}
                  >
                    <span
                      className={styles.phoneIconWrapper}
                    >
                      <MemoPhoneIcon
                        className={styles.phoneIcon}
                      />
                    </span>
                    {header.data.phone_number}
                  </Typography>
                </button>
              </div>
            </Container>
            <Container
              className={styles.dividerContainer}
              notFullHeight
            >
              <div
                className={styles.divider[variant]}
              />
            </Container>
            <LogoutConfirmationModal
              isOpen={auth.logoutConfirmationModalIsOpen}
              onClose={() => auth.setLogoutConfirmationModalIsOpen(false)}
            />
            <DeleteAccountModal
              isOpen={auth.deleteAccountConfirmationModalIsOpen}
              onClose={() =>
                auth.setDeleteAccountConfirmationModalIsOpen(false)
              }
            />
            <BookingModal
              isOpen={landign.bookingModalIsOpen}
              onClose={() => landign.setBookingModalIsOpen(false)}
            />
            <OpportunitiesModal
              isOpen={landign.opportunitiesModalIsOpen}
              onClose={() => landign.setOpportunitiesModalIsOpen(false)}
            />
            <CallUsModal
              phoneNumber={header.data.phone_number as string}
              open={landign.callUsModalIsOpen}
              onClose={() => landign.setCallUsModalIsOpen(false)}
            />
          </div>
        </Accordion>
        <div
          className={styles.mainContent[variant]}
        >
          <Container
            notFullHeight
            className={classNames(
              styles.container,
              gridSprinkle({ type: "grid" })
            )}
          >
            <div
              className={gridSprinkle({
                type: "item",
                cols: {
                  mobile: 3,
                  tablet: 2,
                },
              })}
            >
              <Link
                title="Home Page"
                href={"/"}
                prefetch={false}
                className={classNames(
                  styles.linkWithIcon[variant],
                  styles.logoLink
                )}
              >
                <Logo
                  className={styles.logo}
                />
              </Link>
            </div>
            <div
              className={gridSprinkle({
                type: "item",
                cols: {
                  mobile: 4,
                },
                justifySelf: "center",
                display: {
                  mobile: "flex",
                  tablet: "none",
                },
              })}
            >
              <Typography
                as={Link}
                prefetch={false}
                variant="subTitleSmall"
                href={`tel:${header.data.phone_number?.replaceAll(" ", "")}`}
                className={styles.linkWithIcon[variant]}
              >
                <span
                  className={styles.phoneIconWrapper}
                >
                  <MemoPhoneIcon
                    className={styles.phoneIcon}
                  />
                </span>
                {header.data.phone_number}
              </Typography>
            </div>
            <div
              className={gridSprinkle({
                type: "item",
                cols: {
                  mobile: 6,
                },
                justifySelf: "center",
                display: {
                  mobile: "none",
                  tablet: "flex",
                },
              })}
            >
              <nav
                className={styles.navigation}
              >
                {!!anchorLinks
                  ? anchorLinks.map(({ id, name }) => {
                    return (
                      <Typography
                        variant="subTitleSmall"
                        href={`#${id}`}
                        as={Link}
                        className={classNames(styles.linkWithIcon[variant])}
                        key={id}
                      >
                        {name}
                      </Typography>
                    );
                  })
                  : header.data.navigation_menu.map((link) => {
                    const isSubMenu = false;
                    // IDK why prismic doesn't have 'url' here
                    //@ts-ignore
                    const isCurrentActivePage = pathname?.includes(link.menu_item_link.url);
                    return (
                      <Popup
                        arrow={false}
                        key={link.menu_item_name}
                        disabled={!isSubMenu}
                        on={["hover"]}
                        trigger={(open) => (
                          <div
                            className={styles.subItemLinkWrapper}
                          >
                            <Typography
                              as={PrismicLink}
                              variant="subTitleSmall"
                              field={link.menu_item_link}
                              className={classNames(
                                styles.linkWithIcon[variant],
                                {
                                  [styles.linkWithIconActive]:
                                      open || isCurrentActivePage,
                                }
                              )}
                            >
                              {link.menu_item_name}
                              {isSubMenu && (
                                <div
                                  className={styles.linkIcon}
                                >
                                  <ChevronIcon
                                    turn={open ? "top" : "bottom"}
                                  />{" "}
                                </div>
                              )}
                            </Typography>
                          </div>
                        )}
                      >
                        {isSubMenu && (
                          <FloatingMenu
                            items={emergencyLocations?.map(
                              ({ label, uuid }) => {
                                return (
                                  <Typography
                                    className={styles.menuLink}
                                    key={label}
                                    variant="bodySmall"
                                    as={Link}
                                    href={`emergencies/${uuid}`}
                                  >
                                    {label}
                                  </Typography>
                                );
                              }
                            )}
                          />
                        )}
                      </Popup>
                    );
                  })}
              </nav>
            </div>
            <div
              className={classNames(
                styles.actions,
                gridSprinkle({
                  type: "item",
                  cols: {
                    mobile: 2,
                  },
                  justifySelf: "end",
                  display: {
                    mobile: "none",
                    tablet: "flex",
                  },
                })
              )}
            >
              {auth.isAuthorized ? (
                <Popup
                  ref={setPopupRef}
                  arrow={false}
                  on={["click"]}
                  repositionOnResize
                  position="bottom right"
                  closeOnDocumentClick
                  trigger={(open) => (
                    <IconButton
                      title="Profile Menu"
                      // color={open ? (variant === "secondary" ? "primaryInverted" : "primary") : (variant === "secondary" ? "secondary" : "primaryInverted")}
                      color={
                        open
                          ? variant === "secondary"
                            ? "primaryInverted"
                            : "primary"
                          : "primaryInverted"
                      }
                      variant={open ? "outlined" : "filled"}
                      shape="circle"
                      size="small"
                      className={classNames({
                        [styles.avatarOpenDarkMode]: open,
                      })}
                    >
                      <Typography
                        variant="buttonSmall"
                      >
                        {getInitials(auth.user?.full_name ?? "")}
                      </Typography>
                    </IconButton>
                  )}
                >
                  <FloatingMenu
                    items={[
                      <>
                        <Typography
                          className={styles.userName}
                        >
                          {auth.user?.full_name}
                        </Typography>
                        <Typography
                          variant="note"
                        >
                          {auth.user?.email}
                        </Typography>
                      </>,
                      <Divider
                        key="divider"
                      />,
                      ...PROFILE_HEADER_LINKS.map((item, index) => (
                        <Typography
                          className={styles.menuLink}
                          key={index}
                          variant="bodySmall"
                          as={Link}
                          href={item.url}
                        >
                          {item.label}
                        </Typography>
                      )),
                      <Divider
                        key="divider"
                      />,
                      <Typography
                        key="sign-out"
                        onClick={() =>
                          auth.setLogoutConfirmationModalIsOpen(true)
                        }
                        className={styles.menuLink}
                        variant="bodySmall"
                      >
                        Log Out
                      </Typography>,
                    ]}
                  />
                </Popup>
              ) : (
                <ModeProvider>
                  {(mode) => (
                    <IconButton
                      title="Login"
                      color={
                        mode === "residential"
                          ? "primaryInverted"
                          : variant === "secondary"
                            ? "primaryInverted"
                            : "primary"
                      }
                      as={Link}
                      prefetch={false}
                      href="/login"
                      variant="outlined"
                      shape="circle"
                      size="small"
                    >
                      <UserIcon />
                    </IconButton>
                  )}
                </ModeProvider>
              )}

              <Button
                size="small"
                // color={isPrimaryStyle && mode !== "residential" ? "secondary" : "secondaryInverted"}
                color={
                  isPrimaryStyle && isCommercial
                    ? "secondary"
                    : isProfile
                      ? "secondary"
                      : "secondaryInverted"
                }
                // variant={auth.isAuthorized && mode === "residential" ? "filled" : "outlined"}
                variant={
                  auth.isAuthorized && !isCommercial && !isProfile
                    ? "filled"
                    : !isPrimaryStyle
                      ? "filled"
                      : "outlined"
                }
                className={classNames(
                  gridSprinkle({
                    display: {
                      mobile: "none",
                      tablet: "flex",
                    },
                  }),
                  {
                    [styles.bookButton]: isPrimaryStyle && auth.isAuthorized,
                    [styles.bookButtonInverted]: !isPrimaryStyle,
                    // [styles.whiteColor]: !isPrimaryStyle && !auth.isAuthorized
                  }
                )}
                onClick={() => landign.setBookingModalIsOpen(true)}
              >
                Book an Expert
              </Button>
            </div>
            <div
              className={gridSprinkle({
                type: "item",
                cols: {
                  mobile: 3,
                },
                justifySelf: "end",
                display: {
                  mobile: "flex",
                  tablet: "none",
                },
              })}
            >
              {auth.isAuthorized ? (
                <IconButton
                  as={Link}
                  prefetch={false}
                  href="/profile"
                  className={styles.avatar}
                  title="User Profile"
                  color="primaryInverted"
                  variant="filled"
                  shape="circle"
                  size="small"
                >
                  <Typography
                    className={styles.nameAbbr}
                    variant="buttonSmall"
                  >
                    {getInitials(auth.user?.full_name ?? "")}
                  </Typography>
                </IconButton>
              ) : (
                <IconButton
                  className={classNames(
                    styles.avatar,
                    styles.avatarNotLoggedIn,
                    {
                      [styles.avatarNotLoggedInAlternative]: !isPrimaryStyle,
                    }
                  )}
                  title="Login"
                  as={Link}
                  prefetch={false}
                  href="/login"
                  variant="outlined"
                  shape="circle"
                  size="small"
                >
                  <UserIcon />
                </IconButton>
              )}
              <button
                title="Menu"
                className={classNames(
                  styles.menuButton,
                  styles.linkWithIcon[variant]
                )}
                onClick={(event) => {
                  event.stopPropagation();
                  landign.setHeaderMenuIsOpen();
                }}
              >
                {landign.headerMenuIsOpen ? (
                  <MemoCloseIcon />
                ) : (
                  <MemoMenuIcon />
                )}
              </button>
            </div>
            <Accordion
              duration={400}
              className={gridSprinkle({ type: "item", cols: 10 })}
              isOpen={landign.headerMenuIsOpen}
            >
              <div
                className={classNames(
                  styles.menu,
                  gridSprinkle({
                    display: {
                      mobile: "flex",
                      tablet: "none",
                    },
                  })
                )}
              >
                <Container
                  className={styles.mobileMenuContainer[variant]}
                >
                  <nav
                    className={styles.menuItems}
                  >
                    {!!anchorLinks
                      ? anchorLinks.map(({ id, name }, idx) => {
                        return (
                          <Fragment
                            key={id}
                          >
                            {!!idx && (
                              <div
                                className={styles.mobileLinkDivider}
                              />
                            )}
                            <Typography
                              href={`#${id}`}
                              as={Link}
                              prefetch={false}
                              className={styles.mobileLink}
                            >
                              {name}
                            </Typography>
                          </Fragment>
                        );
                      })
                      : header.data.navigation_menu.map((link, index) => {
                        const isSubMenu =
                            "id" in link.relate_to && !!emergencyLocations;
                        return (
                          <Fragment
                            key={link.menu_item_name}
                          >
                            <Typography
                              as={isSubMenu ? "div" : PrismicLink}
                              field={link.menu_item_link}
                              className={styles.mobileLink}
                            >
                              {isSubMenu ? (
                                <PrismicLink
                                  className={styles.mobileDropdownLink}
                                  field={link.menu_item_link}
                                >
                                  {link.menu_item_name}
                                </PrismicLink>
                              ) : (
                                link.menu_item_name
                              )}
                              {/*{isSubMenu && (
                                <button
                                  type="button"
                                  onClick={(e) => {
                                    e.preventDefault();
                                    e.stopPropagation();
                                    setOpenedSubLinks(openedSubLinks === link.menu_item_name ? null : link.menu_item_name);
                                  }}
                                  className={styles.linkIndicatorWrapper}
                                >
                                  <div>
                                    <ChevronIcon
                                      className={styles.linkIndicator}
                                      turn={openedSubLinks === link.menu_item_name ? "top" : "bottom"}
                                    />  
                                  </div>
                                </button>
                              )}*/}
                            </Typography>
                            {/*{isSubMenu && (
                              <Accordion
                                isOpen={openedSubLinks === link.menu_item_name}
                              >
                                <div
                                  className={styles.menuItems}
                                >
                                  {emergencyLocations.map(({label,uuid}) => (
                                    <Typography
                                      key={label}
                                      variant="bodySmall"
                                      as={Link}
                                      prefetch={false}
                                      href={`emergencies/${uuid}`}
                                      className={styles.mobileSubLink}
                                    >
                                      {label}
                                    </Typography>
                                  )
                                  )}
                                </div>
                              </Accordion>
                            )}*/}
                            {index <
                                header.data.navigation_menu.filter(
                                  (link) => !link.parent_menu_item_name
                                ).length -
                                  1 && (
                              <div
                                className={styles.mobileLinkDivider}
                              />
                            )}
                          </Fragment>
                        );
                      })}
                  </nav>
                  <div
                    className={styles.mobileButtonWrapper}
                  >
                    <BookServiceButton
                      color={isPrimaryStyle ? "secondary" : "primary"}
                    />
                  </div>
                </Container>
              </div>
            </Accordion>
          </Container>
        </div>
      </header>
    );
  }
);

const Header: FC<HeaderProps> = (props) => {
  return (
    <Suspense>
      <HeaderElement
        {...props}
      />
    </Suspense>
  );
};

export default Header;
