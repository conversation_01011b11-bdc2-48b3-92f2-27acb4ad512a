
import { breakpoints } from "@/styles/constants.css";
import { mode, modeGlobal } from "@/styles/functions.css";
import { theme } from "@/styles/themes.css";
import { keyframes, style, styleVariants } from "@vanilla-extract/css";

export const rootBase = style({
  position: "fixed",
  top: 0,
  left: 0,
  right: 0,
  zIndex: 30,
});

const rootBasePrimary = style({
  color: theme.colors.primary.castletonGreen
});
const rootBaseSecondary = style({
  color: theme.colors.primary.ivory
});
export const root = styleVariants({
  primary: [rootBase, rootBasePrimary],
  secondary: [rootBase, rootBaseSecondary]
});


const firstPartBase = style({
  userSelect: "none",
  pointerEvents: "none",
  transitionProperty: "padding, background-color",
  transitionDuration: "200ms",
  paddingTop: 14,
});
const firstPartPrimary = style({
  backgroundColor: theme.colors.primary.ivory,

  selectors: {
    [mode("residential")]: {
      backgroundColor: theme.colors.primary.castletonGreen,
      color: theme.colors.primary.ivory
    }
  },
});
const firstPartSecondary = style({
  backgroundColor: theme.colors.primary.castletonGreen,
});

export const firstPart = styleVariants({
  primary: [firstPartBase, firstPartPrimary],
  secondary: [firstPartBase, firstPartSecondary],
});

export const logo = style({
  width: 84,

  "@media": {
    [breakpoints.tablet]: {
      width: 108,
    },
  },
});

export const container = style({
  alignItems: "center",
  pointerEvents: "visible",
  borderRadius: "0px !important",
  backgroundColor: "transparent !important"
});

const mainContentBase = style({
  padding: "8px 0",

  selectors: {
    [mode("residential")]: {
      backgroundColor: theme.colors.primary.castletonGreen,
      color: theme.colors.primary.ivory
    }
  },
});

const mainContentPrimary = style({
  backgroundColor: theme.colors.primary.ivory,
});
const mainContentSecondary = style({
  backgroundColor: theme.colors.primary.castletonGreen,
});

export const mainContent = styleVariants({
  primary: [mainContentBase, mainContentPrimary],
  secondary: [mainContentBase, mainContentSecondary],
});

export const dividerContainer = style({
  background: "transparent",
});

export const navigation = style({
  display: "flex",
  gap: 12,
});

export const menuButton = style({
  color: "inherit",
  fontSize: 26,
  alignItems: "center",
  justifyContent: "center",
  border: "none",
  backgroundColor: "transparent",
  width: 42,
  aspectRatio: "1 / 1",
  padding: "5px !important",
});

export const menu = style({
  flex: "1 1",
  pointerEvents: "visible",
  overflowY: "auto"
});

export const menuItems = style({
  display: "flex",
  flexDirection: "column",
});

const mobileMenuContainerBase = style({
  display: "flex",
  flexDirection: "column",
  height: "calc(100dvh - 120px)",
  overflowY: "auto",
  padding: "40px 20px",
  
});

const mobileMenuContainerPrimary = style({

});

const mobileMenuContainerSecondary = style({
  backgroundColor: theme.colors.primary.castletonGreen
});

export const mobileMenuContainer = styleVariants({
  primary: [mobileMenuContainerBase,mobileMenuContainerPrimary],
  secondary: [mobileMenuContainerBase,mobileMenuContainerSecondary],
});

const linkWithIconBase = style({
  display: "inline-flex",
  alignItems: "center",
  whiteSpace: "nowrap",
  gap: 4,
  paddingTop: 6,
  paddingBottom: 2,
  paddingLeft: 10,
  paddingRight: 10,
  cursor: "pointer",
  borderRadius: 8,
  transition: "background-color 50ms, transform 350ms",

  ":active": {
    backgroundColor: theme.colors.primary.softWhite,  
    color: theme.colors.primary.castletonGreenPressed,
    transform: "scale(0.9)",
  },

 
});

export const hideOnMobile = style({
  display: "none",
  "@media": {
    [breakpoints.tablet]: {
      display: "inline-flex"
    }
  }
});

export const hideOnTablet = style({
  display: "inline-flex",
  "@media": {
    [breakpoints.tablet]: {
      display: "none"
    }
  }
});

const linkWithIconPrimary = style({
  "@media": {
    [breakpoints.tablet]: {
      ":hover": {
        backgroundColor: theme.colors.primary.softWhite,
      },
      ":active": {
        backgroundColor: theme.colors.primary.softWhitePressed,  
      },
    }
  },

  selectors: {
    [`${modeGlobal("residential")} &:hover`]: {
      color: theme.colors.primary.castletonGreenPressed,
    },
    [`${modeGlobal("residential")} &:active`]: {
      color: theme.colors.primary.castletonGreenPressed,
    }
  }
});
const linkWithIconSecondary = style({
  "@media": {
    [breakpoints.tablet]: {
      ":hover": {
        backgroundColor: theme.colors.primary.softWhite20,
      },
      ":active": {
        backgroundColor: theme.colors.primary.softWhitePressed,  
      },
    }
  }
});

export const linkWithIcon = styleVariants({
  primary: [linkWithIconBase, linkWithIconPrimary],
  secondary: [linkWithIconBase, linkWithIconSecondary],
});

export const linkWithIconActive = style({
  backgroundColor: theme.colors.primary.softWhite,

  selectors: {
    [mode("residential")]: {
      color: theme.colors.primary.castletonGreenPressed,
    }
  },
});

export const linkIcon = style({
  // transform: "rotate(180deg)",
  display: "flex",
  paddingBottom: 2
});

export const mobileLink = style({
  display: "flex",
  justifyContent: "space-between",
  gap: 4,
  fontWeight: "500 !important",
  fontSize: "20px !important",
  margin: "4px 0",

  selectors: {
    [mode("residential")]: {
      color: theme.colors.primary.castletonGreen,
    }
  },
});

export const mobileDropdownLink = style({
  textDecoration: "none",
  
});

export const mobileSubLink = style({
  padding: "8px 16px",
  color: theme.colors.primary.castletonGreen,
  selectors: {
    "&:first-of-type": {
      marginTop: 8,
    },
    "&:last-of-type": {
      marginBottom: 8,
    },
  },
});

export const linkIndicator = style({
  transitionProperty: "transform",
  transitionDuration: "100ms",
  fontSize: 20,
});

// export const linkIndicator = styleVariants({
//   isActive: [
//     linkIndicatorBase,
//   ],
//   isInactive: [linkIndicatorBase, {
//     transform: "rotateZ(180deg)",
//   }],
// });

export const linkIndicatorWrapper  =style({
  flex: 1,
  border: 0,
  background: "transparent",
  padding: 0,
  display: "flex",
  justifyContent: "flex-end",
  alignItems: "center",
  color: theme.colors.primary.castletonGreen,
});

export const mobileLinkDivider = style({
  height: "1px",
  backgroundColor: theme.colors.grayscale[100],
  margin: "12px 0",
});

const dividerBase = style({
  height: "1px",
  backgroundColor: theme.colors.grayscale[100],
  margin: "14px 0 7px",

  selectors: {
    [mode("residential")]: {
      backgroundColor: theme.colors.grayscale[200],
      opacity: .3,
    }
  },
});

export const divider = styleVariants({
  primary: [dividerBase],
  secondary: [dividerBase, {opacity: .3}]
});

export const modeSwitcher = style({
  display: "flex",
  alignItems: "center",
  gap: 8,
});

export const pulse = keyframes({
  "0%": {
    transform: "scale(1.5)",
  },
  "50%": {
    transform: "scale(1)",  
  },
  "75%": {
    transform: "scale(1.5)",
  },
  "100%": {
    transform: "scale(1.5)",
  },
});

export const pulseWithOpacity = keyframes({
  "0%": {
    opacity: 0,
    transform: "scale(2)",
  },
  "50%": {
    opacity: 1,
    transform: "scale(0)",  
  },
  "75%": {
    opacity: 0,
    transform: "scale(2)",
  },
  "100%": {
    opacity: 0,
    transform: "scale(2)",
  },
});

export const phoneIcon = style({
  selectors: {
    [`${linkWithIconBase}:not(:hover) &`]: {
      animation: `${pulse} 2s ease-in-out infinite`,    
    }
  }
});

export const phoneIconWrapper = style({
  width: 16,
  height: 16,
  position: "relative",
  display: "inline-flex",
  alignItems: "center",
  justifyContent: "center",
  marginRight: 4,

  selectors: {
    [`${linkWithIconBase}:not(:hover) &:before`]: {
      width: 16,
      height: 16,
      content: "",
      display: "block",
      position: "absolute",
      borderRadius: "50%",
      backgroundColor: theme.colors.primary.asidGreen,
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      margin: "auto",
      animation: `${pulseWithOpacity} 2s ease-in-out infinite`,
    }
  },
});

export const avatar = style({
  marginRight: 16,
});

export const avatarNotLoggedIn = style({
  selectors: {
    [mode("residential")]: {
      color: theme.colors.primary.ivory,
      borderColor: theme.colors.primary.ivory,
    }
  },
});

export const avatarNotLoggedInAlternative = style({
  color: theme.colors.primary.ivory,
  borderColor: theme.colors.primary.ivory,
  borderWidth: 1,
  borderStyle: "solid",
});

export const avatarOpenDarkMode = style({
  selectors: {
    [mode("residential")]: {
      color: theme.colors.primary.ivory,
      borderColor: theme.colors.primary.ivory,
    }
  },
});

export const actions = style({
  gap: 16,
});

export const menuLink = style({
  display: "grid",
  
  

  ":hover": {
    cursor: "pointer",
    fontWeight: "500 !important",
  },
  ":focus": {
    outline: 0
  }
});

export const userName = style({
  marginBottom: 8,
  fontWeight: "500 !important",
});

export const bookButton = style({
  selectors: {
    [mode("residential")]: {
      backgroundColor: theme.colors.primary.ivory,
      color: theme.colors.primary.castletonGreen,
    },
    [mode("residential") + ":hover"]: {
      backgroundColor: theme.colors.primary.asidGreen,
      color: theme.colors.primary.castletonGreen,
    }
  },
});

export const bookButtonInverted = style({
  color: `${theme.colors.primary.castletonGreen} !important`,
});

export const whiteColor = style({
  color: theme.colors.primary.ivory,
});

export const logoLink = style({
  selectors: {
    [`${modeGlobal("residential")} &:hover`]: {
      backgroundColor: theme.colors.primary.castletonGreen,
      color: theme.colors.primary.ivory,
    }
  },  
});


export const callUsButton = style({
  backgroundColor: "transparent",
  color: "inherit",
  border: 0,
  padding: 0,
  margin: 0
});


export const subItemLinkWrapper = style({
  display: "grid"
});

export const modeSwitcherDarkMode = style({
  selectors: {
    [mode("residential")]: {
      boxShadow: "none !important",
      background: "rgba(255, 253, 248, 0.16)",
    }
  },
});


export const mobileButtonWrapper = style({
  display: "grid",
  marginTop: "auto",
  paddingTop: 16,
});

export const desktopPhoneContainer = style({
  paddingTop: 4,
  paddingBottom: 4,
});


export const nameAbbr = style({
  paddingTop: 3
});