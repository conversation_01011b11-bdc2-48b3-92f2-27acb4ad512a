
"use client";

import { FC, useEffect, useMemo, useState } from "react";

import * as styles from "./BillingInfoPage.css";
import Loader from "../Loader";
import Typography from "../Typography";
import { redirect, RedirectType } from "next/navigation";
import { createClient } from "@/utils/supabase/client";
import { useRouter } from "next/navigation";

type BillingInfoPageP = {};

const supabase = createClient();

const BillingInfoPage:FC<BillingInfoPageP> = ({}) => {


  const router = useRouter();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(false);



  useEffect(() => {

    (async function getPortalLink() {
      try {
        const response  = await supabase.functions.invoke<{ redirectUrl: string}>("stripe/customers/portal", {method: "GET"});
        if(!response.data || !!response.error) {
          throw response?.error || "Error";
        }
        router.replace(response.data.redirectUrl);
          
      } catch (e) {
        setError(true);
      }
      setLoading(false);

    })();
  }, []);

  const titleText = useMemo(() => {

    if(loading) {
      return "Loading billing info...";
    }
    if(error) {
      return "Unexpected error occured";
    }
    return "Done!";

  } , [loading, error]);
    
  
  return (<div
    className={styles.container}
  >
    <Typography
      fontFamily="primary"
      as={"h3"}
      variant="h3"
    >{titleText}</Typography>
    {loading && <Loader/>}
  </div>
  );
};

export default BillingInfoPage;
