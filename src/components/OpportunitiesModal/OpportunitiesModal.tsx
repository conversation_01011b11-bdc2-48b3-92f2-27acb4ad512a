"use client";
import { FC, useCallback, useState } from "react";
import { useF<PERSON>, Controller } from "react-hook-form";
import { PatternFormat } from "react-number-format";

import Modal from "../Modal";
import * as styles from "./OpportunitiesModal.css";
import RadioButton from "../RadioButton";
import TextInput from "../TextInput";
import DropdownInput from "../DropdownInput";
import WrapperWithLabel from "../WrapperWithLabel";
import DatePicker from "../DatePicker";
import Typography from "../Typography";
import Button from "../Button";
import { Option } from "../DropdownInput/DropdownInput.types";
import { createClient } from "@/utils/supabase/client";
import {
  AREA_OF_WORK_OPTIONS,
  FULL_NAME_CONTROLLER_RULES,
  FULL_NAME_INPUT_PROPS,
  PHONE_NUMBER_CONTROLLER_RULES,
  PHONE_NUMBER_INPUT_PROPS,
  POST_CODE_CONTROLLER_RULES,
  POST_CODE_INPUT_PROPS,
  QUALIFICATIONS,
  TYPE_OF_EMPLOYMENT_OPTIONS,
  WORK_ONLY_VARIANTS,
  YES_NO_VARIANTS,
} from "@/utils/constants";
import { onlyFloatNumInStr, onlyNumInStr } from "@/utils/helpers";
import { numberValidator } from "@/utils/validators";
import { observer } from "mobx-react-lite";
import useStore from "@/hooks/useStore";
import Props from "./OpportunitiesModal.types";

type FormValuesType = {
  Name: string;
  mobileNumber: string;
  postcode: string;
  travelDistance: string;
  employmentType: Option;
  propertyType: string | undefined;
  isGasSafe: string | undefined;
  experienceYears: string;
  areaOfWork: Option;
  startDate: Date | null;
  qualifications: Option;
};

const defaultValues: FormValuesType = {
  Name: "",
  mobileNumber: "",
  postcode: "",
  travelDistance: "",
  employmentType: null,
  propertyType: undefined,
  isGasSafe: undefined,
  experienceYears: "",
  areaOfWork: null,
  startDate: null,
  qualifications: null,
};
const OpportunitiesModal: FC<Props> = observer(
  ({ isOpen = false, onClose }) => {
    const { landign } = useStore();

    const [successModal, setSuccessModal] = useState(false);

    const form = useForm<FormValuesType>({
      mode: "onBlur",
      defaultValues,
    });

    const onSubmit = useCallback(async (payload: FormValuesType) => {
      try {
        const body = {
          ...payload,
          mobile: payload.mobileNumber.replaceAll(" ", ""),
          experienceYears: +payload.experienceYears.replaceAll(",", "."),
          travelDistance: +payload.travelDistance.replaceAll(",", "."),
          employmentType: payload.employmentType?.value,
          qualifications: payload.qualifications?.value,
          areaOfWork: payload.areaOfWork?.value,
          isGasSafe: payload.isGasSafe === "Yes",
        };

        // TODO: Move api url to enum
        /* const { data, error } =  */
        const supabase = createClient();

        await supabase.functions.invoke("zoho_api/careers", {
          method: "POST",
          body,
        });
        form.reset();
        setSuccessModal(true);
      } catch (e) {}
    }, []);

    const handleClose = useCallback(() => {
      const removeAnchor = () => {
        const { pathname, search } = window.location;
        const urlWithoutAnchor = pathname + search;
        window.history.replaceState({}, document.title, urlWithoutAnchor);
      };

      removeAnchor();

      onClose?.();
      setSuccessModal(false);
    }, []);

    return (
      <>
        {!isOpen && (
          <div
            aria-hidden
            tabIndex={0}
            id="opportunities-form"
            onFocus={() => landign.setOpportunitiesModalIsOpen(true)}
          />
        )}
        <Modal
          stickToBottom={successModal}
          open={isOpen}
          fullWidth={!successModal}
          onClose={handleClose}
        >
          {successModal ? (
            <div className={styles.successContent}>
              <Typography fontFamily="primary" variant="h4">
                Success!
              </Typography>
              <Typography variant="bodyMedium">
                We have received your request and a member of the team will
                contact you as soon as possible.
              </Typography>
              <Button onClick={handleClose}>Ok</Button>
            </div>
          ) : (
            <form
              onSubmit={form.handleSubmit(onSubmit)}
              className={styles.content}
            >
              <Typography
                className={styles.title}
                fontFamily="primary"
                variant="h4"
              >
                Careers
              </Typography>
              <Controller
                name="Name"
                rules={{
                  required: "Required!",
                  ...FULL_NAME_CONTROLLER_RULES,
                }}
                control={form.control}
                render={({ field, fieldState }) => (
                  <TextInput
                    {...FULL_NAME_INPUT_PROPS}
                    {...field}
                    error={fieldState.error?.message}
                  />
                )}
              />

              <div className={styles.twoCol}>
                <Controller
                  name="mobileNumber"
                  rules={{
                    required: "Required!",
                    ...PHONE_NUMBER_CONTROLLER_RULES,
                  }}
                  control={form.control}
                  render={({ field, fieldState }) => (
                    <PatternFormat
                      {...PHONE_NUMBER_INPUT_PROPS}
                      inputMode="tel"
                      error={fieldState.error?.message}
                      {...field}
                      customInput={TextInput}
                    />
                  )}
                />
                <Controller
                  name="postcode"
                  control={form.control}
                  rules={{
                    required: "Required!",
                    ...POST_CODE_CONTROLLER_RULES,
                  }}
                  render={({ field, fieldState }) => (
                    <TextInput
                      {...POST_CODE_INPUT_PROPS}
                      error={fieldState.error?.message}
                      {...field}
                      maxLength={7}
                    />
                  )}
                />
              </div>
              <Controller
                name="travelDistance"
                rules={{
                  required: "Required!",
                  validate: numberValidator,
                }}
                control={form.control}
                render={({ field: { onChange, ...field }, fieldState }) => (
                  <TextInput
                    inputMode="decimal"
                    error={fieldState.error?.message}
                    {...field}
                    onChange={(e) => {
                      const { value } = e.target;
                      if (onlyNumInStr(value) && value.length < 10) {
                        onChange(value);
                      }
                    }}
                    label="How far outside of this postcode are you willing to travel for a job?"
                    description="Miles taken to get to the job, not inclusive of return journey"
                    placeholder="Enter the distance in miles"
                  />
                )}
              />

              <Controller
                name="employmentType"
                rules={{
                  required: "Required!",
                }}
                control={form.control}
                render={({ field, fieldState }) => (
                  <DropdownInput
                    {...field}
                    error={fieldState.error?.message}
                    options={TYPE_OF_EMPLOYMENT_OPTIONS}
                    label="What type of employment best suits you?"
                    placeholder={"Select from list"}
                  />
                )}
              />

              <Controller
                name="propertyType"
                control={form.control}
                rules={{
                  required: "Required!",
                }}
                render={({ field, fieldState }) => (
                  <WrapperWithLabel
                    className={styles.radioButtonsWrapper}
                    error={fieldState.error?.message}
                    label="Are you able to work on only Residential, Commercial or both?"
                  >
                    {WORK_ONLY_VARIANTS.map((label) => (
                      <RadioButton
                        key={label}
                        {...field}
                        value={label}
                        checked={field.value === label}
                      >
                        {label}
                      </RadioButton>
                    ))}
                  </WrapperWithLabel>
                )}
              />
              <Controller
                name="isGasSafe"
                control={form.control}
                rules={{
                  required: "Required!",
                }}
                render={({ field, fieldState }) => (
                  <WrapperWithLabel
                    className={styles.radioButtonsWrapper}
                    label="Are you registered with Gas Safe?"
                    error={fieldState.error?.message}
                  >
                    {YES_NO_VARIANTS.map((label) => (
                      <RadioButton
                        key={label}
                        {...field}
                        value={label}
                        checked={field.value === label}
                      >
                        {label}
                      </RadioButton>
                    ))}
                  </WrapperWithLabel>
                )}
              />
              <Controller
                name="experienceYears"
                control={form.control}
                rules={{
                  required: "Required!",
                }}
                render={({ field: { onChange, ...field }, fieldState }) => (
                  <TextInput
                    {...field}
                    inputMode="decimal"
                    onChange={(e) => {
                      const { value } = e.target;
                      if (onlyFloatNumInStr(value) && value.length < 10) {
                        onChange(value);
                      }
                    }}
                    error={fieldState.error?.message}
                    label="How many years experience do you have working in the industry?"
                    placeholder="Enter the number of years"
                  />
                )}
              />
              <Controller
                name="areaOfWork"
                control={form.control}
                rules={{
                  required: "Required!",
                }}
                render={({ field, fieldState }) => (
                  <DropdownInput
                    {...field}
                    error={fieldState.error?.message}
                    options={AREA_OF_WORK_OPTIONS}
                    placeholder="Select from list"
                    label="Which area of work do you believe you are strongest in?"
                  />
                )}
              />
              <Controller
                name="qualifications"
                control={form.control}
                rules={{
                  required: "Required!",
                }}
                render={({ field, fieldState }) => (
                  <DropdownInput
                    {...field}
                    error={fieldState.error?.message}
                    options={QUALIFICATIONS}
                    placeholder="Select from list"
                    label="Qualification"
                  />
                )}
              />
              <Controller
                name="startDate"
                control={form.control}
                rules={{
                  required: "Required!",
                }}
                render={({ field, fieldState }) => (
                  <DatePicker
                    {...field}
                    error={fieldState.error?.message}
                    minDate={new Date()}
                    label="If we think we would be a good match, how soon can you start?"
                  />
                )}
              />
              <div className={styles.buttonWrapper}>
                <Button
                  type="submit"
                  disabled={!form.formState.isValid || form.formState.isLoading}
                  isAnimated
                >
                  Submit
                </Button>
              </div>
            </form>
          )}
        </Modal>
      </>
    );
  }
);

export default OpportunitiesModal;
