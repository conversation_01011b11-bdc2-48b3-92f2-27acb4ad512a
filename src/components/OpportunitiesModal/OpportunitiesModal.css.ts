import { breakpoints } from "@/styles/constants.css";
import { style } from "@vanilla-extract/css";

export const content = style(
  {
    display: "grid",
    rowGap: 24,
  }
);

export const twoCol = style(
  {
    display: "grid",
    gridAutoFlow: "row",
    gap: 24,
    "@media": {
      [breakpoints.tablet]: {
        gridAutoFlow: "column",
      },
    },
  }
);

export const radioButtonsWrapper = style(
  {
    display: "flex",
    flexWrap: "wrap",
    gap: 14,
  }
);

export const title = style(
  {
    marginBottom: 16,
  }
);

export const button = style(
  {
    width: "100%",

    "@media": {
      [breakpoints.tablet]: {
        width: "max-content",
      },
    },
  }
);

export const successContent = style(
  {
    display: "grid",
    rowGap: 40,
    maxWidth: 432,
    boxSizing: "border-box",
  }
);

export const buttonWrapper = style(
  {
    display: "contents",
    "@media": {
      [breakpoints.tablet]: {
        display: "block",
      },
    },
  }
);
