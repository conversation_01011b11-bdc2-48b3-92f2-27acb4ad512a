import { breakpoints } from "@/styles/constants.css";
import { theme } from "@/styles/themes.css";
import { style } from "@vanilla-extract/css";



export const container = style({
  color: theme.colors.primary.castletonGreen,
  display: "grid",
  rowGap: 26,
  // "@media": {
  //   [breakpoints.tablet]: {
  //     width: 610,
  //   }
  // }

});

export const balanceCardList = style({
  display: "grid",
  rowGap: 20,
  marginTop: 16
});

export const balanceCard = style({
  padding: 24,
  background: theme.colors.primary.ivory,
  display: "grid",
  gridTemplateColumns: "1fr auto",

  columnGap: 16,
  borderRadius: 16,
  alignItems: "center",
  "@media": {
    [breakpoints.tablet]: {
      gridTemplateColumns: "2fr 1fr 1fr",
    }
  },
}

);

export const expectedBalance = style({
  display: "flex",
  alignItems: "center",
  columnGap: 12
});

export const status = style({
  border: `1px solid ${theme.colors.grayscale[100]}`,
  borderRadius: 24,
  padding: "12px 24px",
  width: "fit-content",
  justifySelf: "end",
  gridRow: "-1 / 3",
  gridColumn: 2,
  alignSelf: "center",
  gridTemplateColumns: "1fr auto",
  rowGap: 2,
  "@media": {
    [breakpoints.tablet]: {
      gridRow: "auto",
      gridColumn: "auto",
     
    }
  },
});