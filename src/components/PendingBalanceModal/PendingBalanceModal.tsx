"use client";

import { formatNumToGBP } from "@/utils/helpers";
import Modal from "../Modal";
import Typography from "../Typography";
import * as styles from "./PendingBalanceModal.css";
import PendingBalanceModalProps from "./PendingBalanceModal.types";
import { useMemo } from "react";

const PendingBalanceModal = ({
  pendingBalanceTransactions,
  ...modalProps
}: PendingBalanceModalProps) => {
  const pendingBalanceSum = useMemo(() => {
    return pendingBalanceTransactions.reduce((accum, transaction) => {
      accum += transaction.amount;

      return accum;
    }, 0);
  }, [pendingBalanceTransactions]);

  return (
    <Modal
      {...modalProps}
      fullWidth
    >
      <div
        className={styles.container}
      >
        <Typography
          variant="h4"
          fontFamily="primary"
        >
          Pending <b>balance</b>
        </Typography>
        <div
          className={styles.expectedBalance}
        >
          <Typography
            variant="bodyMedium"
          >
            Expected
          </Typography>
          <Typography
            variant="subTitleMedium"
          >
            {formatNumToGBP(pendingBalanceSum / 100)}
          </Typography>
        </div>
        <div
          className={styles.balanceCardList}
        >
          {pendingBalanceTransactions.map((transaction) => {
            return (
              <div
                key={transaction.id}
                className={styles.balanceCard}
              >
              
                <Typography
                  variant="bodyMedium"
                >
                  {transaction.from}
                </Typography>
                <Typography
                  variant="bodyMedium"
                >
                  {formatNumToGBP(transaction.amount / 100)}
                </Typography>
            
                <Typography
                  className={styles.status}
                  variant="note"
                >
                  Awaiting
                </Typography>
              </div>
            );
          })}
        </div>
      </div>
    </Modal>
  );
};

export default PendingBalanceModal;
