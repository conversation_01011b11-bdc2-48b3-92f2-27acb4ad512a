import { theme } from "@/styles/themes.css";
import { style, styleVariants } from "@vanilla-extract/css";

const radioButtonBase = style({
  borderRadius: 8,
  border: `1px solid ${theme.colors.primary.castletonGreen}`,
  background: theme.colors.primary.softWhite,
  display: "flex",
  width: "max-content",
});

export const radioButton = styleVariants({
  base: [radioButtonBase],
  checked: {
    background: theme.colors.primary.castletonGreen,
  },
});

export const radio = style({
  padding: "14px 18px",
});
