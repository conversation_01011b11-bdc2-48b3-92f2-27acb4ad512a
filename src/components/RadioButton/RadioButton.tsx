"use client";
import { forwardRef } from "react";
import * as styles from "./RadioButton.css";
import classNames from "classnames";
import RadioButtonProps from "./RadioButton.types";
import Radio from "../Radio";

const RadioButton = forwardRef<HTMLInputElement, RadioButtonProps>(
  (
    props, ref
  ) => {
    return (
      <div
        className={classNames(
          styles.radioButton.base, {
            [styles.radioButton.checked]: props.checked,
          }
        )}
      >
        <Radio
          {...props}
          ref={ref}
          className={styles.radio}
        />
      </div>
    );
  }
);

export default RadioButton;
