"use client";

import { useSearchParams } from "next/navigation";
import { FC } from "react";
import ModeProviderProps from "./ModeProvider.types";

const ModeProvider: FC<ModeProviderProps> = ({ children }) => {
  const searchParams = useSearchParams();

 const params = new URLSearchParams(
    (searchParams as URLSearchParams | null) ?? {}
 );

  const currentMode = params.get("mode") || "residential";

  return <>{children(currentMode as "residential" | "commercial")}</>;
};

export default ModeProvider;
