"use client";

import * as styles from "./NotFoundComponent.css";
import NotFoundImage from "./not-found-preview.png";
import NotFoundImageDarkTheme from "./not-found-preview-dt.png";

import Typography from "@/components/Typography";
import Container from "@/components/Container";
import { gridSprinkle } from "@/styles/sprinkles.css";
import Button from "@/components/Button";
import Link from "next/link";
import Image from "next/image";
import classNames from "classnames";
import ModeProvider from "@/components/ModeProvider";

const NotFoundComponent = () => {
  return (
    <Container
      className={styles.container}
    >
      <div
        className={classNames(styles.block, gridSprinkle({ type: "grid" }))}
      >
        <div
          className={classNames(styles.content, gridSprinkle({ type: "item", cols: { mobile: 10, tablet: 5 } }))}
        >
          <Typography
            className={styles.title}
            variant="h1"
          >
            <b>Page</b> not <br/>
          found.
          </Typography>
          <Typography
            className={styles.subtitle}
          >
          Oops, this page was not found. Error 404. Try again.
          </Typography>
          <Button
            as={Link}
            href="/"
            isAnimated
          >
              Go to homepage
          </Button>
        </div>
        <div
          className={classNames(styles.previewWrapper, gridSprinkle({ type: "item", cols: { mobile: 10, tablet: 5 } }))}
        >
          <ModeProvider>
            {(mode) => (
              <Image
                src={mode === "residential" ? NotFoundImageDarkTheme.src : NotFoundImage.src}
                alt="preview"
                fill
                objectFit="contain"
                objectPosition="bottom center"
              />

            )}
          </ModeProvider>
        </div>
      </div>
    </Container>
  );
};

export default NotFoundComponent;