import { breakpoints } from "@/styles/constants.css";
import { mode } from "@/styles/functions.css";
import { theme } from "@/styles/themes.css";
import { style } from "@vanilla-extract/css";

export const root = style({
  paddingTop: 150,
  display: "flex",
  flexDirection: "column",
  minHeight: "100vh",

  selectors: {
    [mode("residential")]: {
      backgroundColor: theme.colors.primary.castletonGreen,
    }
  },
});

export const container = style({
  selectors: {
    [mode("residential")]: {
      backgroundColor: theme.colors.primary.castletonGreen,
    }
  },
});

export const block = style({
  margin: "auto 0",
  padding: "40px 20px",

  "@media": {
    [breakpoints.tablet]: {
      minHeight: "85vh",
      padding: "0 0 20px",
    }
  }
});

export const main = style({
  display: "flex",
  flexDirection: "column",
  flex: "1 1",
  gap: 10,

  "@media": {
    [breakpoints.tablet]: {
      gap: 20,
    }
  }
});

export const content = style({
  display: "flex",
  flexDirection: "column",

  "@media": {
    [breakpoints.tablet]: {
      padding: "80px 0 52px",
      alignItems: "flex-start",
    }
  }
});

export const title = style({
  fontFamily: theme.fonts.primary,
  marginBottom: 24,

  "@media": {
    [breakpoints.tablet]: {
      marginBottom: 40,
    },
  },

  selectors: {
    [mode("residential")]: {
      color: theme.colors.primary.softWhite,
    }
  },
});

export const subtitle = style({
  marginBottom: 32,
  maxWidth: 286,
  fontWeight: "500 !important",

  "@media": {
    [breakpoints.tablet]: {
      marginBottom: "auto",
    },
  },

  selectors: {
    [mode("residential")]: {
      color: theme.colors.primary.softWhite,
    }
  },
});

export const previewWrapper = style({
  marginTop: 32,
  borderRadius: 16,
  position: "relative",
  aspectRatio: "1 / 1",
  backgroundColor: theme.colors.primary.castletonGreen,
  overflow: "hidden",

  "@media": {
    [breakpoints.tablet]: {
      aspectRatio: "auto",
      borderRadius: 24,
      marginTop: 0,
      height: "100%",
    },
  },

  selectors: {
    [mode("residential")]: {
      backgroundColor: theme.colors.primary.ivory,
    }
  },
});