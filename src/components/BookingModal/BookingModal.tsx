"use client";

// import supabase from "@/config/supabase/client";
import { createClient } from "@/utils/supabase/client";
import { gridSprinkle } from "@/styles/sprinkles.css";
import classNames from "classnames";
import { FC, useEffect, useState } from "react";
import { Controller, useForm } from "react-hook-form";
import { PatternFormat } from "react-number-format";
import Button from "../Button";
import CheckButton from "../CheckButton";
import Modal from "../Modal";
import RadioButton from "../RadioButton";
import TextInput from "../TextInput";
import Typography from "../Typography";
import WrapperWithLabel from "../WrapperWithLabel";
import * as styles from "./BookingModal.css";
import BookingModalProps from "./BookingModal.types";
import Link from "next/link";
import { observer } from "mobx-react-lite";
import useStore from "@/hooks/useStore";
import IconButton from "../IconButton";
import Tooltip from "../Tooltip";
import InfoIcon from "@/assets/icons/InfoIcon";
import {
  EMAIL_CONTROLLER_RULES,
  EMAIL_INPUT_PROPS,
  FULL_NAME_CONTROLLER_RULES,
  FULL_NAME_INPUT_PROPS,
  PHONE_NUMBER_CONTROLLER_RULES,
  PHONE_NUMBER_INPUT_PROPS,
  POST_CODE_CONTROLLER_RULES,
  POST_CODE_INPUT_PROPS,
  WHATS_UP_NUMBER,
} from "@/utils/constants";
import MemoWhatsAppIcon from "@/assets/icons/WhatsAppIcon";
import MemoPhoneIcon from "@/assets/icons/PhoneIcon";

const URGENT_VARIANTS = ["Urgent", "Non-urgent"];

const RESPOND_VARIANTS = {
  callMe: "Call me",
  whatsAppMe: "WhatsApp me",
  textMe: "Text me",
  emailMe: "Email me",
};

// TODO:
// Add these service variants for specific
// Refferal Services bookings

const SERVICE_VARIANTS = {
  newBoilerInstallation: "New boiler installation",
  centralHeatingPowerflush: "Central heating powerflush",
  boilerService: "Boiler service",
  centralHeatingControlUpgrade: "Central heating control upgrade",
} as const;

interface FormValues {
  Name: string;
  phone: string;
  Email: string;
  post_code: string;
  urgent: boolean;
  respond_types: string[];
  message?: string; // Message is optional
  ref_id?: string;
  service?: string; // SERVICE_VARIANTS
}

const BookingModal: FC<BookingModalProps> = observer(({ isOpen, onClose }) => {
  const { auth, landign } = useStore();

  const [successModal, setSuccessModal] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const form = useForm<FormValues>({
    mode: "onBlur",
    defaultValues: {
      urgent: true,
      Name: "",
      Email: "",
      phone: "",
      post_code: "",
      message: "",
      respond_types: [],
      ref_id: "",
      service: "New boiler installation",
    },
  });

  const onSubmit = async (body: FormValues) => {
    try {
      const supabase = createClient();

      if (body.ref_id === "") delete body.ref_id;

      const response = await supabase.functions.invoke("zoho_api/bookings", {
        method: "POST",
        body,
      });

      if (response.data.error) {
        throw response.data.error.message;
      }

      setSuccessModal(true);

      form.reset();
    } catch (error) {
      // console.log("Catch: ");
      // console.log(error);
      setError(String(error));
    }
  };

  const handleClose = () => {
    const removeAnchor = () => {
      const { pathname, search } = window.location;
      const urlWithoutAnchor = pathname + search;
      window.history.replaceState({}, document.title, urlWithoutAnchor);
    };
    removeAnchor();
    onClose();
  };

  useEffect(() => {
    if (!auth.isAuthorized || !auth.user) return;

    form.reset({
      urgent: true,
      Name: auth.user.full_name,
      Email: auth.user.email,
      phone: auth.user.mobile_number,
      post_code: auth.user.post_code,
      message: "",
      respond_types: [],
      ref_id: auth.user.ref_by?.ref_id ?? "",
      service: "New boiler installation",
    });
  }, [auth.user, auth.isAuthorized]);

  return (
    <>
      {!isOpen && (
        <div
          aria-hidden
          tabIndex={0}
          id="booking-form"
          onFocus={(event) => {
            landign.setBookingModalIsOpen(true);
          }}
        />
      )}
      <Modal open={isOpen} onClose={handleClose}>
        {error && (
          <div className={styles.errorContent}>
            <Typography
              fontFamily="primary"
              variant="h4"
              className={styles.errorContentTitle}
            >
              Error!
            </Typography>
            <Typography variant="bodyMedium">{error}</Typography>
            <Button
              onClick={() => {
                setError(null);
                handleClose();
              }}
            >
              Ok
            </Button>
          </div>
        )}
        {successModal && (
          <div className={styles.successContent}>
            <Typography fontFamily="primary" variant="h4">
              Success!
            </Typography>
            <Typography variant="bodyMedium">
              We have received your request and a member of the team will
              contact you as soon as possible.
            </Typography>
            <Button
              onClick={() => {
                setSuccessModal(false);
                handleClose();
              }}
            >
              Ok
            </Button>
          </div>
        )}
        {!successModal && !error && (
          <form
            className={classNames(
              styles.content,
              gridSprinkle({ type: "grid" })
            )}
          >
            <div className={gridSprinkle({ type: "item", cols: 10 })}>
              <Typography
                className={styles.title}
                variant="h4"
                fontFamily="primary"
              >
                Book an <b><i>Expert</i></b>
              </Typography>
            </div>
            <div
              className={classNames(
                styles.contactBlock,
                gridSprinkle({ type: "item", cols: 10 })
              )}
            >
              <Typography className={styles.contactBlockTitle}>
                Response times will be faster via Whatsapp or Call us to speak
                to us directly
              </Typography>
              <div className={styles.contactBlockButtonWrapper}>
                <IconButton
                  shape="circle"
                  size="small"
                  onClick={() => {
                    const zohoChatButtonId =
                      document.getElementById("zsiq_agtpic");

                    if (!zohoChatButtonId) return;

                    zohoChatButtonId.click();
                  }}
                  type="button"
                >
                  <em
                    className={classNames(
                      styles.chatIcon,
                      "zsiq_user siqicon siqico-chat"
                    )}
                  />
                </IconButton>
                <Typography className={styles.contactBlockButtonLabel}>
                  Live Chat
                </Typography>
              </div>
              <div className={styles.contactBlockButtonWrapper}>
                <IconButton
                  className={styles.iconButton}
                  target="_blank"
                  shape="circle"
                  as={Link}
                  href={`https://wa.me/${WHATS_UP_NUMBER}`}
                  size="small"
                >
                  <MemoWhatsAppIcon />
                </IconButton>
                <Typography className={styles.contactBlockButtonLabel}>
                  Whatsapp Us
                </Typography>
              </div>
              <div className={styles.contactBlockButtonWrapper}>
                <Tooltip
                  on={["hover", "focus"]}
                  position="bottom center"
                  offsetY={6}
                  trigger={
                    <div>
                      <IconButton
                        className={styles.iconButton}
                        type="button"
                        shape="circle"
                        href="tel:08000461000"
                        size="small"
                      >
                        <MemoPhoneIcon />
                      </IconButton>
                    </div>
                  }
                >
                  <div className={styles.tooltip}>
                    <Typography variant="note">
                      Please call us on this phone number
                    </Typography>
                    <Typography
                      as={Link}
                      className={styles.link}
                      href="tel:08000461000"
                      variant="note"
                    >
                      0800 046 1000
                    </Typography>
                  </div>
                </Tooltip>
                <Typography className={styles.contactBlockButtonLabel}>
                  Call Us
                </Typography>
              </div>
            </div>
            <div className={gridSprinkle({ type: "item", cols: 10 })}>
              <div className={styles.radioButtonWrapper}>
                {URGENT_VARIANTS.map((label) => (
                  <Controller
                    key={label}
                    name="urgent"
                    control={form.control}
                    render={({ field }) => (
                      <RadioButton
                        {...field}
                        value={label}
                        onChange={() => {
                          field.onChange(label === "Urgent");
                        }}
                        checked={field.value === (label === "Urgent")}
                      >
                        {label}
                      </RadioButton>
                    )}
                  />
                ))}
              </div>
            </div>
            <div
              className={gridSprinkle({
                type: "item",
                cols: { mobile: 10, tablet: 5 },
              })}
            >
              <Controller
                rules={{
                  required: "Required!",
                  minLength: {
                    value: 3,
                    message: "At least 3 characters required",
                  },
                  maxLength: {
                    value: 50,
                    message: "Max 50 characters",
                  },
                }}
                name="Name"
                control={form.control}
                render={({ field, fieldState }) => (
                  <TextInput
                    {...FULL_NAME_INPUT_PROPS}
                    {...field}
                    className={styles.field}
                    error={fieldState.error?.message}
                  />
                )}
              />
            </div>
            <div
              className={gridSprinkle({
                type: "item",
                cols: { mobile: 10, tablet: 5 },
              })}
            >
              <Controller
                rules={{
                  required: "Required!",
                  ...EMAIL_CONTROLLER_RULES,
                }}
                name="Email"
                control={form.control}
                render={({ field, fieldState }) => (
                  <TextInput
                    {...EMAIL_INPUT_PROPS}
                    {...field}
                    className={styles.field}
                    error={fieldState.error?.message}
                  />
                )}
              />
            </div>
            <div
              className={gridSprinkle({
                type: "item",
                cols: { mobile: 10, tablet: 5 },
              })}
            >
              <Controller
                rules={{
                  required: "Required!",
                  ...PHONE_NUMBER_CONTROLLER_RULES,
                }}
                name="phone"
                control={form.control}
                render={({ field, fieldState }) => (
                  <PatternFormat
                    {...PHONE_NUMBER_INPUT_PROPS}
                    className={styles.field}
                    error={fieldState.error?.message}
                    {...field}
                    customInput={TextInput}
                  />
                )}
              />
            </div>
            <div
              className={gridSprinkle({
                type: "item",
                cols: { mobile: 10, tablet: 5 },
              })}
            >
              <Controller
                rules={{
                  required: "Required!",
                  ...POST_CODE_CONTROLLER_RULES,
                }}
                name="post_code"
                control={form.control}
                render={({ field, fieldState }) => (
                  <TextInput
                    {...POST_CODE_INPUT_PROPS}
                    {...field}
                    maxLength={7}
                    className={styles.field}
                    error={fieldState.error?.message}
                  />
                )}
              />
            </div>
            {(!auth.isAuthorized || auth.user?.ref_by?.ref_id) && (
              <div className={gridSprinkle({ type: "item", cols: 10 })}>
                <Controller
                  name="ref_id"
                  control={form.control}
                  render={({ field, fieldState }) => (
                    <TextInput
                      {...field}
                      disabled={Boolean(
                        auth.isAuthorized && auth.user?.ref_by?.ref_id
                      )}
                      className={styles.field}
                      error={fieldState.error?.message}
                      label={
                        <span className={styles.labelWithIcon}>
                          Did someone refer you to us? Enter their Member ID
                          here
                          <Tooltip
                            on={["hover", "focus"]}
                            position="bottom center"
                            offsetY={6}
                            trigger={
                              <div>
                                <InfoIcon />
                              </div>
                            }
                          >
                            <div className={styles.tooltip}>
                              <Typography variant="note">
                                You are able to refer friends and family for our
                                services for an array of financial rewards.
                                Whether you refer or are referred to us, you can
                                experience the amazing benefits from our Earn
                                programme.
                              </Typography>
                              <Typography
                                as={Link}
                                className={styles.link}
                                href="/earn"
                                variant="note"
                              >
                                Earn
                              </Typography>
                            </div>
                          </Tooltip>
                          <span className={styles.note}>Optional</span>
                        </span>
                      }
                      placeholder="Enter member ID"
                    />
                  )}
                />
              </div>
            )}
            <div className={gridSprinkle({ type: "item", cols: 10 })}>
              <Controller
                rules={{
                  required: "Required!",
                  minLength: {
                    value: 10,
                    message: "At least 10 characters required",
                  },
                }}
                name="message"
                control={form.control}
                render={({ field, fieldState }) => (
                  <TextInput
                    {...field}
                    className={styles.field}
                    error={fieldState.error?.message}
                    label="How can we help"
                    placeholder="Write your request"
                  />
                )}
              />
            </div>
            <div className={gridSprinkle({ type: "item", cols: 10 })}>
              <WrapperWithLabel
                className={styles.radioButtonWrapper}
                label="How do you want us to respond to your query?"
              >
                {Object.values(RESPOND_VARIANTS).map((label) => (
                  <Controller
                    key={label}
                    name="respond_types"
                    control={form.control}
                    render={({ field }) => (
                      <CheckButton
                        {...field}
                        value={field.value.includes(label)}
                        onChange={() => {
                          const newValue = field.value.includes(label)
                            ? field.value.filter((lab) => lab !== label)
                            : [...field.value, label];
                          field.onChange(newValue);
                        }}
                      >
                        {label}
                      </CheckButton>
                    )}
                  />
                ))}
              </WrapperWithLabel>
            </div>
            <div className={gridSprinkle({ type: "item", cols: 10 })}>
              <Button
                type="button"
                onClick={form.handleSubmit(onSubmit)}
                isAnimated
                disabled={
                  !form.formState.isValid ||
                  form.formState.isLoading ||
                  form.formState.isSubmitting
                }
                className={styles.submitButton}
              >
                Submit
              </Button>
            </div>
          </form>
        )}
      </Modal>
    </>
  );
});

export default BookingModal;
