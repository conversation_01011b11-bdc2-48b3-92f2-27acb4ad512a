import { breakpoints } from "@/styles/constants.css";
import { theme } from "@/styles/themes.css";
import { style } from "@vanilla-extract/css";

export const title = style(
  {
    marginTop: 40,
    marginBottom: 40,

    "@media": {
      [breakpoints.tablet]: {
        marginTop: 0,
      }
    }
  }
);

export const radioButtonWrapper = style(
  {
    display: "flex",
    flexWrap: "wrap",
    gap: 10,
  }
);

export const content = style(
  {
    rowGap: 20,
  }
);

export const submitButton = style(
  {
    width: "100%",
    marginTop: 16,

    "@media": {
      [breakpoints.tablet]: {
        width: "max-content",
      }
    }
  }
);

export const successContent = style(
  {
    display: "grid",
    rowGap: 40,
    maxWidth: 432,
    boxSizing: "border-box",
  }
);

export const errorContent = style(
  {
    display: "grid",
    rowGap: 40,
    maxWidth: 432,
    boxSizing: "border-box",
  }
);

export const errorContentTitle = style({
  color: theme.colors.primary.error,
});

export const contactBlock = style({
  display: "flex",
  alignItems: "center",
  padding: "32px 24px",
  borderRadius: 8,
  backgroundColor: theme.colors.primary.ivory,
  flexWrap: "wrap",
  justifyContent: "center",
  textAlign: "center",
  marginBottom: 10,

  "@media": {
    [breakpoints.tablet]: {
      marginBottom: 20,
      textAlign: "left",
      borderRadius: 16,
      padding: "20px 30px",
    }
  }
});

export const contactBlockTitle = style({
  fontWeight: "500 !important",
  flex: "1 1",
  minWidth: "100%",
  marginBottom: 16,

  "@media": {
    [breakpoints.tablet]: {
      marginRight: 40,
      minWidth: "auto",
      marginBottom: 0,
    }
  }
});

export const contactBlockButtonLabel = style({
  fontSize: "12px !important",
  opacity: 0.8,
});

export const contactBlockButtonWrapper = style({
  display: "flex",
  flexDirection: "column",
  alignItems: "center",
  gap: 10,
  margin: "0 8px",
  textAlign: "center",
});

export const chatIcon = style({
  "::before": {
    position: "relative",
    top: 3,
    color: "currentColor !important",
  }
});

export const field = style({
  marginBottom: 20,
});

export const tooltip = style({
  maxWidth: 160,
  display: "flex",
  flexDirection: "column",
  gap: 10,
});

export const link = style({
  fontWeight: "500 !important",
  textDecoration: "underline !important",
});

export const labelWithIcon = style({
  display: "flex",
  alignItems: "center",
  gap: 4,
});

export const note = style({
  fontSize: 12,
  color: theme.colors.grayscale[200],
  fontWeight: 400,
});

export const iconButton = style({
  fontSize: 24
});