"use client";

import React from "react";
import PhoneIcon from "@/assets/icons/PhoneIcon";
import * as styles from "./callButton.css";
import classNames from "classnames";
import Button from "@components/Button";
import { HEADER_PHONE_NUMBER } from "@/utils/constants";

interface CallButtonProps {
  className?: string;
  text?: string;
  iconClassName?: string;
  style?: React.CSSProperties;
  phoneNumber?: string;
}

const CallButton: React.FC<CallButtonProps> = ({
  className,
  text = "Call Us",
  iconClassName,
  style,
  phoneNumber = HEADER_PHONE_NUMBER,
}) => {
  const handleClick = () => {
    window.location.href = `tel:${phoneNumber}`;
  };

  return (
    <Button
      className={classNames(styles.callButton, className)}
      onClick={handleClick}
      style={style}
    >
      <PhoneIcon className={classNames(styles.buttonIcon, iconClassName)} /> {text}
    </Button>
  );
};

export default CallButton;
