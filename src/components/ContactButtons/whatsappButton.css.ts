import { style } from "@vanilla-extract/css";
import { theme } from "@/styles/themes.css";
import { breakpoints } from "@/styles/constants.css";

export const whatsappButton = style({
  backgroundColor: theme.colors.primary.asidGreen,
  color: theme.colors.primary.castletonGreen,
  display: "flex",
  flex: 1,
  justifyContent: "center",
  fontSize: "18",
  height: "54px",
  ":hover": {
    backgroundColor: theme.colors.primary.asidGreen,
  },
  "@media": {
    [breakpoints.tablet]: {
      flex: "0 0 auto",
      height: "32px",
      fontSize: "28px"
    },
  },
});

export const buttonIcon = style({
  position: "relative",
  top: "3px",
});
