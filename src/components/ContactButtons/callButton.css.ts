import { style } from "@vanilla-extract/css";
import { theme } from "@/styles/themes.css";

export const callButton = style({
  backgroundColor: `${theme.colors.primary.castletonGreen} !important`,
  color: "white !important",
  borderRadius: "50px",
  padding: "12px 24px",
  fontWeight: 600,
  display: "flex",
  alignItems: "center",
  justifyContent: "center",
  gap: "8px",
  width: "211px !important",
  cursor: "pointer",
  transition: "all 0.3s ease",
  fontSize: "16px",
  boxShadow: "none",
  border: "none",
  ":hover": {
    backgroundColor: theme.colors.primary.castletonGreen,
  },
  "@media": {
    "(max-width: 576px)": {
      width: "100% !important",
      padding: "12px 0",
      borderRadius: "50px",
    },
    "(max-width: 480px)": {
      fontSize: "15px",
      padding: "10px 0",
    }
  }
});

export const buttonIcon = style({
  position: "relative",
  top: "3px",
});
