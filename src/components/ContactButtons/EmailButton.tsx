"use client";

import React from "react";
import EmailIcon from "@/assets/icons/EmailIcon";
import * as styles from "./emailButton.css";
import classNames from "classnames";
import Button from "@components/Button";
import { INFO_EMAIL } from "@/utils/constants";

interface EmailButtonProps {
  className?: string;
  text?: string;
  iconClassName?: string;
  style?: React.CSSProperties;
  email?: string;
}

const EmailButton: React.FC<EmailButtonProps> = ({
  className,
  text = "Email Us",
  iconClassName,
  style,
  email = INFO_EMAIL,
}) => {
  return (
    <Button
      as="a"
      href={`mailto:${email}`}
      className={classNames(styles.emailButton, className)}
      style={style}
    >
      <EmailIcon className={classNames(styles.buttonIcon, iconClassName)} /> {text}
    </Button>
  );
};

export default EmailButton;
