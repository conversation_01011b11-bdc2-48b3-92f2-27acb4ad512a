"use client";

import React from "react";
import Button from "@components/Button";
import WhatsAppIcon from "@/assets/icons/WhatsAppIcon";
import classNames from "classnames";
import * as styles from "./whatsappButton.css";
import { WHATS_UP_NUMBER } from "@/utils/constants";

interface WhatsAppButtonProps {
  className?: string;
  text?: string;
  iconClassName?: string;
  whatsAppNumber?: string;
}

const WhatsAppButton: React.FC<WhatsAppButtonProps> = ({
  className,
  text = "WhatsApp Us",
  iconClassName,
  whatsAppNumber = WHATS_UP_NUMBER,
}) => {
  const handleClick = () => {
    const formattedNumber = whatsAppNumber.replaceAll(" ", "");
    window.open(`https://wa.me/${formattedNumber}`, "_blank");
  };

  return (
    <Button
      variant="filled"
      className={classNames(styles.whatsappButton, className)}
      onClick={handleClick}
    >
      <WhatsAppIcon className={classNames(styles.buttonIcon, iconClassName)} /> {text}
    </Button>
  );
};

export default WhatsAppButton;
