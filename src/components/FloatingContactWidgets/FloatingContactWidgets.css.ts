import { breakpoints } from "@/styles/constants.css";
import { theme } from "@/styles/themes.css";
import { globalStyle, style, styleVariants } from "@vanilla-extract/css";

export const root = style({
  position: "fixed",
  bottom: 66,
  right: 10,
  display: "flex",
  flexDirection: "column",
  gap: 10,
  zIndex: 9999,

  "@media": {
    [breakpoints.tablet]: {
      bottom: 80,
    }
  }
});

const hideOnMobile = style({
  display: "none",
  "@media": {
    [breakpoints.tablet]: {
      display: "flex"
    }
  }
});

const hideOnTablet = style({
  display: "flex",
  "@media": {
    [breakpoints.tablet]: {
      display: "none"
    }
  }
});

const contactBlockButtonBase = style({
  cursor: "pointer",
  width: 46,
  height: 46,
  borderRadius: "50%",
  backgroundColor: theme.colors.primary.castletonGreen,
  color: "#fff",
  fontSize: 24,
  border: "none",
  padding: 10,
  alignItems: "center",
  justifyContent: "center",
  lineHeight: "100%",
  boxShadow: "rgba(0, 0, 0, 0.2) 0px 3px 5px -1px, rgba(0, 0, 0, 0.14) 0px 6px 10px 0px, rgba(0, 0, 0, 0.12) 0px 1px 18px 0px",

  "@media": {
    [breakpoints.tablet]: {
      width: 60,
      height: 60,
    }
  }
});

export const contactBlockButton = styleVariants({
  hideOnMobile: [contactBlockButtonBase, hideOnMobile],
  hideOnTablet: [contactBlockButtonBase, hideOnTablet],
  default: [contactBlockButtonBase, {display: "flex"}],
});

globalStyle("#zsiq_float", {
  borderRadius: "50%",
  boxShadow: "rgba(0, 0, 0, 0.2) 0px 3px 5px -1px, rgba(0, 0, 0, 0.14) 0px 6px 10px 0px, rgba(0, 0, 0, 0.12) 0px 1px 18px 0px",
});

globalStyle(".zsiq_flt_rel", {
  width: "46px !important",
  height: "46px !important",
  padding: "8px !important",

  "@media": {
    [breakpoints.tablet]: {
      width: "60px !important",
      height: "60px !important",
    }
  }
});

globalStyle(".zsiq_theme1.zsiq_floatmain", {
  width: "46px !important",

  "@media": {
    [breakpoints.tablet]: {
      width: "60px !important",
    }
  }
});

globalStyle("#zsiq_agtpic", {
  overflow: "visible !important",
  display: "flex",
  alignItems: "center",
  justifyContent: "center",
});

globalStyle("#zsiq_agtpic:before", {
  fontSize: "22px !important",
  lineHeight: "22px !important",

  "@media": {
    [breakpoints.tablet]: {
      fontSize: "28px !important",
      lineHeight: "28px !important",
    }
  }
});