"use client";
import Link from "next/link";
import Script from "next/script";
import { FC} from "react";
import * as styles from "./FloatingContactWidgets.css";
import Props from "./FloatingContactWidgets.types";
import CallUsModal from "../CallUsModal";
import MemoPhoneIcon from "@/assets/icons/PhoneIcon";
import MemoWhatsAppIcon from "@/assets/icons/WhatsAppIcon";
import { observer } from "mobx-react-lite";
import useStore from "@/hooks/useStore";

const FloatingContactWidgets: FC<Props> =  observer(({ phoneNumber, whatsAppNumber }) => {

  const {landign} = useStore();

  return (<>  
    <CallUsModal
      phoneNumber={phoneNumber}
      open={landign.callUsModalIsOpen}
      onClose={() => landign.setCallUsModalIsOpen(false)}
    />
    <div
      className={styles.root}
    >
      <button
        onClick={() => landign.setCallUsModalIsOpen(true)}
        title="Call Us"
        className={styles.contactBlockButton.hideOnMobile}
        type="button"
      >
        <MemoPhoneIcon/>
      </button>
      <Link
        className={styles.contactBlockButton.hideOnTablet}
        title="Call Us"
        // target="_blank"
        href={`tel:${phoneNumber.replaceAll(" ", "")}`}
      >
        <MemoPhoneIcon/>
      </Link>
      <Link
        className={styles.contactBlockButton.default}
        title="WhatsApp"
        target="_blank"
        href={`https://wa.me/${whatsAppNumber.replaceAll(" ", "")}`}
      >
        <MemoWhatsAppIcon/>
      </Link>
      <Script
        strategy="lazyOnload"
        type="text/javascript"
        id="zsiqchat"
        dangerouslySetInnerHTML={{
          __html: `
            var $zoho=$zoho || {};$zoho.salesiq = $zoho.salesiq || {widgetcode:"siq5478f11f7f640b27e42b6c65cffae218a674fb117ddfcc34fb5b2f8c73e197eb", values:{},ready:function(){}};var d=document;s=d.createElement("script");s.type="text/javascript";s.id="zsiqscript";s.defer=true;s.src="https://salesiq.zoho.eu/widget";t=d.getElementsByTagName("script")[0];t.parentNode.insertBefore(s,t);
          `
        }}
      />
    </div>
  </>
  );
});

export default FloatingContactWidgets;