"use client";

import useStore from "@/hooks/useStore";
import { gridSprinkle } from "@/styles/sprinkles.css";
import { observer } from "mobx-react-lite";
import Image from "next/image";
import Link from "next/link";
import { FC } from "react";
import { Controller, useForm } from "react-hook-form";
import AuthLayout from "../AuthLayout";
import Button from "../Button";
import TextInput from "../TextInput";
import Typography from "../Typography";
import * as styles from "./ResetPasswordPage.css";
import PreviewImage from "./preview.png";

interface FormValues {
  password: string;
  confirmPassword: string;
}

const ResetPasswordPage: FC = observer(() => {
  const { auth, alert } = useStore();

  const form = useForm<FormValues>({
    mode: "onBlur",
    defaultValues: {
      password: "",
      confirmPassword: "",
    }
  });

  const handleSubmit = async (data: FormValues) => {
    try {
      await auth.resetPassword(data.password);

      alert.addAlert({ content: "Password changed success!" });
    } catch (error) {
      alert.addAlert({ content: String(error), type: "error" });
    }
  };

  return (
    <form
      onSubmit={form.handleSubmit(handleSubmit)}
    >
      <AuthLayout
        title={(
          <>
          Home
            {" "}
            <Typography
              as="span"
              variant="h2"
            >
            Safety
            </Typography>
            <br />
          Matters
          </>
        )}
        contentTitle={!form.formState.isSubmitSuccessful ? (
          <>
            Reset <b>Password</b>
          </>
        ) : (
          <>
            Password <b>Changed</b>
          </>
        )}
        contentSubtitle={!form.formState.isSubmitSuccessful ? "The password should be at least 8 characters." : "Your password has been changed successfully. Use your new password to log in into your account."}
        content={form.formState.isSubmitSuccessful ? null : (
          <div
            className={gridSprinkle({ type: "grid" })}
          >
            <div
              className={gridSprinkle({ type: "item", cols: 10 })}
            >
              <Controller
                rules={{
                  required: "Required!",
                  minLength: {
                    value: 8,
                    message: "Min characters to enter - 8",
                  },
                  validate: (value) => {
                    if (!value.match(/[A-Z]/)) return "Min one uppercase character";

                    return undefined;
                  }
                }}
                name="password"
                control={form.control}
                render={(
                  { field, fieldState }
                ) => (
                  <TextInput
                    {...field}
                    className={styles.field}
                    error={fieldState.error?.message}
                    placeholder="Enter password"
                    label="New Password"
                    type="password"
                  />
                )}
              />
              <Controller
                rules={{
                  required: "Required!",
                  minLength: {
                    value: 8,
                    message: "Min characters to enter - 8",
                  },
                  validate: (value) => {
                    if (!value.match(/[A-Z]/)) return "Min one uppercase character";

                    if (value !== form.getValues().password) return "Passwords are differ";

                    return undefined;
                  },
                }}
                name="confirmPassword"
                control={form.control}
                render={(
                  { field, fieldState }
                ) => (
                  <TextInput
                    {...field}
                    className={styles.field}
                    error={fieldState.error?.message}
                    placeholder="Confirm password"
                    label="Confirm Password"
                    type="password"
                  />
                )}
              />
            </div>
          </div>
        )}
        contentActions={form.formState.isSubmitSuccessful ? (
          <Button
            as={Link}
            href="/login"
            className={styles.submitButton}
            type="submit"
            isLoading={form.formState.isSubmitting}
          >
            Go to log in page
          </Button>
        ) : (
          <Button
            className={styles.submitButton}
            disabled={!form.formState.isValid || form.formState.isLoading || form.formState.isSubmitting}
            type="submit"
          >
            Reset Password
          </Button>
        )}
        image={(
          <Image
            {...PreviewImage}
            objectFit="contain"
            objectPosition="center"
            alt="Preview"
          />
        )}
      />    
    </form>
  );
});

export default ResetPasswordPage;