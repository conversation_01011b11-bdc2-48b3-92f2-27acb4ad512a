import { components } from "@/slices";
import { FC, Fragment } from "react";
import FloatingContactWidgets from "../FloatingContactWidgets";
import Footer from "../Footer";
import Header from "../Header";
import * as styles from "./LandingPage.css";
import Props from "./LandingPage.types";
import { WHATS_UP_NUMBER } from "@/utils/constants";
import { SliceZone } from "@prismicio/react";
import classNames from "classnames";

const LandingPage: FC<Props> = ({
  header,
  footer,
  page,
  searchParams,
  slicesData,
  emergencyLocations
}) => {


  return (
    <>
      <div
        className={styles.root}
      >
        <Header
          emergencyLocations={emergencyLocations}
          header={header}
        />
        <main
          className={classNames(styles.main, `main-${page.uid}`)}
        >

          {/* {(searchParams.mode === "commercial" ? (page.data.slices2.length ? page.data.slices2 : page.data.slices) : page.data.slices).map((slice) => {
            // Reworked to make it work by slice_type
            // Because when page mode was changed, the slice.id is changed
            // This leads to a bug when reviews can't be passed to the ReviewSection because of different ids
            const sliceData = slicesData.find((item: { sliceType: string; }) => item.sliceType === slice.slice_type)?.data ?? {};
            return (  <Fragment
              key={slice.id}
            >
              {(components as any)[slice.slice_type]({
                slice,
                ...sliceData
              })}
            </Fragment>
            );})} */}
          <SliceZone
            slices={searchParams.mode === "commercial" ? (page.data.slices2.length ? page.data.slices2 : page.data.slices) : page.data.slices}
            components={components}
            context={slicesData}
          />
        </main>    
        <Footer
          footer={footer}
        />
      </div>
      <FloatingContactWidgets
        phoneNumber={String(header.data.phone_number)}
        whatsAppNumber={WHATS_UP_NUMBER}
      />
    </>
  );
};

export default LandingPage;