
import { PageMode, SlicesContextData } from "@/types/common";
import { GroupField } from "@prismicio/client";
import { EmergencyLocationsDocumentDataLocationsItem, FooterDocument, HeaderDocument, LandingPageDocument, LandingPageDocumentDataSlicesSlice, Simplify } from "prismicio-types";

interface Props {
  header: HeaderDocument<string>
  footer: FooterDocument<string>
  emergencyLocations:  GroupField<Simplify<EmergencyLocationsDocumentDataLocationsItem>>;
  page: LandingPageDocument<string>;
  searchParams: { mode: PageMode };
  slicesData: SlicesContextData
};

export default Props;