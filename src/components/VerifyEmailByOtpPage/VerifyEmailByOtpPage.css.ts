import { breakpoints } from "@/styles/constants.css";
import { theme } from "@/styles/themes.css";
import { keyframes, style } from "@vanilla-extract/css";
import { buttonMedium } from "../Typography/Typography.css";

export const field = style({
  marginBottom: 32,
});

export const actionNote = style({
  maxWidth: 480,
});

export const content = style({
  justifyContent: "flex-start",
  minHeight: "calc(100vh - 88px)",

  "@media": {
    [breakpoints.tablet]: {
      justifyContent: "center",
    }
  }
});

export const actions = style({ 
  width: "100%",
});

export const action = style({
  width: "100%",
  marginBottom: 16,
});

export const becomeMemberButton = style({
  marginTop: 24,

  "@media": {
    [breakpoints.tablet]: {
      marginTop: "initial",
    }
  }
});

const wrongFieldData = keyframes({
  "0%": {
    transform: "translateX(0%)",
  },
  "20%": {
    transform: "translateX(-5%)",  
  },
  "40%": {
    transform: "translateX(5%)",  
  },
  "60%": {
    transform: "translateX(-5%)",  
  },
  "80%": {
    transform: "translateX(5%)",  
  },
  "100%": {
    transform: "translateX(0%)",  
  },
});

export const isError = style({
  animation: `${wrongFieldData} 500ms ease-in-out`,
});

export const codeField = style({
  display: "flex",
  alignItems: "center",
  gap: 6,
  justifyContent: "center",

  "@media": {
    [breakpoints.tablet]: {
      gap: 12,
    }
  },
});

export const codeNum = style({
  backgroundColor: "transparent",
  borderRadius: 8,
  border: `1px solid ${theme.colors.grayscale[100]}`,
  padding: 0,
  aspectRatio: "1 / 1",
  width: 48,
  textAlign: "center",
  fontSize: 22,
  fontFamily: "inherit",
  fontWeight: 500,
  color: "inherit",
  outline: "none",

  "::placeholder": {
    color: theme.colors.grayscale[200],
  },

  "@media": {
    [breakpoints.tablet]: {
      width: 80,
      fontSize: 24,
    }
  },

  selectors: {
    "&:focus": {
      border: `1px solid ${theme.colors.primary.castletonGreen}`,
      boxShadow: "0 0 0 2px #003D2320",
    }
  }
});


export const resetButton = style([buttonMedium,{
  border: 0,
  backgroundColor: "transparent",
  color: "inherit",
  padding: 0,
  textDecoration: "underline",
  fontWeight: "bold",
  cursor: "pointer",
  selectors: {
    "&:disabled": {
      cursor: "not-allowed"
    }
  }
}]);