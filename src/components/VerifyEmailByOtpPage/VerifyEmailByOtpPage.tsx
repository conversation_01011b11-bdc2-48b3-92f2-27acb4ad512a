import useStore from "@/hooks/useStore";
import { gridSprinkle } from "@/styles/sprinkles.css";
import classNames from "classnames";
import { observer } from "mobx-react-lite";
import Image from "next/image";
import Link from "next/link";
import { FC, ReactNode, RefObject, useEffect, useMemo, useState } from "react";
import AuthLayout from "../AuthLayout";
import Button from "../Button";
import Typography from "../Typography";
import * as styles from "./VerifyEmailByOtpPage.css";
import { redirectToProfile } from "./actions";
import PreviewImage from "@/assets/images/sign-up-login-preview.webp";
import { CountdownWrapperActions } from "../CountdownWrapper/CountdownWrapper.types";
import CountdownWrapper from "../CountdownWrapper/CountdownWrapper";
import { useSearchParams } from "next/navigation";

const codeLength = 6;

interface Props {
  onBack: () => void;
  onResend: () => void;
  dontRedirect?: boolean, 
  onSuccess?: () => void
  values: {
    email: string
  };
  verifyActionText: string;
  content?: ReactNode;
  countdownRef: RefObject<CountdownWrapperActions>
  forCase: "login" | "sign-up";
}

const VerifyEmailByOtpPage: FC<Props> = observer(({ values, onBack, onResend, content, dontRedirect, onSuccess,countdownRef, forCase}) => {
  const { auth, alert } = useStore();

  const [code, setCode] = useState("");

  const [isError, setIsError] = useState(false);

  const [isLoading, setIsLoading] = useState(false);


  const searchParams = useSearchParams();

  const redirectTo = useMemo(() => {  
    const redirect = searchParams?.get("redirectTo");
    return redirect ? `/${redirect}` : "";
  } , [searchParams]);

  const handleSubmit = async () => {
    try {
      setIsLoading(true);

      await auth.verifyEmailOTP(values.email as string, code);

      alert.addAlert({ content: "Sign In Success!" });

      onSuccess && onSuccess();
      !dontRedirect && redirectToProfile(redirectTo);
    } catch (error) {
      alert.addAlert({ content: String(error), type: "error" });

      setIsError(true);

      setTimeout(() => {
        setIsError(false);
        setCode("");
      }, 1000);
    }

    setIsLoading(false);
  };

  useEffect(() => {
    if (code.length === 6 && code[code.length - 1] !== " ") handleSubmit();
  }, [code]);

  useEffect(() => {
    
    countdownRef.current?.startTimer();
    return () => {
    
    };
  }, []);
  

  const title = useMemo(() => {
    if (forCase === "login") return (
      <>
          Home
        {" "}
        <Typography
          as="span"
          variant="h2"
        >
            Safety
        </Typography>
        <br />
          Matters
      </>
    );
    if (forCase === "sign-up") return <strong>Membership</strong>;
  }, [forCase]);

  const description = useMemo(() => {
    if (forCase === "login") return;
    if (forCase === "sign-up") return "It's free and comes with an array of benefits including shorter wait times and discounted rates";
  }, [forCase]);

  return (
    <form>
      <AuthLayout
        beforeContentTitle={content}
        contentClassName={styles.content}
        title={title}
        description={description}
        contentTitle="Verify your email"
        contentSubtitle={<>
          We&apos;ve sent a code to <b>{values.email}</b>
          <br />
          Enter the code to verify your email.
        </>}
        content={
          <div
            className={gridSprinkle({ type: "grid" })}
          >
            <div
              className={gridSprinkle({ type: "item", cols: 10 })}
            >
              <div
                className={classNames(styles.codeField, {
                  [styles.isError]: isError
                })}
              >
                {Array.from(Array(codeLength)).map((_, index) => (
                  <input
                    inputMode="numeric"
                    readOnly={isLoading || isError}
                    data-code-field={index}
                    key={index}
                    className={styles.codeNum}
                    value={code.slice(index, index + 1) ?? ""}
                    placeholder="X"
                    maxLength={1}
                    onPaste={(event) => {
                      const text = event.clipboardData.getData("text");

                      setCode(text.slice(0, 6));
                    }}
                    onKeyDown={(event) => {
                      const key = event.key;

                      if (key === "Backspace") {
                        setCode(code.slice(0, index) + "" + code.slice(index + 1, code.length));
                      }

                      if (/[0-9]/.test(event.key) && event.key.length === 1) {
                        setCode(code.slice(0, index) + event.key.toUpperCase() + code.slice(index + 1, code.length));
                      }

                      if (key === "Backspace" || key === "ArrowLeft") {
                        const prevCodeField = document.querySelector(`input[data-code-field="${index - 1}"]`);

                        if (prevCodeField && prevCodeField instanceof HTMLInputElement) {
                          setTimeout(() => {
                            prevCodeField.focus();
                            const length = prevCodeField.value.length;
                            prevCodeField.setSelectionRange(length, length);
                          });
                        }
                      } else if (key === "ArrowRight" || (/[0-9]/.test(key) && key.length === 1)) {
                        const nextCodeField = document.querySelector(`input[data-code-field="${index + 1}"]`);

                        if (nextCodeField && nextCodeField instanceof HTMLInputElement) {
                          setTimeout(() => {
                            nextCodeField.focus();
                            const length = nextCodeField.value.length;
                            nextCodeField.setSelectionRange(length, length);
                          });
                        }
                      }
                    }}
                    onFocus={(event) => {
                      if (event.target.value.length === 0) {
                        const nextEmptyCodeField = document.querySelector(`input[data-code-field="${code.length}"]`);

                        if (nextEmptyCodeField && nextEmptyCodeField instanceof HTMLInputElement) {
                          setTimeout(() => {
                            nextEmptyCodeField.focus();
                          });
                        }
                      }
                    }}
                  />                
                ))}
              </div>
            </div>
          </div>
        }
        contentActions={
          <div
            className={classNames(styles.actions, gridSprinkle({ type: "grid" }))}
          >
            <div
              className={gridSprinkle({ type: "item", cols: { mobile: 10, tablet: 5 } })}
            >
              <div
                className={gridSprinkle({ display: { mobile: "flex", tablet: "none" } })}
              >
                <Button
                  className={classNames(styles.action, styles.becomeMemberButton)}
                  onClick={handleSubmit}
                  isLoading={isLoading}
                  disabled={code.length !== 6}
                >
                  Log In
                </Button>
              </div>
              <div
                className={gridSprinkle({ display: { mobile: "none", tablet: "flex" } })}
              >
                <Button
                  onClick={onBack}
                  className={styles.action}
                  variant="outlined"
                  color="secondary"
                >
                    Back
                </Button>                
              </div>
            </div>
            <div
              className={gridSprinkle({ type: "item", cols: { mobile: 10, tablet: 5 } })}
            >
              <div
                className={gridSprinkle({ display: { mobile: "none", tablet: "flex" } })}
              >
                <Button
                  className={styles.action}
                  onClick={handleSubmit}
                  isLoading={isLoading}
                  disabled={code.length !== 6}
                >
                  Log In
                </Button>
              </div>
              <div
                className={gridSprinkle({ display: { mobile: "flex", tablet: "none" } })}
              >
                <Button
                  onClick={onBack}
                  className={styles.action}
                  variant="outlined"
                  color="secondary"
                >
                    Back
                </Button>                
              </div>
            </div>
          </div>
        }
        image={
          <Image
            {...PreviewImage}
            objectFit="contain"
            objectPosition="center"
            alt="Preview"
          />
        }
        tooltip={
          <>
            <Typography
              as="span"
              variant="bodySmall"
            >
              Didn&apos;t get a code?
            </Typography>{" "}
            <CountdownWrapper
              ref={countdownRef}
            >
              {(timerValue: number) => (
                <button
                  type={"button"}
                  disabled={!!timerValue}
                  onClick={onResend}
                  className={styles.resetButton}
                >
                 Click to Resent {timerValue ? `(${timerValue})` : "" }
                </button>
              )}
           
            </CountdownWrapper>
          </>
        }
      />
    </form>
  );
});

export default VerifyEmailByOtpPage;