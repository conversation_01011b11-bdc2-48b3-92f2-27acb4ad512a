import { FC } from "react";
import Props from "./Toggler.types";
import * as styles from "./Toggler.css";
import FloatingElemenet from "../FloatingElemenet";
import classNames from "classnames";

const Toggler: FC<Props> = (
  {
    value,
    size = "normal",
    variant = "outlined",
    onChange,
    className,
    checked,
    preffix, suffix,
    wrapperClassname,
    ...restProps
  }
) => {
  return (
    <label
      className={classNames(styles.label[restProps.disabled ? "disabled" : "active"], wrapperClassname)}
      
    >
      {preffix?.(checked, value)}
      <div
        className={classNames(
          // checked ? styles.root.isActive : styles.root.isInactive, 
          styles.root.base, 
          styles[size],
          styles[variant],
          className,
          {
            [styles.isActive]: checked,
          },
        )}
      >
        <input
          type="checkbox"
          className={styles.hiddenInput}
          onChange={() => {
            onChange?.(!checked, value);}}
          checked={checked}
          {...restProps}
        />
        <div
          aria-hidden
          className={classNames(styles.indicatorShifter[checked ? "active": "unactive"])}
        />
        <div
          className={styles.indicator}
        />
      </div>
      {suffix?.(checked, value)}
    </label>
  );
};

export default Toggler;