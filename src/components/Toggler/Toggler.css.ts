import { theme } from "@/styles/themes.css";
import { createVar, style, styleVariants } from "@vanilla-extract/css";

const togglerFillColor = createVar();
const togglerHeight = createVar();

const labelActive = style({
  cursor: "pointer"
});
const labelDisabled = style({
  cursor: "not-allowed"
});

export const label = styleVariants({
  disabled: [labelDisabled],
  active: [labelActive]
});



export const base = style(
  {
    vars: {
      [togglerFillColor]: "currentColor",
    },
    color: "inherit",
    position: "relative",
    border: "none",
    aspectRatio: "1.67 / 1",
    borderRadius: `calc(${togglerHeight} * 1.5)`,
    backgroundColor: "transparent",
    height: togglerHeight,
    display: "flex",
    alignItems: "center",
    padding: "0 3px",
  }
);

export const normal = style({
  vars: {
    [togglerHeight]: "20px",
  }
});

export const big = style({
  vars: {
    [togglerHeight]: "28px",
  }
});

export const isActive = style({});

export const outlined = style({
  boxShadow: `inset 0 0 0 1px ${togglerFillColor}`,
});

export const filled = style({
  backdropFilter: "brightness(170%)"
});

export const filledDark = style({
  backdropFilter: "brightness(90%)"
});

export const residential = style({
  background: "rgba(255, 253, 248, 0.16)",
});

export const filledDarkDifferActiveState = style({
  backdropFilter: "brightness(90%)",

  vars: {
    [togglerFillColor]: "currentColor",
  },

  selectors: {
    [`${isActive}&`]: {
      backdropFilter: "none",
      backgroundColor: theme.colors.primary.castletonGreen,

      vars: {
        [togglerFillColor]: theme.colors.primary.asidGreen,
      },    
    }
  }
});

export const filledDifferActiveState = style({
  backdropFilter: "brightness(170%)",
  vars: {
    [togglerFillColor]: "currentColor",
  },

  selectors: {
    [`${isActive}&`]: {
      backdropFilter: "brightness(200%)",
      vars: {
        [togglerFillColor]: theme.colors.primary.asidGreen,
      },    
    }
  }
});

export const root = styleVariants({
  base: [base],
  // isActive: [base, {
  //   flexDirection: "row-reverse",
  // }],
  // isInactive: [base, {
  //   flexDirection: "row",
  // }],
});

export const indicator = style({
  aspectRatio: "1 / 1",
  borderRadius: "50%",
  height: `calc(${togglerHeight} - 6px)`,
  backgroundColor: togglerFillColor,
});

export const hiddenInput = style({
  display: "none",
 
});


const indicatorShifterBase = style({
  transition: "250ms width linear",
});

export const indicatorShifter = styleVariants({
  active: [indicatorShifterBase, {
    width: "100%",
  }],
  unactive: [indicatorShifterBase, {
    width: "0%"
  }]
});