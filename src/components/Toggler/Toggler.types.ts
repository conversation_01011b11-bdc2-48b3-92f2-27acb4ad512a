import {
  ButtonHTMLAttributes, DetailedHTMLProps,
  InputHTMLAttributes,
  ReactNode
} from "react";

interface Props extends Omit<DetailedHTMLProps<InputHTMLAttributes<HTMLInputElement>, HTMLInputElement>, "onChange" | "size"> {
  variant?: "outlined" | "filled" | "filledDifferActiveState" | "filledDark" | "filledDarkDifferActiveState" | "residential"
  size?: "normal" | "big"
  color?: "primary" | "secondary"
  onChange?: (checked: boolean, value?: string | number | readonly string[] | undefined) => void
  suffix?: (checked?: boolean, value?: string | number | readonly string[] | undefined) => ReactNode;
  preffix?: (checked?: boolean, value?: string | number | readonly string[] | undefined) => ReactNode;
  wrapperClassname?: string
}

export default Props;