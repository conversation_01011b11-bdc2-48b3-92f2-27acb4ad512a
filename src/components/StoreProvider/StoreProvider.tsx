"use client";
import rootStore from "@/stores";
import React, { createContext, ReactNode, useEffect } from "react";

export const StoreContext = createContext(rootStore);

const StoreProvider = ({ children }: { children: ReactNode }) => {
  useEffect(() => {
    rootStore.auth.initialize();
  }, []);

  return (
    <StoreContext.Provider
      value={rootStore}
    >
      {children}
    </StoreContext.Provider>
  );
};

export default StoreProvider;