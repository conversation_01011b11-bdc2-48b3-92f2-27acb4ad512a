"use client";

import Button from "../Button";
import useStore from "@/hooks/useStore";
import GetCoverButtonProps from "./GetCoverButton.types";
import { useCallback } from "react";
import { useRouter } from "next/navigation";
import { observer } from "mobx-react-lite";
import Link from "next/link";

const GetCoverButton = observer(<P extends {},>({children, isAnimated = true,planName, planId,activeAppliances, recurringInterval,anchorId, ...props}: GetCoverButtonProps<P>) => {

  const { auth: {isAuthorized} } = useStore();
  const router = useRouter();

  const onCLick = useCallback(
    () => {
      if(isAuthorized) {
        const pathToProfileWithAnchor = `/profile/plans#${planName}`;
        router.push(pathToProfileWithAnchor);
        return;
      }
      if(!planId || !recurringInterval) {

        return;
      }
      const params = new URLSearchParams({plan: planName, type: recurringInterval});
        
      if (activeAppliances && activeAppliances.length) {
        params.set("appliances", activeAppliances.join(","));
      }
  
      const url = `/payment/${planId}`;
      router.push(`${url}?${decodeURIComponent(params.toString())}`);
    },
    [isAuthorized, router, activeAppliances, planId, planName,recurringInterval],
  );
  
  const isLink = !!anchorId && !isAuthorized; 

  return (
    <Button
      {...props}
      // @ts-ignore
      as={isLink ? Link : props.as}
      {...(isLink ? {href: `#${anchorId}`} : {})}
      onClick={isLink ? undefined : onCLick}
      isAnimated={isAnimated}
    >
      {children || "Get cover"}
    </Button>
  );
});

export default GetCoverButton;