import { breakpoints } from "@/styles/constants.css";
import { theme } from "@/styles/themes.css";
import { style } from "@vanilla-extract/css";

export const content = style({
  maxWidth: 490,
});

export const actions = style({
  display: "flex",
  flexDirection: "column",
  gap: 16,

  "@media": {
    [breakpoints.tablet]: {
      gap: 24,
      flexDirection: "row",
    }
  }
});

export const title = style({
  fontFamily: theme.fonts.primary,
  marginBottom: 16,

  "@media": {
    [breakpoints.tablet]: {
      marginBottom: 40,
      maxWidth: 300,
    }
  }
});

export const subTitle = style({
  maxWidth: 380,
  marginBottom: 60,

  "@media": {
    [breakpoints.tablet]: {
      maxWidth: 380,
    }
  }
});