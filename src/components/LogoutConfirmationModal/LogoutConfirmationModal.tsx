import { FC } from "react";
import * as styles from "./LogoutConfirmationModal.css";
import Props from "./LogoutConfirmationModal.types";
import Modal from "../Modal";
import Typography from "../Typography";
import Button from "../Button";
import { observer } from "mobx-react-lite";
import useStore from "@/hooks/useStore";

const LogoutConfirmationModal: FC<Props> = observer(({ onClose, isOpen }) => {
  const { auth } = useStore();

  return (
    <Modal
      open={isOpen}
      onClose={onClose}
    >
      <div
        className={styles.content}
      >
        <Typography
          className={styles.title}
          variant="h4"
        >
          Log out
        </Typography>
        <Typography
          className={styles.subTitle}
        >
          Are you sure you want to log out?
        </Typography>
        <div
          className={styles.actions}
        >
          <Button
            onClick={async () => {
              await auth.logout();

              onClose();
            }}
            color="secondary"
          >
            Log out
          </Button>
          <Button
            onClick={onClose}
            variant="outlined"
            color="secondary"
          >
            Cancel
          </Button>
        </div>
      </div>
    </Modal>
  );
});

export default LogoutConfirmationModal;