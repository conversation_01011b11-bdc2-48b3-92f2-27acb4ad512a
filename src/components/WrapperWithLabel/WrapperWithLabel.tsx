"use client";
import * as styles from "./WrapperWithLabel.css";
import classNames from "classnames";
import WrapperWithLabelProps from "./WrapperWithLabel.types";
import Typography from "../Typography";

const WrapperWithLabel = (
  {
    label,
    error,
    children,
    className,
    classNameContainer
  }: WrapperWithLabelProps
) => {
  return (
    <div
      className={classNames(styles.container, classNameContainer)}
    >
      <Typography
        className={classNames(
          { [styles.labelError]: !!error }
        )}
        as={"p"}
        variant="noteMedium"
      >
        {label}
      </Typography>
      {error && (
        <Typography
          className={styles.error}
          variant="note"
        >
          {error}
        </Typography>
      )}
      <div
        className={classNames(
          styles.content, className
        )}
      >{children}</div>
    </div>
  );
};

export default WrapperWithLabel;
