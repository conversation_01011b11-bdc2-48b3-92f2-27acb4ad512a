import { breakpoints } from "@/styles/constants.css";
import { theme } from "@/styles/themes.css";
import { style } from "@vanilla-extract/css";

export const container = style(
  {
    display: "grid",
    gridTemplateColumns: "repeat(2,auto)",
    rowGap: 12,
    "@media": {
      [breakpoints.tablet]: {
        rowGap: 16,

      }
    }
  }
);

export const error = style(
  {
    color: theme.colors.primary.error,
    textAlign: "end",
    marginLeft: "auto",
  }
);

export const content = style(
  {
    gridColumn: "span 2",
  }
);

export const labelError = style(
  {
    color: theme.colors.primary.error,
  }
);
