import classNames from "classnames";
import {
  FC,
  PropsWithChildren,
  useLayoutEffect, useRef,
  useState
} from "react";
import * as styles from "./Accordion.css";
import Props from "./Accordion.types";

const Accordion: FC<PropsWithChildren<Props>> = ({
  isOpen,
  children,
  className,
  duration = 400,
  direction = "vertical",
  shouldUnmountChild = false,
  ...restProps
}) => {
  const ref = useRef<HTMLDivElement | null>(null);

  const [isFirstRender, setIsFirstRender] = useState(true);

  useLayoutEffect(() => {
    const target = ref.current;

    if (!target) return;

    if (!process.browser) return;

    const handleClose = () => {
      if (direction === "vertical") {
        const newValue = getComputedStyle(target).minHeight !== "auto" ? getComputedStyle(target).minHeight : "0px";

        target.style.maxHeight = newValue;
      } else {
        const newValue = getComputedStyle(target).minWidth !== "auto" ? getComputedStyle(target).minHeight : "0px";

        target.style.maxWidth = newValue;
      }
    };

    if (isFirstRender) {
      setIsFirstRender(false);

      if (!isOpen) handleClose();

      return;
    }

    let timeoutId: NodeJS.Timeout;

    if (isOpen) {
      if (direction === "vertical") {
        target.style.maxHeight = getComputedStyle(target).minHeight ?? "0px";
      } else {
        target.style.maxWidth = getComputedStyle(target).minWidth ?? "0px";
      }

      setTimeout(() => {
        if (direction === "vertical") {
          target.style.maxHeight = `${target.scrollHeight}px`;        
        } else {
          target.style.maxWidth = `${target.scrollWidth}px`;                
        }
      }, 10);

      timeoutId = setTimeout(() => {
        if (direction === "vertical") {
          target.style.removeProperty("max-height");
        } else {
          target.style.removeProperty("max-width");
        }
      }, duration);
    } else {
      if (direction === "vertical") {
        target.style.maxHeight = `${target.clientHeight}px`;
      } else {
        target.style.maxWidth = `${target.clientWidth}px`;
      }

      setTimeout(handleClose, 10);
    }

    return () => {
      clearTimeout(timeoutId);
    };
  }, [isOpen, direction]);

  return (
    <div
      className={classNames(styles.root, className)}
      style={{ transitionDuration: duration + "ms", maxHeight: !isOpen && !process?.browser ? "0px" : undefined }}
      ref={ref}
      {...restProps}
    >
      {children}
    </div>
  );
};

export default Accordion;
