import { style } from '@vanilla-extract/css';
import {theme} from "@/styles/themes.css";
import {breakpoints} from "@/styles/constants.css";

export const container = style({
  display: 'flex',
  borderRadius: '15px',
  fontFamily: theme.fonts.primary,
  '@media': {
    'screen and (max-width: 1030px)': {
      flexDirection: 'column',
    },
  },
});

export const leftPanel = style({
  backgroundColor: theme.colors.primary.castletonGreen,
  color: theme.colors.primary.ivory,
  padding: '20px',
  borderRadius: '24px',
  marginRight: '48px',
  display: 'flex',
  flexDirection: 'column',
  minHeight: '700px',
  position: 'relative',
  overflow: 'hidden',
  '@media': {
    'screen and (max-width: 768px)': {
      width: '100%',
      marginRight: '0',
    },
    'screen and (max-width: 1030px)': {
      marginRight: 0,
      marginBottom: '20px',
    }
  },
});

export const title = style({
  fontFamily: theme.fonts.primary,
  fontSize: '40px',
  lineHeight: '95%',
  maxWidth: '300px',
  color: theme.colors.primary.softWhite,
  fontWeight: 400,
  '@media': {
    [breakpoints.tablet]: {
      fontSize: '64px',
      maxWidth: "100%"
    }
  }
});
export const fontWight500 = style({
  fontWeight: 500
})

export const earn = style({
  color: theme.colors.primary.asidGreen,
  // fontStyle: 'italic',
  transform: 'skewX(-10deg)',
  WebkitFontSmoothing: 'antialiased',
});

export const cash = style({
  color: theme.colors.primary.asidGreen,
});

export const subtitle = style({
  color: theme.colors.primary.softWhite,
  fontFamily: theme.fonts.secondary,
  fontSize: '20px',
  marginTop: '40px',
  fontWeight: 700,
  lineHeight: '120%',
});

export const description = style({
  fontFamily: theme.fonts.secondary,
  color: theme.colors.primary.softWhite,
  fontSize: '20px',
  marginTop: '20px',
  lineHeight: '120%',
  marginBottom: '18px',
});

export const image = style({
  width: '120%',
  height: '100%',
  borderRadius: '10px',
  marginTop: '-40px',
  position: "relative",
  top: "40px",
  left: "0",
  right: "20px",
  objectFit: "cover",
  '@media': {
    'screen and (min-width: 1030px)': {
      display: 'block',
      width: '100%',
      height: '100%',
      // marginTop: "1px",
      // marginTop: "-185px",
      // right: "50px",
      left: "40px",
    }
  }
});

// export const image = style({
//   width: '120%',
//   height: '100%',
//   borderRadius: '10px',
//   marginTop: '-40px',
//   position: "relative",
//   top: "40px",
//   right: "20px",
//   objectFit: "cover",
//   '@media': {
//     [breakpoints.tablet]: {
//       display: 'none',
//     },
//     [breakpoints.desktop]: {
//       display: 'block',
//       width: '100%',
//       height: '100%',
//       // marginTop: "1px",
//       // marginTop: "-185px",
//       // right: "50px",
//       left: "40px",
//
//     }
//   }
// });

export const rightPanel = style({
  width: '60%',
  '@media': {
    'screen and (max-width: 1030px)': {
      width: '100%',
    },
  },
});

export const accordion = style({
  // Accordion styles
});

export const accordionItem = style({
  fontSize: '20px',
  backgroundColor: '#FFFDF8',
  padding: '24px',
  borderRadius: '24px',
  marginBottom: '20px',
  color: theme.colors.primary.castletonGreen,
  fontFamily: theme.fonts.secondary,
});

export const accordionTitle = style({
  fontWeight: 700,
  display: 'flex',
  alignItems: 'center',
  fontSize: "20px",
  lineHeight: '120%',
});

export const titleWithDropdown = style({
  fontWeight: 700,
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'space-between',
  fontSize: "20px",
  fontStyle: "normal",
  lineHeight: "120%"
});

export const titleDropDownIcon = style({
  cursor: 'pointer',
})

export const icon = style({
  marginRight: '14px',
});

export const accordionContent = style({
  paddingTop: '14px',
});

export const highlightedParagraph = style({
  margin: '14px 0',
  fontStyle: "italic"
})

export const li = style({
  display: 'flex',
  lineHeight: "120%",
  marginBottom: "14px",
  fontSize: "20px",
  fontWeight: 400,
  fontFamily: theme.fonts.secondary,
})

export const bonus = style({
  fontWeight: 700
})

export const checkIcon = style({
  color: theme.colors.primary.castletonGreen,
  marginRight: '10px',
});

export const list = style({
  listStyleType: 'none',
  paddingLeft: '0',
  lineHeight: '1.6',
});

export const total = style({
  color: "rgba(0, 61, 35, 0.50)",
  fontSize: "20px"
});

export const formContainer = style({
  backgroundColor: '#FFFDF8',
  padding: '24px',
  borderRadius: '24px',
  marginTop: '10px',
  fontSize: "16px",
  fontFamily: theme.fonts.secondary,
});


export const form = style({
  marginTop: '16px'
});

export const formParagraph = style({
  opacity: "0.8",
  maxWidth: "287px",
  "@media": {
    [breakpoints.tablet]: {
      maxWidth: '100%',
    }
  }
})

export const formSubparagraph = style({
  display: "block",
  marginTop: "30px",
  "@media": {
    [breakpoints.tablet]: {
      display: 'initial',
    }
  }
})

export const label = style({
  display: 'block',
  marginBottom: '5px',
  fontWeight: 500,
});

export const inputGroup = style({
  display: 'flex',
});

export const input = style({
  width: '100%',
  height: "56px",
  padding: '17px 24px',
  border: '1px solid #ccc',
  borderRadius: '5px 0 0 5px',
  fontSize: '18px',
  fontWeight: 400,
  lineHeight: '120%',
  backgroundColor: "#FFFDF8",
});

export const button = style({
  backgroundColor: theme.colors.primary.asidGreen,
  marginLeft: '2px',
  border: 'none',
  padding: '10px 15px',
  borderRadius: '8px',
  color: "black",
  cursor: 'pointer',
  fontSize: '24px',
  lineHeight: '1',
  height: '56px',
  width: '80px'
});