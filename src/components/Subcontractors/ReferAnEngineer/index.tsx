"use client"

import React, {useState} from 'react';
import { createClient } from "@/utils/supabase/client";
import * as styles from './ReferAnEngineer.css';
import referalImg from './referal.png';
import Container from "@components/Container";
import Image from "next/image";
import unlimitedEarning from "./unlimited_earning_potential.svg";
import howItWorksIcon from "./how_it_works.svg"
import arrowUp from "@components/Subcontractors/FAQ/arrow-up.svg";
import arrowDown from "@components/Subcontractors/FAQ/arrow-down.svg";
import {highlightedParagraph, fontWight500, titleDropDownIcon} from "./ReferAnEngineer.css";
import Modal from "@components/Modal";
import Button from "@components/Button";
import {theme} from "@/styles/themes.css";
import { EMAIL_CONTROLLER_RULES } from "@/utils/constants";

const ReferAnEngineer: React.FC = () => {
  const [openMenu, setOpenMenu] = useState<boolean>(true);
  const [email, setEmail] = useState<string>('');
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);
  const [submitStatus, setSubmitStatus] = useState<'idle' | 'error'>('idle');
  const [errorMessage, setErrorMessage] = useState<string>('');
  const [isModalOpen, setIsModalOpen] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    const supabase = createClient();

    if (!email) {
      setErrorMessage('Please enter your email address');
      setSubmitStatus('error');
      return;
    }

    const emailError = EMAIL_CONTROLLER_RULES.validate(email);
    if (emailError) {
      setErrorMessage(emailError);
      setSubmitStatus('error');
      return;
    }

    if (email.length > 254) {
      setErrorMessage("Email cannot be longer than 254 characters.");
      setSubmitStatus('error');
      return;
    }

    setIsSubmitting(true);
    setSubmitStatus('idle');
    setErrorMessage('');

    try {
      const { data, error } = await supabase.functions.invoke("create-subcontractor/referral-info-pack", {
        body: { email },
      });

      if (error) {
        throw error;
      }

      setIsModalOpen(true);
      setEmail('');
      console.log('Referral info pack sent successfully:', data);
    } catch (error) {
      console.error('Error sending referral info pack:', error);
      setSubmitStatus('error');
      setErrorMessage('There was a problem sending your request. Please try again shortly.');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Container>
      <section className={styles.container}>
        <div className={styles.leftPanel}>
          <h2 className={styles.title}>
            <span className={styles.fontWight500}>Refer</span> an Engineer<br />
            <span className={styles.fontWight500}>and <span className={styles.earn}>Earn</span></span><br />
            <span className={styles.cash}>£1,000 Cash</span>
          </h2>
          <p className={styles.subtitle}>Got a mate who&apos;s a great engineer?</p>
          <p className={styles.description}>
            Refer them to Pleasant Plumbers — and earn up to £1,000 when they join and get to work.
          </p>
          <img src={referalImg.src} alt="referral image" className={styles.image} />
        </div>
        <div className={styles.rightPanel}>
          <div className={styles.accordion}>
            <div className={styles.accordionItem}>
              <h3 className={styles.titleWithDropdown} onClick={() => setOpenMenu(!openMenu)}>
                <span>Here&apos;s the Breakdown:</span>
                {openMenu
                    ? <Image className={styles.titleDropDownIcon} src={arrowUp.src} width={30} height={30} alt="arrow up" />
                    : <Image className={styles.titleDropDownIcon} src={arrowDown.src} width={30} height={30} alt="arrow down" />
                }
              </h3>
              {openMenu && (
                  <div className={styles.accordionContent}>
                    <ul className={styles.list}>
                      <li className={styles.li}>
                        <div className={styles.checkIcon}>✔</div>
                        <div>
                          <span className={styles.bonus}>£250 bonus</span>
                          <div>once your referral completes 10 hours of work</div>
                        </div>
                      </li>
                      <li className={styles.li}>
                        <div className={styles.checkIcon}>✔</div>
                        <div>
                          <strong>£250 bonus</strong>
                          <div>when they hit 25 hours</div>
                        </div>
                      </li>
                      <li className={styles.li}>
                        <div className={styles.checkIcon}>✔</div>
                        <div>
                          <strong>£500 final bonus</strong>
                          <div>when they reach 50 hours completed</div>
                        </div>
                      </li>
                    </ul>
                    <p className={styles.total}>That&apos;s £1,000 total — paid directly to your bank account in easy stages, as your referral delivers real work.</p>
                  </div>
              )}
            </div>
            <div className={styles.accordionItem}>
              <h3 className={styles.accordionTitle}>
                <Image src={unlimitedEarning.src} className={styles.icon} width={24} height={24} alt='Unlimited Earning icon' />
                <span>Unlimited Earning Potential</span>
              </h3>
              <div className={styles.accordionContent}>
                <p>There&apos;s no cap on how many engineers you can refer. The more quality people you bring in — the more you earn.</p>
              </div>
            </div>
            <div className={styles.accordionItem}>
              <h3 className={styles.accordionTitle}>
                <Image src={howItWorksIcon.src} className={styles.icon} width={24} height={24} alt="how it works icon" />
                <span>How Does It Work?</span>
              </h3>
              <div className={styles.accordionContent}>
                <p>On the engineer onboarding form, there&apos;s a box that asks: </p>
                <p className={styles.highlightedParagraph}>&quot;Were you referred by someone?&quot;</p>
                <p>Just make sure your referral enters your full name in that box. That&apos;s it.</p>
                <p>We&apos;ll handle the rest — and you&apos;ll be paid as soon as they hit each milestone.</p>
              </div>
            </div>
          </div>
          <div className={styles.formContainer}>
            <p className={styles.formParagraph}>Enter your email address below. <span className={styles.formSubparagraph}>We&apos;ll send you an info pack about working with us – simply forward it to anyone you&apos;d like to refer.</span></p>
            <form className={styles.form} onSubmit={handleSubmit}>
              <label htmlFor="email" className={styles.label}>Email</label>
              <div className={styles.inputGroup}>
                <input
                  type="email"
                  id="email"
                  placeholder="<EMAIL>"
                  className={styles.input}
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  disabled={isSubmitting}
                  required
                  maxLength={60}
                />
                <button
                  type="submit"
                  className={styles.button}
                  disabled={isSubmitting || !email}
                >
                  {isSubmitting ? '...' : '→'}
                </button>
              </div>
              {submitStatus === 'error' && (
                <p style={{ color: 'red', marginTop: '10px', fontSize: '14px' }}>
                  ❌ {errorMessage}
                </p>
              )}
            </form>
          </div>
        </div>
      </section>
      <Modal open={isModalOpen} onClose={() => setIsModalOpen(false)}>
        <div style={{ padding: '40px 20px', textAlign: 'center', background: theme.colors.primary.softWhite, borderRadius: '8px' }}>
          <h2 style={{ marginBottom: '15px' }}>
            ✅ Email Sent!
          </h2>
          <p>Request submitted successfully! We&apos;ll send you the info pack shortly.</p>
          <Button
            onClick={() => setIsModalOpen(false)}
            style={{ marginTop: '20px' }}
          >
            Close
          </Button>
        </div>
      </Modal>
    </Container>
  );
};

export default ReferAnEngineer;
