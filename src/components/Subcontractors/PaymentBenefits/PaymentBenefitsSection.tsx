"use client";

import Container from "@components/Container";
import Typography from "@components/Typography";
import Button from "@components/Button";
import Image from "next/image";
import * as styles from "./PaymentBenefitsSection.css";
import React from "react";
import phoneImage from "./phone.png";
import checkIcon from "./noun-check.png";
import Link from "next/link";

const PaymentBenefitsSection = () => {
  const scrollToForm = () => {
    const formElement = document.getElementById('application-form');
    if (formElement) {
      formElement.scrollIntoView({ behavior: 'smooth' });
    }
  };

  return (
    <Container>
      <section className={styles.section}>
        <div className={styles.container}>
          <div className={styles.phoneImageContainer}>
            <Image
              src={phoneImage.src}
              alt="Payment notifications showing fast payments"
              width={500}
              height={600}
              className={styles.phoneImage}
            />
          </div>

          <div className={styles.benefitsContainer}>
            <Typography variant="bodyMedium" className={styles.introText}>
              At Pleasant Plumbers, we&apos;re proud to be more than just a plumbing
              and heating company. We&apos;re building a community of top-tier
              engineers who care deeply about the people they serve. Every
              subcontractor that joins our team gets the tools, support, and
              respect they deserve.
            </Typography>

            <Typography variant="h3" className={styles.benefitsTitle}>
              Here&apos;s what sets us apart:
            </Typography>

            <ul className={styles.benefitsList}>
              <li className={styles.benefitItem}>
                <div className={styles.checkIconWrapper}>
                  <Image src={checkIcon.src} alt="" width={16} height={16} />
                </div>
                <div>
                  <Typography variant="bodyMedium" className={styles.benefitTitle}>
                    Genuine Support
                  </Typography>
                  <Typography variant="bodySmall" className={styles.benefitDescription}>
                    From our office team to your toolbox, we&apos;ve got your back
                  </Typography>
                </div>
              </li>

              <li className={styles.benefitItem}>
                <div className={styles.checkIconWrapper}>
                  <Image src={checkIcon.src} alt="" width={16} height={16} />
                </div>
                <div>
                  <Typography variant="bodyMedium" className={styles.benefitTitle}>
                    Prompt Payments
                  </Typography>
                  <Typography variant="bodySmall" className={styles.benefitDescription}>
                    Get paid next day after job completion
                  </Typography>
                </div>
              </li>

              <li className={styles.benefitItem}>
                <div className={styles.checkIconWrapper}>
                  <Image src={checkIcon.src} alt="" width={16} height={16} />
                </div>
                <div>
                  <Typography variant="bodyMedium" className={styles.benefitTitle}>
                    Zero Admin
                  </Typography>
                  <Typography variant="bodySmall" className={styles.benefitDescription}>
                    Focus on the work, we&apos;ll handle the paperwork
                  </Typography>
                </div>
              </li>

              <li className={styles.benefitItem}>
                <div className={styles.checkIconWrapper}>
                  <Image src={checkIcon.src} alt="" width={16} height={16} />
                </div>
                <div>
                  <Typography variant="bodyMedium" className={styles.benefitTitle}>
                    Flexible Jobs
                  </Typography>
                  <Typography variant="bodySmall" className={styles.benefitDescription}>
                    Take on work that suits your location and schedule
                  </Typography>
                </div>
              </li>

              <li className={styles.benefitItem}>
                <div className={styles.checkIconWrapper}>
                  <Image src={checkIcon.src} alt="" width={16} height={16} />
                </div>
                <div>
                  <Typography variant="bodyMedium" className={styles.benefitTitle}>
                    Perks
                  </Typography>
                  <Typography variant="bodySmall" className={styles.benefitDescription}>
                    Get your uniform, custom tool mat, and branded ID card as part of your
                    welcome pack. Become part of a network of engineers who want more
                    from their work.
                  </Typography>
                </div>
              </li>
            </ul>

            <div className={styles.ctaContainer}>
              <Typography variant="bodyMedium" className={styles.ctaText}>
                Let&apos;s get you onboarded in just a few minutes. 👇
              </Typography>

              <Button
                  as={Link} href="/become-subcontractor"
                  className={styles.onboardingButton}
                  isAnimated
              >
                Start Onboarding
              </Button>
            </div>
          </div>
        </div>
      </section>
    </Container>
  );
};

export default PaymentBenefitsSection;