import { breakpoints } from "@/styles/constants.css";
import { theme } from "@/styles/themes.css";
import { style } from "@vanilla-extract/css";

export const section = style({
  padding: "40px 0",
  fontFamily: theme.fonts.secondary,
  color: theme.colors.primary.castletonGreen,
  lineHeight: 1.2,

  "@media": {
    [breakpoints.tablet]: {
      // padding: "60px 0",
    },
    [breakpoints.desktop]: {
      // padding: "80px 0",
    }
  }
});

export const container = style({
  display: "flex",
  flexDirection: "column",
  borderRadius: 24,
  overflow: "hidden",
  gap: 0,
  position: "relative",

  "@media": {
    [breakpoints.tablet]: {
      flexDirection: "row",
      gap: 0,
    }
  }
});

export const phoneImageContainer = style({
  flex: "1 1 50%",
  display: "flex",
  justifyContent: "center",
  alignItems: "center",
  padding: 0,
  margin: 0,
  position: "relative",

  "@media": {
    [breakpoints.tablet]: {
      justifyContent: "flex-start",
    },
    "(min-width: 768px) and (max-width: 1075px)": {
      display: "none",
    },
  }
});

export const phoneImage = style({
  width: "100%",
  height: "auto",
  borderRadius: 0,
  display: "block",

  "@media": {
    "(min-width: 1076px) and (max-width: 1439px)": {
      width: "630px",
      height: "100%",
    },
    [breakpoints.desktop]: {
      width: "730px",
      height: "770px",
    }
  }
});

export const benefitsContainer = style({
  lineHeight: 1.2,
  padding: "40px 16px",
  backgroundColor: theme.colors.primary.softWhite,
  borderRadius: 24,
  borderTop: "1px",
  boxShadow: "none",
  zIndex: 2,
  position: "relative",
  bottom: "25px",

  "@media": {
    [breakpoints.tablet]: {
      padding: "32px 40px",
      bottom: 0,
      margin: "0 auto",
      display: "block",
    },
    [breakpoints.desktop]: {
      padding: "40px",
      marginLeft: "-80px",
      // height: "744px",
      width: "688px",
      fontSize: '20px',
    }
  }
});

export const introText = style({
  marginBottom: 20,
  fontSize: '20px',
  fontWeight: 400,
  lineHeight: 1.2,
  alignSelf: "stretch",
  fontFamily: theme.fonts.secondary,
});

export const benefitsTitle = style({
  marginBottom: 20,
  fontSize: '20px',
  fontWeight: 700,
  lineHeight: 1.2,
});

export const benefitsList = style({
  listStyle: "none",
  padding: 0,
  margin: 0,
  marginBottom: 32,
  display: "flex",
  flexDirection: "column",
  gap: 16,
});

export const benefitItem = style({
  display: "flex",
  gap: 10,
  alignItems: "flex-start",
});

export const checkIconWrapper = style({
  display: "flex",
  alignItems: "center",
  justifyContent: "center",
  width: 24,
  height: 24,
  marginTop: 2,
});

export const benefitTitle = style({
  fontWeight: 700,
  fontSize: "18px",
  marginBottom: 4,
});

export const benefitDescription = style({
  fontSize: "18px",
  fontWeight: 400,
});

export const ctaContainer = style({
  marginTop: 20,
});

export const ctaText = style({
  marginBottom: 40,
  fontSize: "20px",
  fontWeight: 700,
});

export const onboardingButton = style({
  // backgroundColor: theme.colors.primary.asidGreen,
  // fontWeight: 600,
  // borderRadius: 100,
  padding: "12px 24px",
  maxWidth: "464px",
  height: "56px",
  // display: "flex",
  // justifyContent: "center",
  // alignItems: "center",
  // border: "none",
  // cursor: "pointer",
  // transition: "background-color 0.2s ease",

  "@media": {
    [breakpoints.tablet]: {
      padding: "14px 28px",
    },
    [breakpoints.desktop]: {
      padding: "16px 32px",
    }
  },

  // ":hover": {
  //   backgroundColor: "#00C853",
  // }
});
