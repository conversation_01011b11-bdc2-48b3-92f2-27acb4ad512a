import { breakpoints } from "@/styles/constants.css";
import { theme } from "@/styles/themes.css";
import { style } from "@vanilla-extract/css";

export const section = style({
  backgroundColor: theme.colors.primary.ivory,
  padding: "40px 0 53px 0",
  "@media": {
    [breakpoints.tablet]: {
      padding: "60px 0 80px",
    },
    [breakpoints.desktop]: {
      padding: "120px 48px 140px 48px",
    }
  }
});

export const container = style({
  display: "flex",
  flexDirection: "column",
  margin: "0 auto",

  "@media": {
    [breakpoints.tablet]: {
      flexDirection: "row",
      gap: "100px",
    },
    [breakpoints.desktop]: {
      gap: "186px",
    }
  }
});

export const header = style({
  marginBottom: "20px",

  "@media": {
    [breakpoints.tablet]: {
      width: "30%",
      marginBottom: 0,
    }
  }
});

export const title = style({
  color: theme.colors.primary.castletonGreen,
  fontSize: "44px",
  fontFamily: theme.fonts.primary,
  marginBottom: "24px",
  textAlign: "center",
  fontWeight: 400,
  lineHeight: "95%",
  letterSpacing: "-0.88px",

  "@media": {
    [breakpoints.tablet]: {
      marginBottom: "48px",
      fontSize: "80px",
      textAlign: "left",
      letterSpacing: "-1.6px",
    },
    [breakpoints.desktop]: {

    }
  }
});

export const subtitle = style({
  color: theme.colors.primary.castletonGreen,
  maxWidth: "345px",
  fontSize: "20px",
  fontFamily: theme.fonts.secondary,
  lineHeight: 1.2,
  textAlign: "center",
  alignSelf: "stretch",
  margin: "0 auto",
  opacity: 0.8,

  "@media": {
    [breakpoints.tablet]: {
      margin: 0,
      textAlign: "left",
    }
  }
});

export const faqList = style({
  display: "flex",
  flexDirection: "column",
  gap: "16px",

  "@media": {
    [breakpoints.tablet]: {
    }
  }
});

export const faqItem = style({
  backgroundColor: theme.colors.primary.softWhite,
  borderRadius: "24px",
  overflow: "hidden",
  transition: "all 0.3s ease",
  boxShadow: "0 2px 4px rgba(0, 0, 0, 0.05)",
  padding: "20px",
  margin: "0 10px",

  selectors: {
    '&[data-open="true"]': {
      boxShadow: "0 4px 8px rgba(0, 0, 0, 0.1)",
    }
  },
  "@media": {
    [breakpoints.tablet]: {
      padding: "40px",
      margin: 0,
    },
    [breakpoints.desktop]: {
      width: "773px",
    }
  }
});

export const questionButton = style({
  width: "100%",
  display: "flex",
  background: "none",
  border: "none",
  cursor: "pointer",
  textAlign: "left",
  padding: 0,
  justifyContent: "start",

  "@media": {
    [breakpoints.tablet]: {
      justifyContent: "space-between",
      alignItems: "center",
    }
  }
});

export const  question = style({
  color: theme.colors.primary.castletonGreen,
  fontWeight: 500,
  lineHeight: "120%",
  letterSpacing: "-0.24px",
  flex: 1,
  alignSelf: "stretch",
  fontFamily: theme.fonts.secondary,
  fontSize: "24px",
  marginRight: "20px",
});

export const answer = style({
  color: theme.colors.primary.castletonGreen,
  lineHeight: 1.2,
  fontWeight: 400,
  fontSize: "18px",
  marginTop: "27px",
  opacity: 0.8,
  marginRight: "40px",
  alignItems: "stretch",
  "@media": {
    [breakpoints.tablet]: {
      fontSize: "18px"
    }
  }
});
