"use client";

import Container from "@components/Container";
import Typography from "@components/Typography";
import * as styles from "./SubcontractorFAQ.css";
import React, { useState } from "react";
import arrowUp from "./arrow-up.svg";
import arrowDown from "./arrow-down.svg";
import Image from "next/image";

interface FAQItem {
  question: string;
  answer: string | JSX.Element;
}

const SubcontractorFAQ = () => {
  const [openIndex, setOpenIndex] = useState<number | null>(null);

  const faqItems: FAQItem[] = [
    {
      question: "When will I start receiving jobs?",
      answer: "Once your onboarding documents are approved, you’ll begin receiving job requests within 24 – 48 hours based on your local area and availability."
    },
    {
      question: "How do I receive job details?",
      answer: "Jobs are assigned with full details including the customer name, address, issue description, and any access notes."
    },
    {
      question: "What areas will I be covering?",
      answer: "You’ll only be sent jobs local to you, based on the postcode areas you confirm during onboarding. We aim to minimise travel and keep work within your region."
    },
    {
      question: "What should I wear to jobs?",
      answer: "All engineers are issued with branded uniform and a lanyard, which must be worn on every job to maintain a professional appearance."
    },
    {
      question: "Who supplies the materials?",
      answer: "We supply all required materials. You’ll be directed to the nearest supplier, where materials will be pre-paid and ready for collection."
    },
    {
      question: "How do I report job progress and time?",
      answer: (
        <>
          You must send the following time updates for every job:
          <br /><br />
          • Exact arrival time on site<br />
          • If you leave site to collect materials, your leave time and re-arrival time<br />
          • Completion time once the job is finished
          <br /><br />
          You must also provide a brief summary of the work and clear photos.
        </>
      )
    },
    {
      question: "How do I get paid?",
      answer: "You must submit one invoice at the end of each working week covering all completed jobs. Payment is made within 5 days of receiving the invoice."
    },
    {
      question: "Do you cover congestion and ULEZ charges?",
      answer: "No, we do not cover congestion zone or ULEZ charges. These costs are the responsibility of the attending engineer."
    },
    {
      question: "What documents do I need to provide?",
      answer: (
        <>
          Before starting, you&apos;ll need to send us:
          <br /><br />
          • Photo ID<br />
          • Proof of address<br />
          • Public liability insurance<br />
          • UTR number or company registration<br />
          • Gas Safe certificate (if applicable)
        </>
      )
    },
    {
      question: "What if a job needs a revisit?",
      answer: "If a revisit is required, let us know. We’ll coordinate the follow-up and ensure you are paid for the additional visit."
    },
    {
      question: "Is support available?",
      answer: "Yes — we operate 24/7 and are always available to assist with job-related matters, materials, or urgent issues."
    }
  ];

  const toggleFAQ = (index: number) => {
    setOpenIndex(openIndex === index ? -1 : index);
  };

  return (
    <Container>
      <section className={styles.section}>
        <div className={styles.container}>
          <div className={styles.header}>
            <Typography variant="h2" className={styles.title}>
              FAQs
            </Typography>
            <Typography variant="bodyMedium" className={styles.subtitle}>
              Here you can find answers to most frequently asked questions
            </Typography>
          </div>

          <div className={styles.faqList}>
            {faqItems.map((item, index) => (
              <div
                key={index}
                className={styles.faqItem}
                data-open={openIndex === index}
              >
                <button
                  className={styles.questionButton}
                  onClick={() => toggleFAQ(index)}
                >
                  <Typography variant="bodyMedium" className={styles.question}>
                    {item.question}
                  </Typography>
                  {openIndex === index
                    ? <Image src={arrowUp.src} width={30} height={30} alt="arrow up" />
                    : <Image src={arrowDown.src} width={30} height={30} alt="arrow down" />
                  }
                </button>
                {openIndex === index && (
                  <div className={styles.answer}>
                    <Typography variant="bodySmall">
                      {typeof item.answer === 'string' ? item.answer : item.answer}
                    </Typography>
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      </section>
    </Container>
  );
};

export default SubcontractorFAQ;
