"use client";

import Container from "@components/Container";
import Typography from "@components/Typography";
import Button from "@components/Button";
import Image from "next/image";
import * as styles from "./SubcontractorTestimonialSection.css";
import React from "react";
import Link from "next/link";
import Logo from "@components/Logo/Logo";
import contractorImage from "./contractor.png";
import logotype from "./logotype.svg"

const SubcontractorTestimonialSection = () => {
  return (
    <Container>
      <section className={styles.section}>
        {/* Testimonial Card */}
        <div className={styles.testimonialCard}>
          <div className={styles.logoWrapper}>
            <Image className={styles.logo} src={logotype.src} alt="logo" width={240} height={110} />
          </div>

          <div className={styles.testimonialContent}>
            <Typography variant="bodyMedium" className={styles.testimonialText}>
              &quot;The jobs are well-organised, the team is professional, and best of all, I get paid the next working day — no chasing invoices or waiting weeks for payments.&quot;
            </Typography>

            <div className={styles.testimonialAuthor}>
              <Typography variant="bodyMedium" className={styles.authorName}>
                — <span className={styles.authorNameHighlight}>Roshan</span>, East London
              </Typography>
            </div>
          </div>

          <div className={styles.testimonialImageWrapper}>
            <Image
              src={contractorImage.src}
              alt="Plumber testimonial"
              width={500}
              height={600}
              className={styles.testimonialImage}
              priority
            />
          </div>
        </div>

        {/* Stats Cards */}
        <div className={styles.statsContainer}>
          <div className={styles.statsCard}>
            <Typography variant="bodyMedium" className={styles.statsTitle}>
              Engineers who worked with us made
            </Typography>

            <Typography variant="h2" className={styles.statsHighlight}>
              £4,295
            </Typography>

            <Typography variant="bodySmall" className={styles.statsSubtitle}>
              on average in April 2025
            </Typography>
          </div>

          {/* Vacancies Card */}
          <div className={styles.vacanciesCard}>
            <div className={styles.vacanciesLogoWrapper}>
              <Logo className={styles.vacanciesLogo} />
            </div>

            <Typography variant="h3" className={styles.vacanciesTitle}>
              Act <span className={styles.fast}>Fast!</span>
            </Typography>

            <Typography variant="bodyMedium" className={styles.vacanciesText}>
              we only have 2 vacancies<br />available in London.
            </Typography>
          </div>
        </div>

        {/* CTA Button */}
        <div className={styles.ctaContainer}>
          <Button
            as={Link}
            href="/become-subcontractor"
            className={styles.ctaButton}
            isAnimated
          >
            Join Our Team
          </Button>
        </div>
      </section>
    </Container>
  );
};

export default SubcontractorTestimonialSection;