import React from "react";
import * as styles from "./FormSidebar.css";
import Logo from "@components/Logo/Logo";
import Image from "next/image";
import contractor from "../contractorOnboardingPhoto.png"; 
import thirdlStepImage from "../referalPhoto.png"; 
import secondStepImage from "../EarningsPhoto.png"; 
import classNames from "classnames";
import {finalStepImage} from "./FormSidebar.css";

interface FormSidebarProps {
  currentStep: number;
}

const FormSidebar: React.FC<FormSidebarProps> = ({ currentStep }) => {
  let content: JSX.Element | null = null;

  switch (currentStep) {
    case 1:
    case 4:
      content = (
        <>
          <h1 className={styles.heading}>
            <span className={styles.regularText}><b>Consistent</b> Work.</span>
            <br />
            <span className={styles.highlightText}>No Admin.</span>
            <br />
            <span className={styles.regularText}>No Hassle.</span>
            <br />
            <span className={styles.highlightText}><b>Fast</b> Payments.</span>
          </h1>

          <div className={styles.imageContainer}>
            <Image
              src={contractor}
              width={400}
              height={400}
              alt="contractor"
              className={styles.plumberImage}
            />
          </div>
        </>
      );
      break;

    case 2:
      content = (
        <>
          <h1 className={styles.heading}>
            <span className={styles.highlightText}>We Handle</span>
            <br />
            <span className={styles.highlightText}>The Admin,</span>
            <br />
            <span className={styles.regularText}>So You Can Focus</span>
            <br />
            <span className={styles.regularText}>On Earning</span>
          </h1>

          <div className={styles.imageContainer}>
            <Image
              src={secondStepImage}
              width={400}
              height={400}
              
              alt="final-step"
              className={styles.plumberImage}
            />
          </div>
        </>
      );
      break;

    case 3:
      content = (
        <>
          <h1 className={styles.heading}>
            <span className={classNames(styles.regularText, styles.italicText)}>Refer</span> a Friend.
            <br />
            <span className={styles.highlightText}>Earn £1000.</span>
            <br />
            <span className={styles.regularText}>
              <span className={classNames(styles.texBold, styles.italicText)}>No</span> Limits.
            </span>    
            <br />    <br />
          </h1>

          <div className={styles.imageContainer}>
            <Image
              src={thirdlStepImage}
              width={400}
              height={400}
              
              alt="final-step"
              className={styles.finalStepImage}
            />
          </div>
        </>
      );
      break;

    default:
      content = null;
  }

  return (
    <div className={styles.sidebar}>
      <div className={styles.logoContainer}>
        <Logo className={styles.logo} />
      </div>

      <div className={styles.content}>
        {content}
      </div>
    </div>
  );
};

export default FormSidebar;
