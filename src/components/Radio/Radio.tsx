"use client";
import { ChangeEventHand<PERSON>, forwardRef, useCallback, useState } from "react";
import * as style from "./Radio.css";
import classNames from "classnames";
import RadioProps from "./Radio.types";
import Typography from "../Typography";

const Radio = forwardRef<HTMLInputElement, RadioProps>(
  (
    { onChange, checked, children, ...radioProps }, ref
  ) => {
    const [isChecked, setIsChecked] = useState(
      false
    );

    const changeHandler = useCallback<ChangeEventHandler<HTMLInputElement>>(
      (
        e
      ) => {
        onChange ? onChange(
          e
        ) : setIsChecked(
          (
            prev
          ) => !prev
        );
      },
      [onChange]
    );

    const checkedValue = checked ?? isChecked;

    return (
      <label
        className={classNames(
          style.container, radioProps.className
        )}
      >
        <input
          {...radioProps}
          ref={ref}
          checked={checked}
          onChange={changeHandler}
          className={style.radioInput}
          type="radio"
        />
        <div
          className={style.radioWrapper}
        >
          <span
            className={classNames(
              style.radio.base, {
                [style.radio.checked]: checkedValue,
              }
            )}
            aria-hidden={true}
          />
        </div>
        {!!children && (
          <Typography
            className={style.labelText[checkedValue ? "checked" : "default"]}
            as={"div"}
            variant="bodySmall"
          >
            {children}
          </Typography>
        )}
      </label>
    );
  }
);

export default Radio;
