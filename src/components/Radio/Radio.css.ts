import { theme } from "@/styles/themes.css";
import { style, styleVariants } from "@vanilla-extract/css";

export const radioInput = style(
  {
    clip: "rect(0 0 0 0)",
    clipPath: "inset(50%)",
    height: 1,
    overflow: "hidden",
    position: "absolute",
    whiteSpace: "nowrap",
    width: 1,
  }
);

export const container = style(
  {
    cursor: "pointer",
    display: "flex",
    columnGap: 8,
  }
);

const radioBase = style(
  {
    width: 16,
    height: 16,
    border: `2px solid ${theme.colors.primary.castletonGreen}`,
    transition: "all 150ms",
    borderRadius: "50%",
    display: "flex",
    boxSizing: "border-box",
    marginTop: "0.1em",
  }
);

export const radioWrapper = style(
  {}
);

export const radio = styleVariants(
  {
    base: [radioBase],
    checked: {
      border: `5px solid ${theme.colors.primary.softWhite}`,
      background: theme.colors.primary.castletonGreen,
    },
  }
);

const labelTextBase = style({
  paddingTop: 1
}); 

const labelChecked = style(
  {
    color: theme.colors.primary.softWhite,
  }
);


export const labelText = styleVariants({
  default: [labelTextBase],
  checked: [labelTextBase,labelChecked]
});

