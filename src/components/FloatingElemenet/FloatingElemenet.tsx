"use client";

import { FC, PropsWithChildren, useLayoutEffect, useRef, useState } from "react";
import * as styles from "./FloatingElemenet.css";

const FloatingElemenet: FC<PropsWithChildren> = (
  { children }
) => {
  const [isFirstRender, setIsFirstRender] = useState(true);

  const wrapperRef = useRef<HTMLDivElement | null>(
    null
  );
  const contentRef = useRef<HTMLDivElement | null>(
    null
  );

  useLayoutEffect(
    (
    ) => {
      if (isFirstRender) {
        setIsFirstRender(false);
        return;
      };

      const wrapper = wrapperRef.current;
      const content = contentRef.current;

      if (!wrapper || !content) return;

      setTimeout(
        (
        ) => {
          wrapper.style.width = `${content.clientWidth}px`;
          wrapper.style.height = `${content.clientHeight}px`;
          wrapper.style.display = "block",

          content.classList.add(
            styles.content
          );
          content.style.top = `${wrapper.offsetTop}px`;
          content.style.left = `${wrapper.offsetLeft}px`;
        }
      );

      const handleFinishTransition = (
      ) => {
        wrapper.style.removeProperty(
          "display"
        );

        content.classList.remove(
          styles.content
        );
      };

      content.addEventListener(
        "transitionend", handleFinishTransition
      );

      window.addEventListener("resize", handleFinishTransition);

      return (
      ) => {
        window.removeEventListener("resize", handleFinishTransition);

        content.removeEventListener(
          "transitionend", handleFinishTransition
        );
      };
    },
  );

  return (
    <>
      <div
        className={styles.wrapper}
        ref={wrapperRef}
      />
      <div
        ref={contentRef}
      >
        {children}
      </div>
    </>
  );
};

export default FloatingElemenet;