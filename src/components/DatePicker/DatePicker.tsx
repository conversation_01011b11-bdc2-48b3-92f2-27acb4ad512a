"use client";
import { FC, forwardRef, memo, useState } from "react";
import ReactDatePicker, { ReactDatePickerProps } from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";
import * as styles from "./DatePicker.css";
import * as inputStyles from "../TextInput/TextInput.css";
import DatePickerProps, { DatePickerInputProps } from "./DatePicker.types";
import Typography from "../Typography";
import classNames from "classnames";
import TextInput from "../TextInput";
import { createDateIntl } from "@/utils/helpers";
import MemoChevronIcon from "@/assets/icons/ChevronIcon";
import MemoCalendarIcon from "@/assets/icons/CalendarIcon";

const dateIntl = createDateIntl();

const DatePickerInputSuffix = () => (
  <div
    className={styles.inputSuffix}
  >
    <MemoCalendarIcon />
  </div>
);

const DatePickerInput = forwardRef<HTMLInputElement, DatePickerInputProps>(
  (props, ref) => {
    const { error, value, ...restProps } = props;
    const formattedValue = value ? dateIntl.format(new Date(value)) : "";

    return (
      <TextInput
        ref={ref}
        inputMode="none"
        renderSuffix={DatePickerInputSuffix}
        error={error}
        className={styles.input}
        {...restProps}
        value={formattedValue}
        onChange={() => null}
      />
    );
  }
);

// const monthFormatter = new Intl.DateTimeFormat('en', { month: 'long' });
const yearFormatter = new Intl.DateTimeFormat("en", {
  month: "long",
  year: "numeric",
});

const formatWeekDay = (nameOfDay: string) => (
  <Typography
    as={"div"}
    variant="bodyMedium"
  >
    {nameOfDay.slice(0, 3)}
  </Typography>
);

const renderDayContents = (num: number) => (
  <Typography
    as={"div"}
    variant="bodySmall"
  >
    {num}
  </Typography>
);

const Header: ReactDatePickerProps["renderCustomHeader"] = ({
  date,
  decreaseMonth,
  increaseMonth,
  prevMonthButtonDisabled,
  nextMonthButtonDisabled,
}) => {
  // const month = monthFormatter.format(date);
  // const year = date.getFullYear();
  const monthYear = yearFormatter.format(date);
  return (
    <div
      className={styles.headerContainer}
    >
      <button
        type="button"
        disabled={prevMonthButtonDisabled}
        onClick={decreaseMonth}
        className={styles.changeMonth}
        aria-label="Previous Month"
      >
        <MemoChevronIcon
          turn="left"
        />
      </button>
      <Typography
        variant="subTitleMedium"
      >{monthYear}</Typography>
      <button
        type="button"
        disabled={nextMonthButtonDisabled}
        onClick={increaseMonth}
        className={styles.changeMonth}
        aria-label="Next Month"
      >
        <MemoChevronIcon
          turn="right"
        />
      </button>
    </div>
  );
};

const DatePicker = forwardRef<HTMLInputElement, DatePickerProps>(
  (
    {
      error,
      label,
      description,
      placeholder = "Choose date",
      value,
      ...datePickerProps
    },
    ref
  ) => {
    // TODO: Remove this state becuase it should work without it
    // Instead we need to use  "value" in DatePickerInput
    // datepicker should pass it in props everyime we change a date
    // but it is always empty and IDK why

    return (
      <div
        className={styles.calendarContainer}
      >
        <ReactDatePicker
          {...datePickerProps}
          selected={value}
          placeholderText={placeholder}
          popperClassName={styles.popper}
          calendarClassName={styles.calendar}
          wrapperClassName={styles.wrapper}
          showPopperArrow={false}
          renderCustomHeader={Header}
          formatWeekDay={formatWeekDay}
          renderDayContents={renderDayContents}
          customInput={
            <DatePickerInput
              ref={ref}
              label={label}
              description={description}
              error={error}
              // onClearValues={onClearValues}
            />
          }
        />
      </div>
    );
  }
);

export default memo(DatePicker);
