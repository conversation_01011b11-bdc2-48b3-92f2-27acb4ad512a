import TextInputProps from "../TextInput/TextInput.types";
import { ActionMeta, Props, SingleValue } from "react-select";
import { ReactDatePickerProps } from "react-datepicker";

type DatePickerProps = {
  placeholder?: string;
  value?: Date | null;
} & Pick<
  ReactDatePickerProps,
  "minDate" | "maxDate" | "selected" | "onChange"
> &
  Pick<TextInputProps, "error" | "label" | "description">;

export type DatePickerInputProps = {
  value?: Date | null;
  onClick?: () => void;
  onClearValues?: () => void;
} & Pick<
  TextInputProps,
  "placeholder" | "disabled" | "description" | "label" | "error"
>;

export default DatePickerProps;
