import { theme } from "@/styles/themes.css";
import { globalStyle, style, styleVariants } from "@vanilla-extract/css";
import { bodySmall, bodyText } from "../Typography/Typography.css";
import { breakpoints } from "@/styles/constants.css";

export const calendarContainer = style({
  display: "grid",
});

export const popper = style({
  zIndex: 9999,
  boxShadow: "0px 2px 20px 0px rgba(128, 125, 121, 0.15)",
});

export const calendar = style({
  border: 0,
  borderRadius: 8,
  padding: 12,
  "@media": {
    [breakpoints.tablet]: {
      padding: 40,
    },
  },
});

export const wrapper = style({
  width: "100%",
});

export const input = style({
  caretColor: "transparent",
});

globalStyle(`${calendar} .react-datepicker__header`, {
  background: "transparent",
  color: theme.colors.primary.castletonGreen,
  border: 0,
  padding: 0,
});

globalStyle(
  `${calendar} .react-datepicker__day-names, .react-datepicker__week`,
  {
    display: "flex",
    columnGap: 8,
  }
);
globalStyle(`${calendar} .react-datepicker__month`, {
  margin: 0,
});

globalStyle(`${calendar} .react-datepicker__day-name`, {});

globalStyle(
  `${calendar} .react-datepicker__day.react-datepicker__day--outside-month`,
  {
    color: theme.colors.grayscale[200],
  }
);

globalStyle(`${calendar} .react-datepicker__day-name, .react-datepicker__day`, {
  border: 0,
  width: 36,
  height: 36,
  "@media": {
    [breakpoints.tablet]: {
      width: 48,
      height: 48,
    },
  },
  margin: 0,
  display: "flex",
  alignItems: "center",
  justifyContent: "center",
  color: theme.colors.primary.castletonGreen,
});

globalStyle(
  `${calendar} .react-datepicker__day.react-datepicker__day--selected`,
  {
    color: theme.colors.primary.ivory,
    background: theme.colors.primary.castletonGreen,
  }
);
globalStyle(
  `${calendar} .react-datepicker__day.react-datepicker__day--disabled`,
  {
    color: theme.colors.grayscale[100],
    // background: theme.colors.primary.castletonGreen,
  }
);

globalStyle(
  `${calendar} .react-datepicker__day:hover:not(.react-datepicker__day--selected), .react-datepicker__day--keyboard-selected:not(.react-datepicker__day--selected)`,
  {
    background: theme.colors.primary.ivory,
  }
);

// export const weekDay = style()

export const headerContainer = style({
  display: "flex",
  columnGap: 24,
  justifyContent: "center",
  alignItems: "center",
  background: "transparent",
  paddingBottom: 24,
});

export const changeMonth = style({
  display: "flex",
  cursor: "pointer",
  border: 0,
  background: "transparent",
  fontSize: 24,
});

export const inputSuffix = style({
  paddingRight: 24,
  display: "flex",
  alignItems: "center",
  fontSize: 20,
});
