"use client";

import useStore from "@/hooks/useStore";
import { gridSprinkle } from "@/styles/sprinkles.css";
import { observer } from "mobx-react-lite";
import Image from "next/image";
import Link from "next/link";
import { FC, useState } from "react";
import { Controller, useForm } from "react-hook-form";
import AuthLayout from "../AuthLayout";
import Button from "../Button";
import TextInput from "../TextInput";
import Typography from "../Typography";
import * as styles from "./ForgotPage.css";
import PreviewImage from "./preview.png";
import { EMAIL_CONTROLLER_RULES, EMAIL_INPUT_PROPS } from "@/utils/constants";

interface FormValues {
  email: string;
}

const ForgotPage: FC = observer(() => {
  const { auth } = useStore();

  const [error, setError] = useState<string | null>();

  const form = useForm<FormValues>({
    mode: "onBlur",
    defaultValues: {
      email: "",
    }
  });

  const handleLogIn = async (data: FormValues) => {
    try {
      setError(null);

      await auth.sendResetPasswordConfirmationToEmail(data.email);
    } catch (error) {
      setError(String(error));
    }
  };

  return (
    <form
      onSubmit={form.handleSubmit(
        handleLogIn
      )}
    >
      <AuthLayout
        title={(
          <>
          Home
            {" "}
            <Typography
              as="span"
              variant="h2"
            >
            Safety
            </Typography>
            <br />
          Matters
          </>
        )}
        contentTitle={form.formState.isSubmitSuccessful ? (
          <>
            Check Your <b>Email</b>
          </>
        ) : (
          <>
            Forgot
            {" "}
            <b>Password</b>
          </>
        )}
        contentSubtitle={error ?? (form.formState.isSubmitSuccessful ? "Lorem ipsum dolor sit ametolil col consectetur adipiscing lectus a mauris scelerisque sed." : "Please enter your email address. You will receive a link to create a new password via email.")}
        content={form.formState.isSubmitSuccessful ? null : (
          <div
            className={gridSprinkle({ type: "grid" })}
          >
            <div
              className={gridSprinkle({ type: "item", cols: 10 })}
            >
              <Controller
                rules={{
                  required: "Required!",
                  ...(EMAIL_CONTROLLER_RULES),
                }}
                name="email"
                control={form.control}
                render={(
                  { field, fieldState }
                ) => (
                  <TextInput
                    {...EMAIL_INPUT_PROPS}
                    {...field}
                    error={fieldState.error?.message}
                    className={styles.field}
                  />
                )}
              />
            </div>
          </div>
        )}
        contentActions={form.formState.isSubmitSuccessful ? (
          <Button
            as={Link}
            href="/login"
            className={styles.submitButton}
          >
              Go to log in page
          </Button>        
        ) : (
          <Button
            isLoading={form.formState.isSubmitting}
            className={styles.submitButton}
            disabled={!form.formState.isValid || form.formState.isLoading || form.formState.isSubmitting}
            type="submit"
          >
              Reset Password
          </Button>
        )}
        image={(
          <Image
            {...PreviewImage}
            objectFit="contain"
            objectPosition="center"
            alt="Preview"
          />
        )}
      />    
    </form>
  );
});

export default ForgotPage;