

import classNames from "classnames";
import * as styles from "./Loader.css";

const Loader = () => {
  return (<>
    <svg
      className={styles.gooeyFilter}
      xmlns="http://www.w3.org/2000/svg"
      version="1.1"
    >
      <defs>
        <filter
          id="goo"
        >
          <feGaussianBlur
            in="SourceGraphic"
            stdDeviation="5"
            result="blur"
          />
          <feColorMatrix
            in="blur"
            type="matrix"
            values="1 0 0 0 0  0 1 0 0 0  0 0 1 0 0  0 0 0 19 -9"
            result="goo"
          />
          <feComposite
            in="SourceGraphic"
            in2="goo"
            operator="atop"
          />
        </filter>
      </defs>
    </svg>
    <div
      className={classNames(styles.preloaderNewtonGooey)}
      aria-label="Loading..."
    >
      <div
        className={styles.preloaderInner}
        role="presentation"
      >
        <span
          className={classNames(styles.dot, styles.extraDot)}
        ></span>
        <span
          className={classNames( styles.dot, styles.mainDot)}
        ></span>
        <span
          className={classNames(styles.dot, styles.mainDot)}
        ></span>
        <span
          className={classNames(styles.dot,  styles.mainDot)}
        ></span>
      </div>
    </div>
  </>

  );
};

export default Loader;