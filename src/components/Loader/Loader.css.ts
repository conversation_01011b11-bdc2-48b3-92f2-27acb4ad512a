import { theme } from "@/styles/themes.css";
import { keyframes, style } from "@vanilla-extract/css";

// const dotKeyFrame = keyframes({
//   "50%": {
//     transform:"translateX(96px)"
//   }

// });

// const dotsKeyFrame = keyframes({
//   "50%": {
//     transform: "translateX(-31px)"

//   }
// });
// export const container = style({
//   position: "relative",
//   height:"40px",
// });

// export const gooey = style({
//   position: "absolute",
//   top:"50%",
//   left:"50%",
//   width:"142px",
//   height:"40px",
//   margin:"-20px 0 0 -71px",
//   background: theme.colors.primary.castletonGreen,
//   filter:"contrast(10)",
// });


// export const dot = style({
//   position: "absolute",
//   width: "16px",
//   height: "16px",
//   top: "12px",
//   left: "15px",
//   filter: "blur(4px)",
//   background: theme.colors.primary.ivory,
//   borderRadius: "50%",
//   transform: "translateX(0)",
//   animation: `${dotKeyFrame} 2.8s infinite`,
// });
// export const dots = style({
//   transform:"translateX(0)",
//   marginTop:"12px",
//   marginLeft:"31px",
//   animation:`${dotsKeyFrame} 2.8s infinite`,
// });
// export const staticDot = style({
//   display:"block",
//   float:"left",
//   width:"16px",
//   height:"16px",
//   marginLeft:"16px",
//   filter:"blur(4px)",
//   background: theme.colors.primary.ivory,
//   borderRadius:" 50%",
// });

 

// /* HTML: <div class="loader"></div> */

// const keyframe1 =   keyframes(  {
//   "0%,24%":  {backgroundPosition: "25% 0,75% 0"},
//   "40%":     {backgroundPosition: "25% 0,85% 0"},
//   "50%,72%": {backgroundPosition: "25% 0,75% 0"},
//   "90%":     {backgroundPosition: "15% 0,75% 0"},
//   "100%":    {backgroundPosition: "25% 0,75% 0"},
// });

// const keyframe2 = keyframes({
//   "100%": {transform: "translate(0.1px)"}
// });


// export const loader = style({
//   width: "88px",
//   height: "12px",
//   background: `no-repeat radial-gradient(farthest-side,${theme.colors.primary.ivory} 94%, #F5F1E800) 25% 0, no-repeat radial-gradient(farthest-side,${theme.colors.primary.ivory} 94%,#F5F1E800) 75% 0`,
//   backgroundSize: "12px 12px",
//   position: "relative",
//   animation: `${keyframe1} 1s linear infinite`,
//   selectors: {
//     ["&:before"]: {
//       content: "",
//       position: "absolute",
//       height: "12px",
//       aspectRatio: "1",
//       borderRadius: "50%",
//       background: theme.colors.primary.ivory,
//       inset: "0",
//       margin: "auto",
//       animation: `${keyframe2} 1s cubic-bezier(0.5,300,0.5,-300) infinite`,
//     }
//   }
// });



const preloaderWidth =  "1em";
const dotWidth = "0.1428em";
const dotMargin = dotWidth;

const newtonGooeyExtraDotAnimation = keyframes( {
  "50%": {
    transform: `translateX(calc(${dotWidth} * 2 + ${dotMargin} * 4))`
  }
});
const newtonGooeyMainDotAnimation = keyframes ( {
  "50%" :{
    transform: `translateX(calc(-1 * (${dotWidth} + ${dotMargin})))`
  }
});

// This style is only relevant if you must put the SVG filter in your HTML
export const gooeyFilter = style( {
  position: "absolute",
  left: "-9000px",
  top: "-9000px",
});

// Remove this parent

export const preloaderNewtonGooey = style( {
  width: preloaderWidth,
});


export const preloaderInner = style({
  display: "flex",
  alignItems: "center",
  justifyContent: "flex-end",
  filter: "url(\"#goo\")",
});

export const dot = style({
  backgroundColor: "currentColor",

  display: "block",
  width: dotWidth,

  borderRadius: "100%",

  animation: "none 2.8s infinite",
  transform: "translateX(0)",

  selectors: {
    ["&:not(:first-of-type)"]: {
      marginLeft: dotMargin
    },
  
    ["&::before"]: {
      content: "",
      display: "block",
      width: "100%",
      paddingTop: "100%",
    }
  }
  
});

export const extraDot = style( {
  animationName: newtonGooeyExtraDotAnimation,
});
export const mainDot = style( {
  animationName: newtonGooeyMainDotAnimation,
});