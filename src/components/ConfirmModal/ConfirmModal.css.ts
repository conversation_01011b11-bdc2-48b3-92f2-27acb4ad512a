import { breakpoints } from "@/styles/constants.css";
import { style } from "@vanilla-extract/css";



export const content = style({
  display: "grid",
  maxWidth: 490,
  rowGap: 40,
});

export const title = style({
  wordBreak: "break-all"
});

export const subTitle = style({});

export const buttonsWrapper = style({
  display: "grid",
  gridTemplateColumns: "repeat(2,1fr)",
  columnGap: 8,
  marginTop: 24,
  "@media": {
    [breakpoints.tablet]: {
      columnGap: 24,
    }
  }
});
