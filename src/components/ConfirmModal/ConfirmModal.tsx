

import { FC } from "react";
import * as styles from "./ConfirmModal.css";
import Props from "./ConfirmModal.types";
import Modal from "../Modal";
import Typography from "../Typography";
import Button from "../Button";
import ButtonProps from "../Button/Button.types";

const defaultAcceptButtonProps: ButtonProps<any> = {
  variant: "filled",
  noMinWidth: true,
  children: "Accept"
};

const defaultCancelButtonProps: ButtonProps<any> = {
  variant: "outlined",
  color: "error",
  noMinWidth: true,
  children: "Cancel"

};

const LogoutConfirmationModal: FC<Props> = ({  children,
  title,
  hideCancelButton = false,
  hideAcceptButton = false,
  acceptButtonProps = defaultAcceptButtonProps,
  cancelButtonProps = defaultCancelButtonProps, 
  ...modalProps 
}) => {

  return (
    <Modal
      {...modalProps}
    >
      <div
        className={styles.content}
      >
        <Typography
          fontFamily="primary"
          className={styles.title}
          variant="h4"
        >
          {title}
        </Typography>
        <Typography
          className={styles.subTitle}
        >
          {children}
        </Typography>
        <div
          className={styles.buttonsWrapper}
        >
          {!hideCancelButton && <Button
            noMinWidth
            {...cancelButtonProps}
          />}
          {!hideAcceptButton && <Button
            noMinWidth
            {...acceptButtonProps }
          />}
        </div>
      </div>
    </Modal>
  );
};

export default LogoutConfirmationModal;