import { ReactNode } from "react";
import ModalProps from "../Modal/Modal.types";
import ButtonProps from "../Button/Button.types";


type ConfirmModalProps = {
  children: ReactNode,
  title: ReactNode,
  cancelButtonProps?: ButtonProps<any>
  acceptButtonProps?: ButtonProps<any>
  hideCancelButton?: boolean
hideAcceptButton?: boolean

} & Omit<ModalProps, "children">;

export default ConfirmModalProps;