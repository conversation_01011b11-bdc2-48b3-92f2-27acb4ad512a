import { FC, Fragment } from "react";
import * as styles from "./Steps.css";
import Typography from "../Typography";
import classNames from "classnames";

interface Props {
  options: Array<{
    value: string
    label: string
  }>
  value?: string
}

const Steps: FC<Props> = ({ options, value = options[0].value }) => {
  return (
    <div
      className={styles.root}
    >
      {options.map((option, index) => (
        <Fragment
          key={option.value}
        >
          <div
            className={classNames(styles.step, {
              [styles.isActive]: index <= options.findIndex((opt) => opt.value === value),
            })}
          >
            <Typography
              className={styles.stepLabel}
              variant="noteMedium"
            >
              {option.label}
            </Typography>
          </div>
          {index < options.length - 1 && (
            <div
              className={classNames(styles.divider, {
                [styles.isActive]: index < options.findIndex((opt) => opt.value === value),
              })}
            />
          )}
        </Fragment>
      ))}
    </div>
  );
};

export default Steps;