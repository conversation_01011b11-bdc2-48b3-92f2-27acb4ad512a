import { theme } from "@/styles/themes.css";
import { style } from "@vanilla-extract/css";

export const root = style({
  display: "flex",
  alignItems: "center",
  marginBottom: 32,
});

export const isActive = style({});

export const step = style({
  width: 32,
  aspectRatio: "1 / 1",
  borderRadius: "50%",
  border: `1px solid ${theme.colors.grayscale[100]}`,
  display: "flex",
  alignItems: "center",
  justifyContent: "center",
  color: theme.colors.primary.castletonGreen,

  selectors: {
    [`&${isActive}`]: {
      backgroundColor: theme.colors.primary.castletonGreen,
      borderColor: theme.colors.primary.castletonGreen,
      color: theme.colors.primary.softWhite,
    }
  }
});

export const stepLabel = style({
  lineHeight: 1
});

export const divider = style({
  height: 1,
  backgroundColor: theme.colors.grayscale[100],
  width: 24,

  selectors: {
    [`&${isActive}`]: {
      backgroundColor: theme.colors.primary.castletonGreen,
    }
  }
});