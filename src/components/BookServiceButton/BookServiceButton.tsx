"use client";

import Button from "../Button";
import useStore from "@/hooks/useStore";
import BookServiceButtonProps from "./BookServiceButton.types";

const BookServiceButton = <P extends {},>({children, isAnimated = true, ...props}: BookServiceButtonProps<P>) => {
  const { landign } = useStore();
  // @ts-ignore
  return <Button
    onClick={() => landign.setBookingModalIsOpen(true)}
    isAnimated={isAnimated}
    {...props}
         >{children || "Book an Expert"}</Button>;
};

export default BookServiceButton;