"use client";

import FullscreenIcon from "@/assets/icons/FullscreenIcon";
import PlayIcon from "@/assets/icons/PlayIcon";
import { FC, PointerEventHandler, PropsWithChildren, useEffect, useLayoutEffect, useMemo, useRef, useState } from "react";
import Typography from "../Typography";
import * as styles from "./Video.css";
import Props from "./Video.types";
import classNames from "classnames";
import PauseIcon from "@/assets/icons/PauseIcon";
import dayjs from "dayjs";
import { gridSprinkle } from "@/styles/sprinkles.css";
import Range from "../Range";
import Modal from "../Modal";

const Video: FC<PropsWithChildren<Props>> = ({
  playing = false,
  fullscreen,
  className,
  preview,
  children,
  onEnd,
  description,
  ...restProps
}) => {
  const controlHideTimeout = useRef<NodeJS.Timeout>();
  const [controlIsVisible, setControlIsVisible] = useState(true);
  const [duration, setDuration] = useState(0);
  const [progress, setProgress] = useState(0);
  const [isPlaying, setIsPlaying] = useState(playing);
  const videoRef = useRef<HTMLVideoElement>(null);
  const [inFullscreen, setInFullscreen] = useState(playing);

  useEffect(() => {
    if (playing) handlePlayVideo();
    else setIsPlaying(false);
  }, [playing]);

  useLayoutEffect(() => {
    if (!videoRef.current) return;

    const updateTime = () => {
      if (!videoRef.current) return;

      setProgress(videoRef.current.currentTime);

      setDuration(videoRef.current.duration);

      if (videoRef.current.currentTime === videoRef.current.duration) {
        if (isPlaying) onEnd?.();

        setIsPlaying(false);
        handleShowControl();
      }
    };

    videoRef.current.addEventListener("timeupdate", updateTime);

    if (isPlaying) {
      videoRef.current.play();
    } else {
      videoRef.current.pause();
    }

    return () => {
      videoRef.current?.removeEventListener("timeupdate", updateTime);
    };
  }, [isPlaying]);
  
  const handleShowControl = () => {
    clearTimeout(controlHideTimeout.current);

    setControlIsVisible(true);

    controlHideTimeout.current = setTimeout(() => {
      setControlIsVisible(false);
    }, 2000);
  };

  const handlePlayVideo = () => {
    setIsPlaying(!isPlaying);

    handleShowControl();
  };

  const handlePointerDown: PointerEventHandler<HTMLDivElement> = (event) => {
    const handlePreventDefault = (event: Event) => {
      event.preventDefault();
      event.stopPropagation();
    };

    const handlePointerUp = () => {
      onEnd?.();

      document.removeEventListener("touchmove", handlePreventDefault);
      window.removeEventListener("pointerup", handlePointerUp);
    };

    document.addEventListener("touchmove", handlePreventDefault, { passive: false });
    window.addEventListener("pointerup", handlePointerUp);
  };

  // const metadataJsonLd = useMemo(() =>createVideoJsonLd({min: Math.floor(duration / 60), sec: duration % 60, description: description || "",name: restProps.title || "",uploadDate: "",url: restProps.src || "" }) , [duration, description, restProps]);

  return (
    <div
      className={classNames(styles.root, className, {
        [styles.rootIsActive]: isPlaying,
        [styles.fullSize]: fullscreen,
      })}
      onMouseMove={handleShowControl}
    >
      <video
        {...restProps}
        muted
        playsInline
        controls={false}
        onLoadedData={() => {
          if (!videoRef.current) return;

          setDuration(videoRef.current.duration);
        }}
        className={styles.video}
        ref={videoRef}
      >
        {children}
      </video>
      {/*{preview && (
        <div
          className={classNames(styles.preview, {
            [styles.previewIsHidden]: (isPlaying && progress > 0) || (!isPlaying && (progress !== 0 || progress !== (videoRef.current?.duration ?? 0))),
          })}
        >
          {preview}
        </div>      
      )}*/}
      <div
        className={styles.controls}
      >
        <div
          onPointerDown={handlePointerDown}
          className={classNames(styles.controlBar, {
            [styles.controlBarIsHidden]: !controlIsVisible,
          })}
        >
          <button
            title="Play Video"
            className={classNames(styles.videoFilledButton, gridSprinkle({ display: { mobile: "none", tablet: "flex" } }))}
            onClick={handlePlayVideo}
          >
            {isPlaying ? <PauseIcon /> : <PlayIcon />}
          </button>
          <Typography
            variant="bodySmall"
          >
            {dayjs().minute(0).second(progress).format("mm:ss")}
          </Typography>
          <Range
            value={progress / (duration / 100)}
            onStart={() => {
              videoRef.current?.pause();
            }}
            onChange={(newValue) => {
              if (!videoRef.current) return;

              const percentOfDuration = duration / 100;

              setProgress(newValue * percentOfDuration);
              videoRef.current.currentTime = newValue * percentOfDuration;
            }}
          />
          <Typography
            variant="bodySmall"
          >
            {dayjs().minute(0).second(duration).format("mm:ss")}
          </Typography>
          <button
            onClick={() => setInFullscreen(!inFullscreen)}
            className={styles.videoTextButton}
          >
            <FullscreenIcon />
          </button>
        </div>
        <button
          title="Play Video"
          className={classNames(styles.playButton, {
            [styles.playButtonIsHidden]: !controlIsVisible && isPlaying,
          })}
          onClick={handlePlayVideo}
        >
          {isPlaying ? <PauseIcon /> : <PlayIcon />}
        </button>      
      </div>
      {!fullscreen && (
        <Modal
          fullHeight
          fullWidth
          open={inFullscreen}
          onClose={() => setInFullscreen(false)}
        >
          <Video
            playing={playing}
            preview={preview}
            fullscreen
            {...restProps}
          >
            {children}
          </Video>
        </Modal>      
      )}
    </div>
  );
};

export default Video;