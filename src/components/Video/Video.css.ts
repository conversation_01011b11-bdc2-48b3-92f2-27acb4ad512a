import { breakpoints } from "@/styles/constants.css";
import { theme } from "@/styles/themes.css";
import { style } from "@vanilla-extract/css";

export const root = style({
  userSelect: "none",
  position: "relative",
  width: "100%",
  aspectRatio: "1.7 / 1",
  borderRadius: 16,
  overflow: "hidden",
  transitionProperty: "transform",
  transitionDuration: "240ms",

  "@media": {
    [breakpoints.tablet]: {
      borderRadius: 24,
    },
  },
});

export const fullSize = style({
  width: "100%",
  height: "100%",
  aspectRatio: "auto",
  borderRadius: 0,
});

export const rootIsActive = style({
});

export const video = style({
  backgroundColor: "#01190F",
  width: "100%",
  height: "100%",
  objectFit: "cover",
  objectPosition: "center",

  selectors: {
    [`${fullSize} &`]: {
      objectFit: "contain",
    }
  }
});

export const controls = style({
  position: "absolute",
  top: 0,
  right: 0,
  left: 0,
  bottom: 0,
});

export const preview = style({
  position: "absolute",
  top: 0,
  bottom: 0,
  left: 0,
  right: 0,
  transitionProperty: "opacity, transform",
  transitionDuration: "180ms",
  opacity: 1,
  transform: "scale(1.3)",
});

export const previewIsHidden = style({
  opacity: 0,
  transform: "scale(1)",
});

export const playButton = style({
  position: "absolute",
  zIndex: 10,
  top: 0,
  bottom: 0,
  right: 0,
  left: 0,
  padding: 0,
  margin: "auto",
  width: 54,
  aspectRatio: "1 / 1",
  borderRadius: "50%",
  backgroundColor: theme.colors.primary.castletonGreen,
  color: theme.colors.primary.softWhite,
  border: "none",
  display: "flex",
  alignItems: "center",
  justifyContent: "center",
  fontSize: 24,
  cursor: "pointer",
  boxShadow: "rgba(0, 0, 0, 0.2) 0px 3px 5px -1px, rgba(0, 0, 0, 0.14) 0px 6px 10px 0px, rgba(0, 0, 0, 0.12) 0px 1px 18px 0px",
  transitionProperty: "transform, opacity",
  transitionDuration: "180ms",

  ":hover": {
    transform: "scale(1.1)",
  }
});

export const playButtonIsHidden = style({
  opacity: 0,
  transform: "scale(0.7)",
});

export const controlBar = style({
  position: "absolute",
  zIndex: 10,
  bottom: 10,
  right: 10,
  left: 10,
  borderRadius: 10,
  backgroundColor: "#F5F1E880",
  height: 40,
  backdropFilter: "blur(20px) saturate(140%)",
  padding: "0 8px",
  display: "flex",
  alignItems: "center",
  gap: 12,
  transitionProperty: "transform",
  transitionDuration: "380ms",

  "@media": {
    [breakpoints.tablet]: {
      height: 48,
      borderRadius: 14,
    },
  },
});

export const controlBarIsHidden = style({
  transform: "scale(0.9) translateY(calc(100% + 20px))",
});

export const videoButton = style({
  height: 32,
  aspectRatio: "1 / 1",
  borderRadius: 8,
  padding: 0,
  fontSize: 20,
  display: "flex",
  alignItems: "center",
  justifyContent: "center",
  border: "none",
  backgroundColor: "transparent",
  cursor: "pointer",
});

export const videoFilledButton = style([videoButton, {
  backgroundColor: theme.colors.primary.castletonGreen,
  color: theme.colors.primary.softWhite,

  ":hover": {
    backgroundColor: theme.colors.primary.asidGreen,
    color: theme.colors.primary.castletonGreen,  
  }
}]);

export const videoTextButton = style([videoButton, {
  color: theme.colors.primary.castletonGreen,

  ":hover": {
    backgroundColor: theme.colors.primary.softWhite,
  }
}]);
