import { breakpoints } from "@/styles/constants.css";
import { theme } from "@/styles/themes.css";
import { createVar, style } from "@vanilla-extract/css";

export const buttonFillColor = createVar(
);
export const buttonContentColor = createVar(
);
export const buttonSize = createVar(
);

export const root = style(
  {
    textDecoration: "none",
    paddingBlock: 0,
    paddingInline: 0,
    fontSize: 16,
    cursor: "pointer",
    display: "flex",
    alignItems: "center",
    justifyContent: "center",
    transitionProperty: "background-color, color",
    transitionDuration: "100ms",
    border: "none",
    borderRadius: 8,
    height: buttonSize,
    aspectRatio: "1 / 1",

    ":disabled": {
      cursor: "inherit",
      vars: {
        [buttonFillColor]: theme.colors.grayscale[100],
        [buttonContentColor]: theme.colors.grayscale[200]
      }
    }
  }
);

export const box = style(
  {
    aspectRatio: "1 / 1",
  }
);

export const circle = style(
  {
    aspectRatio: "1 / 1",
    borderRadius: "50%",
  }
);

export const rect = style(
  {
    aspectRatio: "1.5 / 1",
  }
);

export const primary = style(
  {
    vars: {
      [buttonFillColor]: theme.colors.primary.castletonGreen,
      [buttonContentColor]: theme.colors.primary.softWhite,
    },

    "@media": {
      [breakpoints.tablet]: {
        selectors: {
          "&:not(:disabled):hover": {
            vars: {
              [buttonFillColor]: theme.colors.primary.asidGreen,
              [buttonContentColor]: theme.colors.primary.castletonGreen,
            }
          },
        }
      }
    },

    selectors: {
      "&:not(:disabled):active": {
        vars: {
          [buttonFillColor]: theme.colors.primary.asidGreenPressed,
          [buttonContentColor]: theme.colors.primary.castletonGreenPressed,
        }
      },
    },
  }
);

export const secondary = style(
  {
    vars: {
      [buttonFillColor]: theme.colors.primary.asidGreen,
      [buttonContentColor]: theme.colors.primary.castletonGreen,
    },

    border: `1px solid ${buttonFillColor}`,

    "@media": {
      [breakpoints.tablet]: {
        selectors: {
          "&:not(:disabled):hover": {
            border: `1px solid ${buttonContentColor}`,
            vars: {
              [buttonFillColor]: "transparent",
              [buttonContentColor]: theme.colors.primary.asidGreen,
            },
          },
        }
      }
    },

    selectors: {
      "&:not(:disabled):active": {
        vars: {
          [buttonFillColor]: theme.colors.primary.asidGreenPressed,
          [buttonContentColor]: theme.colors.primary.castletonGreenPressed,
        }
      },
    },
  }
);

export const primaryInverted = style(
  {
    vars: {
      [buttonFillColor]: theme.colors.primary.softWhite,
      [buttonContentColor]: theme.colors.primary.castletonGreen,
    },

    "@media": {
      [breakpoints.tablet]: {
        selectors: {
          "&:not(:disabled):hover": {
            vars: {
              [buttonFillColor]: theme.colors.primary.asidGreen,
              [buttonContentColor]: theme.colors.primary.castletonGreen,
            }
          },
        }
      }
    },

    selectors: {
      "&:not(:disabled):active": {
        vars: {
          [buttonFillColor]: theme.colors.primary.asidGreenPressed,
          [buttonContentColor]: theme.colors.primary.castletonGreenPressed,
        }
      },
    },
  }
);

export const normal = style({
  vars: {
    [buttonSize]: "48px",
  },
  "@media": {
    [breakpoints.tablet]: {
      vars: {
        [buttonSize]: "56px",
      }
    }
  }
});

export const small = style({
  vars: {
    [buttonSize]: "42px",
  }
});

export const filled = style({
  backgroundColor: buttonFillColor,
  color: buttonContentColor,
});

export const outlined = style({
  backgroundColor: "transparent",
  color: buttonFillColor,
  border: `1px solid ${buttonFillColor}`,

  selectors: {
    [`${secondary}&:not(:disabled):hover`]: {
      color: `${buttonContentColor} !important`,
    },
  }
});