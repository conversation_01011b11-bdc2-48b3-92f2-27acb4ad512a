import classNames from "classnames";
import { ForwardedRef, createElement, forwardRef } from "react";
import * as styles from "./IconButton.css";
import Props from "./IconButton.types";

function IconButton<P extends {}>(
  {
    as = "button",
    variant = "filled",
    color = "primary",
    shape = "box",
    size = "normal",
    className,
    children,
    ...restProps
  }: Props<P>,
  ref: ForwardedRef<any>
) {
  
  return createElement(
    as,
    {
      ...(as === "button" ? { type: "button" } : {}),
      className: classNames(
        styles.root,
        styles[variant],
        styles[color],
        styles[shape],
        styles[size],
        className,
      ),
      ref,
      ...restProps as any,
    },
    children,
  );
};

export default forwardRef<any, any>(IconButton);