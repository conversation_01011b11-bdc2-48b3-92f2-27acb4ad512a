import { ButtonHTMLAttributes, DetailedHTMLProps, FunctionComponent } from "react";

interface Props<P> extends DetailedHTMLProps<ButtonHTMLAttributes<HTMLButtonElement>, HTMLButtonElement> {
  as?: "button" | FunctionComponent<P>;
  variant?: "filled" | "outlined"
  size?: "normal" | "small"
  color?: "primary" | "secondary" | "primaryInverted";
  shape?: "box" | "rect" | "circle";
}

export default Props;