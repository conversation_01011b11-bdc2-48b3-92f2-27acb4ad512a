'use client';

import { GoogleMap, Marker, LoadScript, InfoWindow } from '@react-google-maps/api';
import { useEffect, useState } from 'react';
import { boroughs } from "@/utils/constants";
import { useSearchParams } from 'next/navigation';

const containerStyle = {
  width: '100%',
  height: '600px'
};

const center = {
  lat: 51.509865,
  lng: -0.118092
};

export default function CustomMap({ selectedBorough }: { selectedBorough: any }) {
  const [selected, setSelected] = useState<any>(null);
  console.log('selectedBorough', selectedBorough)
  const textColor = "#003D23";

  useEffect(() => {
    if (selectedBorough) {
      setSelected(selectedBorough);
    }
  }, [selectedBorough]);

  return (
    <LoadScript googleMapsApiKey={process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY!}>
      <GoogleMap
        mapContainerStyle={containerStyle}
        center={selectedBorough || center}
        zoom={selectedBorough ? 12 : 10}
      >
        {boroughs.map((borough, idx) => (
          <Marker
            key={idx}
            position={{ lat: borough.lat, lng: borough.lng }}
            onClick={() => setSelected(borough)}
          />
        ))}

        {selected && (
          <InfoWindow
            position={{ lat: selected.lat, lng: selected.lng }}
            onCloseClick={() => setSelected(null)}
          >
            <div>
              <strong style={{ color: textColor }}>{selected.name}</strong>
              <p style={{ color: textColor }}>Postcode: {selected.postcode}</p>
              <a
                href="#"
                onClick={(e) => e.preventDefault()}
                style={{
                  display: 'block',
                  marginTop: '10px',
                  color: textColor,
                  textDecoration: 'none',
                  fontWeight: 'bold'
                }}
              >
                View Location
              </a>
            </div>
          </InfoWindow>
        )}
      </GoogleMap>
    </LoadScript>
  );
}
