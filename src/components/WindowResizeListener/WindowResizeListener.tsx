"use client";

import useStore from "@/hooks/useStore";
import { windowHeight } from "@/styles/global.css";
import { observer } from "mobx-react-lite";
import { usePathname, useRouter, useSearchParams } from "next/navigation";
import { FC, Suspense, useEffect, useLayoutEffect } from "react";

const WindowResizeListenerElement: FC = observer(() => {
  const { alert } = useStore();

  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();

  const codeExist = searchParams?.has("code");

  useEffect(() => {
    if (codeExist && !alert.alerts.some((alert) => alert.content === "Email changed successfully")) {
      alert.addAlert({ content: "Email changed successfully", type: "success" });

      router.replace(pathname ?? "/");
    }
  }, [codeExist]);

  useLayoutEffect(() => {
    const heightVar = windowHeight.replace("var(", "").replace(")", "");
    document.documentElement.style.setProperty(heightVar, window.innerHeight + "px");

    const handleResize = () => {
      document.documentElement.style.setProperty(heightVar, window.innerHeight + "px");
    };

    window.addEventListener("scroll", handleResize);

    return () => {
      window.removeEventListener("scroll", handleResize);
    };
  }, []);

  return null;
});

const WindowResizeListener: FC = () => {
  return (
    <Suspense>
      <WindowResizeListenerElement />
    </Suspense>
  );
};

export default WindowResizeListener;