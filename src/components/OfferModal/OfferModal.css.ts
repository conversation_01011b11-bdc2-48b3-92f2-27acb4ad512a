import { style } from "@vanilla-extract/css";
import { theme } from "@/styles/themes.css";

export const modalContent = style({
  display: "flex",
  flexDirection: "column",
  gap: "1.5rem",
  backgroundColor: theme.colors.primary.softWhite,
  borderRadius: "0.5rem",
  width: "100%",
  maxWidth: "900px",
  margin: "0 auto",
  "@media": {
    "(max-width: 768px)": {
      padding: "1.5rem",
      gap: "1.25rem",
      maxWidth: "90%",
    },
  },
});

export const title = style({
  color: theme.colors.primary.castletonGreen,
  fontSize: "3rem",
  fontFamily: theme.fonts.primary,
  marginBottom: "0.5rem",
  lineHeight: 1.2,
  "@media": {
    "(max-width: 768px)": {
      fontSize: "1.75rem",
    },
  },
});

export const subtitle = style({
  color: theme.colors.primary.castletonGreen,
  fontSize: "1.125rem",
  fontWeight: 500,
  marginBottom: "1rem",
  "@media": {
    "(max-width: 768px)": {
      fontSize: "1rem",
    },
  },
});

export const buttonsContainer = style({
  display: "flex",
  flexDirection: "column",
  gap: "1rem",
  width: "100%",
  "@media": {
    "(min-width: 768px)": {
      flexDirection: "row",
      justifyContent: "space-between",
    },
  },
});

export const emailButton = style({
  backgroundColor: theme.colors.primary.softWhite,
  border: `2px solid ${theme.colors.primary.castletonGreen}`,
});

export const buttonIcon = style({
  position: "relative",
  top: "3px",
  marginRight: "5px"
});

export const description = style({
  marginTop: "15px"
})

export const offerPrice = style({
  color: theme.colors.primary.asidGreen,
});
