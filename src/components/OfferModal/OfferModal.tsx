"use client";
import Modal from "@components/Modal";
import Typography from "@components/Typography";
import * as styles from "./OfferModal.css";
import OfferModalProps from "./OfferModal.types";
import WhatsAppIcon from "@/assets/icons/WhatsAppIcon";
import PhoneIcon from "@/assets/icons/PhoneIcon";
import EmailIcon from "@/assets/icons/EmailIcon";
import Button from "@components/Button";
import { theme } from "@/styles/themes.css";
import FeatureList from "@components/ProtectYourBusiness/components/FeatureList/FeatureList";
import { CallButton, WhatsAppButton, EmailButton } from "@components/ContactButtons";
import { INFO_EMAIL } from "@/utils/constants";

const titleDefault = <div><div>Contact Us </div>to Claim This <span style={{ fontWeight: 500 }}>Offer</span></div>;
const featureList = [
  'Professional Commercial Plumbing Survey',
  'Full Digital + Physical Report For Insurance & Compliance',
  'Guaranteed Appointment Within 7 Days',
  '£200 Rebate Towards Any Remedial Work (If Required)'
];

const OfferModal = ({
  open,
  onClose,
  title = titleDefault,
  subtitle = "Over 50% off your Commercial Survey",
  whatsappNumber,
  phoneNumber,
  email,
  ...modalProps
}: OfferModalProps) => {
  return (
    <Modal
      open={open}
      onClose={onClose}
      fullWidth={true}
      {...modalProps}
    >
      <div className={styles.modalContent}>
        <Typography variant="h2" className={styles.title}>
          {title}
        </Typography>
        <Typography variant="bodyMedium" className={styles.subtitle}>
          <div>{subtitle}</div>
          <div className={styles.description }>
            <s>£420</s> Now <span className={ styles.offerPrice }>£200</span>. Protect Your Property, Prevent Downtime. Act Fast - Limited Availability
          </div>
        </Typography>
        <FeatureList features={featureList}/>

        <div className={styles.buttonsContainer}>
          <WhatsAppButton />
          <CallButton />
          <EmailButton email={email || INFO_EMAIL} />
        </div>
      </div>
    </Modal>
  );
};

export default OfferModal;
