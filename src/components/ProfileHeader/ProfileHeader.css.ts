import { breakpoints } from "@/styles/constants.css";
import { theme } from "@/styles/themes.css";
import { style } from "@vanilla-extract/css";


export const container = style({

});

export const name = style({
  marginBottom: 8,
  "@media": {
    [breakpoints.tablet]: {
      marginBottom: 24,
      display: "block"
    }
  }
});

export const email = style({
  opacity: .8,
  maxWidth: "100%",
  wordBreak: "break-all",
  overflowWrap: "break-word",
  whiteSpace: "normal",
});

export const divLine = style({
  display: "none",
  borderTop: `1px solid ${theme.colors.grayscale[100]}`,
  margin: "32px 0",
  
  "@media": {
    [breakpoints.tablet]: {
      display: "block"
    }
  }
});

export const dropDownWrapper = style({
  display: "flex",
  columnGap: 6,
  margin: "24px 0",
  "@media": {
    [breakpoints.tablet]: {
      
      display: "none"
    }
  }
});