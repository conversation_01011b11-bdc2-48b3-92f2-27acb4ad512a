import Typography from "../Typography";
import ProfileHeaderProps from "./ProfileHeader.types";
import * as styles from "./ProfileHeader.css";
import DropdownInput from "../DropdownInput";
import MemoLogOutIcon from "@/assets/icons/LogOutIcon";
import { observer } from "mobx-react-lite";
import useStore from "@/hooks/useStore";
import IconButton from "../IconButton";
import { useParams, usePathname } from "next/navigation";

const ProfileHeader = observer(
  ({
    fullName = "",
    email = "",
    options = [],
    selected = null,
    onSelectChange,
  }: ProfileHeaderProps) => {
    const pathname = usePathname();

    const shouldDisplayEmail = !pathname?.includes("personal-info");

    const { auth } = useStore();

    const [first, ...rest] = fullName.split(" ");

    return (
      <div>
        <Typography
          variant="h4"
          fontFamily="primary"
          className={styles.name}
        >
          <b>{first}</b> {rest.join(" ")}
        </Typography>
        {shouldDisplayEmail && (
          <>
            <Typography
              className={styles.email}
              variant="bodyMedium"
            >
              {email}
            </Typography>
            <div
              className={styles.divLine}
            />
          </>
        )}
        <div
          className={styles.dropDownWrapper}
        >
          <DropdownInput
            onChange={onSelectChange}
            options={options}
            value={selected}
          />
          <IconButton
            onClick={auth.logout}
            type="button"
            variant="outlined"
          >
            <MemoLogOutIcon />
          </IconButton>
        </div>
      </div>
    );
  }
);

export default ProfileHeader;
