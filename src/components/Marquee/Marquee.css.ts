import { breakpoints } from "@/styles/constants.css";
import { keyframes, style } from "@vanilla-extract/css";
import { h1 } from "../Typography/Typography.css";

export const marquee = style({
  gap: "16px",
  position: "relative",
  display: "flex",
  overflow: "hidden",
  userSelect: "none",
});


const scroll = keyframes({
  from: {
    transform: "translateX(0)"
  },
  to: {
    transform: "translateX(calc(-100% - 16px))",
  }
});

export const marqueeContent = style({
  flexShrink: 0,
  display: "flex",
  justifyContent: "space-around",
  gap: "16px",
  minWidth: "100%",
  animationName: scroll,
  animationDuration: "20s",
  animationTimingFunction: "linear",
  animationIterationCount: "infinite",
  padding: 0,
  margin: 0,

  "@media": {
    "(prefers-reduced-motion: reduce)": {
      animationPlayState: "paused !important"
    },
    // [breakpoints.ultraWide]: {
    //   animationDuration: "10s",
    // }
  }
});


export const marqueeItem = style({
  
});