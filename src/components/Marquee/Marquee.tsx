import { FC, memo, useCallback, useMemo } from "react";

import * as styles from "./Marquee.css";
import MarqueeProps from "./Marquee.types";
import Typography from "../Typography";
import classNames from "classnames";


const Marquee:FC<MarqueeProps> = ({ children, className }) => {


  const list = useMemo(
    () => {
      return new Array(5).fill(0).map((_,idx) => {
        return children;
      });
    },
    [],
  );
  


  return (
    <div
      className={classNames(styles.marquee, className)}
    >
      <ul
        className={styles.marqueeContent}
      >
        {list}
      </ul>
      <ul
        aria-hidden="true"
        className={styles.marqueeContent}
      >
        {list}
      </ul>
    </div>
  );
};

export default memo(Marquee);
