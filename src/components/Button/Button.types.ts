import {
  ButtonHTMLAttributes, DetailedHTMLProps, FunctionComponent,
  ReactNode, AnchorHTMLAttributes
} from "react";

interface ButtonProps<P> extends DetailedHTMLProps<ButtonHTMLAttributes<HTMLButtonElement>, HTMLButtonElement> {
  variant?: "filled" | "outlined";
  size?: "normal" | "small";
  color?: "primary" | "secondary" | "secondaryInverted" | "error";
  shape?: "rect" | "round";
  as?: "button" | "a" | FunctionComponent<P>;
  isAnimated?: boolean;
  isLoading?: boolean;
  startIcon?: ReactNode;
  noMinWidth?: boolean;
  alternateHover?: boolean;
  href?: string;
}

export default ButtonProps;
