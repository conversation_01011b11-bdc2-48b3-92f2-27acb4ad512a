import {
  breakpoints 
} from "@/styles/constants.css";
import {
  theme 
} from "@/styles/themes.css";
import {
  createVar, style 
} from "@vanilla-extract/css";

const buttonHeigth = createVar(
);
const buttonMinWidth = createVar(
);

const buttonFillColor = createVar(
);
const buttonContentColor = createVar(
);

export const root = style(
  {
    userSelect: "none",
    height: "max-content",
    position: "relative",
    border: "none",
    minHeight: buttonHeigth,
    minWidth: buttonMinWidth,
    padding: "0 32px",
    font: "inherit",
    cursor: "pointer",
    display: "flex",
    alignItems: "center",
    justifyContent: "center",
    gap: "8px",
    transitionProperty: "background-color",
    transitionDuration: "100ms",
    textDecoration: "none",

    ":focus": {
      outline: "4px solid rgba(19,249,117,0.2)",
    },

    ":disabled": {
      cursor: "inherit",
      vars: {
        [buttonFillColor]: theme.colors.grayscale[100],
        [buttonContentColor]: theme.colors.grayscale[200]
      }
    }
  }
);

export const isReversed = style(
  {
    flexDirection: "row-reverse",
  }
);

export const normal = style(
  {
    vars: {
      [buttonHeigth]: "46px",
      [buttonMinWidth]: "180px",
    },

    "@media": {
      [breakpoints.tablet]: {
        vars: {
          [buttonHeigth]: "56px",
        },
      }
    },
  }
);

export const small = style(
  {
    vars: {
      [buttonHeigth]: "42px",
      [buttonMinWidth]: "140px",
    }
  }
);

export const round = style({
  borderRadius: buttonHeigth,
});

export const rect = style({
  borderRadius: 8,
});

export const noMinWidth = style({
  vars: {
    [buttonMinWidth]: "none",
  }
});

export const filled = style(
  {
    backgroundColor: buttonFillColor,
    color: buttonContentColor,
  }
);

export const outlined = style(
  {
    backgroundColor: "transparent",
    color: buttonFillColor,
    border: `1px solid ${buttonFillColor}`,

    selectors: {
      "&:not(:disabled):hover": {
        backgroundColor: buttonFillColor,
        color: buttonContentColor,
      },
      "&:not(:disabled):active": {
        backgroundColor: buttonContentColor,
        color: buttonFillColor
      }
    }
  }
);

export const primary = style(
  {
    vars: {
      [buttonFillColor]: theme.colors.primary.asidGreen,
      [buttonContentColor]: theme.colors.primary.castletonGreen,
    },

    "@media": {
      [breakpoints.tablet]: {
        selectors: {
          "&:not(:disabled):hover": {
            vars: {
              [buttonFillColor]: theme.colors.primary.softWhite,
            }
          },
        }
      }
    },

    selectors: {
      "&:not(:disabled):active": {
        vars: {
          [buttonFillColor]: theme.colors.primary.softWhitePressed,
          [buttonContentColor]: theme.colors.primary.castletonGreenPressed,
        }
      },
    },
  }
);

export const alternatePrimaryHover = style({
  selectors: {
    "&:not(:disabled):hover": {
      "@media": {
        [breakpoints.tablet]: {
          vars: {
            [buttonFillColor]: theme.colors.primary.castletonGreen,
            [buttonContentColor]: theme.colors.primary.asidGreen,
          }
        }
      },
    },
  }
});

export const secondary = style(
  {
    vars: {
      [buttonFillColor]: theme.colors.primary.castletonGreen,
      [buttonContentColor]: theme.colors.primary.ivory,
    },

    "@media": {
      [breakpoints.tablet]: {
        selectors: {
          "&:not(:disabled):hover": {
            vars: {
              [buttonFillColor]: theme.colors.primary.asidGreen,
              [buttonContentColor]: theme.colors.primary.castletonGreen,
            }
          },
        }
      }
    },

    selectors: {
      "&:not(:disabled):active": {
        vars: {
          [buttonFillColor]: theme.colors.primary.asidGreenPressed,
          [buttonContentColor]: theme.colors.primary.castletonGreenPressed,
        }
      },
    },
  }
);

export const alternateSecondaryHover = style({
  selectors: {
    "&:not(:disabled):hover": {
      "@media": {
        [breakpoints.tablet]: {
          vars: {
            [buttonFillColor]: theme.colors.primary.softWhite,
            [buttonContentColor]: theme.colors.primary.castletonGreen,
          }
        }
      },
    },
  }
});

export const secondaryInverted = style({
  vars: {
    [buttonFillColor]: theme.colors.primary.ivory,
    [buttonContentColor]: theme.colors.primary.ivory,
  },

  "@media": {
    [breakpoints.tablet]: {
      selectors: {
        "&:not(:disabled):hover": {
          vars: {
            [buttonFillColor]: theme.colors.primary.asidGreen,
            [buttonContentColor]: theme.colors.primary.castletonGreen,
          }
        },
      }
    }
  },

  selectors: {
    "&:not(:disabled):active": {
      vars: {
        [buttonFillColor]: theme.colors.primary.asidGreenPressed,
        [buttonContentColor]: theme.colors.primary.castletonGreenPressed,
      }
    },
  },
});


export const error = style(
  {
    vars: {
      [buttonFillColor]: theme.colors.primary.error,
      [buttonContentColor]: theme.colors.primary.ivory,
    },
  }
);

export const loaderWrapper = style({
  color: buttonContentColor,
  fontSize: 110,
});

export const buttonLabel = style({
  lineHeight: 1 
});