"use client";

import classNames from "classnames";
import { MouseEvent, createElement, useCallback, useState } from "react";
import FloatingElemenet from "../FloatingElemenet";
import Loader from "../Loader";
import Typography from "../Typography";
import * as styles from "./Button.css";
import ButtonProps from "./Button.types";
import NextArrowIcon from "@/assets/icons/NextArrowIcon";

function Button<P extends {}>(
  {
    as = "button",
    color = "primary",
    variant = "filled",
    size = "normal",
    shape = "round",
    className,
    children,
    isAnimated,
    isLoading,
    startIcon,
    noMinWidth,
    alternateHover = false,
    ...restProps
  }: ButtonProps<P> & Omit<P, keyof ButtonProps<P>>
) {
  const [loading, setLoading] = useState(false);

  const [isHovered, setIsHovered] = useState(
    false
  );

  const onMouseEnter = useCallback(
    () => {
      setIsHovered(true);
    },
    [],
  );
  const onMouseLeave = useCallback(
    () => {
      setIsHovered(false);
    },
    [],
  );


  const renderChildren = useCallback(
    () => {

      const typography = (<Typography
        className={styles.buttonLabel}
        variant={size === "normal" ? "buttonMedium" : "buttonSmall"}
      >
        {children}
      </Typography>  );    


      if(isAnimated) {
        return (
          <FloatingElemenet>
            {typography}        
          </FloatingElemenet>
        );
      }
      return typography;

    },
    [children, size, isAnimated],
  );
  
  

  return createElement(
    as,
    {
      ...restProps as any,
      ...(as === "button" ? { type: ((isLoading ?? loading) ? "button" : restProps.type ?? "button") } : {}),
      className: classNames(
        styles.root,
        styles[color],
        styles[variant],
        styles[size],
        styles[shape],
        className,
        {
          [styles.isReversed]: isHovered,
          [styles.noMinWidth]: noMinWidth,
          [styles.alternatePrimaryHover]: color === "primary" && alternateHover,
          [styles.alternateSecondaryHover]: color === "secondary" && alternateHover,
        }
      ),
      onMouseEnter: isAnimated ? onMouseEnter : undefined, 
      onMouseLeave: isAnimated ? onMouseLeave : undefined,
      onClick: (isLoading ?? loading) ? undefined : async (event: MouseEvent<HTMLButtonElement>) => {
        setLoading(true);

        await restProps.onClick?.(event);

        setLoading(false);
      },
    },
    (
      (isLoading ?? loading) ? (
        <div
          className={styles.loaderWrapper}
        >
          <Loader />
        </div>
      ) : (
        <>
          {(isAnimated || startIcon) && (
            <FloatingElemenet>
              {startIcon ?? (
                <NextArrowIcon />
              )}

            </FloatingElemenet>
          )}
          {renderChildren()}
        </>      
      )
    ),
  );
};

export default Button;