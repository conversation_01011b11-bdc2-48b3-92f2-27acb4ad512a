import { FC, memo } from "react";

import styles from "./PlanPurchaseModal.module.scss";
import PlanPurchaseModalProps from "./PlanPurchaseModal.types";
import { EmbeddedCheckout, EmbeddedCheckoutProvider } from "@stripe/react-stripe-js";
import Modal from "../Modal";
import { loadStripe } from "@stripe/stripe-js";

const stripePromise = loadStripe(process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY);

const PlanPurchaseModal:FC<PlanPurchaseModalProps> = ({onClose, customSecret}) => {
  return (
    <Modal
      open={!!customSecret}
      onClose={onClose}
    >
      <div
        style={{ minWidth: 600 }}
      >
        {!!customSecret &&  <EmbeddedCheckoutProvider
          stripe={stripePromise}
          options={{ clientSecret: customSecret }}
                            >
          <EmbeddedCheckout />
        </EmbeddedCheckoutProvider>}
      </div>
    </Modal>  
  );
};

export default memo(PlanPurchaseModal);
