import { breakpoints } from "@/styles/constants.css";
import { theme } from "@/styles/themes.css";
import { keyframes, style } from "@vanilla-extract/css";

export const root = style({
  position: "fixed",
  zIndex: 9999,
  bottom: 0,
  left: 0,
  padding: 20,
  gap: 8,
  display: "flex",
  flexDirection: "column",
  alignItems: "center",
  justifyContent: "flex-end",
  pointerEvents: "none",
  height: "100%",
  width: "100%",

  "@media": {
    [breakpoints.tablet]: {
      alignItems: "flex-start",
      padding: 32,
      gap: 12,
      width: "auto",
    }
  }
});

const appear = keyframes({
  "0%": {
    transform: "translateY(150%) scale(0.6)",
  },
  "10%": {
    transform: "translateY(0px) scale(1.05)",
  },
  "90%": {
    opacity: 1,
    transform: "translateY(0px) scale(1)",
  },
  "100%": {
    opacity: 0,
    transform: "translateY(150%) scale(0.6)",
  }
});

export const alert = style({
  transformOrigin: "bottom center",
  position: "relative",
  borderRadius: 16,
  boxShadow: "rgba(0, 0, 0, 0.2) 0px 3px 5px -1px, rgba(0, 0, 0, 0.14) 0px 6px 10px 0px, rgba(0, 0, 0, 0.12) 0px 1px 18px 0px",
  backgroundColor: theme.colors.primary.softWhite,
  padding: "20px 32px 20px 20px",
  animation: `${appear} 5s forwards`,
  pointerEvents: "visible",
  margin: "0 auto",
  display: "flex",
  flexDirection: "column",
  alignItems: "stretch",
  width: "90vw",
  minWidth: "90%",
  maxWidth: "90%",

  "@media": {
    [breakpoints.tablet]: {
      alignItems: "flex-start",
      borderRadius: 24,
      padding: "32px 48px 32px 32px",

      margin: 0,
      minWidth: 380,
      maxWidth: "max-content",
      width: "fit-content"
    }
  }
});

export const error = style({
  color: theme.colors.primary.error,
});

export const success = style({
  color: theme.colors.primary.castletonGreen,
});

export const info = style({
});

export const alertTitle = style({
  fontFamily: theme.fonts.primary,
  marginBottom: 20,
});

export const alertInfo = style({
  color: theme.colors.primary.castletonGreen,
  wordBreak: "break-all",
  // overflowWrap: "break-word",
  whiteSpace: "normal",
});

export const alertCloseButton = style({
  top: 12,
  right: 12,
  position: "absolute",
  padding: 6,
  width: 32,
  aspectRatio: "1 / 1",
  backgroundColor: "transparent",
  border: "none",
  fontSize: 24,
  color: theme.colors.primary.castletonGreen,
  cursor: "pointer",
  borderRadius: "50%",
  display: "flex",
  alignItems: "center",
  justifyContent: "center",
  transition: "background-color 50ms, transform 350ms",

  ":hover": {
    backgroundColor: theme.colors.primary.ivory,
  },

  ":active": {
    backgroundColor: theme.colors.grayscale[100],
    color: theme.colors.primary.castletonGreenPressed,
    transform: "scale(0.9)",
  },

  "@media": {
    [breakpoints.tablet]: {
      width: 40,
    }
  }
});

export const action = style({
  marginTop: 20,
});