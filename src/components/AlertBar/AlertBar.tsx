"use client";

import { FC, memo } from "react";
import Props from "./AlertBar.types";
import { observer } from "mobx-react-lite";
import * as styles from "./AlertBar.css";
import useStore from "@/hooks/useStore";
import FloatingElemenet from "../FloatingElemenet";
import classNames from "classnames";
import Typography from "../Typography";
import MemoCloseIcon from "@/assets/icons/CloseIcon";
import Button from "../Button";

const AlertBar: FC<Props> = observer(() => {
  const { alert: alertStore } = useStore();

  return (
    <div
      className={styles.root}
    >
      {alertStore.alerts.map((alert) => (
        <FloatingElemenet
          key={alert.id}
        >
          <div
            className={classNames(styles.alert, styles[alert.type ?? "info"])}
          >
            {alert.type !== "info" && (
              <Typography
                variant="h4"
                className={styles.alertTitle}
              >
                {alert.type === "error" ? "Error!" : "Success!"}
              </Typography>
            )}
            {typeof alert.content === "string" ? (
              <Typography
                className={styles.alertInfo}
              >
                {alert.content}
              </Typography>
            ) : (
              alert.content
            )}
            <Button
              type="button"
              onClick={() => alertStore.removeAlert(alert.id)}
              color={alert.type === "error" ? "secondary" : "primary"}
              size="small"
              className={styles.action}
            >
              Ok
            </Button>
            <button
              onClick={() => alertStore.removeAlert(alert.id)}
              className={styles.alertCloseButton}
              type="button"
            >
              <MemoCloseIcon />
            </button>
          </div>
        </FloatingElemenet>
      ))}
    </div>
  );
});

export default memo(AlertBar);