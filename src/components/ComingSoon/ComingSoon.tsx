import { gridSprinkle } from "@/styles/sprinkles.css";
import { theme } from "@/styles/themes.css";
import Container from "@components/Container";
import classNames from "classnames";
import Image from "next/image";
import Link from "next/link";
import { FC } from "react";
import Logo from "../Logo/Logo";
import OpportunitiesModal from "../OpportunitiesModal";
import Typography from "../Typography";
import * as styles from "./ComingSoon.css";
import PreviewImage from "./preview.png";

const ComingSoon: FC = () => {
  return (
    <Container>
      <div
        className={classNames(styles.root)}
      >
        <div
          className={classNames(
            styles.sectionsWrapper,
            gridSprinkle({
              type: "grid",
            })
          )}
        >
          <div
            className={classNames(
              styles.info,
              gridSprinkle({
                type: "item",
                cols: {
                  mobile: 10,
                  tablet: 5,
                },
              })
            )}
          >
            <div
              className={styles.logo}
            >
              <Logo />
            </div>
            <Typography
              className={styles.title}
              variant="h2"
            >
              We are coming
              <br />
              <b>soon!</b>
            </Typography>
            <Typography
              className={classNames(
                styles.description,
                gridSprinkle({ display: { mobile: "none", tablet: "block" } })
              )}
              variant="subTitleMedium"
            >
              The work on the website is in progress.
              <br />
              It will be released soon!
            </Typography>
            <Typography
              className={classNames(
                styles.description,
                gridSprinkle({ display: { mobile: "block", tablet: "none" } })
              )}
            >
              The work on the website is in progress.
              <br />
              It will be released soon!
            </Typography>
            <OpportunitiesModal />
            <div
              className={styles.contact}
            >
              <Typography
                as={Link}
                href="tel:08000461000"
                className={styles.link}
              >
                <svg
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    fillRule="evenodd"
                    clipRule="evenodd"
                    d="M11.5317 12.4724C15.5208 16.4604 16.4258 11.8467 18.9657 14.3848C21.4143 16.8328 22.8216 17.3232 19.7192 20.4247C19.3306 20.737 16.8616 24.4943 8.1846 15.8197C-0.493479 7.144 3.26158 4.67244 3.57397 4.28395C6.68387 1.17385 7.16586 2.58938 9.61449 5.03733C12.1544 7.5765 7.54266 8.48441 11.5317 12.4724Z"
                    fill={theme.colors.primary.asidGreen}
                  />
                </svg>
                0800 046 1000
              </Typography>
              <Typography
                as={Link}
                href="mailto:<EMAIL>"
                className={styles.link}
              >
                <svg
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    fillRule="evenodd"
                    clipRule="evenodd"
                    d="M16.939 3C18.28 3 19.57 3.53 20.519 4.481C21.469 5.43 22 6.71 22 8.05V15.95C22 18.74 19.73 21 16.939 21H7.06C4.269 21 2 18.74 2 15.95V8.05C2 5.26 4.259 3 7.06 3H16.939ZM18.5319 9.53999L18.6119 9.45999C18.8509 9.16999 18.8509 8.74999 18.6009 8.45999C18.4619 8.31099 18.2709 8.21999 18.0719 8.19999C17.8619 8.18899 17.6619 8.25999 17.5109 8.39999L13.0019 12C12.4219 12.481 11.5909 12.481 11.0019 12L6.50188 8.39999C6.19088 8.16999 5.76088 8.19999 5.50188 8.46999C5.23188 8.73999 5.20188 9.16999 5.43088 9.46999L5.56188 9.59999L10.1119 13.15C10.6719 13.59 11.3509 13.83 12.0619 13.83C12.7709 13.83 13.4619 13.59 14.0209 13.15L18.5319 9.53999Z"
                    fill={theme.colors.primary.asidGreen}
                  />
                </svg>
                <EMAIL>
              </Typography>
            </div>
          </div>
          <div
            className={classNames(
              styles.previewWrapper,
              gridSprinkle({
                type: "item",
                cols: {
                  mobile: 10,
                  tablet: 5,
                },
              })
            )}
          >
            <Image
              src={PreviewImage.src}
              alt="preview"
              fill
              objectFit="contain"
              objectPosition="bottom center"
            />
          </div>
        </div>
      </div>
    </Container>
  );
};

export default ComingSoon;
