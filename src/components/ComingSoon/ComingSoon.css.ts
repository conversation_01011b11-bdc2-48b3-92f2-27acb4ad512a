import { breakpoints } from "@/styles/constants.css";
import { theme } from "@/styles/themes.css";
import { style } from "@vanilla-extract/css";

export const root = style(
  {
    position: "relative",
    overflow: "hidden",
    height: "calc(100vh - 40px)",
    backgroundColor: theme.colors.primary.castletonGreen,
    color: theme.colors.primary.ivory,
    margin: "10px 0",
    borderRadius: "16px",
    padding: "32px 20px 0",

    "@media": {
      [breakpoints.tablet]: {
        margin: "20px 0",
        borderRadius: "24px",
        padding: 0
      }
    },
  }
);

export const sectionsWrapper = style(
  {
    height: "100%",
  }
);

export const section = style(
  {
    position: "relative",

    "@media": {
      [breakpoints.tablet]: {
        height: "100%",
      }
    },
  }
);

export const title = style(
  {
    fontFamily: theme.fonts.primary,
    color: theme.colors.primary.asidGreen,
    marginBottom: "32px",

    "@media": {
      [breakpoints.tablet]: {
        marginBottom: "40px",
      }
    },
  }
);

export const description = style(
  {
    marginBottom: "32px",

    "@media": {
      [breakpoints.tablet]: {
        marginBottom: "48px",
      }
    },
  }
);

export const logo = style(
  {
    marginBottom: "40px",

    "@media": {
      [breakpoints.tablet]: {
        marginBottom: "auto",
      }
    },
  }
);

export const contact = style(
  {
    position: "absolute",
    marginTop: "auto",
    display: "flex",
    flexDirection: "column",
    alignItems: "center",
    bottom: 20,
    zIndex: 1,
    gap: "8px",

    "@media": {
      [breakpoints.tablet]: {
        position: "relative",
        bottom: "auto",
        flexDirection: "row",
        marginTop: "auto",
        gap: "56px",
      }
    },
  }
);

export const info = style(
  {
    display: "flex",
    flexDirection: "column",
    alignItems: "center",
    textAlign: "center",

    "@media": {
      [breakpoints.tablet]: {
        padding: "56px 40px",
        justifyContent: "center",
        alignItems: "flex-start",
        textAlign: "left",
      }
    },
  }
);

export const action = style(
  {
    width: "100%",

    "@media": {
      [breakpoints.tablet]: {
        width: "max-content",
      }
    },
  }
);

export const previewWrapper = style(
  {
    position: "relative",
    height: "50vh",

    "@media": {
      [breakpoints.tablet]: {
        height: "auto",
      }
    },
  }
);

export const link = style(
  {
    display: "flex",
    alignItems: "center",
    gap: "16px",
  }
);