import { style } from "@vanilla-extract/css";
import { theme } from "@/styles/themes.css";
import { breakpoints, breakpointValues } from "@/styles/constants.css";

export const root = style({
  backgroundColor: "#FFFDF8",
  padding: "20px",
  borderRadius: "16px",
  margin: "40px 10px 0px 10px",
  "@media": {
    [breakpoints.tablet]: {
      margin: "40px 0px 0px 0px",
      padding: "40px",
      borderRadius: "24px",
    },
  },
});

export const title = style({
  fontSize: "40px",
  lineHeight: "95%",
  letterSpacing: "-2%",
  color: theme.colors.primary.castletonGreen,
  marginBottom: "24px",
  fontFamily: theme.fonts.primary,
  textAlign: "left",
  "@media": {
    [breakpoints.tablet]: {
      fontSize: "56px",
      marginBottom: "40px",
    },
    [breakpoints.desktop]: {
      fontSize: "64px",
    },
  },
});

export const titleBold = style({
  fontWeight: 700,
  fontStyle: "italic",
});

export const titleNormal = style({
  fontWeight: 400,
  fontStyle: "normal",
});

export const optionsContainer = style({
  display: "grid",
  gridTemplateColumns: "1fr",
  gap: "20px",

  "@media": {
    [breakpoints.tablet]: {
      gridTemplateColumns: "1fr 1fr",
      gap: "20px",
    },
  },
});

export const optionCard = style({
  backgroundColor: "#F5F1E8",
  borderRadius: "16px",
  padding: "16px",
  "@media": {
    [breakpoints.tablet]: {
      borderRadius: "24px",
      padding: "24px",
    },
  },
});

export const optionTitle = style({
  fontSize: "18px",
  fontWeight: 300,
  color: theme.colors.primary.castletonGreen,
  marginBottom: "12px",
  fontFamily: theme.fonts.secondary,
  "@media": {
    [breakpoints.tablet]: {
      fontSize: "20px",
      marginBottom: "16px",
    },
  },
});

export const optionTitleBold = style({
  fontWeight: 900,
});

export const optionSubtitle = style({
  fontSize: "18px",
  fontWeight: 600,
  color: theme.colors.primary.castletonGreen,
  marginBottom: "16px",
  fontFamily: theme.fonts.secondary,
  "@media": {
    [breakpoints.tablet]: {
      fontSize: "20px",
      marginBottom: "24px",
    },
  },
});

export const optionDescription = style({
  fontSize: "16px",
  fontWeight: 400,
  color: theme.colors.primary.castletonGreen,
  marginBottom: "8px",
  fontFamily: theme.fonts.secondary,
  lineHeight: "140%",
  "@media": {
    [breakpoints.tablet]: {
      fontSize: "20px",
      marginBottom: "12px",
    },
  },
});

export const buttonGroup = style({
  display: "flex",
  flexDirection: "column",
  gap: "12px",
  marginBottom: "24px",
  "@media": {
    [breakpoints.desktop]: {
      flexDirection: "row",
      gap: "16px",
      alignItems: "center",
    },
  },
});

export const signUpButton = style({
  width: "100%",
  flex: 1,
  padding: "10px 16px",
  fontSize: "22px",
  minHeight: "56px",
  "@media": {
    [breakpoints.tablet]: {
      flex: "none",
      width: "auto",
      fontSize: "22px",
      padding: "12px 24px",
    },
    [breakpoints.desktop]: {
      padding: "14px 28px",
    },
  },
});

export const copyButton = style({
  backgroundColor: "transparent",
  color: theme.colors.primary.castletonGreen,
  border: `2px solid ${theme.colors.primary.castletonGreen}`,
  borderRadius: "100px",
  padding: "10px 16px",
  fontSize: "22px !important",
  fontWeight: 600,
  cursor: "pointer",
  transition: "all 0.2s ease",
  minHeight: "56px",
  flex: 1,
  display: "inline-flex",
  alignItems: "center",
  justifyContent: "center",
  ":hover": {
    backgroundColor: theme.colors.primary.castletonGreen,
    color: theme.colors.primary.softWhite,
  },
  "@media": {
    [breakpoints.tablet]: {
      fontSize: "22px !important",
      padding: "12px 24px",
      flex: "none",
      minHeight: "auto",
    },
    [breakpoints.desktop]: {
      fontSize: "22px !important",
      padding: "14px 28px",
    },
  },
});

export const instructionText = style({
  marginTop: "16px",
  "@media": {
    [breakpoints.tablet]: {
      marginTop: "24px",
    },
  },
});

export const instructionTitle = style({
  fontSize: "16px",
  fontWeight: 400,
  color: theme.colors.primary.castletonGreen,
  marginBottom: "6px",
  fontFamily: theme.fonts.secondary,
  lineHeight: "140%",
  "@media": {
    [breakpoints.tablet]: {
      fontSize: "20px",
      marginBottom: "8px",
    },
  },
});

export const questionText = style({
  fontSize: "16px",
  fontWeight: 900,
  color: theme.colors.primary.castletonGreen,
  marginBottom: "6px",
  fontFamily: theme.fonts.secondary,
  fontStyle: "normal",
  "@media": {
    [breakpoints.tablet]: {
      fontSize: "20px",
      marginBottom: "8px",
    },
  },
});

export const instructionDetail = style({
  fontSize: "16px",
  fontWeight: 400,
  color: theme.colors.primary.castletonGreen,
  fontFamily: theme.fonts.secondary,
  lineHeight: "140%",
  "@media": {
    [breakpoints.tablet]: {
      fontSize: "20px",
    },
  },
});

export const highlight = style({
  fontWeight: 900,
});

export const emailForm = style({
  marginTop: "16px",
  "@media": {
    [breakpoints.tablet]: {
      marginTop: "24px",
    },
  },
});

export const emailLabel = style({
  fontSize: "16px",
  fontWeight: 600,
  color: theme.colors.primary.castletonGreen,
  marginBottom: "8px",
  fontFamily: theme.fonts.secondary,
  display: "block",
});

export const emailInputGroup = style({
  display: "flex",
  gap: "8px",
  alignItems: "stretch",
  "@media": {
    [breakpoints.tablet]: {
      gap: "12px",
    },
  },
});

export const emailInput = style({
  flex: 1,
  padding: "10px 12px",
  border: `none`,
  borderRadius: "8px",
  fontSize: "16px",
  fontFamily: theme.fonts.secondary,
  color: theme.colors.primary.castletonGreen,
  backgroundColor: theme.colors.primary.softWhite,
  minHeight: "56px",
  "::placeholder": {
    color: "#999",
  },
  ":focus": {
    outline: "none",
    borderColor: theme.colors.primary.asidGreen,
  },
  "@media": {
    [breakpoints.tablet]: {
      padding: "12px 16px",
      fontSize: "20px",
      minHeight: "auto",
    },
  },
});

export const sendButton = style({
  width: "60px",
  minWidth: "60px",
  maxWidth: "60px",
  backgroundColor: theme.colors.primary.asidGreen,
  color: theme.colors.primary.castletonGreen,
  border: "none",
  borderRadius: "8px",
  padding: "0",
  height: "56px",
  fontSize: "14px",
  fontWeight: 600,
  cursor: "pointer",
  transition: "all 0.2s ease",
  whiteSpace: "nowrap",
  display: "flex",
  alignItems: "center",
  justifyContent: "center",
  ":hover": {
    backgroundColor: theme.colors.primary.softWhite,
  },
  "@media": {
    [breakpoints.tablet]: {
      width: "80px",
      minWidth: "80px",
      maxWidth: "80px",
      height: "48px",
      fontSize: "16px",
    },
  },
});

export const toast = style({
  position: "fixed",
  top: "20px",
  right: "20px",
  backgroundColor: theme.colors.primary.asidGreen,
  color: theme.colors.primary.castletonGreen,
  padding: "12px 20px",
  borderRadius: "8px",
  fontSize: "14px",
  fontFamily: theme.fonts.secondary,
  fontWeight: 500,
  boxShadow: "0 4px 12px rgba(0, 0, 0, 0.15)",
  zIndex: 9999,
  opacity: 0,
  transform: "translateX(100%)",
  transition: "all 0.3s ease",
});

export const toastVisible = style({
  opacity: 1,
  transform: "translateX(0)",
});

export const toastError = style({
  backgroundColor: "#dc3545",
  color: "#fff",
});

export const buttonTextMobile = style({
  display: "inline",
  fontSize: "20px",
  "@media": {
    [breakpoints.tablet]: {
      display: "none",
    },
  },
});

export const buttonTextDesktop = style({
  display: "none",
  fontSize: "22px",
  "@media": {
    [breakpoints.tablet]: {
      display: "inline",
      fontSize: "16px",
    },
    [breakpoints.desktop]: {
      fontSize: "18px",
    },
  },
});
