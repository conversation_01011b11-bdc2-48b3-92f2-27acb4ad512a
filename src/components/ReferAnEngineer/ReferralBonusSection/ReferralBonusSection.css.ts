import { style } from "@vanilla-extract/css";
import { theme } from "@/styles/themes.css";
import { breakpoints, breakpointValues } from "@/styles/constants.css";

export const root = style({
  display: "flex",
  gap: "48px",
  marginTop: "40px",
  "@media": {
    [`screen and (max-width: ${breakpointValues.lg - 1}px)`]: {
      flexDirection: "column",
    },
  },
});

export const leftPanel = style({
  backgroundColor: theme.colors.primary.softWhite,
  borderRadius: "24px",
  padding: "20px 20px 0",
  flex: "0 0 40%",
  display: "flex",
  flexDirection: "column",
  justifyContent: "space-between",
  color: theme.colors.primary.castletonGreen,
  "@media": {
    [breakpoints.tablet]: {
      padding: "40px 40px 0",
    },
  },
});

export const leftTextWrapper = style({});

export const leftTitle = style({
  fontFamily: theme.fonts.primary,
  fontSize: "40px",
  fontWeight: 400,
  marginBottom: "40px",
  lineHeight: "95%",
  "@media": {
    [breakpoints.tablet]: {
      fontSize: "64px",
    },
  },
});

export const sum = style({
  fontWeight: 500,
});

export const leftDescription = style({
  fontSize: "20px",
  fontFamily: theme.fonts.secondary,
  fontWeight: 400,
  lineHeight: "120%",
});

export const leftImageWrapper = style({
  display: "flex",
  justifyContent: "center",
  width: 300,
  margin: "0 auto",
  marginTop: "20px",
  "@media": {
    [breakpoints.tablet]: {
      width: "381px",
    },
    [breakpoints.desktop]: {
      position: "relative",
      // top: 25,
      width: "381px",
      height: "381px",
    },
  },
});

export const engineerImage = style({
  width: "100%",
  height: "auto",
  objectFit: "contain",
  display: "block",
});

export const rightPanel = style({
  flex: 1,
  display: "flex",
  flexDirection: "column",
  justifyContent: "space-between",
  gap: "20px",
  "@media": {
    [breakpoints.desktop]: {
      gap: "0px",
    },
  },
});

export const breakdownBox = style({
  backgroundColor: theme.colors.primary.softWhite,
  borderRadius: "24px",
  padding: "24px",
});

export const breakdownText = style({
  fontSize: "20px",
  marginBottom: "14px",
  lineHeight: "120%",
  fontFamily: theme.fonts.secondary,
  color: "#003D23",
});

export const breakdownTitle = style({
  fontWeight: 700,
  fontSize: "20px",
  marginBottom: "14px",
  lineHeight: "120%",
});

export const breakdownList = style({
  listStyle: "none",
  padding: 0,
  marginBottom: "20px",
  display: "flex",
  flexDirection: "column",
  gap: "10px",
});
export const li = style({
  display: "flex",
  gap: "5px",
  fontSize: "20px",
  lineHeight: "120%",
  fontFamily: theme.fonts.secondary,
});

export const price = style({
  fontWeight: 700,
});

export const checkIcon = style({
  marginRight: "10px",
  flexShrink: 0,
  display: "block",
});

export const breakdownInfo = style({
  display: "flex",
  flexDirection: "column",
  gap: "14px",
});

export const breakdownFooter = style({
  fontSize: "20px",
  color: theme.colors.primary.castletonGreen,
  fontFamily: theme.fonts.secondary,
  fontWeight: 400,
});

export const shareBox = style({
  backgroundColor: theme.colors.primary.asidGreen,
  borderRadius: "24px",
  padding: "24px",
  color: theme.colors.primary.castletonGreen,
  fontFamily: theme.fonts.secondary,
});

export const button = style({
  borderColor: theme.colors.primary.castletonGreen,
  border: "1px solid",
  width: "100%",
  display: "flex",
  justifyContent: "center",
  "@media": {
    [breakpoints.tablet]: {
      width: "auto",
    },
  },
});

export const shareTitle = style({
  fontWeight: 700,
  marginBottom: "14px",
  fontSize: "20px",
});

export const shareDescription = style({
  marginBottom: "30px",
  fontSize: "20px",
});

export const toast = style({
  position: "fixed",
  top: "20px",
  right: "20px",
  backgroundColor: theme.colors.primary.asidGreen,
  color: theme.colors.primary.castletonGreen,
  padding: "12px 20px",
  borderRadius: "8px",
  fontSize: "14px",
  fontFamily: theme.fonts.secondary,
  fontWeight: 500,
  boxShadow: "0 4px 12px rgba(0, 0, 0, 0.15)",
  zIndex: 9999,
  opacity: 0,
  transform: "translateX(100%)",
  transition: "all 0.3s ease",
});

export const toastVisible = style({
  opacity: 1,
  transform: "translateX(0)",
});
