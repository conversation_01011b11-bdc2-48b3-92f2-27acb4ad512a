import { style } from "@vanilla-extract/css";
import { theme } from "@/styles/themes.css";
import { breakpoints } from "@/styles/constants.css";

export const root = style({
  backgroundColor: theme.colors.primary.castletonGreen,
  color: theme.colors.primary.ivory,
  borderRadius: "24px",
  padding: "20px 20px 0px 20px",
  display: "flex",
  flexDirection: "column",
  gap: "40px",
  fontFamily: theme.fonts.secondary,
  marginTop: "40px",
  "@media": {
    [breakpoints.desktop]: {
      flexDirection: "row",
      gap: "20px",
      alignItems: "flex-end",
      padding: "40px",
      marginTop: "40px",
    },
  },
});

export const content = style({
  // flex: 1.5,
});

export const italic = style({
  fontStyle: "italic",
});

export const title = style({
  fontSize: "40px",
  fontWeight: 400,
  marginBottom: "40px",
  display: "flex",
  flexDirection: "column",
  lineHeight: "95%",
  fontFamily: theme.fonts.primary,
  "@media": {
    [breakpoints.tablet]: {
      fontSize: "48px",
    },
    [breakpoints.desktop]: {
      fontSize: "64px",
    },
  },
});

export const highlight = style({
  color: theme.colors.primary.asidGreen,
});

export const bold = style({
  fontWeight: 900,
});

export const description = style({
  fontSize: "20px",
  marginBottom: "20px",
  maxWidth: "600px",
});

export const howItWorksBox = style({
  background: "rgba(255, 253, 248, 0.10)",
  borderRadius: "16px",
  padding: "24px",
  marginTop: "30px",
});

export const howItWorksTitle = style({
  fontSize: "20px",
  fontWeight: "bold",
  marginBottom: "15px",
  display: "flex",
  alignItems: "center",
  gap: "10px",
});

export const howItWorksList = style({
  listStyle: "none",
  padding: 0,
  margin: 0,
  display: "flex",
  flexDirection: "column",
  gap: "14px",
});

export const howItWorksListItem = style({
  display: "flex",
  alignItems: "flex-start",
  gap: "14px",
  fontSize: "20px",
  fontWeight: 100,
});

export const checkIcon = style({
  marginTop: "4px",
  flexShrink: 0,
});

export const finalSentences = style({
  fontSize: "20px",
  marginTop: "14px",
  display: "flex",
  flexDirection: "column",
  gap: "15px",
});

export const imageWrapper = style({
  flex: 1,
  display: "flex",
  alignItems: "flex-end",
  justifyContent: "center",
  minWidth: 300,
  overflow: "hidden",

  "@media": {
    [breakpoints.desktop]: {
      justifyContent: "flex-end",
      marginBottom: "-40px",
    },
  },
});

export const image = style({
  maxWidth: "100%",
  height: "auto",
  objectFit: "contain",
  position: "relative",
  alignSelf: "flex-end",

  "@media": {
    [breakpoints.desktop]: {
      width: "576px",
      height: "auto",
    },
  },
});
