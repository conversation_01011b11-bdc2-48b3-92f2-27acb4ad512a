"use client";
import React from 'react';
import * as styles from './LeaderboardChallenge.css';
import Typography from '@components/Typography';
import Image from 'next/image';
import engineerImage from '../LeaderboardChallenge/man_holding_currency.png'
import Container from "@components/Container";
import classNames from "classnames";
import docImg from "./doc.svg"
import checkImg from "./check.svg"

const LeaderboardChallenge = () => {
  return (
    <Container>
      <section className={styles.root}>
        <div className={styles.content}>
          <Typography variant="h2" className={styles.title}>
            <span>NEW: <span className={styles.italic}>Leaderboard Challenge</span></span>
            <span>– <span className={styles.highlight}>£1,000 Monthly Winner</span></span>
          </Typography>
          <Typography className={classNames(styles.description, styles.bold)}>
            Think you’ve got the biggest network? Prove it.
          </Typography>
          <Typography className={styles.description}>
            We’re now paying £1,000 every month to the person who refers the most successful engineers.
          </Typography>

          <div className={styles.howItWorksBox}>
            <Typography variant="h4" className={classNames(styles.howItWorksTitle, styles.bold)}>
              <Image src={docImg.src} width={24} height={24} alt="doc icon" /> How it works:
            </Typography>
            <ul className={styles.howItWorksList}>
              <li className={styles.howItWorksListItem}>
                <Image src={checkImg.src} width={17} height={14} alt="check" className={styles.checkIcon} />
                <span>The leaderboard resets monthly</span>
              </li>
              <li className={styles.howItWorksListItem}>
                <Image src={checkImg.src} width={17} height={14} alt="check" className={styles.checkIcon} />
                <span>Every successful referral (who hits 50 hours) earns you a point</span>
              </li>
              <li className={styles.howItWorksListItem}>
                <Image src={checkImg.src} width={17} height={14} alt="check" className={styles.checkIcon} />
                <span>Whoever finishes top wins the <strong className={styles.bold}>£1,000 cash prize</strong></span>
              </li>
            </ul>
            <div className={styles.finalSentences}>
              <Typography>
                <p>No splitting. No draws. <span className={styles.bold}>Winner takes it all.</span></p>
                <p>You’re already referring mates. Now you’ve got a reason to go all in.</p>
              </Typography>
            </div>
          </div>
        </div>
        <div className={styles.imageWrapper}>
          <Image src={engineerImage} alt="Engineer" className={styles.image} width={400} height={450} />
        </div>
      </section>
    </Container>
  );
};

export default LeaderboardChallenge;
