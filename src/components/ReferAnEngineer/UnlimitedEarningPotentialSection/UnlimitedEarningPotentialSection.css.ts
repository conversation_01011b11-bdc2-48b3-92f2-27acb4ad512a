import { style } from "@vanilla-extract/css";
import { theme } from "@/styles/themes.css";
import { breakpoints, breakpointValues } from "@/styles/constants.css";

export const root = style({
  backgroundColor: theme.colors.primary.castletonGreen,
  borderRadius: "24px",
  padding: "20px",
  marginTop: "40px",
  display: "flex",
  flexDirection: "column",
  gap: "40px",
  color: theme.colors.primary.ivory,
  overflow: "hidden",
  position: "relative",
  "@media": {
    [breakpoints.desktop]: {
      maxHeight: "307px",
      flexDirection: "row",
      alignItems: "flex-start",
      gap: "60px",
      padding: "40px 40px",
    },
  },
});

export const leftSection = style({
  flex: "0 0 auto",
  "@media": {
    [breakpoints.desktop]: {
      flex: "0 0 45%",
    },
  },
});

export const rightSection = style({
  flex: 1,
  display: "flex",
  flexDirection: "column",
  gap: "20px",
});

export const title = style({
  fontSize: "44px",
  lineHeight: "95%",
  letterSpacing: "-2%",
  fontFamily: theme.fonts.primary,
  fontWeight: 400,
  marginBottom: "0",
  "@media": {
    [breakpoints.tablet]: {
      fontSize: "56px",
    },
    [breakpoints.desktop]: {
      fontSize: "64px",
    },
  },
});

export const titleHighlight = style({
  color: theme.colors.primary.asidGreen,
  fontWeight: 400,
});

export const titleBold = style({
  fontWeight: 700,
  fontStyle: "italic",
});

export const descriptionList = style({
  display: "flex",
  flexDirection: "column",
  gap: "16px",
});

export const descriptionItem = style({
  fontSize: "24px",
  lineHeight: "140%",
  fontFamily: theme.fonts.secondary,
  color: theme.colors.primary.asidGreen,
  fontWeight: 400,
  "@media": {
    [breakpoints.tablet]: {
      fontSize: "20px",
    },
  },
});

export const highlight = style({
  color: theme.colors.primary.asidGreen,
  fontWeight: 600,
});

export const finalText = style({
  fontSize: "20px",
  lineHeight: "140%",
  fontFamily: theme.fonts.secondary,
  fontWeight: 400,
  "@media": {
    [breakpoints.tablet]: {
      fontSize: "20px",
    },
  },
});

export const copyButton = style({
  padding: "16px 24px",
  backgroundColor: "transparent",
  color: theme.colors.primary.asidGreen,
  border: `2px solid ${theme.colors.primary.asidGreen}`,
  borderRadius: "100px",
  fontSize: "20px",
  fontWeight: 200,
  fontFamily: theme.fonts.secondary,
  cursor: "pointer",
  transition: "all 0.2s ease",
  textAlign: "center",
  textDecoration: "none",
  display: "inline-block",
  alignSelf: "flex-start",
  ":hover": {
    backgroundColor: theme.colors.primary.asidGreen,
    color: theme.colors.primary.castletonGreen,
    transform: "translateY(-1px)",
  },
  ":active": {
    transform: "translateY(0)",
  },
});

export const toast = style({
  position: "fixed",
  top: "20px",
  right: "20px",
  backgroundColor: theme.colors.primary.asidGreen,
  color: theme.colors.primary.castletonGreen,
  padding: "12px 20px",
  borderRadius: "8px",
  fontSize: "14px",
  fontFamily: theme.fonts.secondary,
  fontWeight: 500,
  boxShadow: "0 4px 12px rgba(0, 0, 0, 0.15)",
  zIndex: 9999,
  opacity: 0,
  transform: "translateX(100%)",
  transition: "all 0.3s ease",
});

export const toastVisible = style({
  opacity: 1,
  transform: "translateX(0)",
});
