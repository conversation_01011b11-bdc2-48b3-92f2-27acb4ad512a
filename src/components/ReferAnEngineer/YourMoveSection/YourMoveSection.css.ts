import { style } from "@vanilla-extract/css";
import { theme } from "@/styles/themes.css";
import { breakpoints, breakpointValues } from "@/styles/constants.css";

export const root = style({
  backgroundColor: theme.colors.primary.softWhite,
  borderRadius: "24px",
  padding: "0px",
  marginTop: "40px",
  marginBottom: "56px",
  display: "flex",
  flexDirection: "column",
  gap: "10px",
  color: theme.colors.primary.castletonGreen,
  position: "relative",

  "@media": {
    [breakpoints.desktop]: {
      gap: "40px",
      maxHeight: "359px",
      flexDirection: "row",
      padding: "0px",
    },
  },
});

export const imageSection = style({
  display: "flex",
  justifyContent: "center",
  alignSelf: "flex-end",
  
  order: 1,
  "@media": {
    [breakpoints.desktop]: {
      order: 0,
      flex: "0 0 433px",
      justifyContent: "flex-end",
    },
  },
});

export const engineerImage = style({
  width: "100%",
  height: "auto",
  objectFit: "contain",
  display: "block",
  maxWidth: "335px",
  "@media": {
    [breakpoints.desktop]: {
      width: "433px",
      maxWidth: "433px",
    },
  },
});

export const content = style({
  display: "flex",
  flexDirection: "column",
  gap: "24px",
  padding: "20px",
  "@media": {
    [breakpoints.desktop]: {
      maxWidth: "900px",
      padding: "40px",
    },
  },
});

export const title = style({
  fontSize: "44px",
  lineHeight: "95%",
  letterSpacing: "-2%",
  fontFamily: theme.fonts.primary,
  fontWeight: 400,

  "@media": {
    [breakpoints.tablet]: {
      fontSize: "56px",
    },
    [breakpoints.desktop]: {
      fontSize: "64px",
    },
  },
});

export const titleHighlight = style({
  color: theme.colors.primary.castletonGreen,
  fontWeight: 400,
  fontStyle: "normal",
});

export const titleBold = style({
  fontWeight: 700,
  fontStyle: "normal",
});

export const infoList = style({
  display: "flex",
  flexDirection: "column",
  gap: "12px",
});

export const infoItem = style({
  fontSize: "18px",
  lineHeight: "140%",
  fontFamily: theme.fonts.secondary,
  fontWeight: 400,
  display: "block",
  "@media": {
    [breakpoints.tablet]: {
      fontSize: "20px",
    },
  },
});

export const infoLabel = style({
  fontWeight: 900,
});

export const infoValue = style({
  fontWeight: 400,
});

export const finalText = style({
  fontSize: "18px",
  lineHeight: "140%",
  fontFamily: theme.fonts.secondary,
  fontWeight: 400,

  "@media": {
    [breakpoints.tablet]: {
      fontSize: "20px",
    },
  },
});

export const startButton = style({
  alignSelf: "flex-start",
  padding: "16px 32px !important",
  fontSize: "18px !important",
  "@media": {
    [breakpoints.tablet]: {
      fontSize: "20px !important",
      padding: "18px 36px !important",
    },
  },
});


