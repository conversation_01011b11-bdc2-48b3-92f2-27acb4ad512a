"use client";
import React from 'react';
import * as styles from './WhyEngineersChoose.css';
import Typography from '@components/Typography';
import Image from 'next/image';
import Container from "@components/Container";
import engineerImage from './engineer-portrait.png';
import checkIcon from './check.svg';

const WhyEngineersChoose = () => {
  const benefits = [
    {
      title: "Consistent, quality work",
      description: "domestic &amp; commercial jobs daily",
      isBold: true
    },
    {
      title: "Next-day",
      description: "payments"
    },
    {
      title: "Zero admin",
      description: "we handle the paperwork",
      isBold: true
    },
    {
      title: "Friendly, expert support team",
      description: "",
      isBold: true
    },
    {
      title: "Respect and trust from day one",
      description: "They show up, do the job, and get paid — fast.",
      isBold: true
    }
  ];

  return (
    <Container>
      <section className={styles.root}>
        <div className={styles.imageSection}>
          <div className={styles.imageWrapper}>
            <Image 
              src={engineerImage} 
              alt="Pleasant Plumbers Engineer" 
              className={styles.engineerImage}
              width={400}
              height={500}
            />
          </div>
        </div>
        
        <div className={styles.contentSection}>
          <Typography variant="h2" className={styles.title}>
            <span className={styles.titleFirstLine}>Why Engineers</span>
            <span className={styles.titleSecondLine}>Choose <span className={styles.brandName}>Pleasant</span></span>
          </Typography>
          
          <Typography className={styles.description}>
            When you refer someone to us, they&apos;re in good hands. We&apos;re a premium
            plumbing and heating firm trusted across London.
          </Typography>
          
          <div className={styles.benefitsSection}>
            <Typography className={styles.benefitsTitle}>
              Here&apos;s what your referral gets:
            </Typography>

            <ul className={styles.benefitsList}>
              {benefits.map((benefit, index) => (
                <li key={index} className={styles.benefitItem}>
                  <Image
                    src={checkIcon}
                    alt="Check"
                    className={styles.checkIcon}
                    width={17}
                    height={14}
                  />
                  <div className={styles.benefitContent}>
                    <span className={benefit.isBold ? styles.benefitTitleBold : styles.benefitTitle}>
                      {benefit.title}
                    </span>
                    {benefit.description && (
                      <span className={styles.benefitDescription}> – {benefit.description}</span>
                    )}
                  </div>
                </li>
              ))}
            </ul>
          </div>
        </div>
      </section>
    </Container>
  );
};

export default WhyEngineersChoose;
