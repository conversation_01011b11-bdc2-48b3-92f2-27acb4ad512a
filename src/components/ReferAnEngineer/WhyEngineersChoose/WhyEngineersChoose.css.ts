import { style } from "@vanilla-extract/css";
import { theme } from "@/styles/themes.css";
import { breakpoints, breakpointValues } from "@/styles/constants.css";

export const root = style({
  display: "flex",
  gap: "88px",
  alignItems: "center",
  marginTop: "40px",
  "@media": {
    [`screen and (max-width: ${breakpointValues.lg - 1}px)`]: {
      flexDirection: "column",
      gap: "40px",
      padding: "0 10px",
    },
  },
});

export const imageSection = style({
  "@media": {
    [`screen and (max-width: ${breakpointValues.lg - 1}px)`]: {
      flex: "none",
      width: "100%",
      order: 2,
    },
  },
});

export const imageWrapper = style({
  backgroundColor: theme.colors.primary.castletonGreen,
  borderRadius: "24px",
  display: "flex",
  justifyContent: "center",
  alignItems: "flex-end",
  overflow: "hidden",
  width: "546px",
  height: "608px",
  "@media": {
    [`screen and (max-width: ${breakpointValues.lg - 1}px)`]: {
      width: "100%",
      height: "400px",
    },
  },
});

export const engineerImage = style({
  width: "100%",
  height: "auto",
  maxWidth: "550px",
  objectFit: "cover",
  "@media": {
    [`screen and (max-width: ${breakpointValues.lg - 1}px)`]: {
      maxWidth: "350px",
    },
  },
});

export const contentSection = style({
  maxWidth: "806px",
  "@media": {
    [`screen and (max-width: ${breakpointValues.lg - 1}px)`]: {
      order: 1,
    },
  },
});

export const title = style({
  fontSize: "44px",
  lineHeight: "95%",
  letterSpacing: "-2%",
  color: theme.colors.primary.castletonGreen,
  marginBottom: "24px",
  fontFamily: theme.fonts.primary,
  "@media": {
    [breakpoints.tablet]: {
      fontSize: "56px",
    },
    [breakpoints.desktop]: {
      fontSize: "64px",
    },
  },
});

export const titleFirstLine = style({
  fontWeight: 700,
  fontStyle: "italic",
  display: "block",
});

export const titleSecondLine = style({
  fontWeight: 400,
  fontStyle: "normal",
  display: "block",
});

export const brandName = style({
  fontStyle: "italic",
  fontWeight: 400,
});

export const description = style({
  fontSize: "18px",
  lineHeight: "140%",
  color: theme.colors.primary.castletonGreen,
  marginBottom: "32px",
  fontFamily: theme.fonts.secondary,
  fontWeight: 400,
  "@media": {
    [breakpoints.tablet]: {
      fontSize: "20px",
    },
  },
});

export const benefitsSection = style({
  backgroundColor: "#FFFDF8",
  padding: "24px",
  borderRadius: "24px",
  maxWidth: "726px",
  // Стилі для секції переваг
});

export const benefitsTitle = style({
  fontSize: "20px",
  fontWeight: 600,
  color: theme.colors.primary.castletonGreen,
  marginBottom: "20px",
  fontFamily: theme.fonts.secondary,
  "@media": {
    [breakpoints.tablet]: {
      fontSize: "22px",
    },
  },
});

export const benefitsList = style({
  listStyle: "none",
  padding: 0,
  margin: 0,
  display: "flex",
  flexDirection: "column",
  gap: "16px",
});

export const benefitItem = style({
  display: "flex",
  alignItems: "flex-start",
  gap: "12px",
});

export const checkIcon = style({
  marginTop: "2px",
  flexShrink: 0,
});

export const benefitContent = style({
  flex: 1,
});

export const benefitTitle = style({
  fontSize: "18px",
  fontStyle: "bold",
  fontWeight: 900,
  color: theme.colors.primary.castletonGreen,
  fontFamily: theme.fonts.secondary,
  "@media": {
    [breakpoints.tablet]: {
      fontSize: "20px",
    },
  },
});

export const benefitTitleBold = style({
  fontSize: "18px",
  fontStyle: "bold",
  fontWeight: 900,
  color: theme.colors.primary.castletonGreen,
  fontFamily: theme.fonts.secondary,
  "@media": {
    [breakpoints.tablet]: {
      fontSize: "20px",
    },
  },
});

export const benefitDescription = style({
  fontSize: "18px",
  fontWeight: 400,
  color: theme.colors.primary.castletonGreen,
  fontFamily: theme.fonts.secondary,
  "@media": {
    [breakpoints.tablet]: {
      fontSize: "20px",
    },
  },
});
