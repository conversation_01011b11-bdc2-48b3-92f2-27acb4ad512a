import { style } from "@vanilla-extract/css";
import { theme } from "@/styles/themes.css";
import {breakpoints, breakpointValues} from "@/styles/constants.css";

export const root = style({
  backgroundColor: theme.colors.primary.castletonGreen,
  borderRadius: "24px",
  padding: "20px",
  display: "flex",
  // justifyContent: "space-between",
  // alignItems: "center",
  // gap: "40px",
  flexDirection: "row",
  overflow: "hidden",
  '@media': {
    [`screen and (max-width: ${breakpointValues.md - 1}px)`]: {
      flexDirection: "column",
      padding: "20px",
    },
    [breakpoints.tablet]: {
      padding: "30px 48px 0 48px",
    },
  },
});

export const content = style({
  flex: 1,
  display: "flex",
  flexDirection: "column",
  paddingBottom: "60px",
  '@media': {
    [`screen and (max-width: ${breakpointValues.md - 1}px)`]: {
      paddingBottom: 0,
    },
  },
});

export const title = style({
  color: theme.colors.primary.softWhite,
  fontSize: "44px",
  lineHeight: "95%",
  letterSpacing: "-2%",
  maxWidth: "240px",
  '@media': {
    [breakpoints.tablet]: {
      maxWidth: "100%",
      width: "724px",
      fontSize: "88px",
      marginTop: "90px",
    }
  }
  
});

export const description = style({
  color: theme.colors.primary.softWhite,
  fontFamily: theme.fonts.secondary,
  marginBottom: "20px",
  maxWidth: "450px",
  fontSize: "20px",
  fontWeight: 500,
  lineHeight: "120%",
  letterSpacing: "-0.18px",
  marginTop: "20px",
  display: "inline",
  '@media': {
    [breakpoints.tablet]: {
      width: "100%",
      fontSize: "24px",
      marginTop: "40px",
    },
    [`screen and (max-width: ${breakpointValues.md - 1}px)`]: {
      maxWidth: "100%",
    },
  },
});

export const descriptionParagraph = style({
  display: "inline",
})

export const buttonWrapper = style({
  margin: "auto",
  width: '100%',
  '@media': {
    [breakpoints.tablet]: {
      margin: "0",
      maxWidth: "311px",
    },
  }

});

export const button = style({
  minHeight: "56px",
  height: "56px",
  '@media': {
    [breakpoints.tablet]: {
      minHeight: "56px",
      height: "56px",
    },
    [`screen and (max-width: ${breakpointValues.md - 1}px)`]: {
      minHeight: "52px",
      height: "52px",
      padding: "16px 32px",
    },
  }
});

export const whiteText = style({
  color: theme.colors.primary.softWhite,
  fontStyle: "italic",
  fontWeight: "bold",
});

export const highlight = style({
  color: theme.colors.primary.asidGreen,
});

export const imgWrapper = style({
  flex: 1,
  maxWidth: "550px",
  height: "auto",
  '@media': {
    [`screen and (max-width: ${breakpointValues.md - 1}px)`]: {
      width: "130%",
      maxWidth: "none",
      // alignSelf: "flex-end",
      marginTop: "40px",
    },
  },
});

export const img = style({
  width: "110%",
  height: "auto",
  objectFit: "cover",
  display: "block",
  position: "relative",
  top: "20px",
  right: "85px",
  marginTop: "-60px",
  '@media': {
    [breakpoints.tablet]: {
      display: "none"
    },
    [breakpoints.desktop]: {
      maxWidth: "120%",
      display: "block",
      // minWidth: "400px",
      right: 115,
      marginTop: 0
    }
  }
});
