"use client";
import Container from "@components/Container";
import * as styles from "./ReferAnEngineerSection.css";
import Typography from "@components/Typography";
import Button from "@components/Button";
import Link from "next/link";
import Image from "next/image";
import referalReady from "../../Subcontractors/ReferAnEngineer/referal.png"
import classNames from "classnames";

const ReferAnEngineerSection = () => {
  return (
    <Container>
      <section
        className={styles.root}
      >
        <div className={styles.content}>
          <Typography
            fontFamily="primary"
            className={styles.title}
            variant="h2"
            isGreenItalic
          >
            <span className={styles.whiteText}>Refer</span> an Engineer
            <p className={styles.highlight}>and Earn.</p>
            £1,000 Cash
          </Typography>
          <Typography
            className={styles.description}
            variant="bodySmall"
          >
            Got a mate who’s a quality engineer?{" "}
            <p className={classNames(styles.descriptionParagraph, styles.highlight)}>Turn that connection into serious cash</p>
          </Typography>
          <div
            className={styles.buttonWrapper}
          >
            <Button
              as={Link}
              isAnimated
              href="/become-subcontractor"
              className={styles.button}
            >
              Sub-Contractor Sign Up
            </Button>
          </div>
        </div>
        <div className={styles.imgWrapper}>
          <Image src={referalReady.src} alt="Refer an Engineer" width={500} height={400} className={styles.img} />
        </div>
      </section>
    </Container>
  );
};

export default ReferAnEngineerSection;
