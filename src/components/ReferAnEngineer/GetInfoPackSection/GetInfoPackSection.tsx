"use client";
import React, { useState } from "react";
import * as styles from "./GetInfoPackSection.css";
import Typography from "@components/Typography";
import Container from "@components/Container";
import { requestInfoPack } from "@/utils/api/info-pack";

const GetInfoPackSection = () => {
  const [email, setEmail] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showEmailToast, setShowEmailToast] = useState(false);
  const [showErrorToast, setShowErrorToast] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!email.trim()) return;

    setIsSubmitting(true);

    try {
      await requestInfoPack({ email: email.trim() });

      // Reset form after successful submission
      setEmail("");
      setShowEmailToast(true);
      setTimeout(() => setShowEmailToast(false), 2000);
    } catch (error) {
      console.error("Error sending info pack:", error);
      setShowErrorToast(true);
      setTimeout(() => setShowErrorToast(false), 3000);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Container>
      <section className={styles.root}>
        <Typography className={styles.title}>
          <span className={styles.titleBold}>Get</span> the Info Pack, Get Paid
        </Typography>

        <div className={styles.contentWrapper}>
          <div className={styles.content}>
            <Typography className={styles.description}>
              We will email the info pack to you, so that you can then forward
              it to the engineer that you wish to refer to us.
            </Typography>
          </div>

          <div className={styles.formSection}>
            <form onSubmit={handleSubmit}>
              <div className={styles.formContainer}>
                <label htmlFor="email" className={styles.emailLabel}>
                  Email
                </label>
                <div className={styles.inputWrapper}>
                  <input
                    id="email"
                    type="email"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    placeholder="<EMAIL>"
                    className={styles.emailInput}
                    required
                    disabled={isSubmitting}
                  />
                  <button
                    type="submit"
                    className={styles.submitButton}
                    disabled={isSubmitting || !email.trim()}
                  >
                    {isSubmitting ? "..." : "→"}
                  </button>
                </div>
              </div>
            </form>

            <Typography className={styles.privacyText}>
              We&apos;ll never spam or share your data. Just one email, from us to
              you.
            </Typography>
          </div>
        </div>
      </section>

      {/* Toast notifications */}
      {showEmailToast && (
        <div className={`${styles.toast} ${styles.toastVisible}`}>
          Info pack sent successfully!
        </div>
      )}

      {showErrorToast && (
        <div className={`${styles.toast} ${styles.toastVisible} ${styles.toastError}`}>
          Failed to send info pack. Please try again.
        </div>
      )}
    </Container>
  );
};

export default GetInfoPackSection;
