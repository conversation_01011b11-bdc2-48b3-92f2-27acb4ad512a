import { style } from "@vanilla-extract/css";
import { theme } from "@/styles/themes.css";
import { breakpoints, breakpointValues } from "@/styles/constants.css";

export const root = style({
  backgroundColor: theme.colors.primary.asidGreen,
  borderRadius: "24px",
  padding: "20px",
  marginTop: "40px",
  display: "flex",
  flexDirection: "column",
  gap: "20px",
  color: theme.colors.primary.castletonGreen,
  "@media": {
    [breakpoints.tablet]: {
      padding: "40px",
    },
  },
});

export const contentWrapper = style({
  display: "flex",
  flexDirection: "column",
  gap: "40px",
  "@media": {
    [breakpoints.desktop]: {
      flexDirection: "row",
      alignItems: "flex-end",
      gap: "60px",
    },
  },
});

export const content = style({
  flex: 1,
  display: "flex",
  flexDirection: "column",
  justifyContent: "flex-end",
});

export const title = style({
  fontSize: "44px",
  lineHeight: "95%",
  letterSpacing: "-2%",
  fontFamily: theme.fonts.primary,
  fontWeight: 400,
  marginBottom: "40px",

  "@media": {
    [breakpoints.tablet]: {
      fontSize: "56px",
      marginBottom: "0",
    },
    [breakpoints.desktop]: {
      fontSize: "64px",
    },
  },
});

export const titleBold = style({
  fontWeight: 700,
  fontStyle: "italic",
});

export const description = style({
  fontSize: "18px",
  lineHeight: "140%",
  fontFamily: theme.fonts.secondary,
  fontWeight: 400,
  maxWidth: "672px",
  "@media": {
    [breakpoints.tablet]: {
      fontSize: "20px",
    },
  },
});

export const formSection = style({
  flex: 1,
  display: "flex",
  flexDirection: "column",
  gap: "16px",
  "@media": {
    [breakpoints.desktop]: {
      maxWidth: "600px",
    },
  },
});

export const emailLabel = style({
  fontSize: "16px",
  fontWeight: 600,
  fontFamily: theme.fonts.secondary,
  color: theme.colors.primary.castletonGreen,

  display: "block",
});

export const formContainer = style({
  display: "flex",
  flexDirection: "column",
  gap: "8px",
});

export const inputWrapper = style({
  display: "flex",
  gap: "12px",
  "@media": {
    [`screen and (max-width: ${breakpointValues.sm - 1}px)`]: {
      flexDirection: "column",
    },
  },
});

export const emailInput = style({
  flex: 1,
  padding: "16px 20px",
  borderRadius: "8px",
  border: "none",
  fontSize: "16px",
  fontFamily: theme.fonts.secondary,
  backgroundColor: theme.colors.primary.ivory,
  color: theme.colors.primary.castletonGreen,
  outline: "none",
  "::placeholder": {
    color: "#999",
  },
  ":focus": {
    boxShadow: `0 0 0 2px ${theme.colors.primary.castletonGreen}`,
  },
});

export const submitButton = style({
  padding: "16px 24px",
  backgroundColor: theme.colors.primary.castletonGreen,
  color: theme.colors.primary.ivory,
  border: "none",
  borderRadius: "8px",
  fontSize: "16px",
  fontWeight: 600,
  fontFamily: theme.fonts.secondary,
  cursor: "pointer",
  transition: "all 0.2s ease",
  display: "flex",
  alignItems: "center",
  justifyContent: "center",
  minWidth: "80px",
  ":hover": {
    backgroundColor: "#002A1A",
    transform: "translateY(-1px)",
  },
  ":active": {
    transform: "translateY(0)",
  },
  "@media": {
    [`screen and (max-width: ${breakpointValues.sm - 1}px)`]: {
      width: "100%",
    },
  },
});

export const privacyText = style({
  fontSize: "16px",
  lineHeight: "140%",
  fontFamily: theme.fonts.secondary,
  color: theme.colors.primary.castletonGreen,
  opacity: 0.8,
});

export const toast = style({
  position: "fixed",
  top: "20px",
  right: "20px",
  backgroundColor: theme.colors.primary.asidGreen,
  color: theme.colors.primary.castletonGreen,
  padding: "12px 20px",
  borderRadius: "8px",
  fontSize: "14px",
  fontFamily: theme.fonts.secondary,
  fontWeight: 500,
  boxShadow: "0 4px 12px rgba(0, 0, 0, 0.15)",
  zIndex: 9999,
  opacity: 0,
  transform: "translateX(100%)",
  transition: "all 0.3s ease",
});

export const toastVisible = style({
  opacity: 1,
  transform: "translateX(0)",
});

export const toastError = style({
  backgroundColor: "#dc3545",
  color: "#fff",
});
