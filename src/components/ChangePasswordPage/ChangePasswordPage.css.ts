import { breakpoints } from "@/styles/constants.css";
import { theme } from "@/styles/themes.css";
import { style } from "@vanilla-extract/css";


export const container = style({
  display: "grid"
});


export const deleteAccountWrapper = style({
  display: "grid",
  
  gridAutoFlow: "row",
  justifyContent: "center",
  justifyItems: "center",
  alignItems: "center",
  columnGap: 8,
  rowGap: 16,
  borderTop: `1px solid ${theme.colors.grayscale[100]}`,
  paddingTop: 40,
  textAlign: "center",
  "@media": {
    [breakpoints.tablet]: {
      textAlign: "left",
      gridAutoFlow: "column",
      justifyContent: "space-between",
    }
  }
});

export const personalInfoForm = style({
  display: "grid",
  columnGap: 24,
  rowGap: 14,
  marginBottom: 24,
  "@media": {
    [breakpoints.tablet]: {
      rowGap: 16,
      marginBottom: 56,
    }
  }
});

export const submitButton = style({
  marginTop: 24,
});

export const field = style({
  marginTop: 32
});