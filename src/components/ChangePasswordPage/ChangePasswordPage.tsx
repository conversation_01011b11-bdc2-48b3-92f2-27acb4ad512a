"use client";

import { Controller, useForm } from "react-hook-form";
import Button from "../Button";
import * as styles from "./ChangePasswordPage.css";

import classNames from "classnames";
import TextInput from "../TextInput";
import { ChangePasswordPageFormValues } from "./ChangePasswordPage.types";
import { observer } from "mobx-react-lite";
import useStore from "@/hooks/useStore";

const defaultValues:ChangePasswordPageFormValues = {
  newPassword: "",
  confirmPasswrod: "",
};


const PlansPage = observer(() => {
  const { auth, alert } = useStore();

  const form = useForm<ChangePasswordPageFormValues>(
    {
      mode: "onBlur",
      defaultValues,
    }
  );

  const handleSubmit = async (data: ChangePasswordPageFormValues) => {
    try {
      await auth.resetPassword(data.newPassword);

      alert.addAlert({ content: "Password changed success!" });

      setTimeout(() => {
        form.reset({
          newPassword: "",
          confirmPasswrod: "",
        });
      }, 3000);
    } catch (error) {
      alert.addAlert({ content: String(error), type: "error" });
    }
  };

  return (
    <div
      className={styles.container}
    >
      <form
        onSubmit={form.handleSubmit(handleSubmit)}
        className={styles.personalInfoForm}
      >
        <Controller
          name="newPassword"
          rules={{
            required: "Required!",
            minLength: {
              value: 8,
              message: "Min characters to enter - 8",
            },
          }}
          control={form.control}
          render={(
            { field, fieldState }
          ) => (
            <TextInput
              {...field}
              className={styles.field}
              error={fieldState.error?.message}
              label="New password"
              placeholder="Enter new password"
              type="password"
            />
          )}
        />
        <Controller
          name="confirmPasswrod"
          rules={{
            required: "Required!",
            minLength: {
              value: 8,
              message: "Min characters to enter - 8",
            },
            validate: (value) => {
              if (!value.match(/[A-Z]/)) return "Min one uppercase character";

              if (value !== form.getValues().newPassword) return "Passwords are differ";

              return undefined;
            },
          }}
          control={form.control}
          render={(
            { field, fieldState }
          ) => (
            <TextInput
              {...field}
              className={styles.field}
              error={fieldState.error?.message}
              label="Confirm password"
              placeholder="Enter password again"
              type="password"
            />
          )}
        />
        <Button
          isLoading={form.formState.isSubmitting}
          disabled={!form.formState.isValid}
          type="submit"
          className={classNames(styles.submitButton)}
          startIcon={form.formState.isSubmitSuccessful ? (
            <svg
              width="25"
              height="24"
              viewBox="0 0 25 24"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M3.21484 11.2873C3.21484 11.2873 5.26444 9.99943 5.93116 9.76087C6.21868 10.2841 8.36044 13.7165 8.69356 14.5239C9.74332 13.5236 16.4124 6.04375 22.4148 4.75879C21.8912 5.61559 21.558 5.99767 21.558 5.99767C21.558 5.99767 11.2208 12.6673 9.74332 19.2413C8.4094 19.2413 7.7422 19.2413 7.7422 19.2413C7.7422 19.2413 5.54908 14.3343 3.21484 11.2873Z"
                fill="#003D23"
              />
            </svg>
          ) : undefined}
        >
          {form.formState.isSubmitSuccessful ? "Changes have been saved" : "Reset password"}
        </Button>
      </form>
    </div>
  );
});

export default PlansPage;