import Image from "next/image";
import vector from "./vector.png";
import * as styles from "./featureList.css";

const FeatureList = ({ features }: {features: string[]}) => {
  return (
    <ul className={ styles.featureList }>
      {features?.map((feature: string) => (
        <li className={styles.listItem} key={feature}>
          <Image src={vector.src} alt="" width={16} height={16} className={styles.checkIcon} />
          {feature}
        </li>
      ))}
    </ul>
  )
}

export default FeatureList;
