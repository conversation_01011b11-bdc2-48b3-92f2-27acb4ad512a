import { style } from "@vanilla-extract/css";
import { breakpoints } from "@/styles/constants.css";
import { theme } from "@/styles/themes.css";

export const featureList = style({
  fontSize: 16,
  lineHeight: "120%",
  listStyle: "none",
  paddingLeft: 0,
  marginBottom: "10px",

  "@media": {
    [breakpoints.desktop]: {
      fontSize: 18,
      marginBottom: "14px",
    },
  },
});

export const checkIcon = style({
  color: theme.colors.primary.asidGreen,
  marginRight: 8,
  width: "18px",
  height: "18px",
  "@media": {
    [breakpoints.desktop]: {
      width: "20px",
      height: "20px",
    },
  },
});

export const listItem = style({
  marginBottom: "14px"
})

