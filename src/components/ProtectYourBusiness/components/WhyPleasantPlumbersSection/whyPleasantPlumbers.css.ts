import { style } from "@vanilla-extract/css";
import { theme } from "@/styles/themes.css";
import { breakpoints } from "@/styles/constants.css";

export const section = style({
  backgroundColor: theme.colors.primary.ivory,
});

export const container = style({
  padding: "40px 40px 80px 40px",
  display: "flex",
  flexDirection: "column",
  gap: "48px",
  "@media": {
    "(min-width: 992px)": {
      flexDirection: "row",
      alignItems: "flex-start",
      justifyContent: "space-between",
    }
  }
});

export const leftColumn = style({
  flex: "0 0 45%",
  "@media": {
    "(max-width: 991px)": {
      marginBottom: "40px",
    }
  }
});

export const cardsColumn = style({
  flex: "0 0 50%",
  display: "grid",
  gridTemplateColumns: "1fr 1fr",
  gridTemplateRows: "auto auto",
  gap: "24px",
  "@media": {
    "(max-width: 991px)": {
      gridTemplateColumns: "1fr",
    }
  }
});

export const title = style({
  fontSize: "40px",
  lineHeight: "95%",
  color: theme.colors.primary.castletonGreen,
  marginBottom: "24px",
  fontFamily: theme.fonts.primary,
  "@media": {
    [breakpoints.desktop]: {
      fontSize: "64px",
    },
  }
});

export const italic = style({
  fontStyle: "italic",
  fontWeight: 500,
  color: theme.colors.primary.castletonGreen,
});

export const subtitle = style({
  fontSize: "18px",
  lineHeight: 1.5,
  color: theme.colors.primary.castletonGreen,
  marginBottom: "24px",
  maxWidth: "450px",
  fontWeight: 500,
});

export const description = style({
  fontSize: "16px",
  lineHeight: 1.5,
  color: theme.colors.primary.castletonGreen,
  marginBottom: "32px",
  fontWeight: 500,
});

export const card = style({
  backgroundColor: "white",
  borderRadius: "8px",
  padding: "24px",
  boxShadow: "0 4px 12px rgba(0, 0, 0, 0.05)",
  border: "1px solid rgba(0, 0, 0, 0.05)",
});

export const cardFirst = style({
  gridColumn: "1 / 2",
  gridRow: "1 / 3",
  "@media": {
    "(max-width: 991px)": {
      gridColumn: "1",
      gridRow: "1",
    }
  }
});

export const cardSecond = style({
  gridColumn: "2 / 3",
  gridRow: "1 / 2",
  "@media": {
    "(max-width: 991px)": {
      gridColumn: "1",
      gridRow: "2",
    }
  }
});

export const cardThird = style({
  gridColumn: "2 / 3",
  gridRow: "2 / 3",
  "@media": {
    "(max-width: 991px)": {
      gridColumn: "1",
      gridRow: "3",
    }
  }
});

export const cardHeader = style({
  display: "flex",
  alignItems: "center",
  gap: "12px",
  marginBottom: "16px",
});

export const cardIcon = style({
  width: "24px",
  height: "24px",
  objectFit: "contain",
});

export const cardTitle = style({
  fontSize: "18px",
  fontWeight: 600,
  color: theme.colors.primary.castletonGreen,
  margin: 0,
  fontFamily: "inherit",
});

export const cardContent = style({
  fontSize: "15px",
  lineHeight: 1.5,
  color: theme.colors.primary.castletonGreen
});

export const bulletList = style({
  listStyleType: "none",
  padding: 0,
  margin: 0,
});

export const bulletItem = style({
  position: "relative",
    paddingLeft: "20px",
    marginBottom: "16px",
    "::before": {
    content: "'•'",
      position: "absolute",
      left: 0,
      color: theme.colors.primary.castletonGreen,
      fontSize: "20px",
      lineHeight: "1",
  }
},)

export const buttonsWrapper = style({
  display: "flex",
  gap: "20px",
  marginTop: "32px",
  "@media": {
    "(max-width: 576px)": {
      flexDirection: "column",
    }
  }
});

export const whatsappButton = style([
  {
    backgroundColor: "#4CD964",
    color: "white",
    ":hover": {
      backgroundColor: "#3cb953",
    }
  }
]);

export const callButton = style({
  backgroundColor: theme.colors.primary.castletonGreen,
  color: "white",
  ":hover": {
    backgroundColor: theme.colors.primary.castletonGreen,
  }
});

export const callIcon = style({
  display: "flex",
  alignItems: "center",
  justifyContent: "center",
});
