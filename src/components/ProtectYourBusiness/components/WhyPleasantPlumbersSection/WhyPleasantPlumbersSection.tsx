import * as styles from "./whyPleasantPlumbers.css";
import Typography from "@components/Typography";
import Container from "@components/Container";
import ShieldIcon from "./shield-check-fill.png";
import ExpertiseIcon from "./clip_path_group.png";
import RecognizedIcon from "./group.png";
import { WhatsAppButton, CallButton } from "@components/ContactButtons";
import { theme } from "@/styles/themes.css";

const WhyPleasantPlumbersSection = () => (
  <Container>
    <section className={styles.section}>
      <div className={styles.container}>
        <div className={styles.leftColumn}>
          <Typography variant="h2" className={styles.title}>
            Why <span className={styles.italic}>Pleasant<br/> Plumbers?</span>
          </Typography>
          <Typography variant="h5" className={styles.subtitle}>
            Expert surveys. Zero disruption. Trusted by London&apos;s leading property managers.
          </Typography>
          <Typography variant="h5" className={styles.description}>
            We deliver clear, compliant reports — fast.
          </Typography>
          <div className={styles.buttonsWrapper}>
            <WhatsAppButton
              className={styles.whatsappButton}
            />
            <CallButton/>
          </div>
        </div>

        <div className={styles.cardsColumn}>
          <div className={`${styles.card} ${styles.cardFirst}`}>
            <div className={styles.cardHeader}>
              <img src={ExpertiseIcon.src} alt="Expertise" className={styles.cardIcon} />
              <h3 className={styles.cardTitle}>Unmatched Expertise</h3>
            </div>
            <div className={styles.cardContent}>
              <ul className={styles.bulletList}>
                <li className={styles.bulletItem}>75+ years of commercial plumbing experience between our team</li>
                <li className={styles.bulletItem}>Trusted by facilities managers, landlords, and property developers across all of London</li>
              </ul>
            </div>
          </div>

          <div className={`${styles.card} ${styles.cardSecond}`}>
            <div className={styles.cardHeader}>
              <img src={RecognizedIcon.src} alt="Recognized" className={styles.cardIcon} />
              <h3 className={styles.cardTitle}>Recognised and Accredited</h3>
            </div>
            <div className={styles.cardContent}>
              <p>Our engineers and operations are certified to the highest industry standards</p>
            </div>
          </div>

          <div className={`${styles.card} ${styles.cardThird}`}>
            <div className={styles.cardHeader}>
              <img src={ShieldIcon.src} alt="Shield" className={styles.cardIcon} />
              <h3 className={styles.cardTitle}>Client-First Guarantee</h3>
            </div>
            <div className={styles.cardContent}>
              <p>If you&apos;re not 100% satisfied with the value we deliver, we&apos;ll refund your survey fee — no questions asked.</p>
            </div>
          </div>
        </div>
      </div>
    </section>
  </Container>
);

export default WhyPleasantPlumbersSection;
