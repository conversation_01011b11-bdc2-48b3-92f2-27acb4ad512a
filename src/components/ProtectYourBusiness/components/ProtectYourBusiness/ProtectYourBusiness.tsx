"use client";

import Container from "../../../Container";
import * as styles from "./ProtectYourBusiness.css";
import Image from "next/image";
// import plumberImage from "@/assets/plumber.png";
import Typography from "../../../Typography";
import Button from "@components/Button";
import ProtectYourBusinessImg from "./protect_business.png";
import WhatsAppIcon from "@/assets/icons/WhatsAppIcon";
import PhoneIcon from "@/assets/icons/PhoneIcon";
import FeatureList from "@components/ProtectYourBusiness/components/FeatureList/FeatureList";
import { WhatsAppButton, CallButton } from "@components/ContactButtons";

const featureList = [
  'Professional Commercial Plumbing Survey',
  'Full Digital + Physical Report For Insurance & Compliance',
  'Guaranteed Appointment Within 7 Days'
]

const ProtectYourBusiness = () => {
  return (
    <Container>
      <div className={ styles.headerRoot }>
        <div className={ styles.headerContainer }>
          <div className={ styles.textColumn }>
            <Typography variant="h1" className={ styles.title }>
              <span className={ styles.highlight }>Protect</span> Your <br className={styles.desktopBreak} />
              Business From Hidden <br className={styles.desktopBreak} />
              Plumbing Risks
            </Typography>
            <Typography variant="h2" className={ styles.offer }>
              Full Survey, Full Documentation — <span className={styles.priceBreak}>Usually <s>£420+VAT</s> Now <span className={ styles.offerPrice }>£200+VAT</span></span>
            </Typography>
            <FeatureList features={featureList}/>
            <p className={styles.contactUs}>Contact Us to Claim Offer</p>
            <div className={ styles.buttons }>
              <WhatsAppButton
              />
              <CallButton
                className={styles.callButton}
              />
            </div>
          </div>
          <div className={styles.imageContainer}>
            <Image
              src={ ProtectYourBusinessImg.src }
              alt="Plumber"
              fill
              objectFit="contain"
              objectPosition="center"
              priority
            />
          </div>
        </div>
      </div>
    </Container>
  );
};

export default ProtectYourBusiness;
