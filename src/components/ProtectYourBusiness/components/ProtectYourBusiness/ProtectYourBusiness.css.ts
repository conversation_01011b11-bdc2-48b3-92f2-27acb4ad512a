import { breakpoints } from "@/styles/constants.css";
import { theme } from "@/styles/themes.css";
import { style } from "@vanilla-extract/css";

export const headerRoot = style({
  backgroundColor: theme.colors.primary.castletonGreen,
  scrollSnapAlign: "start",
  position: "relative",
  margin: "8px 0 0 0",
  borderRadius: "24px",
  "@media": {
    [breakpoints.tablet]: {
      margin: "12px 0 0 0",
    }
  }
});

export const headerContainer = style({
  backgroundColor: theme.colors.primary.castletonGreen,
  borderRadius: 16,
  padding: "30px 20px",
  display: "flex",
  flexDirection: "column",
  alignItems: "flex-start",
  color: theme.colors.primary.softWhite,
  position: "relative",
  overflow: "hidden",

  "@media": {
    [breakpoints.tablet]: {
      borderRadius: 24,
      padding: "48px",
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
      minHeight: "500px",
    },
  },
});

export const textColumn = style({
  flex: 1,
  display: "flex",
  flexDirection: "column",
  gap: 16,
  maxWidth: "100%",
  zIndex: 2,

  "@media": {
    [breakpoints.tablet]: {
      maxWidth: "992px",
      gap: 20,
    },
  },
});

export const title = style({
  fontFamily: theme.fonts.primary,
  fontSize: 32,
  lineHeight: "95%",
  fontWeight: 400,
  maxWidth: "100%",
  marginBottom: "10px",
  color: "white",

  "@media": {
    "(max-width: 767px)": {
      fontSize: 28,
      whiteSpace: "normal",
      wordWrap: "break-word",
      width: "100%",
    },
    [breakpoints.tablet]: {
      fontSize: 48,
    },
    [breakpoints.desktop]: {
      fontSize: 88,
      letterSpacing: "-2%",
    },
  },
});

export const desktopBreak = style({
  display: "none",

  "@media": {
    [breakpoints.tablet]: {
      display: "block",
    },
  },
});

export const highlight = style({
  color: theme.colors.primary.asidGreen,
  fontStyle: "italic",
  fontWeight: 400,
});

export const offer = style({
  fontSize: 18,
  fontWeight: 500,
  letterSpacing: "-2%",
  lineHeight: "120%",
  marginBottom: "15px",

  "@media": {
    [breakpoints.desktop]: {
      fontSize: 24,
      lineHeight: "95%",
    },
  },
});

export const offerPrice = style({
  color: theme.colors.primary.asidGreen,
});

export const contactUs = style({
  color: theme.colors.primary.ivory,
  fontFeatureSettings: 'liga off, clig off',
  fontFamily: "Helvetica Neue",
  fontSize: "24px",
  fontWeight: 500,
  lineHeight: "120%",
  letterSpacing: "-0.24px",
  "@media": {
    [breakpoints.mobile]: {
      fontSize: "18px"
    },
  },
});

export const buttons = style({
  fontSize: "18px",
  display: "flex",
  flexWrap: "wrap",
  gap: 24,
  width: "100%",

  "@media": {
    [breakpoints.tablet]: {
      width: "auto",
      gap: 16,
      fontSize: "28px",
    },
  },
});

export const callButton = style({
  backgroundColor: `${theme.colors.primary.softWhite} !important`,
  color: `${theme.colors.primary.castletonGreen} !important`,
  display: "flex",
  alignItems: "center",
  justifyContent: "center",
  gap: 8,
  flex: 1,
  fontSize: "18",
  height: "54px",
  "@media": {
    [breakpoints.tablet]: {
      flex: "0 0 auto",
      height: "32px",
      fontSize: "28px",
      width: "211px",
    },
  },
});

export const imageContainer = style({
  "@media": {
    [breakpoints.tablet]: {
      display: "block",
      position: "absolute",
      width: "50%",
      height: "100%",
      right: 0,
      top: 0,
    },
    "(max-width: 950px)": {
      display: "none",
    },
  },
});

export const priceBreak = style({
  display: "inline",

  "@media": {
    "(max-width: 540px)": {
      display: "block",
      marginTop: "8px",
    }
  }
});

