import { style } from "@vanilla-extract/css";
import { theme } from "@/styles/themes.css";

export const section = style({
  backgroundColor: theme.colors.primary.ivory,
  color: theme.colors.primary.castletonGreen,
  padding: "60px 20px",
  borderRadius: 24,
  margin: "40px 0",
  "@media": {
    "(max-width: 768px)": {
      padding: "40px 20px",
      borderRadius: 16,
    },
    "(max-width: 480px)": {
      padding: "30px 20px",
      maxWidth: "400px",
      margin: "30px auto",
      borderRadius: 0,
    },
  },
});

export const content = style({
  display: "flex",
  flexDirection: "row",
  alignItems: "center",
  gap: "40px",
  "@media": {
    "(max-width: 992px)": {
      flexDirection: "column",
    },
    "(max-width: 480px)": {
      gap: "20px",
    }
  }
});

export const imageContainer = style({
  flex: "0 0 40%",
  "@media": {
    "(max-width: 992px)": {
      width: "100%",
      maxWidth: "450px",
      marginBottom: "30px",
    },
    "(max-width: 480px)": {
      display: "none",
    }
  }
});

export const image = style({
  objectFit: "cover",
  borderRadius: "16px",
});

export const textContent = style({
  flex: "0 0 55%",
  textAlign: "left",
  "@media": {
    "(max-width: 992px)": {
      width: "100%",
      textAlign: "center",
    },
    "(max-width: 480px)": {
      textAlign: "left",
      padding: "0",
    }
  }
});

export const buttonsWrapper = style({
  display: "flex",
  gap: "20px",
  marginTop: "32px",
  "@media": {
    "(max-width: 768px)": {
      justifyContent: "center",
      gap: "16px",
    },
    "(max-width: 576px)": {
      flexDirection: "column",
      width: "100%",
      maxWidth: "320px",
      margin: "20px auto 0",
      gap: "12px",
    }
  }
});

export const title = style({
  fontSize: "2.5rem",
  fontFamily: theme.fonts.primary,
  marginBottom: "20px",
  color: theme.colors.primary.castletonGreen,
  lineHeight: 1.2,
  "@media": {
    "(max-width: 768px)": {
      fontSize: "2rem",
    },
    "(max-width: 480px)": {
      fontSize: "1.5rem",
      marginBottom: "15px",
    }
  }
});

export const subtitle = style({
  fontSize: "1.25rem",
  lineHeight: 1.3,
  marginBottom: "16px",
  color: theme.colors.primary.castletonGreen,
  "@media": {
    "(max-width: 480px)": {
      fontSize: "1rem",
      marginBottom: "10px",
    }
  }
});

export const description = style({
  fontSize: "1rem",
  lineHeight: 1.6,
  marginBottom: "24px",
  color: theme.colors.primary.castletonGreen,
  "@media": {
    "(max-width: 480px)": {
      fontSize: "0.9rem",
      marginBottom: "15px",
      lineHeight: 1.4,
    }
  }
});

export const list = style({
  listStyle: "none",
  padding: 0,
  fontSize: "1rem",
  lineHeight: 1.6,
  marginBottom: "24px",
  "@media": {
    "(max-width: 992px)": {
      display: "inline-block",
      textAlign: "left",
      margin: "0 auto 24px",
    },
    "(max-width: 480px)": {
      fontSize: "0.9rem",
      marginBottom: "15px",
      lineHeight: 1.4,
    }
  }
});

export const lisItem = style({
  display: "flex",
  alignItems: "center",
  marginBottom: "8px",
  "::before": {
    content: '""',
    display: "inline-block",
    width: "20px",
    height: "20px",
    marginRight: "12px",
    backgroundImage: "url('/icons/check.svg')",
    backgroundSize: "contain",
    backgroundRepeat: "no-repeat",
    color: theme.colors.primary.asidGreen,
    // "@media": {
    //   "(max-width: 480px)": {
    //     width: "16px",
    //     height: "16px",
    //     marginRight: "8px",
    //   }
    // }
  },
  "@media": {
    "(max-width: 480px)": {
      marginBottom: "5px",
    }
  }
});

export const savingText = style({
  fontSize: "1.1rem",
  marginBottom: "24px",
  color: theme.colors.primary.castletonGreen,
  "@media": {
    "(max-width: 480px)": {
      fontSize: "0.9rem",
      marginBottom: "15px",
      lineHeight: 1.4,
    }
  }
});

export const buttons = style({
  display: "flex",
  flexWrap: "wrap",
  gap: "16px",
  "@media": {
    "(max-width: 992px)": {
      justifyContent: "center",
    }
  }
});

export const whatsappButton = style({
  backgroundColor: "#25D366",
  color: "white",
  borderRadius: "50px",
  padding: "12px 24px",
  fontWeight: 600,
  display: "flex",
  alignItems: "center",
  justifyContent: "center",
  gap: "8px",
  width: "auto",
  cursor: "pointer",
  transition: "all 0.3s ease",
  fontSize: "16px",
  boxShadow: "none",
  border: "none",
  ":hover": {
    backgroundColor: "#1da851",
  },
  "@media": {
    "(max-width: 576px)": {
      width: "100%",
      padding: "12px 0",
      borderRadius: "50px",
    },
    "(max-width: 480px)": {
      fontSize: "15px",
      padding: "10px 0",
    }
  }
});

export const callButton = style({
  backgroundColor: theme.colors.primary.castletonGreen,
  color: "white",
  borderRadius: "50px",
  padding: "12px 24px",
  fontWeight: 600,
  display: "flex",
  alignItems: "center",
  justifyContent: "center",
  gap: "8px",
  width: "auto",
  cursor: "pointer",
  transition: "all 0.3s ease",
  fontSize: "16px",
  boxShadow: "none",
  border: "none",
  ":hover": {
    backgroundColor: "#0a3b2c",
  },
  "@media": {
    "(max-width: 576px)": {
      width: "100%",
      padding: "12px 0",
      borderRadius: "50px",
    },
    "(max-width: 480px)": {
      fontSize: "15px",
      padding: "10px 0",
    }
  }
});

export const icon = style({
  marginRight: 5,
});
