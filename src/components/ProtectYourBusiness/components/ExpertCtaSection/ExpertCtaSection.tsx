import Button from "@components/Button";
import * as styles from "./expertCtaSection.css";
import WhatsAppIcon from "@/assets/icons/WhatsAppIcon";
import PhoneIcon from "@/assets/icons/PhoneIcon";
import Typography from "@components/Typography";
import Container from "@components/Container";
import Image from "next/image";
import bg from "./bg.jpg"
import { theme } from "@/styles/themes.css";
import checkIcon from "@components/ProtectYourBusiness/components/SolutionSection/noun-check.png";
import { CallButton, WhatsAppButton } from "@components/ContactButtons";

const ExpertCtaSection = () => (
  <section className={styles.section}>
    <Container>
      <div className={styles.content}>
        <div className={styles.imageContainer}>
          <Image
            src={bg.src}
            alt="Woman on a video call with plumbing experts"
            width={450}
            height={450}
            className={styles.image}
          />
        </div>
        <div className={styles.textContent}>
          <Typography variant="h2" className={styles.title}>
            Speak to a Plumbing <span style={{ fontWeight: 500 }}>Expert</span> — No Obligation, No Pressure
          </Typography>

          <Typography variant="h3" className={styles.subtitle}>
            Got questions before booking?<br />
            Want to understand if a survey is the right move?
          </Typography>

          <Typography variant="h3" className={styles.description}>
            We offer free, no-obligation video calls with our senior plumbing specialists.
            Whether you&apos;re dealing with an ongoing issue or just want a second opinion,
            we&apos;re here to help.
          </Typography>

          <ul className={styles.list}>
            <li className={styles.lisItem}>
              <Image src={checkIcon.src} alt="" width={16} height={16} className={styles.icon} />
              Quick expert advice
            </li>
            <li className={styles.lisItem}>
              <Image src={checkIcon.src} alt="" width={16} height={16} className={styles.icon} />
              No obligation to book
            </li>
            <li className={styles.lisItem}>
              <Image src={checkIcon.src} alt="" width={16} height={16} className={styles.icon} />
              Real, qualified engineers — not salespeople
            </li>
          </ul>

          <Typography variant="h3" className={styles.savingText}>
            Sometimes all it takes is a quick chat to save yourself thousands later.
          </Typography>

          <div className={styles.buttonsWrapper}>
            <WhatsAppButton />
            <CallButton />
          </div>
        </div>
      </div>
    </Container>
  </section>
);

export default ExpertCtaSection;
