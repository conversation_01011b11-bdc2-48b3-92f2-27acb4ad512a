import { style } from "@vanilla-extract/css";
import { theme } from "@/styles/themes.css";
import { breakpoints } from "@/styles/constants.css";

export const section = style({
  marginTop: "40px",
  padding: "40px 20px",
  color: theme.colors.primary.softWhite,
  fontFamily: theme.fonts.primary,
  minHeight: 400,
  backgroundRepeat: "no-repeat",
  borderRadius: "16px",
  backgroundPosition: "center right",
  backgroundSize: "cover",
  display: "flex",
  flexDirection: "column",
  justifyContent: "center",
  position: "relative",

  "@media": {
    [breakpoints.tablet]: {
      padding: "60px 20px",
      minHeight: 500,
      borderRadius: "24px",
      backgroundPosition: "right center",
      justifyContent: "flex-start",
    }
  }
});

export const overlay = style({
  position: "absolute",
  top: 0,
  left: 0,
  right: 0,
  bottom: 0,
  backgroundColor: "rgba(0, 0, 0, 0.5)",
  borderRadius: "16px",

  "@media": {
    [breakpoints.tablet]: {
      backgroundColor: "transparent",
      background: "linear-gradient(90deg, rgba(0,0,0,0.6) 0%, rgba(0,0,0,0.4) 40%, rgba(0,0,0,0) 70%)",
      borderRadius: "24px",
    }
  }
});

export const content = style({
  position: "relative",
  zIndex: 1,
  width: "100%",

  "@media": {
    [breakpoints.tablet]: {
      width: "auto",
    }
  }
});

export const title = style({
  fontWeight: "lighter",
  fontSize: 40,
  lineHeight: 1.2,
  width: "100%",
  marginBottom: 20,
  marginTop: "20px",
  marginLeft: "0",
  textAlign: "left",

  "@media": {
    [breakpoints.tablet]: {
      fontSize: 56,
      lineHeight: "95%",
      width: "607px",
      marginTop: "40px",
      marginLeft: "25px",
    }
  }
});

export const text = style({
  fontSize: 16,
  lineHeight: 1.6,
  width: "100%",
  marginLeft: "0",
  textAlign: "center",

  "@media": {
    [breakpoints.tablet]: {
      width: "35%",
      marginLeft: "25px",
      textAlign: "left",
    }
  }
});
