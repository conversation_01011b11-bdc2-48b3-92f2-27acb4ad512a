import * as styles from "./whatsIncludedSection.css";
import Typography from "@components/Typography";
import Container from "@components/Container";
import DocImage from "./doc.png";

const WhatsIncludedSection = () => (
  <section className={styles.section}>
    <Container>
      <div className={styles.container}>
        <div className={styles.contentWrapper}>
          <div className={styles.text}>
            <div className={styles.titleWrapper}>
              <Typography variant="h2" className={styles.title}>{"What's Included"}</Typography>
            </div>
          </div>
          <div className={styles.content}>
            <div className={styles.cardsGrid}>
              <div className={styles.card}>
                <span className={styles.checkmark} style={{color: 'green'}}>✓</span>
                <span className={styles.cardText}>
                  Comprehensive on-site plumbing inspection by certified commercial specialists
                </span>
              </div>

              <div className={styles.card}>
                <span className={styles.checkmark} style={{color: 'green'}}>✓</span>
                <span className={styles.cardText}>
                  Full written report, provided both physically and digitally for your records
                </span>
              </div>

              <div className={styles.card}>
                <span className={styles.checkmark} style={{color: 'green'}}>✓</span>
                <span className={styles.cardText}>
                  £200 rebate towards any remedial work booked within 30 days
                </span>
              </div>

              <div className={styles.card}>
                <span className={styles.checkmark} style={{color: 'green'}}>✓</span>
                <span className={styles.cardText}>
                  100% Satisfaction Guarantee — or your money back
                </span>
              </div>

              <div className={styles.card}>
                <span className={styles.checkmark} style={{color: 'green'}}>✓</span>
                <span className={styles.cardText}>
                  Immediate actionable recommendations
                </span>
              </div>

              <div className={styles.card}>
                <span className={styles.checkmark} style={{color: 'green'}}>✓</span>
                <span className={styles.cardText}>
                  Guaranteed appointment within 7 days of booking
                </span>
              </div>

              <div className={`${styles.card} ${styles.fullWidthCard}`}>
                <span className={styles.checkmark} style={{color: 'green'}}>✓</span>
                <span className={styles.cardText}>
                  Documentation structured to assist with insurance policies, risk management, and compliance reporting
                </span>
              </div>
            </div>
            <img src={DocImage.src} alt="Plumbing Survey Report" className={styles.docImage} />
          </div>
        </div>
      </div>
    </Container>
  </section>
);

export default WhatsIncludedSection;
