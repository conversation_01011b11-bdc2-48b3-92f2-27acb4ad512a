import { style } from "@vanilla-extract/css";
import { theme } from "@/styles/themes.css";
import { breakpoints } from "@/styles/constants.css";

export const section = style({
  padding: "80px 0",
  color: "white",
  "@media": {
    "(max-width: 767px)": {
      padding: "40px 0",
    }
  }
});

export const container = style({
  backgroundColor: theme.colors.primary.castletonGreen,
  borderRadius: 24,
  padding: "60px 40px 60px 40px",
  display: "flex",
  flexDirection: "column",
  position: "relative",
  overflow: "visible",
  minHeight: "500px",
  "@media": {
    "(max-width: 767px)": {
      padding: "40px 20px",
      minHeight: "auto",
    }
  }
});

export const text = style({
  display: "flex",
  flexDirection: "row",
  justifyContent: "space-between",
  alignItems: "flex-start",
  marginBottom: "40px",
  position: "relative",
  zIndex: 2,
  "@media": {
    "(max-width: 767px)": {
      flexDirection: "column",
      marginBottom: "30px",
    }
  }
})

export const titleWrapper = style({
  flex: "0 0 auto",
});

export const descriptionWrapper = style({
  flex: "0 0 auto",
  maxWidth: "320px",
  textAlign: "right",
  "@media": {
    "(max-width: 767px)": {
      maxWidth: "100%",
      marginTop: "16px",
      textAlign: "left",
    }
  }
});

export const title = style({
  fontFamily: "Roca One",
  fontSize: "40px",
  lineHeight: "95%",
  color: theme.colors.primary.ivory,
  margin: 0,
  "@media": {
    [breakpoints.desktop]: {
      fontSize: "64px",
    },
  }
});

export const description = style({
  fontSize: 16,
  lineHeight: 1.6,
  opacity: 0.8,
  margin: 0,
  textAlign: "left"
});

export const contentWrapper = style({
  display: "flex",
  flexDirection: "column",
  position: "relative",
});

export const content = style({
  display: "flex",
  position: "relative",
  overflow: "visible",
  "@media": {
    "(max-width: 767px)": {
      flexDirection: "column",
    }
  }
})

export const docImage = style({
  position: "absolute",
  width: "60%",
  maxWidth: "650px",
  right: "0",
  top: "55%",
  transform: "translateY(-50%) rotate(0deg)",
  zIndex: 1,
  "@media": {
    "(max-width: 767px)": {
      position: "relative",
      width: "100%",
      margin: "-125px auto 0 auto",
      right: "auto",
      top: "13%",
      transform: "rotate(1deg)",
      order: 2,
    },
    [breakpoints.desktop]: {
      top: "57%",
      maxWidth: "600px",
    }
  }
});

export const cardsGrid = style({
  display: "grid",
  gridTemplateColumns: "1fr",
  gap: "24px",
  width: "50%",
  position: "relative",
  zIndex: 2,
  "@media": {
    "(min-width: 768px)": {
      gridTemplateColumns: "repeat(2, 1fr)",
      gap: "20px",
    },
    "(max-width: 767px)": {
      width: "100%",
      order: 1,
    }
  }
});

export const card = style({
  lineHeight: "120%",
  fontSize: "16px",
  backgroundColor: theme.colors.primary.ivory,
  borderRadius: "16px",
  padding: "20px",
  color: theme.colors.primary.castletonGreen,
  display: "flex",
  alignItems: "flex-start",
  minHeight: "80px",
  boxSizing: "border-box",
  "@media": {
    "(max-width: 767px)": {
      minHeight: "auto",
    },
    [breakpoints.desktop]: {
      borderRadius: "16px",
      padding: "24px",
      fontSize: "20px",
      letterSpacing: "-1%"
    }
  }
});

export const checkmark = style({
  color: "#4CD964",
  marginRight: "12px",
  fontSize: "16px",
  fontWeight: "bold",
  flexShrink: 0,
  marginTop: "3px",
});

export const cardText = style({
  fontSize: "15px",
  fontWeight: 500,
  lineHeight: 1.4,
});

export const fullWidthCard = style({
  gridColumn: "1 / -1",
});
