import { style } from "@vanilla-extract/css";
import { theme } from "@/styles/themes.css";
import { breakpoints } from "@/styles/constants.css";

export const section = style({
  backgroundColor: theme.colors.primary.ivory,
  padding: "14px 0",
  position: "sticky",
  top: 0,
  zIndex: 100,
  transition: "box-shadow 0.3s ease",
});

export const container = style({
  display: "flex",
  flexDirection: "column",
  gap: "15px",
  padding: "0 20px",

  "@media": {
    [breakpoints.tablet]: {
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
      gap: "0",
    },
  },
});

/* Top row — only on mobile */
export const topMobile = style({
  display: "flex",
  justifyContent: "space-between",
  gap: "20px",
  flexWrap: "wrap",
  borderBottom: `1px solid ${theme.colors.grayscale[100]}`,
  paddingBottom: "14px",
  "@media": {
    [breakpoints.tablet]: {
      display: "none",
    },
  },
});

/* Bottom row — mobile: logo left + button right, desktop: all right-aligned */
export const bottom = style({
  display: "flex",
  justifyContent: "space-between",
  alignItems: "center",

  "@media": {
    [breakpoints.tablet]: {
      width: "100%",
    },
  },
});

/* Desktop right part (email, phone, button) */
export const rightDesktop = style({
  display: "none",

  "@media": {
    [breakpoints.tablet]: {
      display: "flex",
      alignItems: "center",
      gap: "32px",
      marginLeft: "auto",
      marginRight: "32px"
    },
  },
});

export const logo = style({
  objectFit: "contain",
  width: "80px",
  height: "47px",

  "@media": {
    [breakpoints.tablet]: {
      width: "108px",
      height: "50px",
    },
  },
});

export const contactLink = style({
  display: "flex",
  alignItems: "center",
  gap: "4px",
  color: theme.colors.primary.castletonGreen,
  fontSize: "0.8rem",
  fontWeight: 500,
  textDecoration: "underline",

  "@media": {
    [breakpoints.tablet]: {
      gap: "8px",
      fontSize: "0.85rem",
    },
    [breakpoints.desktop]: {
      fontSize: "1rem",
    },
  },
});


export const phoneLink = style({
  display: "flex",
  alignItems: "center",
  gap: "4px",
  textDecoration: "none",

  "@media": {
    [breakpoints.tablet]: {
      gap: "8px",
    },
  },
});


export const ctaButton = style({
  backgroundColor: theme.colors.primary.asidGreen,
  color: theme.colors.primary.castletonGreen,
  fontSize: "16px",
  letterSpacing: "-2%",
  borderRadius: "30px",
  padding: "11.5px 24px",
  border: "none",
  cursor: "pointer",
  whiteSpace: "nowrap",
  fontWeight: 500,
  fontFamily: theme.fonts.secondary
});

// New mobile-specific styles
export const mobileContainer = style({
  display: "flex",
  justifyContent: "space-between",
  alignItems: "center",
  padding: "0 20px",
  backgroundColor: "#f5f2ec",
});

export const mobileActions = style({
  display: "flex",
  alignItems: "center",
  gap: "10px",
});

export const mobileIconButton = style({
  background: "transparent",
  border: "none",
  padding: "8px",
  cursor: "pointer",
  display: "flex",
  alignItems: "center",
  justifyContent: "center",
});

export const mobileCTA = style({
  backgroundColor: "#00ff66",
  color: "#000",
  fontWeight: 500,
  fontSize: "16px",
  padding: "12px 24px",
  borderRadius: "50px",
  border: "none",
  cursor: "pointer",
  textDecoration: "none",
  display: "inline-flex",
  alignItems: "center",
  justifyContent: "center",
  width: "auto",
  height: "42px"
});
