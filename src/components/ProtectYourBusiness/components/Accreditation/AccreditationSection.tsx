import * as styles from "./accreditation.css";
import { section } from "./accreditation.css";
import Typography from "@components/Typography";
import Container from "@components/Container";
import { PrismicNextImage, PrismicNextLink } from "@prismicio/next";
import { ImageFieldImage } from "@prismicio/client";

type AccreditationItem = {
  accreditation_image: ImageFieldImage;
  accreditation_link: {
    link_type: string;
    url?: string;
    [key: string]: any;
  };
}

type Props = {
  accreditationsItems: AccreditationItem[]
};

const AccreditationSection = (props: Props) => {
  return <Container>
    <section className={styles.section}>
      <div className={styles.accreditationContainer}>
        <div className={styles.titleColumn}>
          <Typography variant="h2" className={styles.accreditationTitle}>
            Vetted and <br />
            <span className={styles.italic}>Accredited by</span>
          </Typography>
        </div>
        <div className={styles.logosColumn}>
          <div className={styles.logosGrid}>
            {props?.accreditationsItems?.map((item) => (
              <div
                className={styles.logoContainer}
                key={item.accreditation_image.id}
              >
                <div
                  className={styles.accreditationWrapper}
                >
                  <PrismicNextLink
                    field={item.accreditation_link}
                    className={styles.accreditationImageWr}
                  >
                    <PrismicNextImage
                      className={styles.accreditationImage}
                      field={item.accreditation_image} height={100} width={100}
                    />
                  </PrismicNextLink>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  </Container>
};

export default AccreditationSection;
