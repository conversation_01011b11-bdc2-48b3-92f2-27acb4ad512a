import { style } from "@vanilla-extract/css";
import { theme } from "@/styles/themes.css";
import { mode } from "@/styles/functions.css";
import { breakpoints } from "@/styles/constants.css";

export const section = style({
  backgroundColor: theme.colors.primary.castletonGreen,
  borderRadius: "16px",
  padding: "40px 60px",
  margin: "40px 0",
});

export const accreditationContainer = style({
  display: "flex",
  justifyContent: "space-between",
  "@media": {
    "(max-width: 768px)": {
      flexDirection: "column",
      gap: "40px",
    }
  }
});

export const titleColumn = style({
  flex: "0 0 35%",
  "@media": {
    "(max-width: 768px)": {
      flex: "1 1 100%",
      textAlign: "center",
      marginBottom: "30px",
    }
  }
});

export const logosColumn = style({
  flex: "0 0 50%",
  "@media": {
    "(max-width: 768px)": {
      flex: "1 1 100%",
    }
  }
});

export const accreditationTitle = style({
  fontSize: "42px",
  lineHeight: "1.2",
  color: "white",
  fontFamily: theme.fonts.primary,
  fontWeight: "normal",
  "@media": {
    "(max-width: 768px)": {
      fontSize: "32px",
    }
  }
});

export const italic = style({
  fontStyle: "italic",
  fontWeight: 500,
  color: "white",
});

export const logosGrid = style({
  display: "grid",
  gridTemplateColumns: "repeat(3, 1fr)",
  gridTemplateRows: "repeat(2, 1fr)",
  gap: "20px",
  "@media": {
    "(max-width: 768px)": {
      gridTemplateColumns: "repeat(2, 1fr)",
    },
    "(max-width: 480px)": {
      gridTemplateColumns: "1fr",
    }
  }
});

export const logoContainer = style({
  backgroundColor: "white",
  borderRadius: "8px",
  display: "flex",
  alignItems: "center",
  justifyContent: "center",
  padding: "15px",
  height: "100px",
  width: "100%",
  border: `2px solid ${theme.colors.primary.asidGreen}`,
});

export const accreditationWrapper = style({
  display: "flex",
  alignItems: "center",
  justifyContent: "center",
  width: "100%",
  height: "100%",
});

export const accreditationImageWr = style({
  display: "flex",
  alignItems: "center",
  justifyContent: "center",
  width: "100%",
  height: "100%",
});

export const accreditationImage = style({
  maxWidth: "100%",
  maxHeight: "100%",
  // objectFit: "contain",
});

