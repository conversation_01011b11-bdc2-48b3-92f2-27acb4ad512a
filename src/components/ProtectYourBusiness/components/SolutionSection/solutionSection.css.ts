import { style } from "@vanilla-extract/css";
import { theme } from "@/styles/themes.css";
import { breakpoints } from "@/styles/constants.css";

export const section = style({
  padding: "40px 20px",
  backgroundColor: theme.colors.primary.ivory,
  "@media": {
    [breakpoints.tablet]: {
      padding: "60px 20px",
    },
  },
});

export const container = style({
  display: "flex",
  flexDirection: "column",
  gap: "30px",

  "@media": {
    [breakpoints.tablet]: {
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "space-between",
    },
  },
});

export const info = style({
  flex: 1,
  maxWidth: "100%",
  padding: 0,

  "@media": {
    [breakpoints.tablet]: {
      paddingRight: "20px",
      maxWidth: "60%",
    },
    [breakpoints.desktop]: {
      maxWidth: "564px",
    },
  },
});

export const imageWrapper = style({
  position: "relative",
  flex: 1,
  maxWidth: "640px",
  maxHeight: "480px",
  aspectRatio: "4 / 3",

  "@media": {
    [breakpoints.tablet]: {
      // maxWidth: "50%",
      // aspectRatio: "4 / 3",
    },
    [breakpoints.desktop]: {
      // maxWidth: "400px",
      // aspectRatio: "4 / 3",
    },
  },
});

export const image = style({
  width: "500px"
});

export const title = style({
  fontSize: "40px",
  lineHeight: "95%",
  color: theme.colors.primary.castletonGreen,
  marginBottom: "73px",
  fontFamily: theme.fonts.primary,
  "@media": {
    [breakpoints.desktop]: {
      fontSize: "64px",
    },
  },
});

export const list = style({
  listStyle: "none",
  padding: 0,
  margin: 0,
  fontSize: "16px",
  fontWeight: 500,
  lineHeight: 1.2,
  color: theme.colors.primary.castletonGreen,

  "@media": {
    [breakpoints.tablet]: {
      fontSize: "18px",
    },
  },
});

export const listItem = style({
  display: "flex",
  alignItems: "flex-start",
  gap: "10px",
  marginBottom: "14px",
});

export const icon = style({
  marginTop: "5px",
  flexShrink: 0,
});
