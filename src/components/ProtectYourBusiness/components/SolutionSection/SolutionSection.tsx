import * as styles from "./solutionSection.css";
import Container from "@components/Container";
import Typography from "@components/Typography";
import solutionImg from "./solution.png";
import Image from "next/image";
import checkIcon from "./noun-check.png";

const SolutionSection = () => (
  <section className={styles.section}>
    <Container className={styles.container}>
      <div className={styles.info}>
        <Typography variant="h2" className={styles.title}>
          Our Commercial Plumbing Survey gives you complete clarity:
        </Typography>
        <ul className={styles.list}>
          <li className={styles.listItem}>
            <Image src={checkIcon.src} alt="" width={16} height={16} className={styles.icon} />
            We diagnose hidden risks across your entire plumbing system.
          </li>
          <li className={styles.listItem}>
            <Image src={checkIcon.src} alt="" width={16} height={16} className={styles.icon} />
            We provide full physical and digital documentation for your records — curated to strengthen your insurance policies and compliance standing.
          </li>
          <li className={styles.listItem}>
            <Image src={checkIcon.src} alt="" width={16} height={16} className={styles.icon} />
            Clear action plan to prevent emergencies, reduce liability, and save money long term.
          </li>
        </ul>
      </div>
      <div className={styles.imageWrapper}>
        <Image
          src={solutionImg.src}
          alt="Solution"
          fill
          className={styles.image}
        />
      </div>
    </Container>
  </section>
);

export default SolutionSection;
