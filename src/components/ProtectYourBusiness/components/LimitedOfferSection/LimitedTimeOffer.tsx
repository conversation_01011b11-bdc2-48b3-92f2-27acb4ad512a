"use client";
import { useState } from "react";
import Button from "@components/Button";
import * as styles from "./limitedTimeOffer.css";
import Typography from "@components/Typography";
import Container from "@components/Container";
import Image from "next/image";
import person from "./engineer.png";
import OfferModal from "@components/OfferModal";

const LimitedTimeOffer = () => {
  const [isModalOpen, setIsModalOpen] = useState(false);

  const openModal = () => setIsModalOpen(true);
  const closeModal = () => setIsModalOpen(false);

  return (
    <Container>
      <section className={styles.section}>
        <div className={styles.wrapper}>
          <div className={styles.content}>
            <Typography variant="h2" className={styles.title}>
              Limited Time <span className={styles.italic}>Offer</span>
            </Typography>

            <Typography variant="h3" className={styles.subtitle}>
              Only 10 discounted surveys available per month to ensure quality and thoroughness.
              Act now to secure your priority slot.
            </Typography>

            <Button variant="filled" className={styles.ctaButton} onClick={openModal}>
              → Claim Offer
            </Button>
          </div>

          <div className={styles.imageWrapper}>
            <Image
              src={person}
              alt="Pleasant Plumbers technician"
              className={styles.image}
              width={375}
              height={375}
              priority
            />
          </div>
        </div>
      </section>

      <OfferModal
        open={isModalOpen}
        onClose={closeModal}
        whatsappNumber="+1234567890"
        phoneNumber="+1234567890"
        email="<EMAIL>"
      />
    </Container>
  );
};

export default LimitedTimeOffer;
