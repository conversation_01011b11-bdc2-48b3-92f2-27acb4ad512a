import { style } from "@vanilla-extract/css";
import { theme } from "@/styles/themes.css";
import { breakpoints } from "@/styles/constants.css";

export const section = style({
  marginBottom: "40px",
  marginTop: "40px",
  position: "relative",
});

export const wrapper = style({
  borderRadius: "24px",
  display: "flex",
  alignItems: "center",
  backgroundColor: theme.colors.primary.asidGreen,
  position: "relative",
  overflow: "visible",
  "@media": {
    "(min-width: 1050px)": {
      flexDirection: "row",
      padding: "30px 40px 30px 0",
      minHeight: "220px",
    },
    "(max-width: 1049px)": {
      flexDirection: "column",
      textAlign: "center",
      padding: "20px",
      overflow: "hidden",
      height: "100%"
    },
  },
});

export const imageWrapper = style({
  display: "flex",
  justifyContent: "center",
  "@media": {
    "(min-width: 1050px)": {
      // width: "375px",
      height: "375px",
      flexShrink: 0,
      position: "absolute",
      bottom: "0",
      left: "40px",
      zIndex: 1,
    },
    "(max-width: 1049px)": {
      // maxWidth: "375px",
      // maxHeight: "375px",
      minWidth: "300px",
      height: "100%",
      position: "relative",
      top: "20px"
    },
  },
});

export const image = style({
  width: "100%",
  height: "auto",
});

export const content = style({
  display: "flex",
  flexDirection: "column",
  justifyContent: "center",
  gap: "20px",
  color: theme.colors.primary.castletonGreen,
  "@media": {
    "(min-width: 1050px)": {
      paddingLeft: "300px",
      maxWidth: "800px",
      marginLeft: "auto",
      marginRight: "auto",
      alignItems: "flex-start",
      textAlign: "left",
    },
    "(max-width: 1049px)": {
      alignItems: "center",
      textAlign: "center",
      gap: 0
    },
  },
});

export const title = style({
  fontSize: "48px",
  lineHeight: 1.2,
  fontFamily: theme.fonts.primary,
  marginBottom: "10px",
  "@media": {
    "(max-width: 767px)": {
      fontSize: "36px",
    },
  },
});

export const italic = style({
  fontStyle: "italic",
  fontWeight: 500,
});

export const subtitle = style({
  fontFamily: "Helvetica Neue, Arial, sans-serif",
  fontSize: "18px",
  fontStyle: "normal",
  fontWeight: 500,
  lineHeight: "140%",
  marginBottom: "20px",
});

export const ctaButton = style({
  backgroundColor: `${theme.colors.primary.castletonGreen} !important`,
  color: "white !important",
  borderRadius: "50px",
  padding: "12px 30px",
  fontWeight: 600,
  display: "inline-flex",
  alignItems: "center",
  justifyContent: "center",
  gap: "8px",
  cursor: "pointer",
  transition: "background-color 0.3s ease",
  "@media": {
    "(max-width: 1049px)": {
      position: "absolute",
      bottom: "25px",
      gap: "0",
      margin: "0 auto",
      zIndex: 2,
      backgroundColor: "white !important",
      color: "black !important",
      width: "80%",
    },
  },
  ":hover": {
    backgroundColor: theme.colors.primary.castletonGreen,
    color: "white",
  },
});
