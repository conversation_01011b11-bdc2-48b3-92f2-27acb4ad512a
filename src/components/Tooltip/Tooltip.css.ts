import { theme } from "@/styles/themes.css";
import { style, styleVariants } from "@vanilla-extract/css";

export const root = style({
  opacity: 0.5,
});

export const tooltipContainerPrimary = style({
  borderRadius: 8,
  padding: "20px 44px 20px 20px",
  background: theme.colors.primary.castletonGreen,
  color: theme.colors.primary.softWhite,
  marginLeft: -8,
  marginRight: -8,
  width: "calc(100% + 16px)",
});

export const tooltipContainerSecondary = style({
  borderRadius: 4,
  columnGap: 4,
  padding: "6px 12px",
  background: theme.colors.primary.asidGreen,
  color: theme.colors.primary.castletonGreen,
});
export const tooltipContainerTertiary = style({
  borderRadius: 8,
  columnGap: 4,
  padding: "20px 36px 20px 20px",
  background: theme.colors.primary.softWhite,
  color: theme.colors.primary.castletonGreen,
});

export const tooltipContainer = styleVariants({
  primary: [tooltipContainerPrimary],
  secondary: [tooltipContainerSecondary],
  tertiary: [tooltipContainerTertiary]
});

export const closeButtom = style({
  position: "absolute",
  top: 12,
  right: 12,
  fontSize: 16,
  background: "transparent",
  border: 0,
  cursor: "pointer",
  paddingBlock: 0,
  paddingInline: 0,
});