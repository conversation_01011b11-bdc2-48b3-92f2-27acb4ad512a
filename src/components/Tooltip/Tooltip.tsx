"use client";

import Popup from "reactjs-popup";
import * as styles from "./Tooltip.css";
import { PopupActions } from "reactjs-popup/dist/types";
import { CSSProperties, useCallback, useEffect, useRef } from "react";
import { theme } from "@/styles/themes.css";
import MemoCloseIcon from "@/assets/icons/CloseIcon";
import TooltipProps, { TooltipVariants } from "./Tooltip.types";

const arrowStyle:Record<TooltipVariants, CSSProperties> =  {primary: {
  color: theme.colors.primary.castletonGreen,
  top: 1
}, 
secondary: {

},
tertiary: {
  top: 1,
  color: theme.colors.primary.softWhite,
}};

const Tooltip = ({ on = ["click",],closeDelay, position = ["bottom center"], variant = "primary", children,...popupProps}: TooltipProps,) => {
  const popupRef = useRef<PopupActions | null>(null);
  const timerId  = useRef<NodeJS.Timeout | null>(null);

  const triggerCloseDelay = useCallback(
    () => {
      if(timerId.current) { 
        clearTimeout(timerId.current);
      }
      timerId.current =setTimeout(() => {
        popupRef.current?.close();
      },closeDelay);
    },
    [],
  );

  return (
    <Popup 
      onOpen={closeDelay ? triggerCloseDelay : undefined}
      ref={popupRef}
      arrowStyle={arrowStyle[variant]}
      position={position}
      on={on}
      repositionOnResize
      {...popupProps}
    >
      <div
        className={styles.tooltipContainer[variant]}
      >
        {variant !== "secondary" && <button
          className={styles.closeButtom}
          type="button"
          onClick={() => popupRef.current?.close()}
        >
          <MemoCloseIcon/>
        </button>}
        {children}
      </div>
    </Popup>
  );
};

export default Tooltip;
