import { breakpoints } from "@/styles/constants.css";
import { theme } from "@/styles/themes.css";
import { globalStyle, style } from "@vanilla-extract/css";

export const root = style({
  padding: 10,
  minHeight: "100dvh",
  backgroundColor: theme.colors.primary.castletonGreen,
  display: "flex !important",
  flexDirection: "column",

  "@media": {
    [breakpoints.tablet]: {
      minHeight: "auto",
      height: "100vh",
      overflow: "hidden",
      display: "grid !important",
      padding: 20,
      backgroundColor: "transparent",
    }  
  }
});

export const primary = style({});

export const secondary = style({});

export const preview = style({
  position: "relative",
  overflow: "hidden",
  borderRadius: 24,
  backgroundColor: theme.colors.primary.castletonGreen,
  color: theme.colors.primary.asidGreen,
  padding: "8px 0 0",
  display: "flex",
  flexDirection: "column",
  alignItems: "center",

  "@media": {
    [breakpoints.tablet]: {
      padding: "50px 70px 0",
      height: "calc(100vh - 40px)",
      overflowY: "auto",
    }  
  }
});

export const content = style({
  position: "relative",
  borderRadius: 16,
  padding: "32px 20px",
  backgroundColor: theme.colors.primary.softWhite,
  display: "grid",
  justifyItems: "center",
  alignContent: "center",
  "@media": {
    [breakpoints.tablet]: {
      padding: "48px 100px",
      borderRadius: 24,
      background: "transparent"
    }  
  },

  selectors: {
    [`${secondary} &`]: {
      // backgroundColor: "transparent",
      // height: "100vh",
      //marginTop: -20,
      paddingTop: 36,
      overflowY: "auto",
    }
  }
});


export const logo = style({
  height: 44,
  minHeight: 44,
  marginBottom: 16,

  "@media": {
    [breakpoints.tablet]: {
      marginBottom: 48,
    }  
  }
});

export const title = style({
  fontFamily: theme.fonts.primary,
  textAlign: "center",
  marginBottom: 32,
  display: "none",

  "@media": {
    [breakpoints.tablet]: {
      display: "block"
    }  
  }
});

export const description = style({
  textAlign: "center",
  marginBottom: 28,
  maxWidth: 480,
});

export const previewImage = style({
  position: "relative",
  marginTop: "auto",
  width: "100%",
  height: "100%",
  display: "none",

  "@media": {
    [breakpoints.tablet]: {
      display: "block"
    }  
  }
});

globalStyle(`${previewImage} img`, {
  width: "100%",
  height: "100%",
  objectFit: "contain",
  objectPosition: "bottom center",
});

export const contentTitle = style({
  fontFamily: theme.fonts.primary,
  marginBottom: 16,
  textAlign: "center",

  "@media": {
    [breakpoints.tablet]: {
      marginBottom: 48,
    }  
  }
});

export const fields = style({
  width: "100%",
  marginBottom: 24,
  display: "grid",
  
  "@media": {
    [breakpoints.tablet]: {
      marginBottom: 32,
    }  
  }
});

export const actions = style({
  display: "flex",
  flexDirection: "column",
  alignItems: "center",
  width: "100%",
  marginBottom: 32,
  textAlign: "center",

  "@media": {
    [breakpoints.tablet]: {
      marginBottom: 24,
    }  
  }
});

export const floatingContent = style({
  position: "absolute",
  bottom: 32,
  left: 0,
  right: 0,
  margin: "0 auto",
  textAlign: "center",

  "@media": {
    [breakpoints.tablet]: {
      bottom: 48,
    }  
  }
});

globalStyle(`${floatingContent} a`, {
  textDecoration: "underline !important",
});

export const contentSubTitle = style({
  marginBottom: 48,
  textAlign: "center",
  maxWidth: 450,
  wordBreak: "break-all",
  overflowWrap: "break-word",
  whiteSpace: "normal",

  "@media": {
    [breakpoints.tablet]: {
      marginBottom: 48,
    }  
  }
});

export const floatingContentMobile = style({
  position: "relative",
  marginBottom: 20,
  textAlign: "center",

  "@media": {
    [breakpoints.tablet]: {
      position: "absolute",
      bottom: 20,
      right: 20
    }
  }
});
