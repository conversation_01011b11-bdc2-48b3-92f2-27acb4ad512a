import { gridSprinkle } from "@/styles/sprinkles.css";
import classNames from "classnames";
import { FC } from "react";
import Logo from "../Logo/Logo";
import Typography from "../Typography";
import * as styles from "./AuthLayout.css";
import Props from "./AuthLayout.types";

const AuthLayout: FC<Props> = ({
  contentClassName,
  variant = "primary",
  beforeContentTitle,
  afterTitle,
  contentTitle,
  contentSubtitle,
  title,
  content,
  contentActions,
  description,
  image,
  tooltip
}) => {
  return (
    <div
      className={classNames(styles.root, styles[variant], gridSprinkle({ type: "grid" }))}
    >
      <div
        className={classNames(styles.preview, gridSprinkle({ type: "item", cols: { mobile: 10, tablet: 5 } }))}
      >
        <Logo
          className={styles.logo}
        />
        <Typography
          className={styles.title}
          variant="h3"
        >
          {title}
        </Typography>
        {afterTitle}
        {description && (
          <Typography
            className={classNames(styles.description, gridSprinkle({ display: { mobile: "none", tablet: "block" } }))}
            variant="bodySmall"
          >
            {description}
          </Typography>
        )}
        <div
          className={styles.previewImage}
        >
          {image}        
        </div>
      </div>
      <div
        className={classNames(styles.content, contentClassName, gridSprinkle({ type: "item", cols: { mobile: 10, tablet: 5 } }))}
      >
        {beforeContentTitle}
        <Typography
          variant="h4"
          className={styles.contentTitle}
        >
          {contentTitle}
        </Typography>
        {/* Mobile tooltip - placed right after the "Log In" text */}
        {tooltip && (
          <div className={classNames(styles.floatingContentMobile, gridSprinkle({ display: { mobile: "block", tablet: "none" } }))}>
            {tooltip}
          </div>
        )}
        {contentSubtitle && (
          <Typography
            className={styles.contentSubTitle}
          >
            {contentSubtitle}
          </Typography>
        )}
        {/*{description && (
          <Typography
            className={classNames(styles.description, gridSprinkle({ display: { mobile: "block", tablet: "none" } }))}
            variant="bodySmall"
          >
            {description}
          </Typography>
        )}*/}
        {content && (<div
          className={styles.fields}
                     >
          {content}
        </div>)}
        {contentActions && (<div
          className={styles.actions}
                            >
          {contentActions}        
        </div>)}
        {tooltip && (<div
          className={classNames(styles.floatingContent, gridSprinkle({ display: { mobile: "none", tablet: "block" } }))}
                     >
          {tooltip}
        </div>)}
      </div></div>

  );
};

export default AuthLayout;