import { breakpoints } from "@/styles/constants.css";
import { mode } from "@/styles/functions.css";
import { theme } from "@/styles/themes.css";
import { style } from "@vanilla-extract/css";

export const document = style({
  selectors: {
    [mode("residential")]: {
      background: theme.colors.primary.castletonGreen,
    }
  },
});

export const container = style({
  selectors: {
    [mode("residential")]: {
      background: theme.colors.primary.castletonGreen,
      color: theme.colors.primary.softWhite,
      borderRadius: 0,
    }
  },
});

export const root = style({
  padding: "20px",
  paddingTop: 160,

  "@media": {
    [breakpoints.tablet]: {
      padding: 0,
      paddingTop: 160,
    }
  },
});

export const content = style({
  paddingBottom: 40,

  "@media": {
    [breakpoints.tablet]: {
      paddingBottom: 72,
    }
  }
});

export const section = style({
  padding: "20px 0",

  "@media": {
    [breakpoints.tablet]: {
      padding: "72px 0",
    }
  }
});


export const title = style({
  fontFamily: theme.fonts.primary,
  marginBottom: 24,

  "@media": {
    [breakpoints.tablet]: {
      marginBottom: 56,
    }
  }
});

export const menu = style({
  display: "flex",
  gap: 24,
  flexDirection: "column",
  borderTop: `1px solid ${theme.colors.grayscale[100]}`,
  borderBottom: `1px solid ${theme.colors.grayscale[100]}`,
  padding: "24px 0",
  marginBottom: 24,

  "@media": {
    [breakpoints.tablet]: {
      padding: "24px 20px",
      position: "sticky",
      top: 140,
      height: "fit-content",
    }
  },

  selectors: {
    [mode("residential")]: {
      borderTop: "1px solid rgba(222, 222, 214, 0.3)",
      borderBottom: "1px solid rgba(222, 222, 214, 0.3)",
    }
  },
});

export const menuLink = style({
  position: "relative",
  opacity: 0.5,
});

export const menuLinkActive = style({
  opacity: 1,
  fontWeight: 500,
});

export const menuIndicator = style({
  position: "absolute",
  top: 0,
  transform: "translateX(calc(-100% - 6px)) rotate(90deg)",
});

export const articleHeader = style({
  display: "flex",
  alignItems: "center",
  gap: 16,
  color: theme.colors.grayscale[200],
  marginTop: 16,
  marginBottom: 16,

  "@media": {
    [breakpoints.tablet]: {
      marginTop: 72,
      marginBottom: 32,
    },
  }
});

export const articleTags = style({
  display: "flex",
  alignItems: "center",
  gap: 12,
});

export const articleTag = style({
  padding: "0 12px",
  display: "flex",
  alignItems: "center",
  minHeight: 32,
  borderRadius: 32,
  border: `1px solid ${theme.colors.grayscale[100]}`,
  color: theme.colors.primary.castletonGreen,

  selectors: {
    [mode("residential")]: {
      borderRadius: 24,
      border: "1px solid rgba(150, 150, 145, 0.50)",
      color: theme.colors.primary.softWhite,
      fontSize: 16,
    }
  },
});

export const image = style({
  position: "relative",
  aspectRatio: "16 / 9",
  borderRadius: 16,
  overflow: "hidden",
  marginBottom: 16,
});
