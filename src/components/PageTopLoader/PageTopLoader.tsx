"use client";

import NextTopLoader from "nextjs-toploader";
import ModeProvider from "../ModeProvider";
import { theme } from "@/styles/themes.css";
import { usePathname } from "next/navigation";
import { Suspense, useMemo } from "react";

const PageTopLoader = () => {

  const pathname = usePathname();

  const isProfile = useMemo(() => pathname?.includes("/profile") , [pathname]);

  return  (<Suspense>
    <ModeProvider>
      {(mode) => <NextTopLoader
        easing="ease"
        speed={200}
        color={theme.colors.primary[mode === "commercial" || isProfile ? "castletonGreen" : "ivory"]}
        showSpinner={false}
                 />}
    </ModeProvider>
  </Suspense>);
};

export default PageTopLoader;