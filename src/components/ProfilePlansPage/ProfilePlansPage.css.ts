import { breakpoints } from "@/styles/constants.css";
import { theme } from "@/styles/themes.css";
import { style, styleVariants } from "@vanilla-extract/css";


export const container = style({
  display: "grid"
});

export const noActivePlans = style({
  display: "flex",
  height: "93px",
  padding: "20px 40px",
  justifyContent: "center",
  alignItems: "center",
  borderRadius: "16px",
  border: `1px solid ${theme.colors.grayscale[100]}`,
  marginBottom: 40,
  "@media": {
    [breakpoints.tablet]: {
      padding: 32,
      marginBottom: 32,
    }
  }
});

export const payServiceList  =style({
  display: "flex",
  flexDirection: "column-reverse",
  rowGap: 24,
  "@media": {
    [breakpoints.tablet]: {
      rowGap: 32,
    }
  }
});

export const navHeader = style({
  display: "flex",
  columnGap: 32,
  marginBottom: 40,
  justifySelf: "center",
  justifyContent: "center",
  marginTop: 20,
  "@media": {
    [breakpoints.tablet]: {
      marginTop: 0,
      justifySelf: "unset",
      rowGap: 32,
    }
  }
});

export const togglerWrapper = style({
  display: "flex",
  columnGap: 24,
  position: "relative",
  cursor: "pointer",
  alignItems: "center"
});

export const labelBadge = style({
  minHeight: 26,
  padding: "0 10px",
  borderRadius: 26,
  backgroundColor: theme.colors.primary.asidGreen,
  color: theme.colors.primary.castletonGreenPressed,
  position: "absolute",
  left: "calc(100% - 16px)",
  top: "calc(-100% - 8px)",
  whiteSpace: "nowrap",
  display: "flex",
  alignItems: "center",

  "@media": {
    [breakpoints.tablet]: {
      left: "calc(100% + 8px)",
      top: 0,
      bottom: 0,
      margin: "auto 0",
    }
  },
});

const payRecurringTypeLabelUnactive = style({
  color: theme.colors.grayscale[200],
});
const payRecurringTypeLabelBase = style({
  fontWeight: 500
});

export const payRecurringTypeLabel = styleVariants({
  unactive: [payRecurringTypeLabelBase,payRecurringTypeLabelUnactive],
  active: [payRecurringTypeLabelBase]
});


export const divLine = style({
  borderTop: `1px solid ${theme.colors.grayscale[100]}`,
  margin: "48px 0",
});

export const paymentErrorWrapper = style({
  position: "relative",
  padding: 24,
  borderRadius: 16,
  backgroundColor: theme.colors.primary.error20,
  display: "flex",
  columnGap: 16,
  alignItems: "center",
  marginBottom: 32
});

export const closeButton = style({
  cursor: "pointer",
  position: "absolute",
  top: 0,
  right: 0,
  backgroundColor: "transparent",
  padding: 16,
  border: 0
});

export const warningIcon = style({
  color: theme.colors.primary.error
});

export const loaderWrapper = style({
  height: "100%",
  display: "flex",
  justifyContent: "center",
  alignItems: "center",
  color: "currentColor",
  fontSize: 120,
});