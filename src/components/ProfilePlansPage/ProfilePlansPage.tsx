"use client";

import { use<PERSON><PERSON>back, useEffect, useMemo, useState } from "react";
import { usePathname, useSearchParams, useRouter } from "next/navigation";
// import { useRouter,} from "next/router";
import classNames from "classnames";

import ServiceToPayCard from "../ServiceToPayCard";
import * as styles from "./ProfilePlansPage.css";
import Toggler from "../Toggler";
import Typography from "../Typography";
import ProfilePlansPageProps, { PayQueryValues } from "./ProfilePlansPage.types";
import WarningIcon from "@/assets/icons/WarningIcon";
import MemoCloseIcon from "@/assets/icons/CloseIcon";
import { PLANS } from "@/utils/constants";
import { CustomerPlan } from "@/types/plans";
import { createClient } from "@/utils/supabase/client";
import { observer } from "mobx-react-lite";
import useStore from "@/hooks/useStore";
import Loader from "../Loader";
import { createQueryString, setNewParamWithoutRefresh } from "@/utils/helpers";



const PAY_QUERY_PARAM = "pay";

const supabase = createClient();


const ProfilePlansPage = observer( ({ serverPlans, initCustomerPlans }: ProfilePlansPageProps) => {

  // const router = useRouter();
  // const pathname = usePathname();
  const searchParams = useSearchParams();
  const { auth } = useStore();

  // const [customerPlans, setCustomerPlans] = useState(initCustomerPlans && initCustomerPlans.length ? initCustomerPlans : null);
  const [disabledPlans, setDisabledPlans] = useState(false);

  const [portalRedirectUrl, setPortalRedirectUrl] = useState<string | null>(null);
  const [errorNotification, setErrorNotification] = useState(true);
  
  const isYearly = searchParams?.get(PAY_QUERY_PARAM) ===  PayQueryValues.YEARLY;

  const refreshCustomerPlans = useCallback(
    async (newCustomerPlan?: CustomerPlan) => {
      auth.setCustomerPlan(newCustomerPlan ? [newCustomerPlan] : null);
      // const customerPlansResponse = await supabase.functions.invoke<CustomerPlan[]>("stripe/customers/plans", {method: "GET"});
      // setCustomerPlans({ data:customerPlansResponse.data && customerPlansResponse.data.length ? customerPlansResponse.data : null ,isLoading: false});
    },
    [],
  );
  

  const onToggleClick = useCallback(
    () => {
     

      setNewParamWithoutRefresh(createQueryString(PAY_QUERY_PARAM, isYearly ? PayQueryValues.MONTHLY : PayQueryValues.YEARLY, searchParams));
    },
    [isYearly,searchParams],
  );

  const { availablePlans,purchasedPlans } = useMemo(() =>{
    const availablePlans:typeof PLANS = [];
    const purchasedPlans:typeof PLANS = [];

    for (let i = 0; i < PLANS.length; i++) {
      const item = PLANS[i];
      const customerPlan = auth.customerPlans?.find(({metadata: {plan_name}}) =>   plan_name.includes(item.id));
      if(!!customerPlan) {
        purchasedPlans.push({...item, expires: customerPlan.current_period_end});
        continue;
      }
      availablePlans.push(item);
    }

    return {
      availablePlans,purchasedPlans
    };
  } , [auth.customerPlans]);


  const isError = !!auth.customerPlansError;

  useEffect(() => {
    if(!isError) return;

    // (async function getPortalLink () {
    //   const response  = await supabase.functions.invoke<{ redirectUrl: string}>("stripe/customers/portal", {method: "GET"});
    //   if(!response.data) return;
    //   setPortalRedirectUrl(response.data.redirectUrl);
    // })();


  }, [isError]);

  useEffect(() => {

    if(initCustomerPlans && initCustomerPlans.length) {
      auth.setCustomerPlan( initCustomerPlans );
      if(initCustomerPlans[0].plan.interval === "year") {
        setNewParamWithoutRefresh(createQueryString(PAY_QUERY_PARAM, PayQueryValues.YEARLY, searchParams));
      }
    }

    
    // (async function getCustomerPlan () {
    //   await auth.getCustomerPlanAPI();
    // })();
    
  
    return () => {
      
    };
  }, [initCustomerPlans]);
  
  
  

  return (
    <div
      className={styles.container}
    >
      {isError && errorNotification && (
        <div
          className={styles.paymentErrorWrapper}
        >
          <WarningIcon
            className={styles.warningIcon}
          />
          <Typography
            variant="bodyMedium"
          >
          Payment Error: Please update your details in the <a
              target="_blank"
              href={portalRedirectUrl || undefined}
            >Billing Info</a> section
          </Typography>
          <button
            className={styles.closeButton}
            onClick={() => setErrorNotification(false)}
            type="button"
          >
            <MemoCloseIcon/>
          </button>
        </div>)
      }
      {!purchasedPlans.length && (
        <Typography
          className={styles.noActivePlans}
          variant="subTitleMedium"
        >
          You haven’t any active plans yet
        </Typography>
      )}
      <div
        className={styles.navHeader}
      >
        <Toggler
          size="big"
          wrapperClassname={styles.togglerWrapper}
          checked={isYearly}
          variant="filledDarkDifferActiveState"
          onClick={onToggleClick}
          preffix={() => (
            <Typography
              className={styles.payRecurringTypeLabel[!isYearly ? "active" : "unactive"]}
              variant="bodySmall"
            >
              Pay Monthly
            </Typography>
          )}
          suffix={() => (
            <>
              <Typography
                className={styles.payRecurringTypeLabel[isYearly ? "active" : "unactive"]}
                variant="bodySmall"
              >
              Pay Yearly
              </Typography>
              <Typography
                variant="note"
                className={styles.labelBadge}
              >
              5% Off
              </Typography>
            </>
          )}
        />

      </div>
      {auth.customerPlansLoading
        ? (
          <div
            className={styles.loaderWrapper}
          >
            <Loader />
          </div>
        )
        : (
          <>
            <div
              className={styles.payServiceList}
            > 
              {purchasedPlans.map((plan) => {
                return (<ServiceToPayCard
                  supabase={supabase}
                  key={plan.id}
                  expireTime={plan.expires}
                  customerPlans={auth.customerPlans}
                  serverPlan={serverPlans.prices}
                  serverAppliances={serverPlans.additionalAppliances}
                  variant={plan.isHighlighted ?  "secondary" : "primary"} 
                  isYearly={isYearly}
                  isDisabled={disabledPlans}
                  setDisabledPlans={setDisabledPlans}
                  refreshCustomerPlans={refreshCustomerPlans}
                  isInactive={isError}
                  {...plan}
                        />);
              })
              }
            </div>
            <div
              className={styles.divLine}
            />
            <div
              className={styles.payServiceList}
            > 
              {availablePlans.map((plan) => {
                return (<ServiceToPayCard
                  key={plan.id}
                  supabase={supabase}
                  customerPlans={auth.customerPlans}
                  serverPlan={serverPlans.prices}
                  setDisabledPlans={setDisabledPlans}
                  serverAppliances={serverPlans.additionalAppliances}
                  variant={plan.isHighlighted ?  "secondary" : "primary"} 
                  refreshCustomerPlans={refreshCustomerPlans}
                  isYearly={isYearly}
                  havePlan={!!purchasedPlans.length}
                  isDisabled={disabledPlans}
                  {...plan}
                        />);
              })
              }
            </div>
          </>
        )
      }
    </div>
  );

});

export default ProfilePlansPage;

