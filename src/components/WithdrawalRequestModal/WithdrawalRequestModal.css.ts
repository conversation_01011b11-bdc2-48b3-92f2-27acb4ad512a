import { breakpoints } from "@/styles/constants.css";
import { theme } from "@/styles/themes.css";
import { style } from "@vanilla-extract/css";

export const field = style({
  marginBottom: 24,

  "@media": {
    [breakpoints.tablet]: {
      marginBottom: 32,
    }
  },
});

export const subLabel = style(
  {
    paddingTop: 4,
    opacity: 0.8,
  }
);

export const action = style({
  width: "100%",
});

export const container = style({
  paddingTop: 20,
  color: theme.colors.primary.castletonGreen,
  display: "grid",
  rowGap: 26,
  // "@media": {
  //   [breakpoints.tablet]: {
  //     width: 610,
  //   }
  // }

});

export const balanceCardList = style({
  display: "grid",
  rowGap: 20,
  marginTop: 16
});

export const balanceCard = style({
  padding: 24,
  background: theme.colors.primary.ivory,
  display: "grid",
  gridTemplateColumns: "1fr auto",

  columnGap: 16,
  borderRadius: 16,
  alignItems: "center",
  "@media": {
    [breakpoints.tablet]: {
      gridTemplateColumns: "2fr 1fr 1fr",
    }
  },
}

);

export const expectedBalance = style({
  display: "flex",
  alignItems: "center",
  columnGap: 12
});

export const status = style({
  border: `1px solid ${theme.colors.grayscale[100]}`,
  borderRadius: 24,
  padding: "12px 24px",
  width: "fit-content",
  justifySelf: "end",
  gridRow: "-1 / 3",
  gridColumn: 2,
  alignSelf: "center",
  gridTemplateColumns: "1fr auto",
  rowGap: 2,
  "@media": {
    [breakpoints.tablet]: {
      gridRow: "auto",
      gridColumn: "auto",
     
    }
  },
});

export const successContent = style(
  {
    display: "grid",
    rowGap: 40,
    maxWidth: 432,
    boxSizing: "border-box",
  }
);
