"use client";

import { formatNumToGBP } from "@/utils/helpers";
import { Controller, useForm } from "react-hook-form";
import Modal from "../Modal";
import Typography from "../Typography";
import * as styles from "./WithdrawalRequestModal.css";
import PendingBalanceModalProps from "./WithdrawalRequestModal.types";
import { gridSprinkle } from "@/styles/sprinkles.css";
import Button from "../Button";
import TextInput from "../TextInput";
import { PatternFormat, NumericFormat } from "react-number-format";
import classNames from "classnames";
import axios from "axios";
import { useState } from "react";
import { observer } from "mobx-react-lite";
import useStore from "@/hooks/useStore";

interface FormValues {
  amount_to_withdraw: string
  sort_code: string
  account_number: string
  account_holder: string
}

const WithdrawalRequestModal = observer(({
  ...modalProps
}: PendingBalanceModalProps) => {
  const { auth } = useStore();

  const [successModal, setSuccessModal] = useState(false);

  const form = useForm<FormValues>({
    mode: "onBlur",
    defaultValues: {
      amount_to_withdraw: "",
      sort_code: "",
      account_number: "",
      account_holder: "",
    },
  });

  const handleSubmit = async (data: FormValues) => {
    try {
      await axios.post("/api/withdrawal-requests", {
        ...data,
        amount_to_withdraw: Number(data.amount_to_withdraw.replace("£", "")) * 100
      });

      setSuccessModal(true);
    } catch (error) {
      setSuccessModal(false);
    }
  };

  return (
    <Modal
      {...modalProps}
    >
      {successModal ? (
        <div
          className={styles.successContent}
        >
          <Typography
            fontFamily="primary"
            variant="h4"
          >
              Success!
          </Typography>
          <Typography
            variant="bodyMedium"
          >
              We have received your request and a member of the team will
              contact you as soon as possible.
          </Typography>
          <Button
            onClick={(
            ) => {
              setSuccessModal(
                false
              );
              modalProps.onClose();
            }}
          >
              Ok
          </Button>
        </div>
      ) : (
        <div
          className={styles.container}
        >
          <Typography
            variant="h4"
            fontFamily="primary"
          >
            <b>Withdrawal</b> request
          </Typography>
          <div
            className={styles.expectedBalance}
          >
            <Typography
              variant="bodyMedium"
            >
            Approved earn bonuses:
            </Typography>
            <Typography
              variant="subTitleMedium"
            >
              {formatNumToGBP((auth.user?.available_balance ?? 0) / 100)}
            </Typography>
          </div>
          <div
            className={gridSprinkle({ type: "grid" })}
          >
            <div
              className={classNames(styles.field, gridSprinkle({ type: "item", cols: 10 }))}
            >
              <Controller
                rules={{
                  required: "Required!",
                  validate: (value) => {
                    if (Number(value.replace("£", "")) > Number(auth.user?.available_balance) / 100) {
                      return "Amount exceeds available balance";
                    }
                  },
                }}
                name="amount_to_withdraw"
                control={form.control}
                render={(
                  { field, fieldState }
                ) => (
                  <>
                    <NumericFormat
                      inputMode="decimal"
                      prefix="£"
                      error={fieldState.error?.message}
                      {...field}
                      label="Amount"
                      allowLeadingZeros
                      placeholder="£15"
                      customInput={TextInput}
                      onChange={(event) => {
                        if (typeof auth.user?.available_balance !== "number") return;
                        field.onChange(event);
                      }}
                    />
                    {!fieldState.error?.message && (
                      <Typography
                        className={styles.subLabel}
                        variant="note"
                      >
                    The limit needs to be the total of your balance.
                      </Typography>                  
                    )}
                  </>
                )}
              />
            </div>
            <div
              className={classNames(styles.field, gridSprinkle({ type: "item", cols: { mobile: 10, tablet: 5 } }))}
            >
              <Controller
                rules={{
                  required: "Required!",
                  validate: (value) => {
                    if (value.replaceAll("-", "").replaceAll(" ", "").length !== 6) return "Invalid Sort code";

                    return true;
                  },
                }}
                name="sort_code"
                control={form.control}
                render={(
                  { field, fieldState }
                ) => (
                  <PatternFormat
                    format="##-##-##"
                    patternChar="#"
                    inputMode="tel"
                    error={fieldState.error?.message}
                    {...field}
                    label="Sort code"
                    placeholder="XX-XX-XX"
                    customInput={TextInput}
                  />
                )}
              />
            </div>
            <div
              className={classNames(styles.field, gridSprinkle({ type: "item", cols: { mobile: 10, tablet: 5 } }))}
            >
              <Controller
                rules={{
                  required: "Required!",
                }}
                name="account_number"
                control={form.control}
                render={(
                  { field, fieldState }
                ) => (
                  <TextInput
                    error={fieldState.error?.message}
                    placeholder="********"
                    label="Account Number"
                    {...field}
                  />
                )}
              />
            </div>
            <div
              className={classNames(styles.field, gridSprinkle({ type: "item", cols: 10 }))}
            >
              <Controller
                rules={{
                  required: "Required!",
                }}
                name="account_holder"
                control={form.control}
                render={(
                  { field, fieldState }
                ) => (
                  <TextInput
                    error={fieldState.error?.message}
                    placeholder="Write account holder name"
                    label="Account Holder"
                    {...field}
                  />
                )}
              />
            </div>
            <div
              className={gridSprinkle({ type: "item", cols: { mobile: 10, tablet: 5 } })}
            >
              <Button
                onClick={form.handleSubmit(handleSubmit)}
                className={styles.action}
                isAnimated
              >
              Request Withdrawal
              </Button>
            </div>
          </div>
        </div>        
      )}
    </Modal>
  );
});

export default WithdrawalRequestModal;
