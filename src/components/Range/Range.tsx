import { FC, PointerEventHandler, useRef } from "react";
import Props from "./Range.types";
import * as styles from "./Range.css";

const Range: FC<Props> = ({ value, onChange, onStart, onEnd }) => {
  const rootRef = useRef<HTMLDivElement>(null);

  const handlePointerDown: PointerEventHandler<HTMLDivElement> = (event) => {
    const handlePreventDefault = (event: Event) => {
      event.preventDefault();
      event.stopPropagation();
    };

    const handlePointerMove = (event: PointerEvent) => {
      if (!rootRef.current) return;

      const rootRect = rootRef.current.getBoundingClientRect();

      const pixelsInPercent = rootRect.width / 100;

      const newValue = (event.clientX - rootRect.left) / pixelsInPercent;

      onChange?.(Math.min(Math.max(newValue, 0), 100));
    };

    onStart?.();

    handlePointerMove(event.nativeEvent);

    const handlePointerUp = () => {
      onEnd?.();

      document.removeEventListener("touchmove", handlePreventDefault);
      window.removeEventListener("pointermove", handlePointerMove);
      window.removeEventListener("pointerup", handlePointerUp);
    };

    document.addEventListener("touchmove", handlePreventDefault, { passive: false });
    window.addEventListener("pointermove", handlePointerMove);
    window.addEventListener("pointerup", handlePointerUp);
  };

  return (
    <div
      ref={rootRef}
      className={styles.root}
      onPointerDown={handlePointerDown}
    >
      <div
        className={styles.rangeTrack}
      >
        <div
          className={styles.rangeProgress}
          style={{ width: `${value}%` }}
        />
      </div>
      <button
        className={styles.rangeThumb}
        style={{ left: `${value}%` }}
        title="Move Video"
      />
    </div>
  );
};

export default Range;