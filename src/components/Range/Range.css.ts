import { theme } from "@/styles/themes.css";
import { style } from "@vanilla-extract/css";

export const root = style({
  width: "100%",
  position: "relative",
  height: 32,
  display: "flex",
  alignItems: "center",
  cursor: "pointer",
});

export const rangeTrack = style({
  width: "100%",
  height: 4,
  borderRadius: 8,
  backgroundColor: theme.colors.primary.softWhite,
});

export const rangeProgress = style({
  height: "100%",
  borderRadius: "inherit",
  backgroundColor: theme.colors.primary.castletonGreen,
});

export const rangeThumb = style({
  cursor: "pointer",
  padding: 0,
  transform: "translateX(-50%)",
  borderRadius: "50%",
  border: "none",
  width: 14,
  height: 14,
  aspectRatio: "1 / 1",
  backgroundColor: theme.colors.primary.castletonGreen,
  position: "absolute",
  top: 0,
  bottom: 0,
  margin: "auto 0",
});
