"use client";
import { forwardRef } from "react";
import * as styles from "./CheckButton.css";
import classNames from "classnames";
import RadioButtonProps from "./CheckButton.types";
import Typography from "../Typography";
import MemoCheckMarkIcon from "@/assets/icons/CheckMarkIcon";

const CheckButton = forwardRef<HTMLInputElement, RadioButtonProps>(
  (
    { children, value, onChange, ...props }, ref
  ) => {
    return (
      <label
        className={classNames(
          styles.root.base, {
            [styles.root.checked]: value,
          }
        )}
      >
        <input
          {...props}
          ref={ref}
          hidden
          type="checkbox"
          onChange={onChange}
        />
        <div
          className={styles.indicator}
        >
          {value && (
            <MemoCheckMarkIcon/>
          )}
        </div>
        {!!children && (
          <Typography
            className={styles.labelText}
            as="span"
            variant="bodySmall"
          >
            {children}
          </Typography>
        )}
      </label>
    );
  }
);

export default CheckButton;
