import { theme } from "@/styles/themes.css";
import { style, styleVariants } from "@vanilla-extract/css";

const base = style(
  {
    minHeight: 50,
    borderRadius: 8,
    border: `1px solid ${theme.colors.primary.castletonGreen}`,
    backgroundColor: theme.colors.primary.softWhite,
    color: theme.colors.primary.castletonGreen,
    display: "inline-flex",
    alignItems: "center",
    cursor: "pointer",
    padding: "0 20px",
    gap: 8,
    transition: "all 150ms",
  }
);

export const root = styleVariants(
  {
    base: [base],
    checked: {
      backgroundColor: theme.colors.primary.castletonGreen,
      color: theme.colors.primary.softWhite,
    },
  }
);

export const indicator = style(
  {
    display: "flex",
    alignItems: "center",
    justifyContent: "center",
    width: 18,
    fontSize: 12,
    aspectRatio: "1 / 1",
    border: `1px solid ${theme.colors.primary.castletonGreen}`,
    borderRadius: 3,
    color: theme.colors.primary.castletonGreen,
    selectors: {
      [`${root.checked} &`]: {
        backgroundColor: theme.colors.primary.softWhite,
      }
    }
  }
);

export const labelText = style({
  paddingTop: 1

});