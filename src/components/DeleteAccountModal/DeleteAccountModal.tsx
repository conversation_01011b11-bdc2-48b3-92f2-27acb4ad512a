import { FC } from "react";
import * as styles from "./DeleteAccountModal.css";
import Props from "./DeleteAccountModal.types";
import Modal from "../Modal";
import Typography from "../Typography";
import Button from "../Button";
import { observer } from "mobx-react-lite";
import useStore from "@/hooks/useStore";

const DeleteAccountModal: FC<Props> = observer(({ onClose, isOpen }) => {
  const { auth } = useStore();

  return (
    <Modal
      open={isOpen}
      onClose={onClose}
    >
      <div
        className={styles.content}
      >
        <Typography
          className={styles.title}
          variant="h4"
        >
          Delete account
        </Typography>
        <Typography
          className={styles.subTitle}
        >
          Do you really want to delete account? This process can not be undone.
        </Typography>
        <div
          className={styles.actions}
        >
          <Button
            onClick={async () => {
              await auth.deleteUser();

              onClose();
            }}
            color="error"
          >
            Delete
          </Button>
          <Button
            onClick={onClose}
            variant="outlined"
            color="secondary"
          >
            Cancel
          </Button>
        </div>
      </div>
    </Modal>
  );
});

export default DeleteAccountModal;