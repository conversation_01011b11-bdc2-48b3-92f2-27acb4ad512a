"use client";

import React, { useEffect, useState } from "react";
import Image from "next/image";
import * as styles from "../../slices/ProtectingHouseholdsSection/ProtectingHouseholdsSection.css";
import Link from "next/link";
import PhoneIcon from "@/assets/icons/PhoneIcon";
import img from "./img.png";
import { useSearchParams } from "next/navigation";
import { boroughs } from "@/utils/constants";

const ProtectingHouseholdsSection = () => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [isDesktop, setIsDesktop] = useState(false);
  const searchParams = useSearchParams();

  // Get mode from URL params
  const params = new URLSearchParams(
    (searchParams as URLSearchParams | null) ?? {}
  );

  const currentMode = params.get("mode") || "residential";

  // Check if we're on desktop
  useEffect(() => {
    const checkIfDesktop = () => {
      setIsDesktop(window.innerWidth >= 768); // Tablet breakpoint
    };

    checkIfDesktop();
    window.addEventListener('resize', checkIfDesktop);

    return () => {
      window.removeEventListener('resize', checkIfDesktop);
    };
  }, []);

  // Define how many items to show when collapsed on mobile
  const itemsToShowCollapsed = 10;

  // Calculate how many items to show in each column
  const itemsPerColumn = Math.ceil(boroughs.length / 2);

  // Determine which items to show based on expanded state and device
  const leftColumnItems = isDesktop || isExpanded
    ? boroughs.slice(0, itemsPerColumn)
    : boroughs.slice(0, Math.min(itemsToShowCollapsed, itemsPerColumn));

  const rightColumnItems = isDesktop || isExpanded
    ? boroughs.slice(itemsPerColumn)
    : boroughs.slice(itemsPerColumn, itemsPerColumn + Math.min(itemsToShowCollapsed, boroughs.length - itemsPerColumn));

  return (
    <div className={styles.wrapper}>
      <section className={styles.root}>
        <div className={styles.container}>
          <div className={styles.content}>
            <h2 className={styles.title}>
              <span className={styles.highlight}>Protecting</span> Households Across<br/>
              the Whole of London
            </h2>

            <p className={styles.description}>
              <span className={styles.trustedText}>Trusted by homeowners, landlords, and businesses in every borough.</span> We offer free, no-obligation advice —
              and if we think it&apos;s something you can fix yourself, we&apos;ll happily guide you through it on a video call, free of
              charge. <span className={styles.needExpert}>Need an expert?</span> Speak to our team in your local area by calling our 24-Hour Call Centre on 0800
              046 1000— and get your problem solved with confidence.
            </p>

            <div className={styles.mobileCallButton}>
              <Link href="tel:***********" className={styles.callButton}>
                <PhoneIcon className={styles.phoneIcon} />
                Call Us
              </Link>
            </div>

            <div className={styles.imageAndBoroughs}>
              <div className={styles.imageContainer}>
                <Image
                  src={img.src}
                  alt="Big Ben and Houses of Parliament in London"
                  fill
                  className={styles.image}
                  priority
                />
              </div>

              <div className={styles.boroughsContainer}>
                <ul className={styles.boroughsList}>
                  {leftColumnItems.map((borough, index) => (
                    <li key={`borough-left-${index}`} className={styles.boroughItem}>
                      <svg className={styles.checkIcon} width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M9 16.17L4.83 12L3.41 13.41L9 19L21 7L19.59 5.59L9 16.17Z" fill="currentColor"/>
                      </svg>
                      <span className={styles.boroughText}>{borough.name}</span>
                    </li>
                  ))}
                </ul>

                <ul className={styles.boroughsList}>
                  {rightColumnItems.map((borough, index) => (
                    <li key={`borough-right-${index}`} className={styles.boroughItem}>
                      <svg className={styles.checkIcon} width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M9 16.17L4.83 12L3.41 13.41L9 19L21 7L19.59 5.59L9 16.17Z" fill="currentColor"/>
                      </svg>
                      <span className={styles.boroughText}>{borough.name}</span>
                    </li>
                  ))}
                </ul>
              </div>
            </div>

            {!isDesktop && (
              <div className={styles.viewAllContainer}>
                <button
                  onClick={() => setIsExpanded(!isExpanded)}
                  className={styles.viewAllLink}
                >
                  View All
                  <svg
                    className={`${styles.viewAllIcon} ${isExpanded ? styles.expanded : ""}`}
                    width="24"
                    height="24"
                    viewBox="0 0 24 24"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M7.41 8.59L12 13.17L16.59 8.59L18 10L12 16L6 10L7.41 8.59Z"
                      fill="currentColor"
                    />
                  </svg>
                </button>
              </div>
            )}
          </div>
        </div>
      </section>
    </div>
  );
};

export default ProtectingHouseholdsSection;
