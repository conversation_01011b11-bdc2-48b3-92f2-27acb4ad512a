import { forwardRef, useCallback, useRef, useState, useImperativeHandle, useEffect } from "react";
import CountdownWrapperProps, { CountdownWrapperActions } from "./CountdownWrapper.types";



const CountdownWrapper = forwardRef<CountdownWrapperActions,CountdownWrapperProps>(({period = 1000, countdown = 60, children}, ref) => {

  const [timerValue, setTimerValue] = useState(0);
  const intervalRef = useRef<NodeJS.Timeout>();




  const stopTimer = useCallback(
    () => {
      if(intervalRef.current) {
        clearInterval(intervalRef.current); 
      }
    },
    [],
  );

  const clearTimer = useCallback(() => {
    setTimerValue(0);
    clearInterval(intervalRef.current);
  },[]);

  useEffect(() => {
    return () => {
      stopTimer();
    };
  }, []);
  
  
  

  const startTimer = useCallback(
    () => {
      if(intervalRef.current) {
        clearInterval(intervalRef.current);
      }
      setTimerValue(countdown);
      intervalRef.current = setInterval(() => {
        setTimerValue(prev => {
          if(prev === 0) {
            clearInterval(intervalRef.current);
            return 0;
          }
          return  prev - 1;
        });
      }, period); 
    },
    [],
  );

  useImperativeHandle(ref, () =>({
    stopTimer,
    clearTimer,
    startTimer,
  }));

  

  return (
    children(timerValue)
  );
});

export default CountdownWrapper;