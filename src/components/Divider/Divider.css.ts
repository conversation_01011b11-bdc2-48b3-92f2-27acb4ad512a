import { mode } from "@/styles/functions.css";
import { theme } from "@/styles/themes.css";
import { style } from "@vanilla-extract/css";

export const root = style({
  backgroundColor: theme.colors.grayscale[100],

  selectors: {
    [mode("residential")]: {
      opacity: 0.3,
    }
  },
});

export const vertical = style({
  width: 1,
  minWidth: 1,
  height: "100%",
});

export const horizontal = style({
  height: 1,
  width: "100%",
});

export const opacity = style({
  opacity: .2
});