import classNames from "classnames";
import { FC } from "react";
import * as styles from "./Divider.css";
import Props from "./Divider.types";

const Divider: FC<Props> = ({
  className,
  direction = "horizontal",
  withOpacity,
  ...restProps
}) => {
  return (
    <div
      {...restProps}
      className={classNames(
        styles.root,
        styles[direction],
        className,
        {[styles.opacity]: withOpacity}
      )}
    />
  );
};

export default Divider;