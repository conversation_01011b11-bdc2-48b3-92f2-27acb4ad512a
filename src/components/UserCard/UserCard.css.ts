import { theme } from "@/styles/themes.css";
import { style } from "@vanilla-extract/css";
import { subTitleSmall } from "../Typography/Typography.css";
import { breakpoints } from "@/styles/constants.css";





export const userCard = style({
  padding: 24,
  borderRadius: 16,
  background: theme.colors.primary.castletonGreen,
  color: theme.colors.primary.ivory,
  display: "grid",
  justifyItems: "center",
  alignItems:"center",
  "@media": {
    [breakpoints.tablet]: {
      justifyItems: "unset",
      gridTemplateRows: "repeat(2, auto)",
      gridTemplateColumns: "1fr auto",
    }
  }
});


export const socialsWrapper = style({
  display: "flex",
  columnGap: 24,
  marginTop: 24,
  "@media": {
    [breakpoints.tablet]: {
      columnGap: 8,
      gridColumnEnd: -1,
      gridRow: "-1 / 1",
      marginTop: 0,
    }
  }
});

export const memberId = style({
  color: theme.colors.primary.asidGreen,
  marginTop: 8,
  "@media": {
    [breakpoints.tablet]: {
      marginTop: 0,
    }
  }
});

export const iconButton = style({
  fontSize: "25px !important",
  height: 48
});

export const copyIdTooltip = style({
  display: "flex",
  rowGap: 12,
});