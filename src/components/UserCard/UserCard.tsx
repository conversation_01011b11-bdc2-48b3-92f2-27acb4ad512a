import MemoCheckMarkIcon from "@/assets/icons/CheckMarkIcon";
import MemoCopyicon from "@/assets/icons/Copyicon";
import MemoTelegramIcon from "@/assets/icons/TelegramIcon";
import MemoWhatsAppIcon from "@/assets/icons/WhatsAppIcon";
import IconButton from "../IconButton";
import Tooltip from "../Tooltip";
import Typography from "../Typography";
import * as styles from "./UserCard.css";
import { observer } from "mobx-react-lite";
import useStore from "@/hooks/useStore";


const UserCard = observer(() => {
  const { auth } = useStore();

  const id = auth.user?.ref_id ?? "";

  const onCopyIdClick = () => {
    navigator.clipboard.writeText(id);
  };
  // const onTelegramClick = () => {
  // };
  // const onWhatsAppClick = () => {

  // };

  return (
    <div
      className={styles.userCard}
    >
      <Typography
        variant="bodySmall"
          
      >Your member ID: </Typography>
      <Typography
        variant="subTitleMedium"
        className={styles.memberId}
      >
        {id}
      </Typography>
      <div
        className={styles.socialsWrapper}
      >
        <Tooltip
          variant="secondary"
          on={"click"}
          position={"top center"}
          closeDelay={2000}
          trigger={<div>
            <IconButton
              onClick={onCopyIdClick}
              size="small"
              color="primaryInverted"
              className={styles.iconButton}
            ><MemoCopyicon/>
            </IconButton>
          </div>}
        >
          <div
            className={styles.copyIdTooltip}
          >
            <MemoCheckMarkIcon/>
            <Typography
              variant="note"
            >Member ID copied</Typography>
          </div>
        </Tooltip>
        {/* <IconButton
          onClick={onTelegramClick}
          size="small"
          color="primaryInverted"
          className={styles.iconButton}
        >
          <MemoTelegramIcon/>
        </IconButton>
        <IconButton
          onClick={onWhatsAppClick}
          size="small"
          color="primaryInverted"
          className={styles.iconButton}
        ><MemoWhatsAppIcon/></IconButton> */}
      </div>
    </div>
  );
});

export default UserCard;