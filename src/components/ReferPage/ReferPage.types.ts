import { SlicesContextData } from "@/types/common";
import { KeyTextField, SliceZone } from "@prismicio/client";
import { FooterDocument, HeaderDocument, ReferPageDocument, } from "prismicio-types";

type ReferPageProps = {
  header: HeaderDocument<string>
  footer: FooterDocument<string>
  page: ReferPageDocument<string>;
  searchParams: { mode: "residential" | "commercial" };
  slicesData: SlicesContextData
  headerAnchorItems: KeyTextField[];
  uid: string
};

export default ReferPageProps;