import { components } from "@/slices";
import { FC, useMemo } from "react";
import FloatingContactWidgets from "../FloatingContactWidgets";
import Footer from "../Footer";
import Header from "../Header";
import * as styles from "./ReferPage.css";
import Props from "./ReferPage.types";
import { toCamelCase } from "@/utils/helpers";
import { WHATS_UP_NUMBER } from "@/utils/constants";
import { SliceZone } from "@prismicio/react";
import classNames from "classnames";

const ReferPage: FC<Props> = ({
  header,
  footer,
  page,
  slicesData,
  headerAnchorItems,
  uid,
}) => {
  

  const anchorLinks = useMemo(() => {
    let links = [];
    for (let i = 0; i < headerAnchorItems.length; i++) {
      const name = headerAnchorItems[i];
      if(!name) continue;
      links.push({id: toCamelCase(name), name});
    }
    return links;
  }  , [headerAnchorItems]);
  return (
    <>
      <div
        className={styles.root}
      >
        <Header
          anchorLinks={anchorLinks}
          variant="secondary"
          header={header}
        />
        <main
          className={classNames(styles.main, "main-refer-page", `main-${uid}`)}
        >
          {/* {(searchParams.mode === "commercial" ? page.data.slices : page.data.slices).map((slice) => (
            <Fragment
              key={slice.id}
            >
              {(components as any)[slice.slice_type]({
                slice,
                ...(slicesData.find((item: { sliceId: string; }) => item.sliceId === slice.id)?.data ?? {}) 
              })}
            </Fragment>
          ))} */}
          <SliceZone
            slices={page.data.slices}
            context={slicesData}
            components={components}
          />
        </main>    
        <Footer
          footer={footer}
        />
      </div>
      <FloatingContactWidgets
        phoneNumber={String(header.data.phone_number)}
        whatsAppNumber={WHATS_UP_NUMBER}
      />
    </>
  );
};

export default ReferPage;