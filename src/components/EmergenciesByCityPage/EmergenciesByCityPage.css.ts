import { breakpoints } from "@/styles/constants.css";
import { theme } from "@/styles/themes.css";
import { style, styleVariants } from "@vanilla-extract/css";

export const root = style({
  display: "flex",
  flexDirection: "column",
  paddingTop: 98,

  "@media": {
    [breakpoints.tablet]: {
      paddingTop: 137,
    }
  }
});

export const main = style({
  display: "flex",
  flexDirection: "column",
  flex: "1 1",
});





// Header styles 

export const container = style({
  padding: "0 !important"
});


export const headerRoot = style({
  backgroundColor: theme.colors.primary.castletonGreen,
  paddingLeft: 10,
  paddingRight: 10,
  "@media": {
    [breakpoints.tablet]: {
      paddingLeft: 20,
      paddingRight: 20,

    }
  }
});
  
export const headerContainer = style({
  backgroundColor: theme.colors.primary.castletonGreen,
  padding: 0
});

export const headerContent = style({
  borderRadius: 24,
  backgroundColor: theme.colors.primary.ivory,
  display: "grid",
  gridTemplateColumns: "1fr",
  alignItems: "center",
  height: 69,
  margin: 10,
  "@media": {
    [breakpoints.tablet]: {
      margin: "12px 0",
      gridTemplateColumns: "repeat(3,1fr)",
    }
  }
});

export const dropdownButton = style({
  borderRadius: 24,
  backgroundColor: theme.colors.primary.castletonGreen,
  color: theme.colors.primary.softWhite,
  alignItems: "center",
  height: "100%",
  display: "flex",
  width: "100%",
  columnGap: 16,
  border: `3px solid ${theme.colors.primary.softWhite}`,
  padding: "0 24px",
  cursor: "pointer",
  "@media": {
    [breakpoints.tablet]: {
      width: 419,
    }
  }


});

export const dropdownContainer = style({
  width: "calc(95vw - 18px)",
  backgroundColor: theme.colors.primary.castletonGreen,
  borderRadius: 16,
  border: `2px solid ${theme.colors.primary.asidGreen}`,
  boxShadow: "0px 2px 20px 0px rgba(128, 125, 121, 0.15)",
  padding: 0,
  maxHeight: 256,
  overflow: "auto",
  color: theme.colors.primary.softWhite,
  marginTop: 6,
  "@media": {
    [breakpoints.tablet]: {
      width: 226,
    }
  }
});

export const dropdownItemBase = style({
  padding: "12px 18px 12px 24px",
  display: "grid",
  color: theme.colors.primary.softWhite,
  "selectors": {
    "&:last-child": {
      paddingBottom: 24,
    },
    "&:first-child": {
      paddingTop: 24,
    },
  }

});

export const dropdownItem = styleVariants({
  active: [dropdownItemBase, {
    color: theme.colors.primary.asidGreen,
    fontWeight: 500
  }],
  unactive: [dropdownItemBase],
});

export const middleSection = style({
  display: "none",
  "@media": {
    [breakpoints.tablet]: {
      columnGap: 8,
      fontSize: 24,
      alignItems: "center",
      justifySelf: "center",
      display: "flex",
    }
  }
});

export const middleSectionLabel  =style({
  fontWeight: 500,
  lineHeight: 1
});

export const phoneSection = style({
  display: "none",
  columnGap: 16,
  paddingRight: 32,
  justifySelf: "end",
  fontSize: 24,
  "@media": {
    [breakpoints.tablet]: {
      display: "flex",
    }
  }
});

export const headerLocationIcon = style({
  fontSize: 24,
});

export const headerChevron = style({
  marginLeft: "auto",
  fontSize: 24,
});