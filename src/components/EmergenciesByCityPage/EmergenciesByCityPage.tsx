import { components } from "@/slices";
import { FC, Fragment, useMemo } from "react";
import FloatingContactWidgets from "../FloatingContactWidgets";
import Footer from "../Footer";
import Header from "../Header";
import * as styles from "./EmergenciesByCityPage.css";
import Props from "./EmergenciesByCityPage.types";
import { toCamelCase } from "@/utils/helpers";
import { WHATS_UP_NUMBER } from "@/utils/constants";
import { SliceZone } from "@prismicio/react";
import EmergenciesByCityHeader from "./EmergenciesByCityHeader";

const EmergenciesByCityPage: FC<Props> = ({
  slicesData,
  header,
  footer,
  page,
  headerAnchorItems,
}) => {
  

  const anchorLinks = useMemo(() => {
    let links = [];
    for (let i = 0; i < headerAnchorItems.length; i++) {
      const name = headerAnchorItems[i];
      if(!name) continue;
      links.push({id: toCamelCase(name), name});
    }
    return links;
  }  , [headerAnchorItems]);


  return (
    <>
      <div
        className={styles.root}
      >
        <Header
          anchorLinks={anchorLinks}
          variant="secondary"
          header={header}
        />
        <main
          className={styles.main}
        >
          <EmergenciesByCityHeader
            locations={page.data.locations}
          />
          <SliceZone
            context={{locations: page.data.locations, ...slicesData}}
            slices={page.data.slices}
            components={components}

          />
        </main>    
        <Footer
          footer={footer}
        />
      </div>
      <FloatingContactWidgets
        phoneNumber={String(header.data.phone_number)}
        whatsAppNumber={WHATS_UP_NUMBER}
      />
    </>
  );
};

export default EmergenciesByCityPage;