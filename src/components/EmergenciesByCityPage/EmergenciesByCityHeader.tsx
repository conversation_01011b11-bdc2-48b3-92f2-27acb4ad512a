"use client";

import Popup from "reactjs-popup";
import Container from "../Container";
import * as styles from "./EmergenciesByCityPage.css";
import Typography from "../Typography";
import MemoMarkerLocationIcon from "@/assets/icons/MarkerLocationIcon";
import ArrowIcon from "@/assets/icons/ArrowIcon";
import { ChevronIcon } from "@/assets/icons/ChevronIcon";
import TimeIcon from "@/assets/icons/TimeIcon";
import Link from "next/link";
import { getNumbersFromString } from "@/utils/helpers";
import MemoPhoneIcon from "@/assets/icons/PhoneIcon";
import Divider from "../Divider/Divider";
import { EmergenciesByCityHeaderProps } from "./EmergenciesByCityPage.types";
import { useParams } from "next/navigation";
import { useMemo } from "react";

const EmergenciesByCityHeader = ({
  locations,
}: EmergenciesByCityHeaderProps) => {
  const params = useParams<{ uid: string }>();

  const currentLocation = useMemo(
    () => locations.find((loc) => loc.uuid === params?.uid),
    [locations]
  );

  const phoneNumber = currentLocation?.phone_number;

  return (
    <div className={styles.headerRoot}>
      <Container removeBg className={styles.container}>
        <div className={styles.headerContainer}>
          <header className={styles.headerContent}>
            <Popup
              arrow={false}
              on={["click"]}
              position={["bottom right", "bottom center", "bottom left"]}
              trigger={(open) => (
                <button className={styles.dropdownButton} type="button">
                  <MemoMarkerLocationIcon
                    className={styles.headerLocationIcon}
                  />
                  <Typography variant="bodyMedium">
                    {currentLocation?.label}
                  </Typography>
                  <ChevronIcon
                    className={styles.headerChevron}
                    turn={open ? "top" : "bottom"}
                  />
                </button>
              )}
            >
              <ul className={styles.dropdownContainer}>
                {locations.map(({ label, uuid }, idx) => {
                  const isActive = currentLocation?.uuid === uuid;
                  return (
                    <li
                      key={uuid}
                      className={
                        styles.dropdownItem[isActive ? "active" : "unactive"]
                      }
                    >
                      <Typography
                        as={Link}
                        href={`/${uuid}`}
                        variant="bodyMedium"
                      >
                        {label}
                      </Typography>
                    </li>
                  );
                })}
              </ul>
            </Popup>
            <div className={styles.middleSection}>
              <TimeIcon />
              <Typography
                className={styles.middleSectionLabel}
                variant="bodyMedium"
              >
                24/7 Plumbing Emergencies
              </Typography>
            </div>
            <Link
              className={styles.phoneSection}
              href={`tel:${getNumbersFromString(phoneNumber || "")}`}
            >
              <MemoPhoneIcon />
              <Typography variant="subTitleMedium">(*************</Typography>
            </Link>
          </header>
        </div>
      </Container>
    </div>
  );
};

export default EmergenciesByCityHeader;
