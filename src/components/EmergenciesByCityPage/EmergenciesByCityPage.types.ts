import { SlicesContextData } from "@/types/common";
import { <PERSON>Field, KeyTextField, SliceZone } from "@prismicio/client";
import { EmergencyLocationsDocument, EmergencyLocationsDocumentDataLocationsItem, FooterDocument, HeaderDocument, Simplify, } from "prismicio-types";



type EmergenciesByCityPageProps = {
  header: HeaderDocument<string>
  footer: FooterDocument<string>
  page: EmergencyLocationsDocument<string>;
  slicesData: SlicesContextData;
  headerAnchorItems: KeyTextField[];
} ;


export type EmergenciesByCityHeaderProps = {
  locations: GroupField<Simplify<EmergencyLocationsDocumentDataLocationsItem>>;
}

export type EmergenciesByCityContextProps = EmergenciesByCityHeaderProps

export default EmergenciesByCityPageProps;