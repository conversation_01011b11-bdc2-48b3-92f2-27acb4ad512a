"use client";

import DeclineIcon from "@/assets/icons/DeclineIcon";
import MemoInfoOutlineIcon from "@/assets/icons/InfoOutlineIcon";
import WarningIcon from "@/assets/icons/WarningIcon";
import { formatNumToGBP } from "@/utils/helpers";
import axios from "axios";
import classNames from "classnames";
import { ReactNode, useCallback, useEffect, useState } from "react";
import IconButton from "../IconButton";
import Modal from "../Modal";
import Tooltip from "../Tooltip";
import Typography from "../Typography";
import * as styles from "./EarnProgramPage.css";
import DeclineFilledIcon from "@/assets/icons/DeclineFilledIcon";
import NextArrowIcon from "@/assets/icons/NextArrowIcon";
import Button from "../Button";
import Link from "next/link";
import ConfirmModal from "../ConfirmModal";
import { INFO_EMAIL } from "@/utils/constants";
import useStore from "@/hooks/useStore";
import { observer } from "mobx-react-lite";
import TextButton from "../TextButton";

const EARN_PROGRAM_DATA = [
  {
    name: "refer_friend_for_boiler_installation",
    title: "Refer a friend for a new boiler installation",
    description: "Provide the friend or relative with your member ID, they will be asked for this when they make the booking/request with us to have a new boiler installed. Once the work is completed, as soon as they settle the invoice, the £100 will be automatically credited to your account.",
    price: 10000,
    link: "/refer/boiler-installation",
  },
  {
    name: "refer_friend_for_12_month_peace_plan",
    title: "Refer a friend for a 12 month Peace Plan",
    description: "Provide the friend or relative with your member ID, they will be asked for this when they purchase our Peace plan, once the 14 day cooling-off period has elapsed, the £25 will be automatically credited to your account.",
    price: 2500,
    link: "/refer/plan",
  },
  {
    name: "refer_friend_for_central_heating_powerflush",
    title: "Refer a friend for a Central Heating Powerflush",
    description: "Provide the friend or relative with your member ID, they will be asked for this when they book the Powerflush with us. Once the work is completed, as soon as they settle the invoice, the £50 will be automatically credited to your account.",
    price: 5000,
    tooltipText: "How do you define appropriate? Your face must be clearly visible in the photo and ensure that your voice is audible or your sign language is clear for all to see. It can be in either andscape or portrait. Don’t worry, you will be asked to re-submit a new video if the first is deemed unusable.",
    link: "/refer/central-heating-powerflush",
  },
  {
    name: "refer_friend_for_boiler_service",
    title: "Refer a friend for a Boiler Service",
    description: `Provide the friend or relative with your member ID, they will be asked for this when they make the booking/request with us to have their boiler
    serviced. Once the work is completed, as soon as they settle the invoice, the £5 will be automatically credited to your account.`,
    price: 500,
    link: "/refer/boiler-service",
  },
  {
    name: "refer_friend_for_central_heating_control_upgrade",
    title: "Refer a friend for a Central Heating Control Upgrade",
    description: `Revolutionise a friend’s central heating controls by referring them to upgrade their old, boring thermostat with a brand new smart thermostat.
    Once the work is completed, as soon as they settle the invoice, the £15 will be automatically credited to your account.`,
    price: 1500,
    link: "/refer/central-heating-control-upgrade"
  },
  {
    name: "submit_video_review",
    title: "Submit a Video Review",
    description: "Submit a video of yourself reviewing our services. Once submitted, our media team will check it is appropriate to be used as marketing material. By providing us with this video review you agree that it may be used - in a positive way- to promote our services via social media advertisements and for testimonials on our website.",
    price: 500,
    tooltipText: "How do you define appropriate? Your face must be clearly visible in the photo and ensure that your voice is audible or your sign language is clear for all to see. It can be in either andscape or portrait. Don’t worry, you will be asked to re-submit a new video if the first is deemed unusable.",
  },
];

interface EarnProgramTask {
  name: string;
  record_id: string;
  history: Array<{
    id: string
    created_at: string
    status: string
  }>
}

const defaultStatuses = [
  {
    id: "no_contact",
    name: "No contact made",
    description: "This person has not contacted us yet regarding your referral.",
  },
  {
    id: "quote_issued",
    name: "Quote Issued",
    description: "We have issued the person with a quote and are awaiting their confirmation.",
  },
  {
    id: "service_booked",
    name: "Service Booked",
    description: "Nearly there! Your referral has booked the service and once it is completed, the funds will reflect in your member account.",
  },
  {
    id: "job_complete",
    name: "Job Complete",
    description: "We have completed the service for your referral. We are now waiting for them to pay the invoice, once they have paid,  your funds will be paid to you within 24 hours.",
  },
];
const videoReviewStatuses = [
  {
    id: "video_in_review",
    name: "Video in Review",
    description: "Our media team is reviewing the video review to ensure it is suitable.",
  },
  {
    id: "video_unsuitable",
    name: "Video Unsuitable",
    description: "Unfortunately your latest video was deemed unsuitable by our media team. Don’t worry, they have sent an email explaining why and you are able to implement the changes and re-upload your video.",
  },
  {
    id: "video_approved",
    name: "Video Approved",
    description: "Thank you so much for helping us spread the word of our work. Your funds will be paid to you within 24 hours.",
  },
];

const planStatuses = [
  {
    id: "purchase_link_sent",
    name: "Purchase link sent",
    description: "You have sent the referral link to the Peace Plan purchase page.",
  },
  {
    id: "awaiting_purchase",
    name: "Awaiting Purchase",
    description: "Your referral is yet to purchase our Peace Plan package.",
  },
  {
    id: "purchase_completed",
    name: "Purchase Completed",
    description: "Your referral has purchased the Peace Plan, as soon as the 14 day cooling-off period has elapsed, your funds will be paid to you.",
  },
];



const getStatusesByTaskName = (taskName: string | null) => {
  if(taskName === "submit_video_review") return videoReviewStatuses;
  if(taskName === "refer_friend_for_12_month_peace_plan") return planStatuses;
  return defaultStatuses;
};

const errorStatuses = [
  {
    id: "job_error",
    name: "Job Error/Problem",
    icon: <WarningIcon />,
    smallIcon: <WarningIcon />,
    description: "An error/problem occurred and we cannot complete this job. You’ll receive more details via email within the next few working days.",
    style: styles.isError,
  },
  {
    id: "referral_declined_services",
    name: "Declined",
    icon: <DeclineIcon />,
    smallIcon: <DeclineFilledIcon />,
    description: "Don’t wait, the next successful referral is only around the corner!",
    style: styles.isDeclined,
  },
];

const EarnProgramPage = observer(() => {
  const [earnProgramTasks, setEarnProgramTasks] = useState<EarnProgramTask[]>([]);
  const [openedTask, setOpenedTask] = useState<string | null>(null);
  const [submitVideoModal, setSubmitVideoModal] = useState(false);

  const { auth } = useStore();

  const memberId = auth.user?.ref_id ?? "";

  const handleCloseSubmitVideoModal = useCallback(
    () => {
      setSubmitVideoModal(false); 
    },
    [],
  );

  const handleOpenSubmitVideoModal = useCallback(
    () => {
      setSubmitVideoModal(true); 
    },
    [],
  );

  const onMemberIdClick = useCallback( () => {
    navigator.clipboard.writeText(memberId);
  },[memberId]);
  

  useEffect(() => {
    axios.get("/api/earn-program").then((res) => {
      setEarnProgramTasks(res.data.data.tasks);
    });
  }, []);

  return (<>
    <ConfirmModal
      title={"Have a video review to share?"}
      open={submitVideoModal}
      onClose={handleCloseSubmitVideoModal}
      hideCancelButton
      acceptButtonProps={{
        children: "OK",
        onClick: handleCloseSubmitVideoModal
      }}
    >
      <div
        className={styles.submitReviewModal}
      >
        <Typography
          as={"p"}
          variant="bodyMedium"
        >
Please, send your video review on the: <strong><a
            href={`mailto:${INFO_EMAIL}`}
          >{INFO_EMAIL}</a></strong>
        </Typography>
        <Typography
          as={"p"}
          variant="bodyMedium"
        >
          {"Don't forget to add your Member ID: "}
         
          <Typography
            className={styles.memberIdButton}
            onClick={onMemberIdClick}
            as={"button"}
            variant="bodyMedium"
          >{memberId}</Typography>
        </Typography>
      </div>
    </ConfirmModal>
    <div
      className={styles.container}
    >
      <Typography
        fontFamily="primary"
        variant="h4"
        className={styles.title}
      >
        <b>Earn</b> Program
      </Typography>
      <div
        className={styles.programmsList}
      >
        {EARN_PROGRAM_DATA.map((program) => {
          const task = earnProgramTasks.find((pr) => pr.name === program.name);
          const statuses = getStatusesByTaskName(program.name);

          const taskStatus = task?.history[task.history.length - 1].status ?? statuses[0].id;


          const successStatus = statuses.find((st) => st.id === taskStatus);

          const errorStatus = errorStatuses.find((st) => st.id === taskStatus);

          const isVideoReview = program.name === "submit_video_review";

          return(
            <div
              key={program.name}
              className={classNames(styles.programCard.default, errorStatus?.style)}
            >
              <div
                className={styles.titleWrapper}
              >
                <Typography
                  className={styles.programCardTitle}
                  variant="subTitleMedium"
                >
                  {program.title}
                </Typography>
                {program?.tooltipText && 
                  (
                    <Tooltip
                      on={["click", "focus", "hover"]}
                      trigger={<span
                        className={styles.tooltipIcon}
                      >
                        <MemoInfoOutlineIcon />
                      </span>
                      }
                    >
                      <Typography
                        variant="note"
                        className={styles.programCardTooltipText}
                      >
                  
                        {program.tooltipText}
                      </Typography>
                    </Tooltip>
                  )}
              </div>
              <Typography
                className={styles.programCardDesc}
                variant="bodySmall"
              >
                {program.description}
              </Typography>
              <Button
                as={isVideoReview ? "button" : Link}
                variant="outlined"
                color='secondary'
                className={styles.programCardLink}
                onClick={isVideoReview ? handleOpenSubmitVideoModal : undefined}
                href={program.link ? program.link : "#"}
                data-test={program.link}
              >
                View Details
              </Button>
              <div
                className={styles.programCardFooter}
              >
                <Typography 
                  className={styles.programCardPrice}
                  variant="bodyMedium"
                >
                  <b>{formatNumToGBP(program.price / 100)}</b>
                </Typography>
                <Typography
                  className={styles.programCardStatus}
                >
                  {errorStatus ? (
                    <span
                      className={styles.programCardStatusIcon}
                    >{errorStatus.smallIcon}</span>
                  ) : (
                    <b>{statuses.findIndex((st) => st.id === taskStatus) + 1}/{statuses.length}</b>
                  )}
                  {" "}
                  {(successStatus || errorStatus)?.name ?? statuses[0].name}
                  <IconButton
                    className={styles.programCardStatusAction}
                    size="small"
                    shape="rect"
                    onClick={() => setOpenedTask(program.name ?? null)}
                  >
                    <NextArrowIcon />
                  </IconButton>
                </Typography>                
              </div>
            </div>
          );})}
      </div>
      <Modal
        open={Boolean(openedTask)}
        onClose={() => setOpenedTask(null)}
      >
        <div
          className={styles.stepsModalContent}
        >
          <div>
            <Typography
              className={styles.modalTitle}
              variant="h3"
            >
              <em><b>Status</b></em> history
            </Typography>
            <Typography
              className={styles.modalDescription}
            >
            Monitor your referral&apos;s status here
            </Typography>
          </div>
          <div
            className={styles.modalSteps}
          >
            {(() => {
              const task = earnProgramTasks.find((pr) => pr.name === openedTask);
              const statuses = getStatusesByTaskName(openedTask);

              const taskStatus = task?.history[task.history.length - 1].status ?? statuses[0].id;

              const lastStatusAsError = errorStatuses.find((st) => st.id === taskStatus);

              const lastSuccessStatus = Boolean(lastStatusAsError) ? task?.history[task.history.length - 2].status : taskStatus;  

              const activeStatusIndex = statuses.findIndex((st) => st.id === lastSuccessStatus);

              return statuses.reduce((accum, status, statusIndex) => {
                if (statusIndex <= activeStatusIndex || !lastStatusAsError) {
                  accum.push(
                    <div
                      key={status.id}
                      className={classNames(styles.modalStep, {
                        [styles.isDone]: statusIndex <= activeStatusIndex
                      })}
                    >
                      <div
                        className={styles.modalStepNumber}
                      >
                        <Typography>
                          <b>{statusIndex + 1}</b>
                        </Typography>
                      </div>
                      <div>
                        <Typography
                          className={styles.modalStepTitle}
                        >
                          {status.name}
                        </Typography>
                        {statusIndex === activeStatusIndex && !lastStatusAsError && (
                          <Typography
                            variant="bodySmall"
                          >
                            {status.description}
                          </Typography>                  
                        )}
                      </div>
                    </div>
                  );              
                } 
              
                if (statusIndex === statuses.length - 1 && lastStatusAsError) {
                  accum.push(
                    <div
                      key={status.id}
                      className={classNames(styles.modalStep, lastStatusAsError.style, {
                        [styles.isDone]: statusIndex <= activeStatusIndex,
                      })}
                    >
                      <div
                        className={styles.modalStepNumber}
                      >
                        {lastStatusAsError.icon}
                      </div>
                      <div>
                        <Typography
                          className={styles.modalStepTitle}
                        >
                          {lastStatusAsError.name}
                        </Typography>
                        <Typography
                          variant="bodySmall"
                        >
                          {lastStatusAsError.description}
                        </Typography>                  
                      </div>
                    </div>
                  );              
                }

                return accum;
              }, [] as ReactNode[]);
            })()}
            {/*{statuses.map((status, index) => {
            const task = earnProgramTasks.find((pr) => pr.name === openedTask);

            const taskStatus = task?.history[task.history.length - 1].status;

            const lastStatusAsError = errorStatuses.find((st) => st.id === taskStatus);

            const activeStatusIndex = statuses.findIndex((st) => st.id === taskStatus);

            if (activeStatusIndex === -1) return null;

            return (
              <div
                key={status.id}
                className={classNames(styles.modalStep, {
                  [styles.isDone]: index <= activeStatusIndex
                })}
              >
                <div
                  className={styles.modalStepNumber}
                >
                  <Typography>
                    <b>{index + 1}</b>
                  </Typography>
                </div>
                <div>
                  <Typography
                    className={styles.modalStepTitle}
                  >
                    {status.name}
                  </Typography>
                  {index === activeStatusIndex && (
                    <Typography
                      variant="bodySmall"
                    >
                      {status.description}
                    </Typography>                  
                  )}
                </div>
              </div>
            );
          })}*/}
          </div>
        </div>
      </Modal>
    </div>  
  </>);
});

export default EarnProgramPage;