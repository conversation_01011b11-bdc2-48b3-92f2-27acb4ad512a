import { breakpoints } from "@/styles/constants.css";
import { theme, themeClass } from "@/styles/themes.css";
import { globalStyle, style, styleVariants } from "@vanilla-extract/css";


export const container = style({
  display: "grid",
});

export const userCard =  style({

});

export const programmsList = style({
  display: "grid",
  gridTemplateColumns: "1fr",
  gap: 16,
  "@media": {
    [breakpoints.tablet]: {
      gap: 32,
      gridTemplateColumns: "repeat(2, 1fr)"
    }
  }
});

export const title = style({
  marginBottom: 24,
  justifySelf: "center",
  "@media": {
    [breakpoints.tablet]: {
      justifySelf: "unset",
      marginBottom: 56,
    }
  }
});


// Program card styles =======

export const programCardBase = style({
  display: "flex",
  flexDirection: "column",
  padding: 20,
  borderRadius: 16,
  color: theme.colors.primary.castletonGreen,
  border: `1px solid ${theme.colors.grayscale[100]}`,

  "@media": {
    [breakpoints.tablet]: {
      padding: 32,
    }
  }
});

export const isDone = style({});

export const isError = style({});

export const isDeclined = style({});

export const programCardFooter = style({
  display: "flex",
  flexDirection: "column",
  gap: 16,
  minHeight: 42,

  "@media": {
    [breakpoints.tablet]: {
      flexDirection: "row",
      alignItems: "center",
    }
  }
});

export const programCardActive = style({
  border: `1px solid ${theme.colors.primary.castletonGreen}`,
});

export const programCard = styleVariants({
  default: [programCardBase],
  completed: [programCardBase, programCardActive]
});

export const programCardPrice = style({
  marginRight: "auto",
});

globalStyle(`${programCardPrice} b`, {
  fontWeight: 500,
});

export const programCardCompleted = style({
  display: "flex",
  alignSelf: "end",
  columnGap: 10,
});

export const programCardTitle = style({
  marginBottom: 14,
});
export const programCardDesc = style({
  gridColumn: "1 / -1",
  opacity: .8,

  "@media": {
    [breakpoints.tablet]: {
      marginBottom: "auto",
    }
  }
});

export const programCardLink = style({
  marginTop: 32,
  marginBottom: 32,
  marginRight: "auto",

  "@media": {
    [breakpoints.tablet]: {
      marginTop: 16,
      marginBottom: 24,
    }
  }
});

export const programCardStatus = style({
  display: "flex",
  alignItems: "center",
  gap: 6,

  selectors: {
    [`${isError} &`]: {
      color: theme.colors.primary.error,
    }
  }
});

globalStyle(`${programCardStatus} b`, {
  fontWeight: 500,
});

export const programCardStatusIcon = style({
  marginTop: 3,
  fontSize: 24,
});

export const programCardStatusAction = style({
  marginLeft: "auto",

  "@media": {
    [breakpoints.tablet]: {
      marginLeft: 16,
    }
  }
});

export const programCardTooltipText = style({
  maxWidth: 260
});

export const titleWrapper = style({
  gridColumn: "1 / -1",
  display: "grid",
  gridTemplateColumns: "1fr max-content",
  fontSize: 20,
  "@media": {
    [breakpoints.tablet]: {
      fontSize: 24,
    }
  }
});

export const tooltipIcon = style({
  height: "max-content",
  cursor: "help",
  fontSize: 24,
});

export const modalTitle = style({
  fontFamily: theme.fonts.primary,
  marginBottom: 24,
});

export const modalDescription = style({
  marginBottom: 40,
});

export const modalSteps = style({
  display: "flex",
  flexDirection: "column",
  //gap: 48,
});

export const modalStep = style({
  position: "relative",
  display: "flex",
  alignItems: "flex-start",
  gap: 12,

  selectors: {
    "&:not(:last-of-type)": {
      paddingBottom: 48,
    },
    "&:not(:last-of-type):after": {
      position: "absolute",
      content: "",
      top: 40,
      left: 20,
      bottom: 0,
      width: 1,
      backgroundColor: theme.colors.grayscale[100],
    },
  },
});

export const modalStepNumber = style({
  fontSize: 24,
  width: 40,
  minWidth: 40,
  aspectRatio: "1 / 1",
  borderRadius: "50%",
  border: `1px solid ${theme.colors.primary.castletonGreen}`,
  display: "flex",
  alignItems: "center",
  justifyContent: "center",

  selectors: {
    [`${isDone} &`]: {
      backgroundColor: theme.colors.primary.castletonGreen,
      color: theme.colors.primary.asidGreen,
    },
    [`${isError} &`]: {
      backgroundColor: theme.colors.primary.error,
      border: `1px solid ${theme.colors.primary.error}`,
      color: theme.colors.primary.softWhite,
    },
    [`${isDeclined} &`]: {
      backgroundColor: theme.colors.primary.castletonGreen,
      color: theme.colors.primary.softWhite,
    },
  }
});

export const modalStepTitle = style({
  margin: "8px 0",

  selectors: {
    [`${isError} &`]: {
      color: theme.colors.primary.error,
    },
  }
});


export const submitReviewModal = style({
  display: "grid",
  rowGap: 24
});

export const memberIdButton = style({
  border: 0,
  padding: 0,
  background: "transparent",
  textDecoration: "underline",
  cursor: "pointer",
  color: "inherit",
  fontWeight: "bold",
  "@media": {
    [breakpoints.tablet]: {
      selectors: {
        "&:hover": {
          color: theme.colors.primary.asidGreen
        }
      }
    }
  }
});

export const stepsModalContent = style({
  "@media": {
    [breakpoints.tablet]: {
      padding: "0 16px",
    }
  }
});