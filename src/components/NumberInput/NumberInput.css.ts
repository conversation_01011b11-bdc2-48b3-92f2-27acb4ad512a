import { breakpoints } from "@/styles/constants.css";
import { theme } from "@/styles/themes.css";
import { style, styleVariants } from "@vanilla-extract/css";

export const numberInputContainer = style({
  width: "fit-content"
});

export const numberInput = style({
  width: 70,
  appearance: "textfield",
  selectors: {
    "&::-webkit-inner-spin-button, &::-webkit-outer-spin-button": { 
      WebkitAppearance: "none",
    } 
  } 
});

export const numberInputSuffix = style({
  display: "grid",
  gridTemplateRows: "repeat(2, 1fr)",
});




const arrowButtonBase = style({
  paddingRight: 24,
  cursor: "pointer",
  background: "transparent",
  border: 0,
  paddingBlock: 0,
  opacity: .5,
  color: theme.colors.primary.castletonGreen,
  "@media": {
    [breakpoints.tablet]: {
      selectors: {
        ["&:not(:disabled):hover, &:not(:disabled):active"]: {
          opacity: 1
        },
      } 
    }
  },
  selectors: {
    ["&:not(:disabled):active"]: {
      opacity: 1
    },
  }
  
});

const arrowButtonTop = style({
  paddingTop: 12
});

const arrowButtonBottom = style({
  paddingBottom: 12
});

export const arrowButton = styleVariants({
  top: [arrowButtonBase, arrowButtonTop],
  bottom: [arrowButtonBase, arrowButtonBottom]
});