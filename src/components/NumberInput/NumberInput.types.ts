import { InputAttributes, NumericFormatProps } from "react-number-format";
import TextInputProps from "../TextInput/TextInput.types";


type NumberInputProps = Omit<NumericFormatProps<InputAttributes>, "onChange"> & Pick<TextInputProps, "error" | "label" |"description"> & {
  allowNegative?: boolean;
  onChange: (value: string) => void
} 
export default NumberInputProps;

export type NumberInputSuffixProps = Pick<NumberInputProps, "value"|"onChange"| "allowNegative">