"use client";

import * as styles from "./NumberInput.css";
import NumberInputProps, { NumberInputSuffixProps } from "./NumberInput.types";
import TextInput from "../TextInput";
import { NumericFormat } from "react-number-format";
import { ChevronIcon } from "@/assets/icons/ChevronIcon";
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, MouseEventHandler, forwardRef } from "react";

const NumberInputSuffix = ({onChange,value = "", allowNegative}: NumberInputSuffixProps) => {

  const increment:  MouseEventHandler<HTMLButtonElement> = () =>  {
    onChange(!!value ? `${+value + 1}` : "1");
  };
  const decrement: MouseEventHandler<HTMLButtonElement> = () => {
    if(!value && value !== 0) return onChange("0");
    if(!allowNegative && +(value || 0) < 1) return;
    return  onChange(!!value ? `${+value - 1}`: "-1") ;
  };
  

  return (
    <div
      className={styles.numberInputSuffix}
    >
      <button
        className={styles.arrowButton.top}
        type="button"
        onClick={increment}
      >
        <ChevronIcon
          turn="top"
        />
      </button>
      <button
        className={styles.arrowButton.bottom}
        type="button"
        onClick={decrement}
      >
        <ChevronIcon/>
      </button>
    </div>
  );
};

const NumberInput =forwardRef<HTMLDivElement, NumberInputProps>((
  {
    allowNegative = false,
    onChange,
    ...inputProps
  },ref
) => {
  
  const handleChange:ChangeEventHandler<HTMLInputElement> = (e) => {
    onChange(e.currentTarget.value);};

  return <NumericFormat
    {...inputProps}
    containerRef={ref}
    onChange={handleChange}
    inputMode="numeric"
    inputClassname={styles.numberInput}
    inputContainerClassName={styles.numberInputContainer}
    customInput={TextInput}
    decimalScale={0}
    allowNegative={allowNegative}
    renderSuffix={() => NumberInputSuffix({value: inputProps.value, onChange,allowNegative})}
  /> ;
});

export default NumberInput;