"use client";

import { UserLoginData } from "@/stores/authStore";
import { gridSprinkle } from "@/styles/sprinkles.css";
import { observer } from "mobx-react-lite";
import Image from "next/image";
import Link from "next/link";
import { FC, ReactNode } from "react";
import { Controller, useForm } from "react-hook-form";
import AuthLayout from "../AuthLayout";
import Button from "../Button";
import TextInput from "../TextInput";
import Typography from "../Typography";
import * as styles from "./LoginPage.css";
import PreviewImage from "@/assets/images/sign-up-login-preview.webp";
import { EMAIL_CONTROLLER_RULES, EMAIL_INPUT_PROPS } from "@/utils/constants";
import classNames from "classnames";

export type LoginFormValues = UserLoginData

interface Props {
  onSubmit: (data: LoginFormValues) => void;
  content?: ReactNode;
}

const LoginPage: FC<Props> = observer(({ onSubmit, content }) => {
  const form = useForm<LoginFormValues>({
    mode: "onBlur",
    defaultValues: {
      email: "",
    }
  });

  const signUpText = (
    <>
      <Typography
        as="span"
        variant="bodySmall"
      >
        Don&apos;t have account?
      </Typography>
      {" "}
      <Typography
        as={Link}
        href="/register"
        variant="buttonMedium"
      >
        Sign up
      </Typography>
    </>
  );

  return (
    <form
      onSubmit={form.handleSubmit(onSubmit)}
    >
      <AuthLayout
        beforeContentTitle={content}
        contentClassName={styles.content}
        title={(
          <>
            Start
            {" "}
            <Typography
              as="span"
              variant="h2"
              style={{ fontStyle: 'italic' }}
            >
              Earning
            </Typography>
            <br />
            Today
          </>
        )}
        contentTitle="Log In"
        content={(
          <div
            className={gridSprinkle({ type: "grid" })}
          >
            <div
              className={gridSprinkle({ type: "item", cols: 10 })}
            >
              <Controller
                rules={{
                  required: "Required!",
                  ...(EMAIL_CONTROLLER_RULES),
                }}
                name="email"
                control={form.control}
                render={(
                  { field, fieldState }
                ) => (
                  <TextInput
                    {...EMAIL_INPUT_PROPS}
                    {...field}
                    error={fieldState.error?.message}
                    className={classNames(styles.field, styles.emailField)}
                  />
                )}
              />
              <div className={classNames(styles.signUpText, gridSprinkle({ display: { mobile: "block", tablet: "none" } }))}>
                {signUpText}
              </div>
            </div>
          </div>
        )}
        contentActions={(
          <>
            <Button
              isLoading={form.formState.isSubmitting}
              className={styles.submitButton}
              disabled={!form.formState.isValid}
              type="submit"
            >
              Log in
            </Button>
          </>
        )}
        image={(
          <Image
            src={PreviewImage.src}
            fill
            alt="Preview"
          />
        )}
        tooltip={
          <div className={gridSprinkle({ display: { mobile: "none", tablet: "block" } })}>
            {signUpText}
          </div>
        }
      />
    </form>
  );
});

export default LoginPage;
