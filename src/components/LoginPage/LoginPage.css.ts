import { breakpoints } from "@/styles/constants.css";
import { theme } from "@/styles/themes.css";
import { style } from "@vanilla-extract/css";

export const content = style({
  minHeight: "calc(100vh - 88px)",
});

export const field = style({
  marginBottom: 32,
});

export const emailField = style({
  marginBottom: 8,

  "@media": {
    [breakpoints.tablet]: {
      marginBottom: 0,
    }
  }
});

export const submitButton = style({
  width: "100%",
  marginBottom: 24,
});

export const link = style({
  position: "relative",
  width: "max-content",

  selectors: {
    "&:after": {
      content: "",
      height: 1,
      position: "absolute",
      left: 0,
      right: 0,
      margin: "0 auto",
      top: "100%",
      transform: "scaleX(0)",
      opacity: 0,
      backgroundColor: theme.colors.primary.castletonGreen,
      transition: "transform 200ms, opacity 200ms",
    },

    "&:hover:after": {
      transform: "scaleX(1)",
      opacity: 1,
    }
  },
});

export const signUpText = style({
  marginTop: '12px',
  textAlign: 'center'
});
