import { theme } from "@/styles/themes.css";
import { globalStyle, style } from "@vanilla-extract/css";
import { breakpoints } from "@/styles/constants.css";

export const container = style({
  color: theme.colors.primary.castletonGreen,
});

export const balanceCard = style({
  padding: 24,
  borderRadius: 16,
  background: theme.colors.primary.asidGreen,
  display: "grid",
  justifyItems: "center",
  "@media": {
    [breakpoints.tablet]: {
      gridTemplateRows: "repeat(2, auto)",
      gridTemplateColumns: "1fr auto",
      alignItems:"center",

      justifyItems: "unset",
    }
  }
});

export const pendingBalanceCard = style({
  margin: "0 24px",
  borderRadius: "0px 0px 16px 16px",
  background: theme.colors.primary.ivory,
  padding: "16px 24px 12px 24px",
  display: "flex",
  columnGap: 4,
  alignItems: "center",
});

globalStyle(`${pendingBalanceCard} > div`, {
  marginTop: 5,
});

globalStyle(`${pendingBalanceCard} b`, {
  fontWeight: 500,
});

export const buttonWithdraw = style({
  justifySelf: "stretch",
  marginTop: 24,
  "@media": {
    [breakpoints.tablet]: {
      gridColumnEnd: -1,
      gridRow: "-1 / 1",
      marginTop: 0,
    }
  }
});

export const textButton = style({
  color: "inherit",
  cursor: "pointer",
  background: "transparent",
  paddingBlock: 0,
  paddingInline: 0,
  fontSize: 18,
  fontWeight: 500,
  border: 0,
  marginLeft: "auto",
  textDecoration: "underline",
  textUnderlineOffset: 3,

  "@media": {
    [breakpoints.tablet]: {
      fontSize: 20,
    },
  }
});

export const price = style({
  marginTop: 8,
  "@media": {
    [breakpoints.tablet]: {
      marginTop: 0,
    }
  }
});