import useStore from "@/hooks/useStore";
import { formatNumToGBP } from "@/utils/helpers";
import { observer } from "mobx-react-lite";
import { useEffect, useMemo, useState } from "react";
import Button from "../Button";
import PendingBalanceModal from "../PendingBalanceModal";
import Typography from "../Typography";
import * as styles from "./BalanceCard.css";
import axios from "axios";
import WithdrawalRequestModal from "../WithdrawalRequestModal";

export interface PendingBalanceTransaction {
  id: number;
  user_id: string;
  status: "Pending";
  zoho_record_id: string;
  from: null;
  amount: number;
}

const BalanceCard = observer(() => {
  const { auth } = useStore();

  const [withdrawalRequestModal, setWithdrawalRequestModal] = useState(false);
  const [pendingBalanceModal, setPendingBalanceModal] = useState(false);
  const [pendingBalanceTransactions, setPendingBalanceTransactions] = useState<PendingBalanceTransaction[]>([]);

  const pendingBalanceSum = useMemo(() => {
    return pendingBalanceTransactions.reduce((accum, transaction) => {
      accum += transaction.amount;

      return accum;
    }, 0);
  }, [pendingBalanceTransactions]);

  useEffect(() => {
    axios.get("/api/transactions/pending").then((res) => {
      setPendingBalanceTransactions(res.data.data.transactions);
    });
  }, []);

  return (
    <>
      <WithdrawalRequestModal
        open={withdrawalRequestModal}
        onClose={() => setWithdrawalRequestModal(false)}
      />
      <PendingBalanceModal
        pendingBalanceTransactions={pendingBalanceTransactions}
        open={pendingBalanceModal}
        onClose={() => setPendingBalanceModal(false)}
      />
      <div
        className={styles.container}
      >
        <div
          className={styles.balanceCard}
        >
          <Typography
            variant="bodySmall"
          >
            Available balance:
          </Typography>
          <Typography
            className={styles.price}
            variant="subTitleMedium"
          >
            {formatNumToGBP((auth.user?.available_balance ?? 0) / 100)}
          </Typography>
          <Button
            className={styles.buttonWithdraw}
            color="secondary"
            size="small"
            onClick={() => setWithdrawalRequestModal(true)}
          >
            Withdraw
          </Button>
        </div>
        <div
          className={styles.pendingBalanceCard}
        >
          <Typography
            variant="note"
          >Pending balance:</Typography>
          <Typography
            variant="bodySmall"
          >
            <b>{formatNumToGBP(pendingBalanceSum / 100)}</b>
          </Typography>
          <button
            onClick={() => setPendingBalanceModal(true)}
            className={styles.textButton}
            type="button"
          >
            View
          </button>
        </div>
      </div>
    </>
  );
});

export default BalanceCard;