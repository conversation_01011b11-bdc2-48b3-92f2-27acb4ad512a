import { breakpoints } from "@/styles/constants.css";
import { KeyTextField, RichTextField } from "@prismicio/client";


export type ListVariants = "primary" | "secondary";

type ShrinksListProps = {
  cols?: Record<keyof typeof breakpoints, number> | number;
  maxAmount?: number;
  data: (KeyTextField | RichTextField)[];
  className?: string;
  listClassName?: string;
  variant?: ListVariants
  ignoreWrapper?: boolean
}

export default ShrinksListProps;