import { breakpoints } from "@/styles/constants.css";
import { createVar, globalStyle, style, styleVariants } from "@vanilla-extract/css";
import { bodySmall, buttonMedium } from "../Typography/Typography.css";
import { theme } from "@/styles/themes.css";

export const colsVar = createVar(); 

export const listBase = style([bodySmall,{
  rowGap: 12,
  columnGap: 8,
  paddingInlineStart: 0,
  display: "grid",
  gridTemplateColumns: "repeat(1,1fr)",
  fontWeight: 500,
  "@media": {
    [breakpoints.tablet]: {
      rowGap: 14,
      gridTemplateColumns: `repeat(${colsVar},1fr)`,
    } 
  },
}]);

export const twoColumns = style({
  "@media": {
    [breakpoints.tablet]: {
      gridAutoFlow: "column",
      columnGap: 16,
    } 
  },
});

globalStyle(`${listBase} li`, {
  marginBottom: 0
});

export const leftItem = style({
  "@media": {
    [breakpoints.tablet]: {
      gridColumn: 1,
    },
  }
});

export const rightItem = style({
  "@media": {
    [breakpoints.tablet]: {
      gridColumn: 2,
    }
  },
});

export const list = styleVariants({
  primary: [listBase,{color: theme.colors.primary.castletonGreen}],
  secondary: [listBase,{color: theme.colors.primary.ivory}],
});



const listItemBase = style({
  columnGap: 10,
});

const listItemVisible = style({
  display: "flex",
}); 
const listItemHidden = style({
  display: "none",
  "@media": {
    [breakpoints.tablet]: {
      display: "flex",
    }
  }
}); 

export const listItem = styleVariants({
  visible: [listItemBase,listItemVisible],
  hidden: [listItemBase,listItemHidden],

});

export const icon = styleVariants({
  primary: [{color: "inherit"}],
  secondary: [{color: theme.colors.primary.asidGreen}]
});

export const showMoreButtonBase = style([buttonMedium,{
  cursor: "pointer",
  columnGap: 4,
  background: "transparent",
  border: 0,
  textDecoration: "underline",
  textUnderlineOffset: 3,
  marginTop: 32,
  display: "flex",
  marginLeft: "auto",
  marginRight: "auto",
  "@media": {
    [breakpoints.tablet]: {
      display: "none"
    }
  }

}]);

const showMoreButtonBasePrimary = style({
  color: theme.colors.primary.castletonGreen,
});
const showMoreButtonBaseSecondary = style({
  color: theme.colors.primary.ivory,
});

export const showMoreButton = styleVariants({
  primary: [showMoreButtonBase, showMoreButtonBasePrimary],
  secondary: [showMoreButtonBase,showMoreButtonBaseSecondary],
});

const divLineBase = style({
  borderBottom: `1px solid ${theme.colors.grayscale[100]}`,
  marginTop: 34,
  display: "flex",
  width: "100%",
  "@media": {
    [breakpoints.tablet]: {
      display: "none"
    }
  }
});

export const divLine = styleVariants({
  primary: [divLineBase],
  secondary: [divLineBase, { opacity: 0.2,}]
});

export const ignoreWrapper = style({
  display: "contents"
});