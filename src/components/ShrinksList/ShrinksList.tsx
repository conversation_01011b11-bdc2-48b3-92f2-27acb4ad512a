"use client";

import MemoCheckMarkIcon from "@/assets/icons/CheckMarkIcon";
import MemoChevronIcon from "@/assets/icons/ChevronIcon";
import { assignInlineVars } from "@vanilla-extract/dynamic";
import classNames from "classnames";
import { useMemo, useState } from "react";
import * as styles from "./ShrinksList.css";
import ShrinksListProps from "./ShrinksList.types";
import { PrismicRichText } from "@prismicio/react";

const ShrinksList = ({cols = 1,data,maxAmount = 10,className, listClassName, ignoreWrapper, variant = "primary"}: ShrinksListProps) => {

  const [isShowingMore, setIsShowingMore] = useState(false);

  const isTwoColumns = useMemo(() => cols === 2, [cols]);

  return (
    <div
      className={classNames(className, {[styles.ignoreWrapper]:ignoreWrapper })}
    >
      <ul
        style={assignInlineVars({
          [styles.colsVar]: `${cols}`
        })}
        className={classNames(styles.list[variant], listClassName, {
          [styles.twoColumns]: isTwoColumns
        })}
      >{data.map((label, idx) => {
          return(
            <li
              key={`${idx}`}
              className={classNames(idx < maxAmount || isShowingMore ? styles.listItem.visible : styles.listItem.hidden, {
                [styles.leftItem]: idx < data.length / 2 && isTwoColumns,
                [styles.rightItem]: idx > data.length / 2 && isTwoColumns,
              })}
            >
              <div
                className={styles.icon[variant]}
              >
                <MemoCheckMarkIcon/>
              </div>
              {typeof label === "string" 
                ? label 
                : (
                  <PrismicRichText
                    field={label}
                  />
                )}
            </li>
          );})
        }
      </ul>
      {maxAmount < data.length && <>
        <button
          className={classNames(styles.showMoreButton[variant])}
          onClick={() => setIsShowingMore(prev => !prev)}
          type="button"
        >{isShowingMore ? "Show less" : "Show all services"}
          <div
            className={styles.icon[variant]}
          >
            <MemoChevronIcon
              turn={isShowingMore ? "top" : "bottom"}
            />
          </div>
        </button>
        <div
          className={ classNames(styles.divLine[variant])}
        />
      </>}
    </div>
  );
};

export default ShrinksList;