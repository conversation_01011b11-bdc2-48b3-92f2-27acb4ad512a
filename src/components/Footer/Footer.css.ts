import { breakpoints } from "@/styles/constants.css";
import { mode } from "@/styles/functions.css";
import { theme } from "@/styles/themes.css";
import { globalStyle, style, styleVariants } from "@vanilla-extract/css";

// export const footerContainer = style({
//   height: "auto !important",
//   scrollSnapAlign: "end",
// });

export const container = style({
  position: "relative",
  // background: theme.colors.primary.ivory,
  scrollSnapAlign: "start",
  padding: "0 10px 10px 10px",
  "@media": {
    [breakpoints.tablet]: {
      padding: "0 20px 20px 20px",
    },
  },

  selectors: {
    [mode("residential")]: {
      backgroundColor: theme.colors.primary.castletonGreen,
    }
  },
});

export const root = style({
  position: "relative",
  borderRadius: 16,
  padding: "20px 20px",
  paddingTop: "40px",
  backgroundColor: theme.colors.primary.castletonGreen,
  color: theme.colors.primary.ivory,
  // marginBottom: "20px",

  "@media": {
    [breakpoints.tablet]: {
      borderRadius: 24,
      padding: "20px 48px",
      paddingTop: "72px",
    },
  },

  selectors: {
    [mode("residential")]: {
      backgroundColor: theme.colors.primary.ivory,
    }
  },
});

export const contactInfo = style(
  {
    display: "flex",
    flexDirection: "column",
    gap: "16px",
    marginBottom: "50px",
    maxWidth: 500,

    "@media": {
      [breakpoints.tablet]: {
        alignItems: "flex-start",
      },
    },
  }
);

export const input = style({
  selectors: {
    "&::placeholder": {
      color: theme.colors.primary.castletonGreen,
      opacity: 0.7,
    },
    [mode("residential")]: {
      border: `1px solid ${theme.colors.grayscale[200]}`,
      borderRadius: 8,
    },
    [`${mode("residential")}::placeholder`]: {
      color: theme.colors.grayscale[200],
      opacity: 0.7,
    },
  },
});

export const menuTitle = style({
  marginBottom: "20px",
  fontWeight: "500 !important",

  "@media": {
    [breakpoints.tablet]: {
      alignItems: "flex-start",
    },
  },

  selectors: {
    [mode("residential")]: {
      color: theme.colors.primary.castletonGreen,
    }
  },
});

export const menu = style(
  {
    display: "flex",
    flexDirection: "column",
    gap: "16px",
    marginBottom: "40px",

    "@media": {
      [breakpoints.tablet]: {
        marginBottom: "80px",
      },
    },
  }
);


export const menuLinkBase = style({
  opacity: 0.8,
  display: "flex",
  gap: "16px",

  ":hover": {
    opacity: 1,
    color: theme.colors.primary.asidGreen,
  },

  selectors: {
    [mode("residential")]: {
      color: theme.colors.primary.castletonGreen,
    }
  },
});

const hideOnMobile = style({
  display: "none",
  "@media": {
    [breakpoints.tablet]: {
      display: "flex"
    }
  }
});
const hideOnTablet = style({
  display: "flex",
  "@media": {
    [breakpoints.tablet]: {
      display: "none"
    }
  }
});

export const menuLink = styleVariants({
  default: [menuLinkBase, { display: "flex" }],
  hideOnMobile: [menuLinkBase, hideOnMobile],
  hideOnTablet: [menuLinkBase, hideOnTablet],
});

globalStyle(`${menuLink} span`, {
  marginTop: 3,
});

globalStyle(`${menuLink} svg`, {
  aspectRatio: "1 / 1",
  minWidth: 24,
});

export const logo = style({
  width: 108,
  margin: "0 auto",
  marginBottom: 32,

  "@media": {
    [breakpoints.tablet]: {
      margin: 0,
      marginBottom: 36,
      width: 130,
    }
  },

  selectors: {
    [mode("residential")]: {
      color: theme.colors.primary.castletonGreen,
    },
  },
});

export const divider = style(
  {
    height: "1px",
    backgroundColor: theme.colors.grayscale[100],
    opacity: 0.2,
    marginBottom: 20,
    order: 11,

    "@media": {
      [breakpoints.tablet]: {
        marginBottom: 20,
      },
    },
  }
);

export const copyright = style(
  {
    opacity: 0.8,
    textAlign: "center",
    padding: "0 20px",
    display: "flex",
    flexDirection: "column",
    gap: 10,
    order: 12,
    selectors: {
      [mode("residential")]: {
        color: theme.colors.primary.castletonGreen,
      }
    },
    "@media": {
      [breakpoints.tablet]: {
        padding: 0,
        textAlign: "left",
      },
    },

  }
);

export const socials = style(
  {
    display: "flex",
    gap: "24px",
    marginBottom: 40,

    "@media": {
      [breakpoints.tablet]: {
        marginBottom: 48,
      },
    },
  }
);

export const registerImage = style(
  {
    marginBottom: 20,

    "@media": {
      [breakpoints.tablet]: {
        marginBottom: 48,
      },
    },
  }
);

export const subscribeField = style(
  {
    maxWidth: 510,
    position: "relative",
    display: "flex",
    gap: 2,
    alignItems: "flex-end",
    marginBottom: 40,

    "@media": {
      [breakpoints.tablet]: {
        marginBottom: 48,
      }
    },
  }
);

export const subscribeFieldLabel = style({
  marginBottom: 20,

  selectors: {
    [mode("residential")]: {
      color: theme.colors.primary.castletonGreen,
    }
  },
});

export const subscribeText = style({
  marginTop: 10
});

export const subscribeFieldSuccessLabel = style(
  {
    position: "absolute",
    top: "calc(100% + 8px)",
    color: theme.colors.primary.asidGreen
  }
);

export const loaderWrapper = style({
  color: "currentColor",
  fontSize: 120,
  transform: "scale(0.5)"
});

export const accreditationsTitleWrapper = style({
  "@media": {
    [breakpoints.tablet]: {
      order: 9,
    }
  },
});

export const registerNumber = style({
  textAlign: "center",
  color: theme.colors.primary.ivory,

  selectors: {
    [mode("residential")]: {
      color: theme.colors.primary.castletonGreen,
    }
  },

  "@media": {
    [breakpoints.tablet]: {
      order: 10,
      marginBottom: 16,
    }
  },
});

export const accreditationsTitle = style({
  marginBottom: 16,
  textAlign: "center",

  selectors: {
    [mode("residential")]: {
      color: theme.colors.primary.castletonGreen,
    }
  },
});

export const accreditations = style({
  display: "flex",
  flexWrap: "wrap",
  justifyContent: "center",
  gap: 12,
  marginBottom: 32,

  "@media": {
    [breakpoints.tablet]: {
      order: 10,
      marginBottom: 40,
    }
  },
});

export const accreditationContainer = style({
  borderRadius: "7.437px",
  border: "1px solid " + theme.colors.primary.asidGreen,
});

export const accreditationWrapper = style({
  borderRadius: "5.577px",
  border: `1px solid ${theme.colors.primary.castletonGreen}`,
  backgroundColor: theme.colors.primary.softWhite,

  selectors: {
    [mode("residential")]: {
      border: `1px solid ${theme.colors.primary.ivory}`,
    }
  },
});

export const accreditationImageWr = style({
  display: "inline-flex",
  alignItems: "center",
  justifyContent: "center",
  maxWidth: "91.142px",
  width: "91.142px",
  height: "58.125px",

  "@media": {
    [breakpoints.tablet]: {
      maxWidth: "88.264px",
      width: "88.264px",
      height: "56.25px",
    }
  }
});

export const accreditationImage = style({
  maxWidth: "100%",
  height: "auto",
});

export const contactInfoItemIcon = style({
  color: theme.colors.primary.asidGreen,
  fontSize: 24,
  flex: "none",

  selectors: {
    [mode("residential")]: {
      color: theme.colors.primary.castletonGreen,
    }
  },
});

export const phoneButtom = style({
  border: 0,
  background: "transparent",
  color: "inherit",
  margin: 0,
  padding: 0,
  cursor: "pointer"
});

export const socialLink = style({
  selectors: {
    [mode("residential")]: {
      backgroundColor: theme.colors.primary.castletonGreen,
      color: theme.colors.primary.softWhite,
    }
  },
});
