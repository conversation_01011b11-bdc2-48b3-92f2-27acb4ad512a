"use client";

import { useSearchParams } from "next/navigation";
import { FC, Suspense, useMemo } from "react";
import Props from "./Link.types";
import { PrismicNextLink } from "@prismicio/next";
import { observer } from "mobx-react-lite";
import useStore from "@/hooks/useStore";

const LinkElement: FC<Props> = observer(({ ...restProps }) => {
  const { landign } = useStore();

  const searchParams = useSearchParams();

  const href = useMemo(() => {
    if ((restProps.field as any)?.url && (restProps.field as any)?.url[0] === "#") return (restProps.field as any)?.url;

    return `${(restProps.field as any)?.url}${searchParams?.toString() !== "" ? "?" : ""}${searchParams?.toString()}`;
  }, [restProps.field, searchParams]);

  return (
    <PrismicNextLink
      {...restProps as any}
      prefetch={false}
      href={href}
      onClick={href[0] === "#" ? (event) => {
        event.preventDefault();

        if (href === "#booking-form") {
          landign.setBookingModalIsOpen(true);
        }

        if (href === "#opportunities-form") {
          landign.setOpportunitiesModalIsOpen(true);
        }
      } : undefined}
    />
  );
});

const Link: FC<Props> = (props) => {
  return (
    <Suspense>
      <LinkElement
        {...props}
      />
    </Suspense>
  );
};

export default Link;