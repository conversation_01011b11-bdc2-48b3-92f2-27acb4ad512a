import { breakpoints } from "@/styles/constants.css";
import { theme } from "@/styles/themes.css";
import { style } from "@vanilla-extract/css";

export const field = style({
  marginBottom: 32,
});

export const submitButton = style({
  width: "100%",
  marginBottom: 24,
});

export const link = style({
  position: "relative",
  width: "max-content",

  selectors: {
    "&:after": {
      content: "",
      height: 1,
      position: "absolute",
      left: 0,
      right: 0,
      margin: "0 auto",
      top: "100%",
      transform: "scaleX(0)",
      opacity: 0,
      backgroundColor: theme.colors.primary.castletonGreen,
      transition: "transform 200ms, opacity 200ms",
    },

    "&:hover:after": {
      transform: "scaleX(1)",
      opacity: 1,
    }  
  },
});

export const paymentBlock = style({
  maxWidth: 480,
  margin: "0 auto",
  width: "100%",
  "@media": {
    [breakpoints.tablet]: {
      minHeight: 400,
    }
  }
});

export const planList = style({
  display: "flex",
  flexDirection: "column",
  gap: 12,
  padding: 0,
  listStyle: "none",
  marginBottom: 32,
  color: theme.colors.primary.softWhite,

  "@media": {
    [breakpoints.tablet]: {
      gap: 14,
    }
  },
});

export const planListItemIcon = style({
  color: theme.colors.primary.asidGreen,
  fontSize: "1.2em",
});

export const planListItem = style({
  display: "flex",
  gap: 10,
  margin: 0,
});

export const planType = style({
  textTransform: "capitalize",
  color: theme.colors.primary.softWhite,
  display: "flex",
  alignItems: "center",
  gap: 8,
  justifyContent: "center",
  marginBottom: 32,
});

export const labelBadge = style({
  minHeight: 26,
  padding: "0 10px",
  borderRadius: 26,
  backgroundColor: theme.colors.primary.asidGreen,
  color: theme.colors.primary.castletonGreenPressed,
  whiteSpace: "nowrap",
  display: "flex",
  alignItems: "center",
  lineHeight: 1
});

export const planAppliancePrice = style({
  marginLeft: "auto",
});

export const appliancesList = style({
  maxWidth: 400,
  minWidth: 260,
  color: theme.colors.primary.softWhite,
  padding: 24,
  borderRadius: 16,
  border: `2px solid ${theme.colors.primary.asidGreen}`,
  display: "flex",
  flexDirection: "column",
  gap: 8,
  marginBottom: 32,
  "@media": {
    [breakpoints.tablet]: {
      position: "relative",
      width: "fit-content",
      marginLeft: "auto",
      right: -62,
      marginBottom: 8
      
    },
    [breakpoints.desktop]: {
      position: "absolute",
      width: "fit-content",
      bottom: 8,
      right: -62,
      marginBottom: 8
    }
  }
});

export const afterTitle = style({
  position: "relative",
  display: "grid",
  width: "100%",
  justifyContent: "center",
  "@media": {
    [breakpoints.tablet]: {
      justifyContent: "normal"
    }
  }

});

export const appliancesListTitle = style({
  color: theme.colors.primary.asidGreen,
  marginBottom: 4,
});

export const action = style({
  width: "100%",
  marginBottom: 16,
});

export const actions = style({ 
  width: "100%",
});

export const title = style({
  display: "flex",
  alignItems: "center",
  gap: 12
});


export const loaderWrapper = style({
  fontSize: 120,
  margin: "0 auto",
});

export const paymentContent = style({
  alignContent: "start"
});