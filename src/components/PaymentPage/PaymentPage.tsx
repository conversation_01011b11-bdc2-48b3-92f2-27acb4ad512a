"use client";

import MemoCheckMarkIcon from "@/assets/icons/CheckMarkIcon";
import { UserLoginData } from "@/stores/authStore";
import { formatNumToGBP } from "@/utils/helpers";
import { createClient } from "@/utils/supabase/client";
import { EmbeddedCheckout, EmbeddedCheckoutProvider } from "@stripe/react-stripe-js";
import { loadStripe } from "@stripe/stripe-js";
import { observer } from "mobx-react-lite";
import { useSearchParams } from "next/navigation";
import PreviewImage from "@/assets/images/payment-preview.webp";
import { FC, ReactNode, useCallback, useEffect, useMemo, useRef, useState } from "react";
import AuthLayout from "../AuthLayout";
import Typography from "../Typography";
import * as styles from "./PaymentPage.css";
import Button from "../Button";
import { gridSprinkle } from "@/styles/sprinkles.css";
import classNames from "classnames";
import Link from "next/link";
import { DotLottiePlayer } from "@dotlottie/react-player";
import { APPLIANCES, PLANS } from "@/utils/constants";
import Loader from "../Loader";
import { SubIntervalTypes } from "@/types/plans";
import useStore from "@/hooks/useStore";
import Image from "next/image";


const stripePromise = loadStripe(process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY);

export type LoginFormValues = UserLoginData

interface Props {
  priceId: string;
  onSubmit: () => void;
  content?: ReactNode
}

const PaymentPage: FC<Props> = observer(({ priceId, onSubmit, content }) => {
  const lottieRef = useRef<any>();

  const { auth } = useStore();

  const [customSecret, setCustomSecret] = useState<string | null>(null);
  const [isLoadingSecret, setIsLoadingSecret] = useState(false);

  const searchParams = useSearchParams();

  const planType = useMemo<SubIntervalTypes>(() => {
    return searchParams?.get("type") as SubIntervalTypes ?? "month";
  }, [searchParams]);

  const selectedPlan = useMemo(() => {
    if (!searchParams) return;

    const planName = searchParams.get("plan");

    return PLANS.find((plan) => plan.id === planName);
  }, [searchParams, PLANS]);

  const additionalAppliances = useMemo(() => {
    if (!searchParams) return [];

    const selectedAppliances = searchParams.get("appliances");

    if (!selectedAppliances) return [];

    return selectedAppliances.split(",").map(appl => APPLIANCES.find((appliance) => appliance.id === appl));
  }, []);

  const [isSuccess, setIsSuccess] = useState(false);

  const handleComplete = useCallback( async () => {
    setIsSuccess(true);
    lottieRef.current?.play();
    await auth.getCustomerPlanAPI();

  }, [auth.getCustomerPlanAPI]);

  useEffect(() => {
    if(isLoadingSecret) return;
    setIsLoadingSecret(true);
    const getPlan = async () => {
      const supabase = createClient();

      const body: {
        priceId: string
        additionalAppliances?: string[]
      } = { priceId };

      if (APPLIANCES?.length && additionalAppliances.length) {
        body.additionalAppliances = additionalAppliances.map((appl) => appl?.id) as any;
      }

      const response = await supabase.functions.invoke<{
        data: {
          clientSecret: string
          url: string | null
        }
      }>("stripe/checkout", {
        method: "POST",
        body,
      });

      setIsLoadingSecret(false);
      setCustomSecret(response.data?.data?.clientSecret ?? null);
    };
      
    getPlan();
  }, []);

  if (!selectedPlan || !planType) {
    console.error("Cannot found plan by name or plan reccuring type is invalid");
    return;
  };

  return (
    <AuthLayout
      image={
        <Image
          src={PreviewImage.src}
          fill
          alt="Preview"
        />
      }
      contentClassName={styles.paymentContent}
      beforeContentTitle={content}
      variant="secondary"
      title={<div
        className={styles.title}
             >
        {selectedPlan.name === "Peace" ? "Peace Plan" : selectedPlan.name}
        <DotLottiePlayer
          ref={lottieRef}
          src="/assets/lock-animation.lottie"
        >
        </DotLottiePlayer>
      </div>}
      contentTitle={isSuccess ? "Success!" : "Payment details"}
      contentSubtitle={isSuccess ? "Welcome to the club! You’ll never need to worry about plumbing and heating issues ever again. Check your email to download our policy." : undefined}
      content={
        !isLoadingSecret ? (
          <div
            className={styles.paymentBlock}
          >
            <EmbeddedCheckoutProvider
              stripe={stripePromise}
              options={{
                clientSecret: customSecret,
                onComplete: handleComplete
              }}
            >
              <EmbeddedCheckout />
            </EmbeddedCheckoutProvider>          
          </div>
        ) : (<div
          className={styles.loaderWrapper}
        ><Loader /></div>)
      }
      contentActions={isSuccess && (
        <>
          <div
            className={classNames(styles.actions, gridSprinkle({ type: "grid" }))}
          >
            <div
              className={gridSprinkle({ type: "item", cols: { mobile: 10, tablet: 5 } })}
            >
              <Button
                as={Link}
                href="/"
                className={styles.action}
                variant="outlined"
                color="secondary"
              >
                Go to the main page
              </Button>
            </div>
            <div
              className={gridSprinkle({ type: "item", cols: { mobile: 10, tablet: 5 } })}
            >
              <Button
                as={Link}
                href="/profile"
                className={styles.action}
              >
                Go to the profile
              </Button>                
            </div>
          </div>
        </>
      )}
      tooltip={null}
      afterTitle={(
        <div
          className={styles.afterTitle}
        >
          <Typography
            className={styles.planType}
            variant="bodySmall"
          >
            {planType}
            {planType === "year" && (

              <Typography
                className={styles.labelBadge}
                variant="note"
              >
                5% Off
              </Typography>              

            )}
          </Typography>
          <ul
            className={styles.planList}
          >
            {selectedPlan.features.map((feature) => (
              <li
                key={feature}
                className={styles.planListItem}
              >
                <MemoCheckMarkIcon
                  className={styles.planListItemIcon}
                />
                <Typography
                  variant="bodySmall"
                >
                  {feature}
                </Typography>
              </li>
            ))}
          </ul>
          {Boolean(additionalAppliances.length) && (
            <div
              className={styles.appliancesList}
            >
              <Typography
                variant="bodySmall"
                className={styles.appliancesListTitle}
              >
                <b>Additional appliances</b>
              </Typography>
              {additionalAppliances.map((appliance) => (
                <div
                  key={appliance?.id}
                  className={styles.planListItem}
                >
                  <Typography
                    variant="buttonSmall"
                  >
                    {appliance?.name}
                  </Typography>
                  <Typography
                    className={styles.planAppliancePrice}
                    variant="buttonSmall"
                  >
                  + {formatNumToGBP(((planType === "year" ? appliance?.yearPrice : appliance?.monthPrice) ?? 0) / 100)}
                  </Typography>
                </div>
              ))}
            </div>          
          )}
        </div>
      )}
    />    
  );
});

export default PaymentPage;