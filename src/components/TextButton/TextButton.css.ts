import { breakpoints } from "@/styles/constants.css";
import { theme } from "@/styles/themes.css";
import {  style, styleVariants } from "@vanilla-extract/css";
import { bodyMedium, subTitleSmall } from "../Typography/Typography.css";

const textButtonBase = style( {
  cursor: "pointer",
  background: "transparent",
  paddingBlock: 0,
  paddingInline: 0,
  border: 0,
  color: theme.colors.primary.castletonGreen,
  selectors: {
    "&:active": {
      textDecoration: "underline",
      textUnderlineOffset: 3,
      opacity: 1
    },
  },
  "@media": {
    [breakpoints.tablet]: {
      selectors: {
        "&:hover": {
          textDecoration: "underline",
          textUnderlineOffset: 3,
          opacity: 1
        }
      }
    },
    
  }
});

const textButtonSmall = style([subTitleSmall,{
  
}]);
const textButtonMedium = style([bodyMedium,{
  opacity: .8
}]);

export const textButton = styleVariants({
  small: [textButtonBase,textButtonSmall],
  medium: [textButtonBase,textButtonMedium]
});


const textButtonColorPrimary = style({
  color: theme.colors.primary.castletonGreen
});
const textButtonColorSecondary = style({
  color: theme.colors.primary.softWhite
});

export const textButtonColor = styleVariants({
  primary: [textButtonColorPrimary],
  secondary: [textButtonColorSecondary]
});


export const textButtonActive = style({
  textDecoration: "underline !important",
  textUnderlineOffset: 3,
  opacity: 1
});