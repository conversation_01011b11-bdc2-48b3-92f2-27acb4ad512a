import classNames from "classnames";
import * as styles from "./TextButton.css";
import TextButtonProps, { AsButtonProps, AsLinkProps } from "./TextButton.types";
import Link from "next/link";



function TextButton(props: AsButtonProps): JSX.Element;
function TextButton(props: AsLinkProps): JSX.Element;
function TextButton (props: TextButtonProps) {

  const className = classNames(
    props.className, 
    styles.textButton[props.size || "small"], 
    styles.textButtonColor[props.color || "primary"], {
      [styles.textButtonActive]: props.active,
    });

  if(props.as === "link") {
    const {size, children, active, as, ...restProps} = props;
    return (
      <Link
        className={className}
        {...restProps}
      >{children}</Link>
    );
  }
  else if (props.as === "button") {
    const {size, children, active, as, ...restProps} = props;
    return (
      <button
        {...restProps}
        className={className}
        type={restProps.type || "button"}
      >{children}
      </button>
    );
  }
  return "";
  
};

export default TextButton;