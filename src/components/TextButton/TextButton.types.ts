import { LinkProps } from "next/link";
import { AnchorHTMLAttributes, ButtonHTMLAttributes, DetailedHTMLProps, ReactNode } from "react";

export type TextButtonSize = "small" |  "medium"

type CommonTextButtonProps = {
  children: ReactNode;
  size?: TextButtonSize;
  active?: boolean;
  color?: "primary" | "secondary"
}

export type AsButtonProps =CommonTextButtonProps & {
  as?: "button"
} & DetailedHTMLProps<ButtonHTMLAttributes<HTMLButtonElement>, HTMLButtonElement>;



export type AsLinkProps = CommonTextButtonProps & {
  as?: "link"
} & LinkProps & AnchorHTMLAttributes<HTMLAnchorElement>;


type TextButtonProps = AsButtonProps | AsLinkProps

export default TextButtonProps;