import TextInputProps from "../TextInput/TextInput.types";
import { ActionMeta, Props, SingleValue } from "react-select";

export type Option<V =  string | number | null> = SingleValue<{
  value: V;
  label: string;
}>;

export type onChangeSelectType = (
  option: Option,
  actionMeta: ActionMeta<Option>
) => void;

export type SelectOptions = Option[];

type DropdownInputProps = Omit<Props<Option>, "onChange" | "value"> &  {
  onChange: onChangeSelectType;
  options: SelectOptions;
  variant?: "filled" | "outlined";
  value: Option;
  enableDarkMode?: boolean
}  &
  Pick<TextInputProps, "error" | "isError" | "label">;

export default DropdownInputProps;
