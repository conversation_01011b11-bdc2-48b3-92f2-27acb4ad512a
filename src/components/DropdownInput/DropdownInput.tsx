"use client";
import React, { FocusEvent, forwardRef, memo, useId } from "react";
import ReactSelect, { DropdownIndicatorProps, GroupBase, InputProps, components } from "react-select";

import * as styles from "./DropdownInput.css";
import DropdownInputProps, { Option } from "./DropdownInput.types";
import Typography from "../Typography";
import classNames from "classnames";
import MemoChevronIcon from "@/assets/icons/ChevronIcon";

// const options = new Array(10)
//   .fill(0)
//   .map((_, idx) => ({ value: idx, label: `Name${idx}` }));

const Input: React.ComponentType<InputProps<Option, false, GroupBase<Option>>> | undefined =  (
  inputProps
) => {
  return(
    <components.Input
      {...inputProps}
      innerRef={(
        instance
      ) => {
      // ref = instance;
        return inputProps.innerRef && inputProps.innerRef(
          instance
        );
      }}
    />
  );};

const  DropdownIndicator:  React.ComponentType<DropdownIndicatorProps<Option, false, GroupBase<Option>>> | null | undefined = (
  {
    innerProps,
    selectProps: { menuIsOpen },
  }
) => (
  <div
    className={styles.dropdownIndicator}
    {...innerProps}
  >
    <MemoChevronIcon
      turn={menuIsOpen ? "top" : "bottom"}
    />
  </div>
);

const DropdownInput = forwardRef<HTMLInputElement, DropdownInputProps>(
  (
    {
      variant = "outlined",
      error,
      isError,
      label,
      enableDarkMode = false,
      ...selectProps
    }, ref
  ) => {

    
    const forLabel = useId();
    const isErrorExist = isError || !!error;
    return (
      <div
        className={classNames(
          styles.container, styles[variant], {
            [styles.inputContainer.disabled]: selectProps.isDisabled,
            [styles.inputContainer.error]: isErrorExist,
            [styles.outlinedInverted]: enableDarkMode,
          }
        )}
      >
        {!!label && (
          <label
            aria-label={typeof label === "string" ? label : undefined}
            className={classNames(styles.label, {
              [styles.labelInverted]: enableDarkMode
            })}
            htmlFor={forLabel}
          >
            {label}
          </label>
        )}
        {error && (
          <Typography
            className={styles.error}
            variant="note"
          >
            {error}
          </Typography>
        )}
        <ReactSelect<Option>
          {...selectProps}
          value={!selectProps.value?.label && !selectProps.value?.value ? null : selectProps.value}
          instanceId={"dropdown-input"}
          isMulti={false}
          unstyled
          inputId={forLabel}
          onChange={(value, actionMeta) => {
            selectProps.onChange?.(value, actionMeta);
            selectProps.onBlur?.({} as FocusEvent<HTMLInputElement>);
          }}
          classNames={{
            placeholder: () => styles.placeholder,
            container: () => styles.inputContainerBase,
            control: (
            ) => styles.input,
            menuList: (
            ) => styles.menuList,
            menu: (
            ) => styles.menu,
            noOptionsMessage: (
            ) => styles.noOptionsMessage,
            option: (
              { isSelected }
            ) =>
              classNames(
                styles.option.base, {
                  [styles.option.selected]: isSelected,
                }
              ),
            singleValue: () => classNames({
              [styles.singleValue]: enableDarkMode,
            }),
            indicatorsContainer: () => classNames({
              [styles.dropdownIndicatorContainerDarkMode]: enableDarkMode,
            }),
          }}
          components={{
            DropdownIndicator,
            Input,
          }}
        />
      </div>
    );
  }
);

export default memo(
  DropdownInput
);
