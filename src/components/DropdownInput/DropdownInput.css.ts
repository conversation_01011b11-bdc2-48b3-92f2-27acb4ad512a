import { theme } from "@/styles/themes.css";
import { createVar, style, styleVariants } from "@vanilla-extract/css";
import { bodySmall, noteMedium } from "../Typography/Typography.css";
import { breakpoints } from "@/styles/constants.css";
import { mode } from "@/styles/functions.css";

const textInputFillColor = createVar(
);
const textInputContentColor = createVar(
);

export const container = style(
  {
    vars: {
      [textInputContentColor]: theme.colors.primary.castletonGreen,
      [textInputFillColor]: theme.colors.grayscale[100],
    },
    display: "grid",
    gridTemplateColumns: "repeat(2,auto)",
    justifyContent: "space-between",
    width: "100%",
    rowGap: 4,
  }
);

export const outlined = style(
  {
    selectors: {
      "&:hover, &:focus-within": {
        vars: {
          [textInputFillColor]: textInputContentColor,
        }
      }
    }
  }
);

export const outlinedInverted = style({
  selectors: {
    [mode("residential")]: {
      vars: {
        [textInputFillColor]: theme.colors.primary.softWhite,
      }
    },
  },
});

export const filled = style(
  {
    vars: {
      [textInputContentColor]: theme.colors.primary.castletonGreen,
      [textInputFillColor]: theme.colors.primary.ivory,
    },
  }
);

export const inputContainer = styleVariants(
  {
    error: {
      vars: {
        [textInputContentColor]: theme.colors.primary.error,
        [textInputFillColor]: theme.colors.primary.error,
      },
    },
    disabled: {
      vars: {
        [textInputContentColor]: theme.colors.grayscale[200],
        [textInputFillColor]: theme.colors.grayscale[100]
      },
      background: theme.colors.grayscale[100],
    },
  }
);

export const inputContainerBase = style(
  {
    color: textInputContentColor,
    display: "flex",
    gridColumn: "span 2",
    borderRadius: 8,

    ":focus-within": {
      boxShadow: `0px 0px 0px 2px ${theme.colors.primary.castletonGreen20}`,
    },

    selectors: {
      [`${outlined} &`]: {
        border: `1px solid ${textInputFillColor}`,
      },
      [`${filled} &`]: {
        backgroundColor: textInputFillColor,
      },
      [`${filled}${inputContainer.error} &`]: {
        backgroundColor: theme.colors.primary.softWhite,      
      },
    },
  }
);

export const input = style(
  [
    bodySmall,
    {
      flex: 1,
      paddingLeft: 20,
      paddingRight: 20,
      paddingTop: 5,
      minHeight: "46px !important",
      fontFamily: "inherit",
      backgroundColor: "transparent",
      border: 0,
      margin: 0,
      color: "inherit",
      selectors: {
        "&:focus-visible": {
          outline: 0,
        },
        "&:placeholder": {
          color: theme.colors.grayscale[200],
        },
        // Hide number input arrows
        /* Chrome, Safari, Edge, Opera */
        "&::-webkit-outer-spin-button, &::-webkit-inner-spin-button": {
          WebkitAppearance: "none",
          margin: 0,
        },
        /* Firefox */
        "&[type=\"number\"]": {
          MozAppearance: "textfield",
        },
      },

      "@media": {
        [breakpoints.tablet]: {
          padding: "0 24px",
          minHeight: "56px !important",
        }
      }
    },
  ]
);

export const error = style(
  {
    color: theme.colors.primary.error,
    textAlign: "end",
    marginLeft: "auto",
  }
);

export const label = style(
  [noteMedium, {
    selectors: {
      [`${inputContainer.error} &`]: {
        color: textInputFillColor,
      },
    }
  }],
);

export const labelInverted = style({
  selectors: {
    [mode("residential")]: {
      color: theme.colors.primary.softWhite,
    },
  },
});

export const menu = style({
  marginTop: 4,
});

export const menuList = style({
  display: "flex",
  flexDirection: "column",
  background: theme.colors.primary.softWhite,
  boxShadow: "0px 2px 20px 0px rgba(128, 125, 121, 0.15)",
  borderRadius: 8,
});

const optionBase = style([
  bodySmall,
  {
    cursor: "pointer",
    padding: "10px 24px",
    position: "relative",
    selectors: {
      "&::after": {
        content: "",
        borderBottom: `1px solid ${theme.colors.grayscale[100]}`,
        position: "absolute",
        bottom: 0,
        left: 24,
        right: 24,
      },
      "&:first-child": {
        paddingTop: 20,
      },
      "&:last-child": {
        borderBottom: 0,
        paddingBottom: 20,
      },
      "&:last-child::after": {
        borderBottom: 0,
      },
    },
  },
]);

export const option = styleVariants({
  base: [optionBase],
  selected: {
    fontWeight: 600,
  },
});

export const singleValue = style({
  selectors: {
    [mode("residential")]: {
      color: theme.colors.primary.softWhite,
    }
  },
});

export const dropdownIndicator = style({
  display: "flex",
});

export const dropdownIndicatorContainerDarkMode = style({
  selectors: {
    [mode("residential")]: {
      color: theme.colors.primary.softWhite,
    }
  },
});

export const noOptionsMessage = style({
  margin: "16px auto",
});


export const placeholder = style({
  color: theme.colors.grayscale[200],
});