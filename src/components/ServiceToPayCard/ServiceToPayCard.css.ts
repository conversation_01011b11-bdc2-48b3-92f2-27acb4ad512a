import { primary } from "./../IconButton/IconButton.css";
import { breakpoints } from "@/styles/constants.css";
import { theme } from "@/styles/themes.css";
import { style, styleVariants } from "@vanilla-extract/css";
import { bodySmall } from "../Typography/Typography.css";


const containerBase = style({
  borderRadius: 16,
  padding: "20px 40px",
  display: "grid",
  rowGap: 24,
  columnGap: 8,
  position: "relative",
  "@media": {
    [breakpoints.tablet]: {
      padding: 40,
    }
  }
});

const containerPrimary = style({
  background: theme.colors.primary.ivory,
  color: theme.colors.primary.castletonGreen,

});
const containerSecondary = style({

  color: theme.colors.primary.ivory,
  background: theme.colors.primary.castletonGreen,
});

export const container = styleVariants({
  primary: [containerBase, containerPrimary],
  secondary: [containerBase, containerSecondary],
});

export const divLineBase = style({
  borderTop: `1px solid ${theme.colors.grayscale[100]}`,
  opacity: .2,
  gridColumn: "1 / -1"
});

const divLineShowOnMobile = style({
  display: "block",
  margin: "24px 0",
  "@media": {
    [breakpoints.tablet]: {
      display: "none"
    }
  }
});

export const divLine = styleVariants({
  default: [divLineBase],
  showOnMobile: [divLineBase, divLineShowOnMobile]
});

export const listItem = style([bodySmall,{
  display: "flex",
  columnGap: 10,
  fontWeight: 500
}]);


export const listBase = style({
  display: "grid",
  gridTemplateColumns: "1fr",
  // justifySelf: "center",
  gap: 12,
  paddingInlineStart: 0,
  "@media": {
    [breakpoints.tablet]: {
      // justifySelf: "unset",
      gap: 14,
    }
  }
});

export const listDefault = style({
  
});
export const listExclusions = style({
  color: theme.colors.grayscale[200]
});

export const list = styleVariants({
  default: [listBase, listDefault],
  exclusions: [listBase, listExclusions]
});


export const priceWrapper = style({
  display: "flex",
  alignItems: "center",
  whiteSpace: "break-spaces"
});

export const rightTopCorner = style({
  display: "grid",
  rowGap: 10,
  justifySelf: "center",
  gridAutoRows: "max-content",
  textAlign: "center",
    
  "@media": {
    [breakpoints.tablet]: {
      justifySelf: "end",
      textAlign: "end"
    }
  }
});

export const leftTopCorner = style({
  display: "grid",
  gridAutoRows: "max-content",
  rowGap: 16,
  textAlign: "center",
  justifySelf: "center",
  justifyContent: "start",
  
  "@media": {
    [breakpoints.tablet]: {
      marginBottom: 8,
      rowGap: 24,
      textAlign: "start",
      justifySelf: "unset",
    }
  }
});

export const expireDate = style({
  opacity: .8
});


const priceBase = style({});
const pricePrimary = style({});
const priceSecondary = style({
  color: theme.colors.primary.asidGreen
});


export const price = styleVariants({
  primary: [priceBase, pricePrimary],
  secondary: [priceBase, priceSecondary],
});

export const listsWrapper = style({
  display: "grid",
  gridTemplateColumns: "1fr",
  gridColumn: "1 / -1",
  gap:12,
  "@media": {
    [breakpoints.tablet]: {
      gridTemplateColumns: "repeat(2, 1fr)",
    }
  }
});


export const unactiveToggleText = style({
  opacity: .6
});

export const togglerWrapper = style({
  backgroundColor: theme.colors.primary.castletonGreenPressed,
  borderRadius: 30,
  position: "absolute",
  color: theme.colors.primary.softWhite,
  display: "flex",
  columnGap: 10,
  padding: "10px 18px",
  left: "50%",
  transform: "translate(-50%,-50%)",
  alignItems: "end",
  cursor: "pointer",
  "@media": {
    [breakpoints.tablet]: {
      left: 24,
      transform: "translateY(-50%)",

    }
  }
  
});


const hideExclusionsButtonBase = style({
  gridColumn: "1 / -1",
  display: "flex",
  textDecoration: "underline",
  "@media": {
    [breakpoints.tablet]: {
      display: "none"
    }
  }
});
const hideExclusionsButtonPrimary = style({

});
const hideExclusionsButtonSecondary = style({
  color: theme.colors.primary.softWhite
});

export const hideExclusionsButton = styleVariants({
  primary: [hideExclusionsButtonBase, hideExclusionsButtonPrimary],
  secondary: [hideExclusionsButtonBase, hideExclusionsButtonSecondary],
});

export const hideList = style({
  display: "none",
  "@media": {
    [breakpoints.tablet]: {
      display: "inherit"   
    }
  }
});

export const appliancesToggle = style({
  display: "flex",
  columnGap: 16,
  cursor: "pointer",
  alignItems: "flex-end"
});


const applliancePriceActive = style({
  color: theme.colors.primary.asidGreen
});
const applliancePriceUnActive = style({
  color: theme.colors.primary.softWhite,
  opacity: .4
});

export const applliancePrice = styleVariants({
  active: [applliancePriceActive],
  unactive: [applliancePriceUnActive]
});


export const togglersWrapper = style({
  display: "grid",
  rowGap: 16,
});


export const bestPlanNote = style({
  display: "flex",
  columnGap: 4,
  color: theme.colors.primary.asidGreen,
  fontSize: 14,
  marginBottom: 8,
  "@media": {
    [breakpoints.tablet]: {
      gridColumn: 2,
      gridRow: 1,
      alignSelf: "end"
    }
  }

});

export const title = style({

});

export const appliancesTitleWrapper = style({
  display: "flex",
  columnGap: 4,
  height: "max-content",
  alignItems: "center",
  
});

export const infoIcon  =style({
  color: theme.colors.primary.asidGreen,
  fontSize: 20

});

export const appliancesListWrapper = style({
  display: "grid",
  rowGap: 12,
  "@media": {
    [breakpoints.tablet]: {
      gridTemplateColumns: "repeat(2,1fr)",
      
      
    }
  }
});


export const cardHeader = style({
  display: "grid",
  gridTemplateColumns: "1fr max-content",
  marginTop: 16,
  "@media": {
    [breakpoints.tablet]: {
      marginTop: 0
    }
  }
});

export const cancelSubButton = style({
  "@media": {
    [breakpoints.tablet]: {
      justifySelf: "end",
    }
  }
});


export const warningIcon = style({
  color: theme.colors.primary.error,
  fontSize: 24
});


const inactiveTextPrimary = style({});
const inactiveTextSecondary = style({
  color: theme.colors.primary.ivory,
  opacity: .8
});

export const inactiveText = styleVariants({
  primary: [inactiveTextPrimary],
  secondary: [inactiveTextSecondary],
});

export const inactiveWrapper = style({
  display: "flex",
  alignItems: "flex-end",
  justifyContent: "flex-end",
  columnGap: 4
});


export const tootlipTrigger  =style({
  cursor: "help"
});