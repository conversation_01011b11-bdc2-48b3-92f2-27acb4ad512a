import { CustomerPlan, PlanAppliances, PlanPrice } from "@/types/plans";
import { SupabaseClient } from "@supabase/supabase-js";
import { Dispatch, SetStateAction } from "react";


export type ServiceToPayCardVariant = "primary" | "secondary"

type ServiceToPayCardProps = {
  variant?: ServiceToPayCardVariant;
  serverPlan: PlanPrice[];
  serverAppliances: PlanAppliances[];
  isYearly: boolean
  customerPlans: CustomerPlan[] | null
  expireTime?: number | null
  havePlan?: boolean
  supabase: SupabaseClient<any, "public", any>
  refreshCustomerPlans: (customerPlan?: CustomerPlan) => Promise<void>;
  setDisabledPlans:  Dispatch<SetStateAction<boolean>>;
  isDisabled?: boolean
  isInactive?: boolean
  
  id: string;
  name: string;
  isHighlighted?: boolean;
  monthPrice: number;
  yearPrice: number;
  features: string[];
  exclusions: string[];
  appliances?: Array<{
    id: string;
    name: string;
    monthPrice: number;
    yearPrice: number;
  }>;
};

export default ServiceToPayCardProps;