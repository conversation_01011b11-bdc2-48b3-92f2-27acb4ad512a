
import { usePathname, useRouter, useSearchParams } from "next/navigation";

import ServiceToPayCardProps from "./ServiceToPayCard.types";
import * as styles from "./ServiceToPayCard.css";
import Typography from "../Typography";
import dayjs from "dayjs";
import MemoCheckMarkIcon from "@/assets/icons/CheckMarkIcon";
import { formatNumToGBP } from "@/utils/helpers";
import Button from "../Button";
import { useCallback, useEffect, useMemo, useState } from "react";
import MemoCloseIcon from "@/assets/icons/CloseIcon";
import Toggler from "../Toggler";
import classNames from "classnames";
import TextButton from "../TextButton";
import Star from "../Star";
import PlanPurchaseModal from "../PlanPurchaseModal";
import MemoInfoOutlineIcon from "@/assets/icons/InfoOutlineIcon";
import ConfirmModal from "../ConfirmModal";
import { CustomerPlan, PeacePlanTypes } from "@/types/plans";
import WarningIcon from "@/assets/icons/WarningIcon";
import Tooltip from "../Tooltip";

const renderToggleLabel = (checked: boolean | undefined, label: string) => (<Typography
  className={classNames({[styles.unactiveToggleText]: !checked })}
  variant="buttonSmall"
>{label}</Typography>);

const APPLIANCES_TOOLTIP_TEXT = <>Additional appliances will<br/>
add additional fee to the<br/>
monthly package cost. <br/>
Each appliance adds <b>£2.50</b><br/>
 onto the monthly plan.</>;

const ServiceToPayCard = ({
  variant = "primary",
  serverPlan,
  serverAppliances,
  isYearly,
  expireTime,
  havePlan,
  customerPlans,
  supabase,
  refreshCustomerPlans,
  setDisabledPlans,
  isDisabled,
  isInactive,

  ...plan
}: ServiceToPayCardProps) => {

  const router = useRouter();

  // const [customSecret, setCustomSecret] = useState<string | null>(null);
  const [type, setType] = useState<PeacePlanTypes>("homeowner");
  const [hideExclusions, setHideExclusions] = useState(false);
  const [activeAppliances, setActiveAppliances] = useState<string[]>([]);

  const [cancelModalOpen, setCancelModalOpen] = useState(false);
  const [updateModalOpen, setUpdateModalOpen] = useState(false);
  const [restoreModalOpen, setRestoreModalOpen] = useState(false);


  useEffect(() => {
    if(!customerPlans) return;
    const currentPurchasedPlan = customerPlans[0];
    const [planName, planType] =  currentPurchasedPlan.metadata.plan_name.split("_");
    if(planName !== "peace" || planType !== "landlord") return;
    if(!currentPurchasedPlan) return;
    const splittedAppliances = currentPurchasedPlan.metadata.additionalAppliances?.split(";") || [];
    setType(planType);
    setActiveAppliances(splittedAppliances);

  }, [customerPlans]);
  

  const serverPlanByPlanType = useMemo(() => {
    const isCancelled = customerPlans && customerPlans[0].cancel_at_period_end;
    const currentRecurring = isYearly ? "year" : "month"; 
    const plansByName =  serverPlan.filter(({product}) => {
      return  product.metadata.plan_name === (plan.appliances ? [plan.id, type].join("_") : plan.id);
      // const isCorrectPlanType = product.metadata.plan_name === (plan.appliances ? [plan.id, type].join("_") : plan.id);
      // const isCorrectRecurring = recurring.inverval === currentRecurring;
      // return isCorrectPlanType && isCorrectRecurring;
    });

    return plansByName.find((plan) => {
      
      const serverPlanName = plan && plan?.product.metadata.plan_name;
      const customerPlanName = customerPlans && customerPlans[0].metadata.plan_name;
      const isCurrentActivePlan = serverPlanName === customerPlanName;
      if(isCurrentActivePlan && isCancelled) {
        const customerPlanRecurringType = customerPlans && customerPlans[0].plan.interval;
        return plan.recurring.inverval === customerPlanRecurringType;
      }
      return currentRecurring === plan.recurring.inverval;
    });

  } , [serverPlan,plan,type, isYearly,customerPlans]);

  const parsedAppliances = useMemo(() => serverAppliances.filter(({recurring_interval}) => {
    const isReccuringTypeYearly = serverPlanByPlanType?.recurring.inverval === "year";
    if(isReccuringTypeYearly) return recurring_interval === "year";
    return recurring_interval === "month";

  }),[serverPlanByPlanType,serverAppliances]);

  const isChanged = useMemo(() => {
    
    if(!serverPlanByPlanType || !customerPlans) return false;
    const currentPurchasedPlan = customerPlans[0];
    if(currentPurchasedPlan.plan.interval !== (isYearly ? "year" : "month")) return true;
    const [planName, planType] =  currentPurchasedPlan.metadata.plan_name.split("_");
    
    if(planName !== "peace") return false;
    // Check if peace plan type change
    if(type !== planType) return true;
    
    if(currentPurchasedPlan.metadata.additionalAppliances && currentPurchasedPlan.metadata.plan_name.includes("landlord")) {

      const splittedAppliances = currentPurchasedPlan.metadata.additionalAppliances.split(";");
      
      // Check if appliances amount change
      if(activeAppliances.length !== splittedAppliances.length) return true;
      
      // Check if appliances change
      for (let i = 0; i < activeAppliances.length; i++) {
        const item = activeAppliances[i];
        if(splittedAppliances.includes(item)) {
          continue;
        }
        return true;
      }
    }

    // Check if interval change
    return false;
    
  } , [customerPlans, serverPlanByPlanType, isYearly,activeAppliances, type]);

  const isLandlord = type === "landlord";
  const isPurchased = !!expireTime;
  const isPlanUpdate = havePlan || (isPurchased && isChanged);



  const formattedDate = useMemo(() => expireTime ? dayjs(expireTime * 1000).format("DD/MM/YYYY") : "Invalid date!", [expireTime] );
  const formteedInterval = useMemo(() => serverPlanByPlanType?.recurring.inverval === "year" ? "yearly" : "monthly", [serverPlanByPlanType]);

  const handleCloseCancelModal = useCallback(
    () => {
      setCancelModalOpen(false);
    },
    [],
  );
  const handleOpenCancelModal = useCallback(
    () => {
      setCancelModalOpen(true);
    },
    [],
  );
  const handleCloseUpdateModal = useCallback(
    () => {
      setUpdateModalOpen(false);
    },
    [],
  );
  const handleOpenUpdateModal = useCallback(
    () => {
      setUpdateModalOpen(true);
    },
    [],
  );
  
  const handleCloseRestoreModal = useCallback(
    () => {
      setRestoreModalOpen(false);
    },
    [],
  );
  const handleOpenRestoreModal = useCallback(
    () => {
      setRestoreModalOpen(true);
    },
    [],
  );
  

  const onToggleChange = useCallback(
    (checked: boolean) => {
      setType(!checked ? "homeowner" : "landlord");
    },
    [],
  );

  
  const onToggleAppliance = useCallback(
    (checked: boolean, value: string | number | readonly string[] | undefined) => {
      setActiveAppliances(prev => checked ? [...prev, value as string] : prev.filter((prevValue) => prevValue !== value));
    },
    [],
  );

  

  const handleGetPlan =  () => {


    // const serverPlanByPlanType = serverPlans.prices.find(({recurring}) => recurring.inverval === planType);

    if (!serverPlanByPlanType) return;


    const recurringType = isYearly ? "year" : "month";

    const params = new URLSearchParams({plan: plan.id, type: recurringType});
        
    if (activeAppliances && activeAppliances.length) {
      params.set("appliances", activeAppliances.join(","));
    }

    const url = `/payment/${serverPlanByPlanType.id}`;
    router.push(`${url}?${decodeURIComponent(params.toString())}`);
    

    // if (!serverPlanByPlanType) return;

    // router.push(`/payment/${serverPlanByPlanType.id}`);

    // const matchedServerPlans = serverPlan.filter((pl) => pl.product.metadata.plan_name === (plan.appliances ? [plan.id, type].join("_") : plan.id));

    // const serverPlanByPlanType = matchedServerPlans.find((plan) => plan.recurring.inverval === (isYearly ? "year" : "month"));

    // if (!serverPlanByPlanType) return;

    // const body: {
    //   priceId: string
    //   additionalAppliances?: string[]
    // } = { priceId: serverPlanByPlanType.id };

    // if (plan.appliances?.length && activeAppliances.length) {
    //   body.additionalAppliances = activeAppliances;
    // }

    // const response = await supabase.functions.invoke<{
    //   data: {
    //     clientSecret: string
    //     url: string | null
    //   }
    // }>("stripe/checkout", {
    //   method: "POST",
    //   body,
    // });

    // setCustomSecret(response.data?.data?.clientSecret ?? null);
  };
  

  const handleUpdatePlan = async () => {

    if(!serverPlanByPlanType) return;

    setDisabledPlans(true);
    
    const body: {
      priceId: string
      additionalAppliances?: string[]
    } = { priceId: serverPlanByPlanType.id };

    if (serverPlanByPlanType.product.metadata.plan_name.includes("landlord")) {
      body.additionalAppliances = activeAppliances;
    }

    try {
      const response = await supabase.functions.invoke<{
        data: CustomerPlan
      }>("stripe/customers/plans", {
        method: "PATCH",
        body,
      });
      const responseData = response.data?.data;
      await refreshCustomerPlans(responseData);
      setUpdateModalOpen(false);
    } catch {
      
    }

   
    setDisabledPlans(false);
    handleCloseUpdateModal();
    
  };

  const onCancelSub = useCallback(
    async () => {

      if(!serverPlanByPlanType) return;

   
      setDisabledPlans(true);
    
      // const currentUserSubResponse = await supabase.functions.invoke<CustomerPlan[]>("stripe/customers/plans", { method: "GET" });
      const currentUserSub = customerPlans?.find(({ metadata }) => (metadata.plan_name === serverPlanByPlanType.product.metadata.plan_name));
      if(!currentUserSub) return;

      const body: {
        subscriptionId: string
    } =  { subscriptionId: currentUserSub.id };

      try {
        const cancelResponse = await supabase.functions.invoke<CustomerPlan>("stripe/customers/plans", { method: "DELETE", body });
        await refreshCustomerPlans(cancelResponse?.data || undefined);
        setCancelModalOpen(false);
      } catch {
      
      }
      setDisabledPlans(false);
     
    },
      
    [serverPlanByPlanType,customerPlans,supabase],
  );
  

  const planPrice = useMemo( () => {
    const isReccuringTypeYearly = serverPlanByPlanType?.recurring.inverval === "year";
    let basePrice = isReccuringTypeYearly ?  plan.yearPrice: plan.monthPrice;

    if (type === "landlord") {
      basePrice =  basePrice + activeAppliances.reduce((accum, applianceName) => {
        const appliance = plan.appliances?.find(({id}) => id === applianceName);
        if(!appliance) return accum;
        const applianceBasePrice = (isReccuringTypeYearly ?  appliance.yearPrice : appliance.monthPrice);

        return accum + applianceBasePrice;
      }, 0);
    }

    return basePrice / 100;
  },[type,serverPlanByPlanType,plan,activeAppliances]);

  const handleRestorePlan = useCallback(
    async () => {

      if(!serverPlanByPlanType) return;

   
      setDisabledPlans(true);
  
      // const currentUserSubResponse = await supabase.functions.invoke<CustomerPlan[]>("stripe/customers/plans", { method: "GET" });
      const currentUserSub = customerPlans?.find(({ metadata }) => (metadata.plan_name === serverPlanByPlanType.product.metadata.plan_name));
      if(!currentUserSub) return;

      const body: {
      subscriptionId: string
  } =  { subscriptionId: currentUserSub.id };

      try {
        const response = await supabase.functions.invoke<CustomerPlan>("stripe/customers/plans/restore", {
          method: "PATCH",
          body,
        });

        await refreshCustomerPlans(response.data || undefined);
        setRestoreModalOpen(false);
      } catch {
        
      }

      setDisabledPlans(false);
  
    },
    [customerPlans,refreshCustomerPlans,supabase,serverPlanByPlanType],
  );
  

  const isThisCurrentActivePlan = useMemo(() =>{
    const serverPlanName = serverPlanByPlanType && serverPlanByPlanType?.product.metadata.plan_name;
    const customerPlanName = customerPlans && customerPlans[0].metadata.plan_name;
    return serverPlanName === customerPlanName;
  }, [customerPlans, serverPlanByPlanType]);
  


  const isCancelled = useMemo(() => !!(customerPlans && customerPlans[0].cancel_at_period_end && isThisCurrentActivePlan), [customerPlans,isThisCurrentActivePlan]); 

  // This function was made to simpligy code readability
  // It is not the best soluiton for code clarity 
  // I think would be better to make some kind of plan status and show appropriate component by object where status is a key and component is a value
  // But I don't have much time for this 
  const renderActionButton = useCallback(
    () => {
      // Here we check if plan was cacelled and we show a button to restore it (to update plan you need to restore it first)
      if(isCancelled && isThisCurrentActivePlan && !isChanged) {
        return (
          <Button
            disabled={isDisabled}
            onClick={handleOpenRestoreModal}
            isAnimated
          >
            Restore plan
          </Button>
        ); 

      }

      // Here we have three cases:
      // 1. Plan is purchased but his appliances or recurring type was changes. So we show button to update this plan
      // 2. We dont't have a plan so we can buy it by pressing Get Cover
      // 3. This plan is purchased and active at the moment so we can show it's expire time
      if(!isPurchased || (isChanged && isPurchased)) {
        if(isPlanUpdate) {
          return (
            <Button
              disabled={isDisabled}
              onClick={handleOpenUpdateModal}
              isAnimated
            >
          Update plan
            </Button>
          ); 
        }
        return (
          <Button
            disabled={isDisabled}
            onClick={handleGetPlan}
            isAnimated
          >
            Get cover
          </Button>); 
      }
      else {
        return <>
          <Typography
            variant="bodyMedium"
          ><b>Current plan {`(${formteedInterval})`}</b></Typography>
          {isInactive ? (
            <Typography
              className={styles.inactiveWrapper}
              variant="bodySmall"
            >
              <WarningIcon
                className={styles.warningIcon}
              />
              <p
                className={styles.inactiveText[variant]}
              >{" Inactive"}
              </p>
            </Typography>
          ) 
            : (
              <Typography
                className={styles.expireDate}
                variant="bodySmall"
              >
              Expires {formattedDate}
              </Typography>
            )}
        </>;
      }
    },
    [isCancelled,
      variant,
      isDisabled,
      isThisCurrentActivePlan,
      isPurchased,
      isChanged,
      isPlanUpdate,
      formteedInterval,
      handleOpenRestoreModal,
      handleOpenUpdateModal,
      handleGetPlan,
    ],
  );
  


  // It would be better to move all this modals in the parent component and call them from there
  
  return (<>
    {/* <PlanPurchaseModal
      customSecret={customSecret}
      onClose={() => setCustomSecret(null)}
    /> */}
    <ConfirmModal 
      title={<span>Cancel <br />Subscription</span>}
      open={cancelModalOpen}
      onClose={handleCloseCancelModal}
      acceptButtonProps={{
        variant: "outlined",
        color: "secondary",
        children: "Dismiss",
        onClick: handleCloseCancelModal
      }}
      cancelButtonProps={{
        variant: "filled",
        color: "error",
        children: "Cancel",
        onClick: onCancelSub,
      }}
    >
      {"By cancelling your subscription, you'll still have access to our"} <b>{`${serverPlanByPlanType?.product.name || "Unknown"} Plan services until ${formattedDate}.`}</b>
    </ConfirmModal>
    <ConfirmModal 
      title={"Update Plan"}
      open={updateModalOpen}
      onClose={handleCloseUpdateModal}
      acceptButtonProps={{
        variant: "outlined",
        color: "secondary",
        children: "Dismiss",
        onClick: handleCloseUpdateModal
      }}
      cancelButtonProps={{
        variant: "filled",
        color: "secondary",
        children: "Update Plan",
        onClick: handleUpdatePlan,
      }}
    >
      <b>
        {"Changing plan can lead to additional charges from your payment method. Are you sure want to proceed?"}
      </b>
    </ConfirmModal>
    <ConfirmModal 
      title={"Restore Plan"}
      open={restoreModalOpen}
      onClose={handleCloseRestoreModal}
      acceptButtonProps={{
        variant: "outlined",
        color: "secondary",
        children: "Dismiss",
        onClick: handleCloseRestoreModal
      }}
      cancelButtonProps={{
        variant: "filled",
        color: "secondary",
        children: "Restore plan",
        onClick: handleRestorePlan,
      }}
    >
      <b>
        {`Your Peace Plan subscription is active until ${formattedDate}. Restore it now to avoid service interruption?`}
      </b>
    </ConfirmModal>
    <div
      id={plan.id}
      className={styles.container[variant]}
    >
      {variant === "secondary" && (<Toggler
        wrapperClassname={styles.togglerWrapper}
        variant="filled"
        onChange={onToggleChange}
        checked={isLandlord} 
        preffix={(checked) => renderToggleLabel(!checked, "Homeowner")}
        suffix={(checked) => renderToggleLabel(checked, "Landlord")}
      />)}
      <div
        className={styles.cardHeader}
      >
        <div
          className={styles.leftTopCorner}
        >
          <Typography
            className={styles.title}
            variant="h4"
            fontFamily="primary"
          >
            {plan.name}
          </Typography>
          <div
            className={styles.priceWrapper}
          >
            <Typography
              className={styles.price[variant]}
              variant="subTitleMedium"
            >{formatNumToGBP(planPrice)}</Typography>
            <Typography
              variant="bodyMedium"
            >{` / per ${serverPlanByPlanType?.recurring.inverval}`}</Typography>
          </div>
          {variant === "secondary" && (<div
            className={styles.bestPlanNote}
          >
            <Star
              filled
            />
            <Typography
              variant="note"
            >Most Popular & Best Value</Typography>
          </div>)
          }
        </div>
        <div
          className={styles.divLine.showOnMobile}
        />
        <div
          className={styles.rightTopCorner}
        >
          {renderActionButton()}
        </div>
      </div>
      {isLandlord && variant === "secondary" && (<div
        className={styles.divLine.default}
      />)}
      {isLandlord && variant === "secondary" && (<div
        className={styles.appliancesListWrapper}
      >
        <div
          className={styles.appliancesTitleWrapper}
        >
          <Typography
            variant="bodyMedium"
          >
            Additional appliances
          </Typography>
          <Tooltip
            arrow
            variant="tertiary"
            on={["hover", "focus"]}
            trigger={(
              <div
                className={styles.tootlipTrigger}
              >
                <MemoInfoOutlineIcon
                  className={styles.infoIcon}
                />
              </div>
            )}
          >
            <Typography
              as={"p"}
              variant="note"
            >
              {APPLIANCES_TOOLTIP_TEXT}
            </Typography>
          </Tooltip>
          
        </div>
        <div
          className={styles.togglersWrapper}
        >
          {parsedAppliances.map(({name, unit_amount}) => {
            const checked = activeAppliances.includes(name);
            return (
              <Toggler
                key={name}
                variant="filledDifferActiveState"
                wrapperClassname={styles.appliancesToggle}
                value={name}
                checked={checked}
                onChange={onToggleAppliance}
                suffix={() => (
                  <>
                    <Typography
                      variant="buttonSmall"
                    >{plan.appliances?.find(({id}) => (id === name))?.name}</Typography>
                    <Typography
                      className={classNames({[ styles.applliancePrice.active]: checked, [styles.applliancePrice.unactive]: !checked})}
                      variant="buttonSmall"
                    >+{formatNumToGBP(unit_amount / 100)}</Typography>
                  </>
                )}
              />
            );})}
        </div>
      </div>)}
      <div
        className={styles.divLine.default}
      />
      <div
        className={styles.listsWrapper}
      >
        <ul
          className={styles.list.default}
        >
          {plan.features.map((item) => (
            <li
              key={item}
              className={styles.listItem}
            >
              <MemoCheckMarkIcon/>
              {item}
            </li>
          ))}
        </ul>
        <ul
          className={classNames(styles.list.exclusions, {[styles.hideList]: hideExclusions})}
        >
          {plan.exclusions.map((item) => (
            <li
              key={item}
              className={styles.listItem}
            >
              <MemoCloseIcon/>
              {item}
            </li>
          ))}
        </ul>
      </div>
      <TextButton
        color={variant}
        className={styles.hideExclusionsButton[variant]}
        as={"button"}
        onClick={() => setHideExclusions(prev => !prev)}
      >
        {hideExclusions ? "Show" : "Hide"} Exclusions
      </TextButton>
      {isPurchased && !isCancelled && !isChanged && (
        <Button
          disabled={isDisabled}
          color={variant === "primary" ? "secondary" : "secondaryInverted"}
          onClick={handleOpenCancelModal}
          className={styles.cancelSubButton}
          variant="outlined"
        >
          Cancel Subscription
        </Button>
      )}
    </div>
  </>
  );
};
  

export default ServiceToPayCard;