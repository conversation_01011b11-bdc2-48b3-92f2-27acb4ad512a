import { UpdateUserDto } from "@/utils/api/types/dto";
import { Option } from "../DropdownInput/DropdownInput.types";

export type HomeInfoPageFormValuesType = {
  property_type: Option;
  boiler_fuel_source: Option;
  bathrooms_count?: string;
  radiators_count?: string;
} & Partial<
  Pick<
    UpdateUserDto,
    | "home_ownership_status"
    | "boiler_age"
    | "airing_cupboard_cylinder"
    | "tanks_in_loft"
    | "smart_thermostat"
    | "thermostat_radiators_valves"
    | "boiler_serviced_recently"
    | "carbon_monoxide_alarm"
    | "any_cover"
    | "power_flush_carried_out"
  >
>;
