"use client";

import useStore from "@/hooks/useStore";
import {
  HOME_OWNERSHIP_STATUSES,
  YES_NO_VARIANTS,
} from "@/utils/api/types/common";
import {
  BOILER_FUEL_SOURCES_OPTIONS,
  PROPERTY_TYPES_OPTIONS,
} from "@/utils/constants";
import classNames from "classnames";
import { useCallback, useEffect, useRef } from "react";
import { Controller, useForm } from "react-hook-form";
import Button from "../Button";
import DropdownInput from "../DropdownInput";
import NumberInput from "../NumberInput";
import RadioButton from "../RadioButton";
import TextInput from "../TextInput";
import WrapperWithLabel from "../WrapperWithLabel";
import * as styles from "./HomeInfoPage.css";
import { HomeInfoPageFormValuesType } from "./HomeInfoPage.types";
import { cleanObjectFromFalsyValues } from "@/utils/helpers";

const HomeInfoPage = () => {
  const { alert, auth } = useStore();

  // This is workaround to fix a bug with init values
  // IDK why but first changes to a form will trigger a useEffect with values from the auth.user
  const valuesIsInitialized = useRef(false);

  const form = useForm<HomeInfoPageFormValuesType>({
    mode: "onBlur",
    defaultValues: {
      home_ownership_status: undefined,
      property_type: null,
      bathrooms_count: "",
      boiler_fuel_source: null,
      boiler_age: "",
      airing_cupboard_cylinder: undefined,
      tanks_in_loft: undefined,
      radiators_count: "",
      smart_thermostat: undefined,
      thermostat_radiators_valves: undefined,
      boiler_serviced_recently: undefined,
      carbon_monoxide_alarm: undefined,
      any_cover: undefined,
      power_flush_carried_out: undefined,
    },
  });

  const onSubmit = useCallback(async (payload: HomeInfoPageFormValuesType) => {
    const body = {
      home_ownership_status: payload.home_ownership_status,
      property_type: payload.property_type?.value || undefined,
      bathrooms_count: payload.bathrooms_count
        ? +payload.bathrooms_count
        : undefined,
      boiler_fuel_source: payload.boiler_fuel_source?.value || undefined,
      boiler_age: payload.boiler_age,
      airing_cupboard_cylinder: payload.airing_cupboard_cylinder,
      tanks_in_loft: payload.tanks_in_loft,
      radiators_count: payload.radiators_count
        ? +payload.radiators_count
        : undefined,
      smart_thermostat: payload.smart_thermostat,
      thermostat_radiators_valves: payload.thermostat_radiators_valves,
      boiler_serviced_recently: payload.boiler_serviced_recently,
      carbon_monoxide_alarm: payload.carbon_monoxide_alarm,
      any_cover: payload.any_cover,
      power_flush_carried_out: payload.power_flush_carried_out,
    };

    const cleanBody = cleanObjectFromFalsyValues(body);

    try {
      await auth.updatePersonalInfo({
        ...(cleanBody as any),
      });

      alert.addAlert({ content: "Updated User info", type: "success" });
    } catch (error) {
      alert.addAlert({ content: String(error), type: "error" });
    }
  }, []);

  useEffect(() => {
    if (!auth.user || valuesIsInitialized.current) return;
    valuesIsInitialized.current = true;

    form.reset({
      home_ownership_status: auth.user.home_ownership_status,
      property_type: auth.user.property_type
        ? {
            value: auth.user.property_type,
            label: auth.user.property_type,
          }
        : null,
      bathrooms_count:
        !!auth.user.bathrooms_count || auth.user.bathrooms_count === 0
          ? String(auth.user.bathrooms_count)
          : "",
      boiler_fuel_source: auth.user.boiler_fuel_source
        ? {
            value: auth.user.boiler_fuel_source,
            label: auth.user.boiler_fuel_source,
          }
        : null,
      boiler_age: auth.user.boiler_age || "",
      airing_cupboard_cylinder: auth.user.airing_cupboard_cylinder,
      tanks_in_loft: auth.user.tanks_in_loft,
      radiators_count:
        !!auth.user.radiators_count || auth.user.radiators_count
          ? String(auth.user.radiators_count)
          : "",
      smart_thermostat: auth.user.smart_thermostat,
      thermostat_radiators_valves: auth.user.thermostat_radiators_valves,
      boiler_serviced_recently: auth.user.boiler_serviced_recently,
      carbon_monoxide_alarm: auth.user.carbon_monoxide_alarm,
      any_cover: auth.user.any_cover,
      power_flush_carried_out: auth.user.power_flush_carried_out,
    });
  }, [auth.user]);

  return (
    <div className={styles.container}>
      <form
        onSubmit={form.handleSubmit(onSubmit)}
        noValidate
        className={styles.personalInfoForm}
      >
        <Controller
          name="home_ownership_status"
          control={form.control}
          render={({ field, fieldState }) => (
            <WrapperWithLabel
              classNameContainer={styles.radioButtonsContainer.default}
              className={styles.radioButtonsWrapper}
              error={fieldState.error?.message}
              label="Do you rent or own your home?"
            >
              {HOME_OWNERSHIP_STATUSES.map((label) => (
                <RadioButton
                  key={label}
                  {...field}
                  value={label}
                  checked={field.value === label}
                >
                  {label}
                </RadioButton>
              ))}
            </WrapperWithLabel>
          )}
        />
        <Controller
          name="property_type"
          control={form.control}
          render={({ field, fieldState }) => (
            <DropdownInput
              options={PROPERTY_TYPES_OPTIONS}
              {...field}
              onChange={(option) => {
                field.onChange(option);
                field.onBlur();
              }}
              error={fieldState.error?.message}
              label="What type of property do you live in?"
              placeholder="Select your type"
            />
          )}
        />
        <Controller
          name="bathrooms_count"
          control={form.control}
          render={({ field, fieldState }) => (
            <NumberInput
              {...field}
              error={fieldState.error?.message}
              label="How many bathrooms are in your home?"
              placeholder="1"
            />
          )}
        />
        <Controller
          name="boiler_fuel_source"
          control={form.control}
          render={({ field, fieldState }) => (
            <DropdownInput
              options={BOILER_FUEL_SOURCES_OPTIONS}
              {...field}
              onChange={(option) => {
                field.onChange(option);
                field.onBlur();
              }}
              error={fieldState.error?.message}
              label="What is the fuel source for the boiler?"
              placeholder="Select your source"
            />
          )}
        />
        <Controller
          name="boiler_age"
          control={form.control}
          render={({ field, fieldState }) => (
            <TextInput
              {...field}
              error={fieldState.error?.message}
              label="How old is your boiler?"
              placeholder="Months/years"
            />
          )}
        />
        <Controller
          name="airing_cupboard_cylinder"
          control={form.control}
          render={({ field, fieldState }) => (
            <WrapperWithLabel
              classNameContainer={styles.radioButtonsContainer.withMarginTop}
              className={styles.radioButtonsWrapper}
              error={fieldState.error?.message}
              label="Have you got a cylinder in your airing cupboard?"
            >
              {YES_NO_VARIANTS.map((label) => (
                <RadioButton
                  key={label}
                  {...field}
                  value={label}
                  checked={field.value === label}
                >
                  {label}
                </RadioButton>
              ))}
            </WrapperWithLabel>
          )}
        />
        <Controller
          name="tanks_in_loft"
          control={form.control}
          render={({ field, fieldState }) => (
            <WrapperWithLabel
              classNameContainer={styles.radioButtonsContainer.default}
              className={styles.radioButtonsWrapper}
              error={fieldState.error?.message}
              label="Have you got tanks in the loft?"
            >
              {YES_NO_VARIANTS.map((label) => (
                <RadioButton
                  key={label}
                  {...field}
                  value={label}
                  checked={field.value === label}
                >
                  {label}
                </RadioButton>
              ))}
            </WrapperWithLabel>
          )}
        />
        <Controller
          name="radiators_count"
          control={form.control}
          render={({ field, fieldState }) => (
            <NumberInput
              {...field}
              error={fieldState.error?.message}
              label="How many radiators do you have in your home?"
              placeholder="1"
            />
          )}
        />
        <Controller
          name="smart_thermostat"
          control={form.control}
          render={({ field, fieldState }) => (
            <WrapperWithLabel
              classNameContainer={styles.radioButtonsContainer.withMarginTop}
              className={styles.radioButtonsWrapper}
              error={fieldState.error?.message}
              label="Do you have a smart thermostat installed?"
            >
              {YES_NO_VARIANTS.map((label) => (
                <RadioButton
                  key={label}
                  {...field}
                  value={label}
                  checked={field.value === label}
                >
                  {label}
                </RadioButton>
              ))}
            </WrapperWithLabel>
          )}
        />
        <Controller
          name="thermostat_radiators_valves"
          control={form.control}
          render={({ field, fieldState }) => (
            <WrapperWithLabel
              classNameContainer={styles.radioButtonsContainer.default}
              className={styles.radioButtonsWrapper}
              error={fieldState.error?.message}
              label="Have you got thermostatic radiator valves?"
            >
              {YES_NO_VARIANTS.map((label) => (
                <RadioButton
                  key={label}
                  {...field}
                  value={label}
                  checked={field.value === label}
                >
                  {label}
                </RadioButton>
              ))}
            </WrapperWithLabel>
          )}
        />
        <Controller
          name="boiler_serviced_recently"
          control={form.control}
          render={({ field, fieldState }) => (
            <WrapperWithLabel
              classNameContainer={styles.radioButtonsContainer.default}
              className={styles.radioButtonsWrapper}
              error={fieldState.error?.message}
              label="Have you had your boiler serviced recently?"
            >
              {YES_NO_VARIANTS.map((label) => (
                <RadioButton
                  key={label}
                  {...field}
                  value={label}
                  checked={field.value === label}
                >
                  {label}
                </RadioButton>
              ))}
            </WrapperWithLabel>
          )}
        />
        <Controller
          name="carbon_monoxide_alarm"
          control={form.control}
          render={({ field, fieldState }) => (
            <WrapperWithLabel
              classNameContainer={styles.radioButtonsContainer.default}
              className={styles.radioButtonsWrapper}
              error={fieldState.error?.message}
              label="Have you got a carbon monoxide alarm?"
            >
              {YES_NO_VARIANTS.map((label) => (
                <RadioButton
                  key={label}
                  {...field}
                  value={label}
                  checked={field.value === label}
                >
                  {label}
                </RadioButton>
              ))}
            </WrapperWithLabel>
          )}
        />
        <Controller
          name="any_cover"
          control={form.control}
          render={({ field, fieldState }) => (
            <WrapperWithLabel
              classNameContainer={styles.radioButtonsContainer.default}
              className={styles.radioButtonsWrapper}
              error={fieldState.error?.message}
              label="Do you have any boiler/plumbing & heating cover?"
            >
              {YES_NO_VARIANTS.map((label) => (
                <RadioButton
                  key={label}
                  {...field}
                  value={label}
                  checked={field.value === label}
                >
                  {label}
                </RadioButton>
              ))}
            </WrapperWithLabel>
          )}
        />
        <Controller
          name="power_flush_carried_out"
          control={form.control}
          render={({ field, fieldState }) => (
            <WrapperWithLabel
              classNameContainer={styles.radioButtonsContainer.default}
              className={styles.radioButtonsWrapper}
              error={fieldState.error?.message}
              label="Have you had a power flush carried out?"
            >
              {YES_NO_VARIANTS.map((label) => (
                <RadioButton
                  key={label}
                  {...field}
                  value={label}
                  checked={field.value === label}
                >
                  {label}
                </RadioButton>
              ))}
            </WrapperWithLabel>
          )}
        />
        <Button
          isLoading={form.formState.isSubmitting}
          type="submit"
          disabled={
            form.formState.disabled ||
            !form.formState.isDirty ||
            !form.formState.isValid
          }
          className={classNames(styles.submitButton)}
        >
          Save home info
        </Button>
      </form>
    </div>
  );
};

export default HomeInfoPage;
