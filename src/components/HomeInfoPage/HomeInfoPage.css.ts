import { breakpoints } from "@/styles/constants.css";
import { theme } from "@/styles/themes.css";
import { style, styleVariants } from "@vanilla-extract/css";


export const container = style({
  display: "grid"
});



export const personalInfoForm = style({
  display: "grid",
  columnGap: 24,
  rowGap: 14,
  marginBottom: 16,
  "@media": {
    [breakpoints.tablet]: {
      rowGap: 16,
      marginBottom: 0,
    }
  }
});

export const variantsWrapper = style({});

export const submitButton = style({
  marginTop: 8
});


export const radioButtonsContainer = styleVariants({
  default: [{
    marginBottom: 6,
  }],
  withMarginTop: [{
    marginBottom: 6,
    marginTop: 6,
  }]
});

export const radioButtonsWrapper = style({
  display: "flex",
  flexWrap: "wrap",
  gap: 14,
}
);
