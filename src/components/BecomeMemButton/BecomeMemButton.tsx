"use client";

import Button from "../Button";
import useStore from "@/hooks/useStore";
import BecomeMemButtonProps from "./BecomeMemButton.types";
import { observer } from "mobx-react-lite";
import Link from "next/link";

const BecomeMemButton = observer(<P extends {},>({children, isAnimated = true, ...props}: BecomeMemButtonProps<P>) => {
  const { auth } = useStore();
  // @ts-ignore
  return <Button
    as={Link}
    prefetch={false}
    href={auth.isAuthorized ? "/profile" : "/register"}
    isAnimated={isAnimated}
    {...props}
         >{children || "Become a member"}</Button>;
});

export default BecomeMemButton;