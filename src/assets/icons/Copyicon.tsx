import * as React from "react";

function Copyicon(props: React.SVGProps<SVGSVGElement>) {
  return (
    <svg
      width="1em"
      height="1em"
      viewBox="0 0 26 26"
      fill="none"
      {...props}
    >
      <path
        d="M19.742 6.025H2.417a.964.964 0 00-.964.964v15.86c0 .532.432.964.964.964h17.325a.964.964 0 00.965-.964V6.989a.964.964 0 00-.965-.964z"
        fill="currentColor"
      />
      <path
        d="M23.571 2.168H5.828v1.928h16.779v15.332h1.928V3.133a.964.964 0 00-.964-.964z"
        fill="currentColor"
      />
    </svg>
  );
}

const MemoCopyicon = React.memo(Copyicon);
export default MemoCopyicon;
