import * as React from "react";

function LogOutIcon(props: React.SVGProps<SVGSVGElement>) {
  return (
    <svg
      width="1em"
      height="1em"
      style={{ minWidth: "1em" }}
      viewBox="12 12 24 24"
      fill="none"
      {...props}
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M24.246 14a4 4 0 00-4 4v5.192H26.9a14.996 14.996 0 01-1.091-1.577 31.365 31.365 0 01-1.091-2.115h1.317c1.58 1.872 3.235 3.256 4.965 4.154v.692c-1.73.872-3.386 2.257-4.965 4.154h-1.317c.376-.82.74-1.538 1.09-2.154.377-.615.74-1.128 1.092-1.538h-6.654V30a4 4 0 004 4h6a4 4 0 004-4V18a4 4 0 00-4-4h-6zM20.234 23.192H14v1.616h6.234v-1.616z"
        fill="currentColor"
      />
    </svg>
  );
}

const MemoLogOutIcon = React.memo(LogOutIcon);
export default MemoLogOutIcon;
