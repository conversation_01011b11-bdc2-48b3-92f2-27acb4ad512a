import * as React from "react";

type RotateIconP = "left" | "right" | "top" | "bottom";

const rotate: Record<RotateIconP, React.CSSProperties> = {
  left: { transform: "rotate(90deg)" },
  right: { transform: "rotate(-90deg)" },
  top: { transform: "rotate(-180deg)" },
  bottom: {},
};

const animStyle = { transition: "transform 250ms" } as React.CSSProperties;

export function ChevronIcon({
  turn = "bottom",
  disableAnimation,
  ...props
}: React.SVGProps<SVGSVGElement> & {
  turn?: RotateIconP;
  disableAnimation?: boolean;
}) {
  const rotateStyle = rotate[turn];
  const style = {
    ...props.style,
    ...rotateStyle,
    ...(disableAnimation ? {} : animStyle),
  };
  return (
    <svg
      width="1em"
      height="1em"
      viewBox="0 0 20 20"
      fill="none"
      {...props}
      style={style}
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M13.993 7.512c.853-.66 1.943-1.312 3.04-1.87v1.454c-2.884 2.509-5.05 5.143-6.485 7.904H9.363C7.927 12.239 5.76 9.605 2.877 7.096V5.642c1.097.558 2.188 1.21 3.04 1.87.869.671 1.864 1.628 2.67 2.403l.013.013c.47.452.882.849 1.153 1.071l.211.175.212-.175c.272-.223.684-.622 1.152-1.076l.014-.014c.8-.774 1.785-1.728 2.651-2.397z"
        fill="currentColor"
      />
    </svg>
  );
}

const MemoChevronIcon = React.memo(ChevronIcon);
export default MemoChevronIcon;
