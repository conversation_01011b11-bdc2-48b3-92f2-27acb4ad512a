import * as React from "react";

function CloseIcon(props: React.SVGProps<SVGSVGElement>) {
  return (
    <svg
      width="1em"
      style={{ minWidth: "1em" }}
      height="1em"
      viewBox="0 0 26 26"
      fill="none"
      {...props}
    >
      <rect
        x="3"
        y="21.3848"
        width="26"
        height="2.5"
        rx="1.25"
        transform="rotate(-45 3 21.3848)"
        fill="currentColor"
      />
      <rect
        x="5"
        y="3.38477"
        width="26"
        height="2.5"
        rx="1.25"
        transform="rotate(45 5 3.38477)"
        fill="currentColor"
      />
    </svg>
  );
}

const MemoCloseIcon = React.memo(CloseIcon);
export default MemoCloseIcon;
