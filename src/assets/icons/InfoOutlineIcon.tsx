import * as React from "react";

function InfoOutlineIcon(props: React.SVGProps<SVGSVGElement>) {
  return (
    <svg
      width="1em"
      height="1em"
      viewBox="0 0 24 24"
      fill="none"
      {...props}
    >
      <path
        d="M10.998 17h2v-6h-2v6zm1-8a.968.968 0 00.713-.288.964.964 0 00.287-.712.965.965 0 00-.288-.712.972.972 0 00-.712-.288.965.965 0 00-.712.288.973.973 0 00-.288.712c0 .283.096.521.288.713.192.192.43.288.712.287zm0 13a9.733 9.733 0 01-3.9-.788 10.114 10.114 0 01-3.175-2.137c-.9-.9-1.612-1.958-2.137-3.175a9.755 9.755 0 01-.788-3.9c0-1.383.263-2.683.788-3.9a10.114 10.114 0 012.137-3.175c.9-.9 1.958-1.612 3.175-2.137a9.755 9.755 0 013.9-.788c1.383 0 2.683.263 3.9.788a10.114 10.114 0 013.175 2.137c.9.9 1.613 1.958 2.138 3.175a9.718 9.718 0 01.787 3.9 9.731 9.731 0 01-.788 3.9 10.112 10.112 0 01-2.137 3.175c-.9.9-1.958 1.613-3.175 2.138a9.72 9.72 0 01-3.9.787zm0-2c2.233 0 4.125-.775 5.675-2.325 1.55-1.55 2.325-3.442 2.325-5.675 0-2.233-.775-4.125-2.325-5.675C16.123 4.775 14.231 4 11.998 4c-2.233 0-4.125.775-5.675 2.325C4.773 7.875 3.998 9.767 3.998 12c0 2.233.775 4.125 2.325 5.675C7.873 19.225 9.765 20 11.998 20z"
        fill="currentColor"
      />
    </svg>
  );
}

const MemoInfoOutlineIcon = React.memo(InfoOutlineIcon);
export default MemoInfoOutlineIcon;
