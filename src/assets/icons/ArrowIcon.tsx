import * as React from "react";

function ArrowIcon(props: React.SVGProps<SVGSVGElement>) {
  return (
    <svg
      width="1em"
      height="1em"
      viewBox="0 0 30 30"
      fill="none"
      style={{ minWidth: "1em" }}
      {...props}
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M20.9904 18.7326C22.2688 19.7208 23.905 20.6997 25.5508 21.5376V19.3566C21.2243 15.593 17.9759 11.642 15.8224 7.50004H14.9478H14.9471H14.0447C11.8913 11.642 8.64292 15.593 4.31641 19.3566V21.5376C5.96219 20.6997 7.59836 19.7208 8.87676 18.7326C10.1796 17.7255 11.6731 16.2894 12.8813 15.127L12.9016 15.1075C13.6059 14.4298 14.2244 13.8348 14.6296 13.5011L14.9474 13.2392L15.2653 13.5011C15.6726 13.8365 16.2906 14.4352 16.9931 15.1156L17.0142 15.1361C18.2131 16.2974 19.6914 17.7285 20.9904 18.7326Z"
        fill="currentColor"
      />
    </svg>
  );
}

export default ArrowIcon;
