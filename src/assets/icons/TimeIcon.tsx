import * as React from "react";

function TimeIcon(props: React.SVGProps<SVGSVGElement>) {
  return (
    <svg
      width="1em"
      height="1em"
      viewBox="0 0 20 20"
      fill="none"
      style={{ minWidth: "1em" }}
      {...props}
    >
      <path
        d="M10.0013 18.3346C5.39893 18.3346 1.66797 14.6036 1.66797 10.0013C1.66797 5.39893 5.39893 1.66797 10.0013 1.66797C14.6036 1.66797 18.3346 5.39893 18.3346 10.0013C18.3346 14.6036 14.6036 18.3346 10.0013 18.3346ZM10.8346 10.0013V5.83464H9.16797V11.668H14.168V10.0013H10.8346Z"
        fill="currentColor"
      />
    </svg>
  );
}

export default TimeIcon;
