import * as React from "react";

function CheckMarkIcon(props: React.SVGProps<SVGSVGElement>) {
  return (
    <svg
      width="1em"
      height="1em"
      viewBox="0 0 20 20"
      fill="none"
      style={{ minWidth: "1em" }}
      {...props}
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M2.266 9.406s1.708-1.073 2.263-1.272c.24.436 2.025 3.297 2.302 3.97.875-.834 6.433-7.067 11.435-8.138-.437.714-.714 1.032-.714 1.032s-8.615 5.558-9.846 11.037H6.038s-1.827-4.09-3.772-6.629z"
        fill="currentColor"
      />
    </svg>
  );
}

const MemoCheckMarkIcon = React.memo(CheckMarkIcon);
export default MemoCheckMarkIcon;
