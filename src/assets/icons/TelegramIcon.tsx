import * as React from "react";

function TelegramIcon(props: React.SVGProps<SVGSVGElement>) {
  return (
    <svg
      width="1em"
      height="1em"
      viewBox="0 0 27 27"
      fill="none"
      {...props}
    >
      <path
        d="M13.154.411c7.05 0 12.857 5.807 12.857 12.857 0 7.103-5.806 12.857-12.857 12.857-7.103 0-12.857-5.754-12.857-12.857C.297 6.218 6.05.411 13.154.411zm5.91 8.762c.052-.156.052-.311 0-.519 0-.103-.104-.259-.155-.31-.156-.156-.415-.156-.519-.156-.466 0-1.244.259-4.821 1.762-1.245.519-3.733 1.556-7.466 3.215-.622.259-.933.466-.985.726-.052.414.622.57 1.4.829.674.207 1.555.467 2.022.467.415 0 .881-.156 1.4-.519 3.473-2.385 5.288-3.577 5.391-3.577.104 0 .208-.052.26 0 .103.104.103.207.052.26-.052.258-3.319 3.265-3.526 3.473-.726.726-1.555 1.192-.26 2.022 1.09.725 1.712 1.192 2.852 1.918.726.466 1.296 1.037 2.022.985.363-.052.726-.363.881-1.348.467-2.23 1.297-7.206 1.452-9.228z"
        fill="currentColor"
      />
    </svg>
  );
}

const MemoTelegramIcon = React.memo(TelegramIcon);
export default MemoTelegramIcon;
