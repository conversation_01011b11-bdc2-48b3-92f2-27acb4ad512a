import * as React from "react";

function UserIcon(props: React.SVGProps<SVGSVGElement>) {
  return (
    <svg
      width="1em"
      height="1em"
      viewBox="0 0 20 20"
      fill="none"
      {...props}
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M14.4123 6.0759C14.4123 8.52342 12.4499 10.4859 10.0007 10.4859C7.55223 10.4859 5.589 8.52342 5.589 6.0759C5.589 3.62837 7.55223 1.66669 10.0007 1.66669C12.4499 1.66669 14.4123 3.62837 14.4123 6.0759ZM10.0007 18.3334C6.38596 18.3334 3.33398 17.7458 3.33398 15.4792C3.33398 13.2116 6.40514 12.645 10.0007 12.645C13.6162 12.645 16.6673 13.2325 16.6673 15.4992C16.6673 17.7667 13.5962 18.3334 10.0007 18.3334Z"
        fill="currentColor"
      />
    </svg>
  );
}

export default UserIcon;
