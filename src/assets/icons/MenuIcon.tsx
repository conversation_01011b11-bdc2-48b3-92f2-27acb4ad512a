import * as React from "react";

function MenuIcon(props: React.SVGProps<SVGSVGElement>) {
  return (
    <svg
      width="1em"
      height="1em"
      viewBox="0 0 26 26"
      fill="none"
      {...props}
    >
      <rect
        y={7.5}
        width={26}
        height={2.5}
        rx={1.25}
        fill="currentColor"
      />
      <rect
        y={16}
        width={26}
        height={2.5}
        rx={1.25}
        fill="currentColor"
      />
    </svg>
  );
}

const MemoMenuIcon = React.memo(MenuIcon);
export default MemoMenuIcon;
