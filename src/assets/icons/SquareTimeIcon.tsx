import * as React from "react";

function SquareTimeIcon(props: React.SVGProps<SVGSVGElement>) {
  return (
    <svg
      width="1em"
      height="1em"
      viewBox="0 0 19 20"
      fill="none"
      {...props}
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M5.53.833h7.948c3.108 0 5.189 2.181 5.189 5.426v7.49c0 3.236-2.081 5.418-5.189 5.418H5.531c-3.108 0-5.198-2.182-5.198-5.419V6.26c0-3.245 2.09-5.426 5.198-5.426zm7.252 12.66a.68.68 0 00.586-.34.682.682 0 00-.238-.944l-3.263-1.943V6.03a.687.687 0 10-1.375 0v4.63c0 .239.128.468.339.587l3.593 2.145c.************.358.1z"
        fill="currentColor"
      />
    </svg>
  );
}

const MemoSquareTimeIcon = React.memo(SquareTimeIcon);
export default MemoSquareTimeIcon;
