import * as React from "react";

function InfoIcon(props: React.SVGProps<SVGSVGElement>) {
  return (
    <svg
      width="1em"
      height="1em"
      viewBox="0 0 16 16"
      fill="none"
      {...props}
    >
      <path
        d="M7.33203 11.3333H8.66536V7.33334H7.33203V11.3333ZM7.9987 6.00001C8.18759 6.00001 8.34603 5.93601 8.47403 5.80801C8.60203 5.68001 8.66581 5.52179 8.66536 5.33334C8.66536 5.14445 8.60136 4.98623 8.47336 4.85868C8.34536 4.73112 8.18714 4.66712 7.9987 4.66668C7.80981 4.66668 7.65159 4.73068 7.52403 4.85868C7.39648 4.98668 7.33248 5.1449 7.33203 5.33334C7.33203 5.52223 7.39603 5.68068 7.52403 5.80868C7.65203 5.93668 7.81025 6.00045 7.9987 6.00001ZM7.9987 14.6667C7.07648 14.6667 6.20981 14.4916 5.3987 14.1413C4.58759 13.7911 3.88203 13.3162 3.28203 12.7167C2.68203 12.1167 2.20714 11.4111 1.85736 10.6C1.50759 9.7889 1.33248 8.92223 1.33203 8.00001C1.33203 7.07779 1.50714 6.21112 1.85736 5.40001C2.20759 4.5889 2.68248 3.88334 3.28203 3.28334C3.88203 2.68334 4.58759 2.20845 5.3987 1.85868C6.20981 1.5089 7.07648 1.33379 7.9987 1.33334C8.92092 1.33334 9.78759 1.50845 10.5987 1.85868C11.4098 2.2089 12.1154 2.68379 12.7154 3.28334C13.3154 3.88334 13.7905 4.5889 14.1407 5.40001C14.4909 6.21112 14.6658 7.07779 14.6654 8.00001C14.6654 8.92223 14.4903 9.7889 14.14 10.6C13.7898 11.4111 13.3149 12.1167 12.7154 12.7167C12.1154 13.3167 11.4098 13.7918 10.5987 14.142C9.78759 14.4922 8.92092 14.6671 7.9987 14.6667ZM7.9987 13.3333C9.48759 13.3333 10.7487 12.8167 11.782 11.7833C12.8154 10.75 13.332 9.4889 13.332 8.00001C13.332 6.51112 12.8154 5.25001 11.782 4.21668C10.7487 3.18334 9.48759 2.66668 7.9987 2.66668C6.50981 2.66668 5.2487 3.18334 4.21536 4.21668C3.18203 5.25001 2.66536 6.51112 2.66536 8.00001C2.66536 9.4889 3.18203 10.75 4.21536 11.7833C5.2487 12.8167 6.50981 13.3333 7.9987 13.3333Z"
        fill="currentColor"
      />
    </svg>
  );
}

export default InfoIcon;