import * as React from "react";

function WhatsAppIcon(props: React.SVGProps<SVGSVGElement>) {
  return (
    <svg
      width="1em"
      height="1em"
      viewBox="0 0 26 26"
      fill="none"
      {...props}
    >
      <g
        clipPath="url(#prefix__clip0_1375_26275)"
        fillRule="evenodd"
        clipRule="evenodd"
        fill="currentColor"
      >
        <path
          d="M12.54 23.577c5.672 0 10.398-4.615 10.398-10.23 0-2.781-1.167-5.339-3.113-7.285a10.137 10.137 0 00-7.229-3.003c-5.672 0-10.287 4.616-10.287 10.232 0 1.946.556 3.836 1.557 5.504l.278.39-1.056 3.78L6.98 21.91l.334.223c1.612.945 3.391 1.445 5.226 1.445zm6.228-7.284c-.056-.167-.278-.222-.556-.389-.334-.167-1.835-.89-2.113-1-.278-.112-.5-.168-.723.166-.167.278-.778 1-.945 1.223-.222.167-.39.223-.667.056-1.835-.89-3.003-1.613-4.226-3.67-.334-.556.333-.5.89-1.668.11-.223.055-.39 0-.556-.056-.167-.723-1.668-.946-2.28-.278-.612-.5-.556-.723-.556h-.556c-.222 0-.556.056-.834.39-.278.333-1.056 1.056-1.056 2.557 0 1.557 1.056 3.003 1.223 ************* 2.169 3.28 5.282 4.615 1.947.89 2.725.945 3.726.779.556-.056 1.78-.723 2.057-1.446.278-.723.278-1.335.167-1.446z"
        />
        <path
          d="M25.051 13.346c0-3.336-1.445-6.394-3.78-8.73a12.146 12.146 0 00-8.73-3.67C5.756.946.195 6.506.195 13.291c0 2.224.612 4.337 1.668 6.172L.141 25.857l6.505-1.668c1.835.945 3.837 1.501 5.894 1.501 6.84 0 12.511-5.56 12.511-12.344zm-2.113 0c0 5.616-4.726 10.231-10.398 10.231-1.835 0-3.614-.5-5.226-1.445l-.334-.223-3.892 1.057 1.056-3.781-.278-.39c-1-1.668-1.557-3.558-1.557-5.504 0-5.616 4.615-10.232 10.287-10.232 2.725 0 5.282 1.057 7.229 3.003 1.946 1.946 3.113 4.504 3.113 7.284z"
        />
      </g>
      <defs>
        <clipPath
          id="prefix__clip0_1375_26275"
        >
          <path
            fill="#fff"
            transform="translate(.14 .143)"
            d="M0 0h25.714v25.714H0z"
          />
        </clipPath>
      </defs>
    </svg>
  );
}

const MemoWhatsAppIcon = React.memo(WhatsAppIcon);
export default MemoWhatsAppIcon;
