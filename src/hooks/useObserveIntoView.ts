import { isChromeBrowser, isMobileOrTablet } from "@/utils/helpers";
import { RefObject, useLayoutEffect, useRef } from "react";

type UseScrollIntoViewProps =
  | {
      onInView?: (target?: HTMLElement) => void;
      onOutOfView?: (target?: HTMLElement) => void;
      threshold?: number;
      isDisabled?: boolean;
    }
  | undefined;

const useObserveIntoView = (
  containerRef: RefObject<HTMLElement>,
  {
    onInView,
    onOutOfView,
    threshold = 0.1,
    isDisabled,
  }: UseScrollIntoViewProps = {}
) => {
  // const containerRef = useRef<HTMLElement | null>(null);
  const observerRef = useRef<IntersectionObserver>();
  // const isInView = useRef<boolean>(false);

  useLayoutEffect(() => {
    if (!containerRef.current || isDisabled) return;
    if (observerRef.current) observerRef.current.disconnect();

    observerRef.current = new IntersectionObserver(
      ([entry]) => {
        // const scrollContainer = document.querySelector("#anim--home_page");

        // const animContainers =
        //   scrollContainer?.querySelectorAll(`#anim--container`) || [];
        // const target = Array.from(animContainers).find((node) =>
        //   entry.target.isSameNode(node)
        // ) as HTMLElement | undefined;

        if (entry.isIntersecting) {
          onInView && onInView(entry.target as HTMLElement);
          // if(isChromeBrowser() && !isMobileOrTablet()) {
          //   const target = entry.target as HTMLElement;
          //   const scrollTop = scrollContainer?.scrollTop || 0;
          //   const targetPosition =
          //   Math.ceil(scrollTop / target.clientHeight) * target.clientHeight;
          //   scrollContainer?.scrollTo({
          //     top: targetPosition,
          //     behavior: "smooth",
          //   });
          // }
        } else {
          onOutOfView && onOutOfView(entry.target as HTMLElement);
        }
      },
      {
        threshold,
      }
    );

    if (containerRef.current && observerRef.current) {
      observerRef.current.observe(containerRef.current);
    }

    return () => {
      containerRef.current &&
        observerRef.current?.unobserve(containerRef.current);
    };
  }, [onOutOfView, onInView, threshold, isDisabled]);
};

export default useObserveIntoView;

// // const [isInView, setIsInView] = useState(false);

// // const wasInView = usePrevious(isInView);

// const checkInView = useCallback(
//   debounce(() => {
//     if (!containerRef.current) return;
//     const rect = containerRef.current.getBoundingClientRect();
//     const isInView = rect.top < window.innerHeight && rect.bottom >= 0;
//     if (isInView) {
//       // console.log(
//       //   "🚀 ~ debounce ~ containerRef.current:",
//       //   containerRef.current
//       // );
//       onIntersection(containerRef.current);
//     }
//     // console.log("🚀 ~ checkInView ~ isInView:", isInView);
//   }, 200),
//   [onIntersection]
// );

// useLayoutEffect(() => {
//   checkInView();
// }, []);

// useLayoutEffect(() => {
//   const animElem = document.querySelector("#anim--home_page");
//   animElem?.addEventListener("scroll", checkInView);
//   return () => {
//     animElem?.removeEventListener("scroll", checkInView);
//   };
// }, [checkInView]);
