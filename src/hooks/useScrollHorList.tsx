import { useCallback, useRef } from "react";

const useScrollHorList = () => {

  const ref = useRef<HTMLElement>();

  const handlePrev = useCallback(
    () => {
      if (!ref.current) return;
  
      ref.current.scrollTo({
        left:
        ref.current.scrollLeft -
        ref.current.clientWidth,
        behavior: "smooth",
      },);
    },
    [ref],
  );

  const handleNext = useCallback( () => {
    if (!ref.current) return;

    ref.current.scrollTo({
      left:
        ref.current.scrollLeft +
        ref.current.clientWidth,
      behavior: "smooth",
    });
  }, [ref]);

  const setRef = useCallback(
    (newRef: HTMLElement | null)  => {
      if(!newRef) return;
      ref.current = newRef;
    },
    [],
  );
  

  return {
    handleNext, handlePrev, setRef
  };
};

export default useScrollHorList;