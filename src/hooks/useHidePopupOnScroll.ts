import { throttle } from "@/utils/helpers";
import { useCallback, useEffect, useRef } from "react";
import { PopupActions } from "reactjs-popup/dist/types";

export const useHidePopupOnScroll = () => {

  const ref = useRef<PopupActions>();

  const setRef = useCallback(
    (newRef: PopupActions | null)  => {
      if(!newRef) return;
      ref.current = newRef;
    },
    [],
  );

  useEffect(() => {

    const hidePopup = () => {
      ref.current?.close();
    };

    window.addEventListener("scroll", throttle(hidePopup, 200));
    
    return () => {
      window.removeEventListener("scroll",hidePopup);
    };
  }, []);
  

  return {
    setRef
  };
};