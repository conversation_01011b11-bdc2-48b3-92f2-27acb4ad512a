import {
  createVanillaExtractPlugin
} from "@vanilla-extract/next-plugin";
const withVanillaExtract = createVanillaExtractPlugin(
);

/** @type {import('next').NextConfig} */
const nextConfig = {
  distDir: "dist",
  images: {
    remotePatterns: [
      {
        protocol: "https",
        hostname: "images.prismic.io",
        port: "",
      },
    ],
  },
};

export default withVanillaExtract(
  nextConfig
);
