FROM node:20
RUN mkdir /usr/src/app
WORKDIR /usr/src/app
ENV PATH /usr/src/app/node_modules/.bin:$PATH

ARG NEXT_PUBLIC_SUPABASE_KEY=${NEXT_PUBLIC_SUPABASE_KEY}
ENV NEXT_PUBLIC_SUPABASE_KEY=${NEXT_PUBLIC_SUPABASE_KEY}

ARG NEXT_PUBLIC_SUPABASE_URL=${NEXT_PUBLIC_SUPABASE_URL}
ENV NEXT_PUBLIC_SUPABASE_URL=${NEXT_PUBLIC_SUPABASE_URL}

ARG SENDGRID_MAILING_ID=${SENDGRID_MAILING_ID}
ENV SENDGRID_MAILING_ID=${SENDGRID_MAILING_ID}

ARG SENDGRID_API_KEY=${SENDGRID_API_KEY}
ENV SENDGRID_API_KEY=${SENDGRID_API_KEY}

ARG PRISMIC_ACCESS_TOKEN=${PRISMIC_ACCESS_TOKEN}
ENV PRISMIC_ACCESS_TOKEN=${PRISMIC_ACCESS_TOKEN}

ARG SUPABASE_SERVICE_ROLE_KEY=${SUPABASE_SERVICE_ROLE_KEY}
ENV SUPABASE_SERVICE_ROLE_KEY=${SUPABASE_SERVICE_ROLE_KEY}

ARG GOCARDLESS_ACCESS_TOKEN=${GOCARDLESS_ACCESS_TOKEN}
ENV GOCARDLESS_ACCESS_TOKEN=${GOCARDLESS_ACCESS_TOKEN}

ARG NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=${NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY}
ENV NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=${NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY}

ARG GOCARDLESS_WEBHOOK_ENDPOINT_SECRET=${GOCARDLESS_WEBHOOK_ENDPOINT_SECRET}
ENV GOCARDLESS_WEBHOOK_ENDPOINT_SECRET=${GOCARDLESS_WEBHOOK_ENDPOINT_SECRET}

ARG SUPABASE_SERVICE_ROLE_KEY=${SUPABASE_SERVICE_ROLE_KEY}
ENV SUPABASE_SERVICE_ROLE_KEY=${SUPABASE_SERVICE_ROLE_KEY}

COPY . /usr/src/app
RUN npm install
RUN npm run build

EXPOSE 80

CMD ["npm", "run", "start"]