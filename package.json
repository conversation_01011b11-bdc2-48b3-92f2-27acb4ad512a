{"name": "pleasant-plumbers-client", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "rm -rf .next/cache/fetch-cache && next build", "start": "next start -p 80", "lint": "next lint", "slicemachine": "start-slicemachine"}, "dependencies": {"@dotlottie/react-player": "^1.6.19", "@next/third-parties": "^15.0.3", "@prismicio/client": "^7.3.1", "@prismicio/next": "^1.5.0", "@prismicio/react": "^2.7.3", "@react-google-maps/api": "^2.19.3", "@sendgrid/mail": "^8.1.5", "@stripe/react-stripe-js": "^2.7.1", "@stripe/stripe-js": "^3.4.0", "@supabase/ssr": "^0.1.0", "@supabase/supabase-js": "^2.39.6", "@vanilla-extract/css": "^1.14.1", "@vanilla-extract/dynamic": "^2.1.0", "@vanilla-extract/next-plugin": "^2.3.6", "@vanilla-extract/sprinkles": "^1.6.1", "axios": "^1.6.7", "classnames": "^2.5.1", "dayjs": "^1.11.10", "luxon": "^3.4.4", "mobx": "^6.12.3", "mobx-react-lite": "^4.0.7", "next": "14.1.0", "nextjs-toploader": "^1.6.12", "react": "^18", "react-datepicker": "^6.1.0", "react-dom": "^18", "react-hook-form": "^7.50.1", "react-number-format": "^5.3.3", "react-select": "^5.8.0", "reactjs-popup": "^2.0.6", "sharp": "^0.33.2", "stripe": "^15.3.0", "zod": "^3.22.4"}, "devDependencies": {"@slicemachine/adapter-next": "^0.3.35", "@stylistic/eslint-plugin": "^1.6.3", "@stylistic/eslint-plugin-js": "^1.6.3", "@types/luxon": "^3.4.2", "@types/node": "^20", "@types/react": "^18", "@types/react-datepicker": "^6.0.1", "@types/react-dom": "^18", "autoprefixer": "^10.0.1", "eslint": "^8", "eslint-config-next": "14.1.0", "postcss": "^8", "slice-machine-ui": "^1.24.0", "typescript": "^5"}}