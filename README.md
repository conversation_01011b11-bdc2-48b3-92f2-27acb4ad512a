# Pleasant Plumbers v0.1.0
 
## Titles
* [Getting Started](#getting-started)
* [Stack of technologies](#stack-of-technologies)
* [Stack of methodologies](#stack-of-methodologies)
* [Project structure map](#project-structure-map)

## Getting Started 

First make sure you are using the correct version of node.js  
To find out which specific version this project is using, go to `.nvmrc` file.  
If you use [`nvm`](https://github.com/nvm-sh/nvm) - you can select the desired version with the following command:  
```bash
# if you don't have correct node version, first use this
nvm install [version]
# next command to use correct node version
nvm use
```


Next you need to fill the environment variables in the file `.env.local`. Use file `.env.example` and support of other developer to fill env correct.

Now you can install all the project dependencies and run it:

```bash
# will install all deps
npm install
# will run project on localhost
npm run dev
```
Open [http://localhost:3000](http://localhost:3000) with your browser to see the result.

You can also run slicemachine to work with prismic:

```bash
npm run slicemachine
```
Open [http://localhost:9999](http://localhost:9999) with your browser to see the result.

## Stack of technologies
* [Next.js](https://nextjs.org) v14
* [Typescript](https://www.typescriptlang.org) v5
* [MobX](https://mobx.js.org) v6 - *using for global storage of application*
* [Vanilla Extract](https://vanilla-extract.style) v1 - *using for styling and write css*
* [Prismic](https://prismic.io) v7 - *using to manage landing content*
* [Supabase](https://www.npmjs.com/package/@supabase/supabase-js) v2 - *using for authentification and server data managing*
* [Day.js](https://day.js.org) v1 - *using to easily work with date's*
* [React Hook Form](https://react-hook-form.com) v7 - *using to control forms*
* [Eslint](https://eslint.style) v8

## Stack of methodologies
* Mobile First - *to easily control styles and optimise perfomence for mobile devices*
* We are using Server Side Rendering for Landing Pages to make the site SEO friendly. But for User Profile pages used Single Page Application
 
## Project structure map
> `/public` - folder with public files and media files \
> `/src` - root folder of app code 
> > `/prismicio.ts` - configuration file of **prismic** client \
> > `/app` - folder of **next.js** pages using App Router 
> > > `/[page-name]` - example of page folder 
> > > > `/page.tsx` - root file which exports page component 
> > > >
> > `/components` - folder of **react** components *(Logic of components can contain some api requests or store state manipulation)* 
> > > `/[ComponentName]` - example of component folder 
> > > > `/index.ts` - element root file which exports component and all public methods of it \
> > > > `/[ComponentName].tsx` - file which includes component code and logic \
> > > > `/[ComponentName].module.scss` - file which includes component styles \
> > > > `/[ComponentName].types.ts` - file which includes component **typescript** types and interfaces used in component file 
> > > >
> `/supabase` - root folder of supabase code \
> `.env.local` - environment file for local application \
> `.env.example` - example of environment file \
> `.env.d.ts` - file with typescript declarations for environment \
> `.nvmrc` - file with node.js version using by nvm \
> `prismicio-types.d.ts` - file with typescript declarations using by prismic \
> `Dockerfile` - file with docker configuration for deployment \
> `next.config.mjs` - file with configuration for next.js \
> `slicemachine.config.json` - file with configuration for prismic slicemachine \
> `tsconfig.json` - file with configuration for typescript \
> `.eslintrc` - file with configuration for eslint \
