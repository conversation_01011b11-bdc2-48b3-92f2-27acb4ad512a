{"extends": "next/core-web-vitals", "plugins": ["@stylistic", "@stylistic/js"], "ignorePatterns": [".history/*"], "rules": {"react/display-name": "off", "react/jsx-max-props-per-line": ["warn", {"maximum": {"single": 1, "multi": 1}}], "react/jsx-first-prop-new-line": ["warn", "always"], "react/jsx-closing-bracket-location": ["warn", "tag-aligned"], "react-hooks/exhaustive-deps": 0, "indent": ["warn", 2], "linebreak-style": ["warn", "unix"], "quotes": ["warn", "double"], "semi": ["warn", "always"]}}