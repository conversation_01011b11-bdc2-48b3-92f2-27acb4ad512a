import { env } from "../_shared/env.ts";
import { HttpMethod } from "./types/common.ts";
import { datetime } from "https://deno.land/x/ptera/mod.ts";
import {
  SupabaseClient,
  createClient,
} from "https://esm.sh/@supabase/supabase-js@2.39.7";

class AccessTokenStore {
  private static instance: AccessTokenStore;
  private supabase: SupabaseClient;
  private ZOHO_ACCESS_TOKENS_TABLE = "zohoAccessTokens";

  private constructor() {
    this.supabase = createClient(
      env.SUPABASE_URL,
      env.SUPABASE_SERVICE_ROLE_KEY
    );
  }

  public static getInstance(): AccessTokenStore {
    if (!AccessTokenStore.instance) {
      AccessTokenStore.instance = new AccessTokenStore();
    }

    return AccessTokenStore.instance;
  }

  private async createInitialAccessTokenRow() {
    const token = "example-token";
    const expiresAt = datetime().subtract({ year: 1 }).toJSDate();

    const { data, error } = await this.supabase
      .from(this.ZOHO_ACCESS_TOKENS_TABLE)
      .insert([
        {
          token,
          expiresAt,
        },
      ])
      .select("*")
      .single();

    if (error) {
      console.log("Failed to create initial access token row");
      console.error(error);
    }

    return data;
  }

  public async setAccessToken(token: string): Promise<string> {
    console.log("setAccessToken: ", token);

    const getAccessTokenResponse = await this.supabase
      .from(this.ZOHO_ACCESS_TOKENS_TABLE)
      .select("*")
      .single();

    let data;

    if (getAccessTokenResponse.error) {
      if (getAccessTokenResponse.error.code !== "PGRST116") {
        throw new Error(getAccessTokenResponse.error.message);
      }
      data = await this.createInitialAccessTokenRow();
    } else {
      data = getAccessTokenResponse.data;
    }

    const expiresAt = datetime().add({ hour: 1 }).toJSDate();

    const updateAccessTokenResponse = await this.supabase
      .from(this.ZOHO_ACCESS_TOKENS_TABLE)
      .update([{ token, expiresAt }])
      .eq("id", data.id)
      .select("*")
      .single();

    if (updateAccessTokenResponse.error) {
      console.error(updateAccessTokenResponse.error);
      throw new Error(updateAccessTokenResponse.error.message);
    }

    return token;
  }

  public async getAccessToken(): Promise<string> {
    console.log("getAccessToken...");

    const getAccessTokenResponse = await this.supabase
      .from(this.ZOHO_ACCESS_TOKENS_TABLE)
      .select("*")
      .single();

    let data;

    if (getAccessTokenResponse.error) {
      if (getAccessTokenResponse.error.code !== "PGRST116") {
        console.error(getAccessTokenResponse.error);
        throw new Error(getAccessTokenResponse.error.message);
      }
      data = await this.createInitialAccessTokenRow();
    } else {
      data = getAccessTokenResponse.data;
    }

    const now = datetime().toJSDate();

    if (now >= new Date(data.expiresAt)) {
      const newAccessToken = await this.updateAccessToken();
      const newExpiresAt = datetime().add({ hour: 1 }).toJSDate();

      const { error } = await this.supabase
        .from(this.ZOHO_ACCESS_TOKENS_TABLE)
        .update([{ token: newAccessToken, expiresAt: newExpiresAt }])
        .eq("id", data.id)
        .select("*")
        .single();

      if (error) {
        console.error(error);
        throw new Error(error.message);
      }

      return newAccessToken;
    }

    return data.token;
  }

  public async updateAccessToken(): Promise<string> {
    console.log("Update access token...");

    const endpoint = `/oauth/v2/token?refresh_token=${env.ZOHO_REFRESH_TOKEN}&grant_type=refresh_token&client_id=${env.ZOHO_CLIENT_ID}&client_secret=${env.ZOHO_CLIENT_SECRET}`;
    const method: HttpMethod = "POST";
    const headers = {
      "Content-Type": "application/x-www-form-urlencoded",
    };

    const url = `${env.ZOHO_ACCOUNTS_URL}${endpoint}`;
    const response = await fetch(url, { method, headers });

    if (!response) {
      console.log(response);
      throw new Error("Failed to refresh access token");
    }
    const data = await response.json();
    const token = data.access_token;

    if (!token) {
      console.log(data);
      throw new Error("New access token is missing from response");
    }

    await this.setAccessToken(token);

    return token;
  }
}

export default AccessTokenStore;
