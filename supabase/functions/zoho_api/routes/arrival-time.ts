import { env } from "../../_shared/env.ts";
import { Router } from "https://deno.land/x/oak@v14.0.0/mod.ts";
import { ZohoApi } from "../api/zoho.ts";
import { ArrivalTimeService } from "../api/modules/arrival-time/arrival-time.ts";

const router = new Router();

router.get("/", async (context) => {
  const zohoApi = new ZohoApi(env.ZOHO_API_URL);
  const arrivalTimeService = new ArrivalTimeService(zohoApi);
  const arrivalTime = await arrivalTimeService.find();

  if (!arrivalTime) {
    context.response.status = 404;
    context.response.type = "json";
    context.response.body = {
      data: null,
      error: "Failed to find arrival times",
    };

    return;
  }

  context.response.status = 200;
  context.response.type = "json";
  context.response.body = { arrivalTime };

  return;
});

export default router;
