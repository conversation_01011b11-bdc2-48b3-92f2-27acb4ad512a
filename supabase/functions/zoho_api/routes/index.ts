import { Router } from "https://deno.land/x/oak@v14.0.0/mod.ts";
import bookingsRouter from "./bookings.ts";
import careersRouter from "./careers.ts";
import arrivalTimeRouter from "./arrival-time.ts";
import commercialRouter from "./commercial.ts";

const router = new Router({
  prefix: "/zoho_api",
});

router.get("/bookings", bookingsRouter.routes());
router.get("/careers", careersRouter.routes());
router.get("/arrival-times", arrivalTimeRouter.routes());
router.get("/commercial", commercialRouter.routes());

export default router;
