import { Context, Router } from "https://deno.land/x/oak@v14.0.0/mod.ts";
import { ZohoApi } from "../api/zoho.ts";
import { CommercialService } from "../api/modules/commercial/commercial.ts";
import { env } from "../../_shared/env.ts";
import { SubmitCommercialContactUsFormDtoSchema } from "../types/commercial.ts";

const router = new Router();

router.post("/contact-us", async (ctx: Context) => {
  const zohoApi = new ZohoApi(env.ZOHO_API_URL);
  const commercialService = new CommercialService(zohoApi);

  if (!ctx.request.hasBody) {
    throw new Error("No body");
  }

  try {
    const body = await ctx.request.body.json();

    const dto = await SubmitCommercialContactUsFormDtoSchema.parseAsync(body);
    const created = await commercialService.submitContactUsForm(dto);

    ctx.response.status = 201;
    ctx.response.type = "json";
    ctx.response.body = created;

    return;
  } catch (error) {
    console.error(error);
    ctx.response.status = 400;
    ctx.response.type = "json";
    ctx.response.body = { message: "Failed to create", error };

    return;
  }
});

export default router;
