import { env } from "../../_shared/env.ts";
import { ZohoApi } from "../api/zoho.ts";
import { Router, Context } from "https://deno.land/x/oak@v14.0.0/mod.ts";
import { CreateBookingDto, CreateBookingDtoSchema } from "../types/booking.ts";
import { BookingsService } from "../api/modules/bookings/bookings.ts";
import { OptionallyAuthenticated } from "../../_shared/utils.ts";
import {
  createClient,
  User,
} from "https://esm.sh/@supabase/supabase-js@2.39.7";

const router = new Router();

const zohoApi = new ZohoApi(env.ZOHO_API_URL);
const bookingsService = new BookingsService(zohoApi);

router.get("/", async (context) => {
  const bookings = await bookingsService.find();

  context.response.status = 200;
  context.response.type = "json";
  context.response.body = { bookings };

  return;
});

router.post("/", OptionallyAuthenticated, async (ctx: Context) => {
  const token: string | null = ctx.state.authorizationToken;
  // let supabase: SupabaseClient | null = null;
  let user: User | null = null;

  if (token) {
    console.log("Token is defined: ", token);

    const SUPABASE_URL = env.SUPABASE_URL;
    const SUPABASE_SERVICE_ROLE_KEY = env.SUPABASE_SERVICE_ROLE_KEY;

    const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY, {
      global: {
        headers: {
          Authorization: token,
        },
      },
    });

    const { data: getUserData, error: getUserError } =
      await supabase.auth.getUser();

    if (getUserError || !getUserData.user) {
      console.log("Session is without user");
    } else {
      user = getUserData.user;
    }
  }

  const body = await ctx.request.body.json();

  const dto: CreateBookingDto = await CreateBookingDtoSchema.parseAsync(body);
  const createBookingResponse = await bookingsService.create(dto, user);

  if (createBookingResponse.error) {
    ctx.response.type = "json";
    ctx.response.body = {
      error: createBookingResponse.error,
    };
    return;
  }

  ctx.response.status = 201;
  ctx.response.type = "json";
  ctx.response.body = {
    data: createBookingResponse.data,
  };
  return;
});

export default router;
