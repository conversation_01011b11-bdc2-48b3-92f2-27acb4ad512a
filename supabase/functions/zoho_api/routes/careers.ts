import { env } from "../../_shared/env.ts";
import { ZohoApi } from "../api/zoho.ts";
import { Context, Router } from "https://deno.land/x/oak@v14.0.0/mod.ts";
import { CareersService } from "../api/modules/careers/careers.ts";
import { CreateCareerDtoSchema } from "../types/career.ts";

const router = new Router();

router.get("/", async (context: Context) => {
  const zohoApi = new ZohoApi(env.ZOHO_API_URL);
  const careersService = new CareersService(zohoApi);

  const careers = await careersService.find();

  context.response.status = 200;
  context.response.type = "json";
  context.response.body = { careers };

  return;
});

router.post("/", async (context: Context) => {
  const zohoApi = new ZohoApi(env.ZOHO_API_URL);
  const careersService = new CareersService(zohoApi);

  if (!context.request.hasBody) {
    throw new Error("No body");
  }

  try {
    const body = await context.request.body.json();

    const dto = await CreateCareerDtoSchema.parseAsync(body);
    const created = await careersService.create(dto);

    context.response.status = 201;
    context.response.type = "json";
    context.response.body = created;

    return;
  } catch (error) {
    console.error(error);
    context.response.status = 400;
    context.response.type = "json";
    context.response.body = { message: "Failed to create", error };

    return;
  }
});

export default router;
