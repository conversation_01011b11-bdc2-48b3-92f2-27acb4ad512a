import { Api } from './api.ts';
import AccessTokenStore from '../store.ts';
import { ZohoAPICallOptions } from '../types/common.ts';

export class ZohoApi extends Api {
  private readonly baseUrl;

  constructor(baseUrl: string) {
    super();
    this.baseUrl = baseUrl;
  }

  // Try to call an endpoint
  // If caught an 1030 error - refresh tokens and try to call API again
  public async call(options: ZohoAPICallOptions) {
    let { endpoint, headers, method } = options;

    const url = options.endpoint.includes('http')
      ? endpoint
      : `${this.baseUrl}${endpoint}`;

    const makeRequest = async (
      url: string,
      method: string,
      headers: Record<string, string>,
      body?: object
    ) => {
      const response = await fetch(url, {
        method,
        headers: headers,
        body: body ? JSON.stringify(body) : undefined,
      });

      return response;
    };

    let response;
    let data;

    response = await makeRequest(url, method, headers, options.body);
    data = await response.json();

    if (response.status === 401) {
      // If response is 401 - possibly access_token had expired
      // We need to validate it via error code "1030"
      const tokenStore = AccessTokenStore.getInstance();

      // Update access token
      const token = await tokenStore.updateAccessToken();

      // Update failed request headers
      headers = {
        Authorization: `Zoho-oauthtoken ${token.trim()}`,
        'Content-Type': 'application/json',
      };

      response = await makeRequest(url, method, headers, options.body);

      if (!response.ok) {
        throw new Error(
          'Something bad happened when tried to update access token'
        );
      }

      data = await response.json();
    }

    return data;
  }
}
