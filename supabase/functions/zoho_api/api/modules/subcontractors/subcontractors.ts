import { env } from "../../../../_shared/env.ts";
import AccessTokenStore from "../../../store.ts";
import { Zoho<PERSON><PERSON> } from "../../zoho.ts";
import { CreateSubcontractorDto, Subcontractor, SubcontractorInput, ZohoSubcontractorResponse } from "../../../types/subcontractor.ts";
import { HttpMethod } from "../../../types/common.ts";
import { SubcontractorsServiceUtils } from "./subcontractors.utils.ts";
import { FilesService } from "../files/files.ts";

export class SubcontractorsService {
  private zohoAPI: ZohoApi;
  private subcontractorsUtils: SubcontractorsServiceUtils;
  private filesService: FilesService;

  constructor(zohoAPI: ZohoApi) {
    this.zohoAPI = zohoAPI;
    this.subcontractorsUtils = new SubcontractorsServiceUtils();
    this.filesService = new FilesService();
  }

  /**
   * Create a new subcontractor in Zoho CRM with file uploads
   */
  public async createWithFiles(
    input: SubcontractorInput,
    files?: { [key: string]: File | File[] }
  ): Promise<ZohoSubcontractorResponse> {
    try {
      // Define all file upload fields
      const fileUploadFields = [
        'idPhoto',
        'vanFrontPhoto',
        'vanBackPhoto',
        'vanLeftPhoto',
        'vanRightPhoto',
        'gasSafeCard',
        'insuranceProof',
        'additionalDocuments'
      ];

      // Transform input to Zoho format first (without files)
      const dto = this.subcontractorsUtils.transformToZohoFormat(input);

      // First, create the record without files
      const createResult = await this.createRecord(dto);

      if (!createResult.success || !createResult.recordId) {
        console.error("❌ Failed to create subcontractor record:", createResult.error);
        return createResult;
      }

      const recordId = createResult.recordId;

      // Handle file uploads as attachments after record creation
      if (files) {
        for (const fieldName of fileUploadFields) {
          if (files[fieldName]) {
            const fieldFiles = files[fieldName];
            const filesToUpload = Array.isArray(fieldFiles) ? fieldFiles : [fieldFiles];

            const uploadResult = await this.filesService.uploadAttachmentsToRecord(
              filesToUpload,
              recordId,
              'Subcontractors'
            );

            if (!uploadResult.success) {
              console.error(`❌ ${fieldName} attachment upload failed:`, uploadResult.error);
              // Continue with other files instead of failing completely
              continue;
            }
          }
        }
      }

      return createResult;

    } catch (error) {
      console.error("Error creating subcontractor with files:", error);

      return {
        success: false,
        error: error instanceof Error ? error.message : "Failed to create subcontractor in Zoho CRM"
      };
    }
  }

  /**
   * Create a new subcontractor in Zoho CRM (without files)
   */
  public async create(input: SubcontractorInput): Promise<ZohoSubcontractorResponse> {
    const dto = this.subcontractorsUtils.transformToZohoFormat(input);
    return await this.createRecord(dto);
  }

  /**
   * Internal method to create the record in Zoho CRM
   */
  private async createRecord(dto: any): Promise<ZohoSubcontractorResponse> {
    try {
      const tokenStore = AccessTokenStore.getInstance();
      const token = await tokenStore.getAccessToken();

      const endpoint = `${env.ZOHO_API_URL}/crm/v2/Subcontractors`;
      const method: HttpMethod = "POST";
      const headers = {
        Authorization: `Zoho-oauthtoken ${token.trim()}`,
        "Content-Type": "application/json",
      };

      const body = { data: [dto] };

      const response = await this.zohoAPI.call({
        endpoint,
        method,
        headers,
        body,
      });

      if (response && response.data && response.data.length > 0) {
        const createdRecord = response.data[0];
        
        // Check if the response contains an error
        if (createdRecord.status === "error") {
          return {
            success: false,
            error: createdRecord.message,
            details: createdRecord.details,
            zohoData: dto
          };
        }
        
        return {
          success: true,
          recordId: createdRecord.details?.id || createdRecord.id,
          data: {
            id: createdRecord.details?.id || createdRecord.id,
            message: "Subcontractor record created successfully in Zoho CRM",
            details: createdRecord.details || createdRecord,
            zohoData: dto
          }
        };
      } else {
        console.log("No data returned from Zoho API");
        throw new Error("No data returned from Zoho API");
      }

    } catch (error) {
      console.error("Error creating subcontractor in Zoho:", error);
      
      return {
        success: false,
        error: error instanceof Error ? error.message : "Failed to create subcontractor in Zoho CRM"
      };
    }
  }
}
