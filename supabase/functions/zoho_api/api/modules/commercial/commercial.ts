import { env } from "../../../../_shared/env.ts";
import AccessTokenStore from "../../../store.ts";
import { SubmitCommercialContactUsFormDto } from "../../../types/commercial.ts";
import { Zoho<PERSON><PERSON> } from "../../zoho.ts";

export class CommercialService {
  private zohoAPI: ZohoA<PERSON>;
  constructor(zohoAPI: ZohoApi) {
    this.zohoAPI = zohoAPI;
  }

  public async submitContactUsForm(dto: SubmitCommercialContactUsFormDto) {
    const tokenStore = AccessTokenStore.getInstance();
    const token = await tokenStore.getAccessToken();

    const endpoint = `${env.ZOHO_API_URL}/crm/v2/commercial_contact_us`;

    const method = "POST";
    const headers = {
      Authorization: `Zoho-oauthtoken ${token.trim()}`,
      "Content-Type": "application/json",
    };

    const body = { data: [dto] };

    const created = await this.zohoAPI.call({
      endpoint,
      method,
      headers,
      body,
    });

    return created;
  }
}
