import {
  createClient,
  SupabaseClient,
  User,
} from "https://esm.sh/v135/@supabase/supabase-js@2.39.7/dist/module/index.js";
import { CreateBookingDto } from "../../../types/booking.ts";
import { RefinedServiceResponse } from "../../../../_shared/common-types.ts";
import { env } from "../../../../_shared/env.ts";

type BookingReferralData = {
  ref_id: string | null;
  zohoRecordId: string | null;
};

export const getBookingReferralData = async (
  // supabase: SupabaseClient | null,
  user: User | null,
  dto: CreateBookingDto
): Promise<RefinedServiceResponse<BookingReferralData>> => {
  // If there are no member_id in either dto or authenticated user with linked referral, ref_id will remain null,
  // and booking will be created without member_id. We prioritize user's ref_by_id over dto.ref_id
  // let user: User | null = null;

  const supabase: SupabaseClient = createClient(
    env.SUPABASE_URL,
    env.SUPABASE_SERVICE_ROLE_KEY
  );

  if (user) {
    // const { data: userData, error: getUserError } =
    //   await supabase.auth.getUser();

    // if (getUserError) {
    //   return {
    //     error: {
    //       message: "Failed to get user data",
    //       details: getUserError,
    //     },
    //   };
    // }

    // if (!userData.user) {
    //   return {
    //     error: {
    //       message: "Failed to fetch user",
    //       details: userData,
    //     },
    //   };
    // }

    // user = userData.user;

    const { data: profile, error: getProfileError } = await supabase
      .from("profiles")
      .select("ref_by_id, zohoRecordId")
      .match({ id: user.id })
      .single();

    if (getProfileError) {
      return {
        error: {
          message: "Failed to fetch current user's profile",
          details: getProfileError,
        },
      };
    }

    // ID of a profile, which is linked to authenticated user
    const { ref_by_id } = profile;

    // If current user has ref_by_id - use it in booking

    // const client = createClient(
    //   env.SUPABASE_URL,
    //   env.SUPABASE_SERVICE_ROLE_KEY
    // );

    if (ref_by_id) {
      const { data: ref_user, error: getProfileError } = await supabase
        .from("profiles")
        .select("ref_id, zohoRecordId")
        .match({ id: ref_by_id })
        .single();

      if (getProfileError) {
        return {
          error: {
            message: "Failed to fetch data about Member",
            details: getProfileError,
          },
        };
      }

      const { ref_id, zohoRecordId } = ref_user;

      return {
        data: {
          ref_id,
          zohoRecordId,
        },
      };
    }
    // If user does not have a refferal linked to his account - proceed to next part of a function
  }

  // If user passed ref_id (member_id) to request:
  // - Validate if it is correct member_id (search for it among other users)
  // - If current user is authenticated, and came to this part of a function - he does not have linked member_id - link it here
  if (dto.ref_id) {
    // const client = createClient(
    //   env.SUPABASE_URL,
    //   env.SUPABASE_SERVICE_ROLE_KEY
    // );

    const { data: profile, error: getProfileError } = await supabase
      .from("profiles")
      .select("id, ref_id, zohoRecordId")
      .match({ ref_id: dto.ref_id })
      .single();

    if (getProfileError) {
      return {
        error: {
          message: "Passed Member ID is invalid!",
          details: getProfileError,
        },
      };
    }

    if (user) {
      if (user.id === profile.id) {
        return {
          error: {
            message: "You can't set yourself as referral",
          },
        };
      }

      const { error: updateProfileError } = await supabase
        .from("profiles")
        .update({ ref_by_id: profile.id })
        .match({ id: user.id })
        .single();

      if (updateProfileError) {
        return {
          error: {
            message: "Failed to update user's profile",
            details: updateProfileError,
          },
        };
      }
    }

    return {
      data: {
        ref_id: profile.ref_id,
        zohoRecordId: profile.zohoRecordId,
      },
    };
  }

  // If user is not authenticated, and did not pass ref_id - return nulls

  return {
    data: {
      ref_id: null,
      zohoRecordId: null,
    },
  };
};
