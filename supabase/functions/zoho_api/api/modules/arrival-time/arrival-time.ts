import { env } from "../../../../_shared/env.ts";
import AccessTokenStore from "../../../store.ts";
import { Zoho<PERSON><PERSON> } from "../../zoho.ts";
import { HttpMethod } from "../../../types/common.ts";
import {
  ArrivalTime,
  ArrivalTimesSchema,
} from "../../../types/arrival-time.ts";

export class ArrivalTimeService {
  private zohoAPI;

  constructor(zohoAPI: ZohoApi) {
    this.zohoAPI = zohoAPI;
  }

  async find() {
    const tokenStore = AccessTokenStore.getInstance();
    const token = await tokenStore.getAccessToken();

    const endpoint = `${env.ZOHO_API_URL}/crm/v2/Arrival_times`;
    const method: HttpMethod = "GET";
    const headers = {
      Authorization: `Zoho-oauthtoken ${token.trim()}`,
      "Content-Type": "application/json",
    };

    const response = await this.zohoAPI.call({ endpoint, method, headers });

    if (!response) {
      throw new Error("Failed to get response");
    }

    const data = response.data;

    if (!data || !Array.isArray(data)) {
      throw new Error("Failed to fetch arrival time");
    }

    try {
      const arrivalTime: ArrivalTime = await ArrivalTimesSchema.parseAsync({
        memberWaitTimeMinutes: data[0].memberWaitTimeMinutes,
        nonMemberWaitTimeMinutes: data[0].nonMemberWaitTimeMinutes,
      });

      return arrivalTime;
    } catch (error) {
      console.error(error);
      return null;
    }
  }
}
