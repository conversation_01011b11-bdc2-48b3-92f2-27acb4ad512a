import { env } from "../../../../_shared/env.ts";
import AccessTokenStore from "../../../store.ts";
import { Zoho<PERSON><PERSON> } from "../../zoho.ts";
import { CreateCareerDto, Career } from "../../../types/career.ts";
import { HttpMethod } from "../../../types/common.ts";
import { CareersServiceUtils } from "./careers.utils.ts";

export class CareersService {
  private zohoAPI: ZohoApi;
  private carrersUtils: CareersServiceUtils;
  constructor(zohoAPI: ZohoApi) {
    this.zohoAPI = zohoAPI;
    this.carrersUtils = new CareersServiceUtils();
  }

  public async create(dto: CreateCareerDto) {
    const tokenStore = AccessTokenStore.getInstance();
    const token = await tokenStore.getAccessToken();

    const endpoint = `${env.ZOHO_API_URL}/crm/v2/Careers`;

    const method = "POST";
    const headers = {
      Authorization: `Zoho-oauthtoken ${token.trim()}`,
      "Content-Type": "application/json",
    };

    dto.startDate = this.carrersUtils.toZohoDate(dto.startDate);
    const body = { data: [dto] };

    const created = await this.zohoAPI.call({
      endpoint,
      method,
      headers,
      body,
    });

    return created;
  }

  public async find(): Promise<Career[]> {
    const tokenStore = AccessTokenStore.getInstance();
    const token = await tokenStore.getAccessToken();

    const endpoint = `${env.ZOHO_API_URL}/crm/v2/Careers`;
    const method: HttpMethod = "GET";
    const headers = {
      Authorization: `Zoho-oauthtoken ${token.trim()}`,
      "Content-Type": "application/json",
    };

    const response = await this.zohoAPI.call({ endpoint, method, headers });

    if (!response) {
      throw new Error("Failed to get response");
    }

    const data = response.data;

    if (!data || !Array.isArray(data)) {
      throw new Error("Failed to fetch careers");
    }

    const careers: Career[] = data.map((item) => {
      return {
        id: item.id ?? null,
        Name: item.Name ?? null,
        areaOfWork: item.areaOfWork ?? null,
        qualifications: item.qualifications ?? null,
        employmentType: item.employmentType ?? null,
        experienceYears: item.experienceYears ?? null,
        isGasSafe: item.isGasSafe ?? null,
        postcode: item.postcode ?? null,
        propertyType: item.propertyType ?? null,
        startDate: item.startDate ?? null,
        travelDistance: item.travelDistance ?? null,
        mobileNumber: item.mobileNumber ?? null,
      };
    });

    return careers;
  }
}
