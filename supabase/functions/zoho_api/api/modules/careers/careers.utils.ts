export class CareersServiceUtils {
  toZohoDate = (utcDateString: string): string => {
    const startDate = new Date(utcDateString);

    const dateNumber = startDate.getDate();
    const monthNumber = startDate.getMonth() + 1;
    const year = startDate.getFullYear();

    const date: string =
      dateNumber > 9 ? dateNumber.toString() : `0${dateNumber}`;

    const month: string =
      monthNumber > 9 ? monthNumber.toString() : `0${monthNumber}`;

    return `${year}-${month}-${date}`;
  };
}
