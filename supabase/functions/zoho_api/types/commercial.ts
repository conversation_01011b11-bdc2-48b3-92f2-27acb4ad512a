import { z } from "https://deno.land/x/zod@v3.22.4/mod.ts";

export const SubmitCommercialContactUsFormDtoSchema = z.object({
  fullName: z.string(),
  // Thx to ZohoCRM :)
  Email: z.string().email(),
  mobileNumber: z.string(),
  address: z.string(),
  businessName: z.string(),
  businessNature: z.string(),
  buildingType: z.string(),
});

export type SubmitCommercialContactUsFormDto = z.infer<
  typeof SubmitCommercialContactUsFormDtoSchema
>;
