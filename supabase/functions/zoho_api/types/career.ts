import { z } from "https://deno.land/x/zod@v3.22.4/mod.ts";

const EMPLOYMENT_TYPES = ["Sub-contractor", "Full-time employment"] as const;
const PROPERTY_TYPES = ["Residential", "Commercial", "Both"] as const;
export const AREAS_OF_WORK = [
  "Boiler installations",
  "Boiler service and repair",
  "Reactive maintenance",
  "General plumbing",
  "Commercial plumbing",
  "Bathroom installations",
  "Commercial heating",
  "Underfloor heating systems",
  "Oil Boiler systems",
  "LPG Systems",
  "Heating system controls and wiring",
] as const;

const QUALIFICATIONS = [
  "Level 1 NVQ Plumbing",
  "Level 2 NVQ Plumbing",
  "Level 3 NVQ Plumbing",
] as const;

export const CreateCareerDtoSchema = z.object({
  Name: z.string(),
  travelDistance: z.number(),
  employmentType: z.enum(EMPLOYMENT_TYPES),
  propertyType: z.enum(PROPERTY_TYPES),
  areaOfWork: z.enum(AREAS_OF_WORK),
  qualifications: z.enum(QUALIFICATIONS),
  isGasSafe: z.boolean(),
  experienceYears: z.number(),
  postcode: z.string(),
  startDate: z.string(),
  mobileNumber: z.string(),
});

export type CreateCareerDto = z.infer<typeof CreateCareerDtoSchema>;

export const CareerSchema = CreateCareerDtoSchema.merge(
  z.object({ id: z.string() })
);

export type Career = z.infer<typeof CareerSchema>;
