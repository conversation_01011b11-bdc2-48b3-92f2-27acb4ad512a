import { z } from "https://deno.land/x/zod@v3.22.4/mod.ts";

const BOOKING_RESPOND_TYPES = [
  "Call me",
  "WhatsApp me",
  "Text me",
  "Email me",
] as const;

const BOOKING_SERVICES = [
  "New boiler installation",
  "Central heating powerflush",
  "Boiler service",
  "Central heating control upgrade",
] as const;

export const CreateBookingDtoSchema = z.object({
  Name: z.string(),
  Email: z.string().email(),
  phone: z.string(),
  post_code: z.string(),
  urgent: z.boolean(),
  respond_types: z.enum(BOOKING_RESPOND_TYPES).array().optional(),
  ref_id: z.string().optional().nullable(),
  message: z.string().optional(),
  service: z.enum(BOOKING_SERVICES).optional(),
});

export type CreateBookingDto = z.infer<typeof CreateBookingDtoSchema>;

export const BookingSchema = CreateBookingDtoSchema.merge(
  z.object({ id: z.string() })
);

export type Booking = z.infer<typeof BookingSchema>;

export const CreateZohoBookingDtoSchema = CreateBookingDtoSchema.merge(
  z.object({
    ref_member_id: z.string().optional(),
  })
);

export type CreateZohoBookingDto = z.infer<typeof CreateZohoBookingDtoSchema>;
