import { Router } from "https://deno.land/x/oak@v14.0.0/mod.ts";
import { handleCreateSubcontractor } from "./subcontractor.ts";
import { handleSendReferralInfoPack } from "./referral.ts";

const router = new Router();

// CORS middleware for all routes
router.use(async (context, next) => {
  // Handle CORS preflight requests
  if (context.request.method === "OPTIONS") {
    context.response.status = 200;
    context.response.headers.set("Access-Control-Allow-Origin", "*");
    context.response.headers.set("Access-Control-Allow-Methods", "POST, GET, OPTIONS");
    context.response.headers.set("Access-Control-Allow-Headers", "Content-Type, Authorization, x-client-info, apikey");
    return;
  }

  // Add CORS headers to all responses
  context.response.headers.set("Access-Control-Allow-Origin", "*");
  context.response.headers.set("Access-Control-Allow-Methods", "POST, GET, OPTIONS");
  context.response.headers.set("Access-Control-Allow-Headers", "Content-Type, Authorization, x-client-info, apikey");

  await next();
});

// Routes
router.post("/", handleCreateSubcontractor);
router.post("/referral-info-pack", handleSendReferralInfoPack);

// Health check route
router.get("/health", (context) => {
  context.response.body = { 
    status: "healthy", 
    timestamp: new Date().toISOString(),
    service: "create-subcontractor"
  };
});

export default router;
