// supabase/functions/create-subcontractor/index.ts

import { handleCreateSubcontractor } from "./routes/subcontractor.ts";
import { handleSendReferralInfoPack } from "./routes/referral.ts";

// @ts-ignore: Deno is available in Supabase Edge Functions
Deno.serve(async (request: Request) => {
  // Handle CORS preflight requests
  if (request.method === "OPTIONS") {
    return new Response(null, {
      status: 200,
      headers: {
        "Access-Control-Allow-Origin": "*",
        "Access-Control-Allow-Methods": "POST, GET, OPTIONS",
        "Access-Control-Allow-Headers": "Content-Type, Authorization, x-client-info, apikey",
      },
    });
  }

  try {
    const url = new URL(request.url);
    const pathname = url.pathname;

    // Route handling
    if (pathname.endsWith("/referral-info-pack")) {
      if (request.method !== "POST") {
        return new Response(
          JSON.stringify({
            error: {
              message: "Method not allowed. Only POST requests are supported for this endpoint.",
            },
          }),
          {
            status: 405,
            headers: {
              "Content-Type": "application/json",
              "Access-Control-Allow-Origin": "*",
              "Access-Control-Allow-Methods": "POST, GET, OPTIONS",
              "Access-Control-Allow-Headers": "Content-Type, Authorization, x-client-info, apikey",
            },
          }
        );
      }

      // Handle referral info pack request
      const mockContext = {
        request: {
          body: () => ({
            value: request.json(),
          }),
        },
        response: {
          status: 200,
          body: {},
        },
      };

      await handleSendReferralInfoPack(mockContext as any);

      return new Response(JSON.stringify(mockContext.response.body), {
        status: mockContext.response.status,
        headers: {
          "Content-Type": "application/json",
          "Access-Control-Allow-Origin": "*",
          "Access-Control-Allow-Methods": "POST, GET, OPTIONS",
          "Access-Control-Allow-Headers": "Content-Type, Authorization, x-client-info, apikey",
        },
      });

    } else if (pathname.endsWith("/health")) {
      return new Response(
        JSON.stringify({
          status: "healthy",
          timestamp: new Date().toISOString(),
          service: "create-subcontractor"
        }),
        {
          status: 200,
          headers: {
            "Content-Type": "application/json",
            "Access-Control-Allow-Origin": "*",
            "Access-Control-Allow-Methods": "POST, GET, OPTIONS",
            "Access-Control-Allow-Headers": "Content-Type, Authorization, x-client-info, apikey",
          },
        }
      );

    } else {
      // Default route - handle subcontractor creation
      if (request.method !== "POST") {
        return new Response(
          JSON.stringify({
            error: {
              message: "Method not allowed. Only POST requests are supported.",
            },
          }),
          {
            status: 405,
            headers: {
              "Content-Type": "application/json",
              "Access-Control-Allow-Origin": "*",
              "Access-Control-Allow-Methods": "POST, GET, OPTIONS",
              "Access-Control-Allow-Headers": "Content-Type, Authorization, x-client-info, apikey",
            },
          }
        );
      }

      // Handle subcontractor creation request
      const mockContext = {
        request: {
          headers: request.headers,
          body: (options: any) => {
            if (options.type === "form-data") {
              return { value: request.formData() };
            } else {
              return { value: request.json() };
            }
          },
        },
        response: {
          status: 200,
          body: {},
        },
      };

      await handleCreateSubcontractor(mockContext as any);

      return new Response(JSON.stringify(mockContext.response.body), {
        status: mockContext.response.status,
        headers: {
          "Content-Type": "application/json",
          "Access-Control-Allow-Origin": "*",
          "Access-Control-Allow-Methods": "POST, GET, OPTIONS",
          "Access-Control-Allow-Headers": "Content-Type, Authorization, x-client-info, apikey",
        },
      });
    }

  } catch (error) {
    console.error("Error in create-subcontractor function:", error);

    return new Response(
      JSON.stringify({
        error: {
          message: "Internal server error",
          details: error instanceof Error ? error.message : "Unknown error",
        },
      }),
      {
        status: 500,
        headers: {
          "Content-Type": "application/json",
          "Access-Control-Allow-Origin": "*",
          "Access-Control-Allow-Methods": "POST, GET, OPTIONS",
          "Access-Control-Allow-Headers": "Content-Type, Authorization, x-client-info, apikey",
        },
      }
    );
  }
});

