import { Router } from 'https://deno.land/x/oak@v14.0.0/mod.ts';
import checkoutRouter from './checkout.ts';
import plansRouter from './plans.ts';
import customersRouter from './customers.ts';

const router = new Router({
  prefix: '/stripe',
});

router.get('/customers', customersRouter.routes());
router.get('/checkout', checkoutRouter.routes());
router.get('/plans', plansRouter.routes());

export default router;
