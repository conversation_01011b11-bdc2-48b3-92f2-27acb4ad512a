import { env } from "../../_shared/env.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2.39.7";
import { Context, Router } from "https://deno.land/x/oak@v14.0.0/mod.ts";
import { EnsureAuthenticated } from "../../_shared/utils.ts";
import { stripeService } from "../../_shared/stripe/stripe.service.ts";
import {
  CreateCheckoutSessionDto,
  CreateCheckoutSessionDtoSchema,
} from "../../_shared/stripe/types.ts";

const router = new Router();

router.post("/", EnsureAuthenticated, async (ctx: Context) => {
  try {
    const token: string = ctx.state.authorizationToken;

    const supabase = createClient(
      env.SUPABASE_URL,
      env.SUPABASE_SERVICE_ROLE_KEY,
      {
        global: {
          headers: {
            Authorization: token,
          },
        },
      }
    );

    const body = await ctx.request.body.json();

    const parseResult =
      await CreateCheckoutSessionDtoSchema.safeParseAsync(body);

    if (!parseResult.success) {
      ctx.response.status = 401;
      ctx.response.body = {
        error: {
          type: "Validation error",
          message: "Invalid body",
          details: parseResult.error,
        },
      };

      return;
    }

    const dto: CreateCheckoutSessionDto = parseResult.data;

    const { data, error: createCheckoutSessionError } =
      await stripeService.createCheckoutSession(dto, supabase);

    if (createCheckoutSessionError) {
      const { message, details } = createCheckoutSessionError;

      ctx.response.status = 400;
      ctx.response.body = {
        error: {
          type: "Bad request",
          message,
          details,
        },
      };

      return;
    }

    ctx.response.status = 200;
    ctx.response.body = {
      message: "Created checkout session",
      data: {
        clientSecret: data.clientSecret,
        url: data.url,
      },
    };
  } catch (error) {
    ctx.response.status = 500;
    ctx.response.body = {
      error: {
        message: "Something bad happened",
        details: error,
      },
    };
  }
});

export default router;
