import { Context, Router } from "https://deno.land/x/oak@v14.0.0/mod.ts";
import { stripeService } from "../../_shared/stripe/stripe.service.ts";
const router = new Router();

// Get all possible plans to purchase from Stripe
router.get("/", async (ctx: Context) => {
  const { data: prices, error: getPricesError } =
    await stripeService.getSubscriptionPlans();

  if (getPricesError) {
    ctx.response.status = 403;
    ctx.response.body = getPricesError;
    return;
  }

  ctx.response.status = 200;
  ctx.response.body = prices;
  return;
});

export default router;
