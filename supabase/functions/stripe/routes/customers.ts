import { createClient } from "https://esm.sh/@supabase/supabase-js@2.39.7";
import { Context, Router } from "https://deno.land/x/oak@v14.0.0/mod.ts";
import { stripeService } from "../../_shared/stripe/stripe.service.ts";
import { env } from "../../_shared/env.ts";
import { EnsureAuthenticated } from "../../_shared/utils.ts";
import {
  CancelSubscriptionDtoSchema,
  RestoreSubscriptionDtoSchema,
  UpdateSubscriptionDtoSchema,
} from "../../_shared/stripe/types.ts";
import { Status } from "https://deno.land/x/oak_commons@0.6.1/status.ts";

const router = new Router();

// Get current user's plans/subscriptions
router.get("/plans", EnsureAuthenticated, async (ctx: Context) => {
  console.log("/stripe/customers/plans");
  const token: string = ctx.state.authorizationToken;

  // Authenticated user that called a function
  const supabase = createClient(
    env.SUPABASE_URL,
    env.SUPABASE_SERVICE_ROLE_KEY,
    {
      global: {
        headers: {
          Authorization: token,
        },
      },
    }
  );

  const { data: subscriptions, error: getSubscriptionsError } =
    await stripeService.getCustomerActiveSubscriptions(supabase);

  console.log("subscriptions: ", subscriptions);

  if (getSubscriptionsError) {
    ctx.response.status = 403;
    ctx.response.body = getSubscriptionsError;
    return;
  }

  ctx.response.status = 200;
  ctx.response.body = subscriptions;
  return;
});

router.get("/portal", EnsureAuthenticated, async (ctx: Context) => {
  const token: string = ctx.state.authorizationToken;

  const supabase = createClient(
    env.SUPABASE_URL,
    env.SUPABASE_SERVICE_ROLE_KEY,
    {
      global: {
        headers: {
          Authorization: token,
        },
      },
    }
  );

  const { data: subscriptions, error: getSubscriptionsError } =
    await stripeService.getCustomerBillingPortal(supabase);

  if (getSubscriptionsError) {
    ctx.response.status = 403;
    ctx.response.body = getSubscriptionsError;
    return;
  }

  ctx.response.status = 200;
  ctx.response.body = subscriptions;
  return;
});

router.patch("/plans", EnsureAuthenticated, async (ctx: Context) => {
  const token: string = ctx.state.authorizationToken;

  const supabase = createClient(
    env.SUPABASE_URL,
    env.SUPABASE_SERVICE_ROLE_KEY,
    {
      global: {
        headers: {
          Authorization: token,
        },
      },
    }
  );

  let body;

  try {
    body = await ctx.request.body.json();
  } catch (error) {
    console.error(error);

    ctx.response.status = 403;
    ctx.response.body = {
      error: {
        message: "Failed to parse request body",
      },
    };

    return;
  }

  const parseResult = await UpdateSubscriptionDtoSchema.safeParseAsync(body);

  if (!parseResult.success) {
    ctx.response.status = 403;
    ctx.response.body = {
      error: {
        message: "Validation error",
        details: parseResult.error,
      },
    };

    return;
  }

  const dto = parseResult.data;

  const { data: updatedSubscription, error: updateSubscriptionError } =
    await stripeService.updateCurrentSubscription(dto, supabase);

  if (updateSubscriptionError) {
    (ctx.response.status = Status.BadRequest),
      (ctx.response.body = {
        error: updateSubscriptionError,
      });

    return;
  }

  ctx.response.status = 200;
  ctx.response.body = {
    data: updatedSubscription,
  };

  return;
});

router.patch("/plans/restore", EnsureAuthenticated, async (ctx: Context) => {
  const token: string = ctx.state.authorizationToken;

  // Authenticated user that called a function
  const supabase = createClient(
    env.SUPABASE_URL,
    env.SUPABASE_SERVICE_ROLE_KEY,
    {
      global: {
        headers: {
          Authorization: token,
        },
      },
    }
  );

  let body;

  try {
    body = await ctx.request.body.json();
  } catch (error) {
    console.error(error);

    ctx.response.status = 403;
    ctx.response.body = {
      error: {
        message: "Failed to parse request body",
      },
    };

    return;
  }

  const parseResult = await RestoreSubscriptionDtoSchema.safeParseAsync(body);

  if (!parseResult.success) {
    ctx.response.status = 403;
    ctx.response.body = {
      error: {
        message: "Validation error",
        details: parseResult.error,
      },
    };

    return;
  }

  const dto = parseResult.data;

  const { data: subscriptions, error: restoreSubscriptionsError } =
    await stripeService.restoreSubscription(dto, supabase);

  if (restoreSubscriptionsError) {
    ctx.response.status = 403;
    ctx.response.body = restoreSubscriptionsError;
    return;
  }

  ctx.response.status = 200;
  ctx.response.body = subscriptions;
  return;
});

router.delete("/plans", EnsureAuthenticated, async (ctx: Context) => {
  const token: string = ctx.state.authorizationToken;

  // Authenticated user that called a function
  const supabase = createClient(
    env.SUPABASE_URL,
    env.SUPABASE_SERVICE_ROLE_KEY,
    {
      global: {
        headers: {
          Authorization: token,
        },
      },
    }
  );

  let body;

  try {
    body = await ctx.request.body.json();
  } catch (error) {
    console.error(error);

    ctx.response.status = 403;
    ctx.response.body = {
      error: {
        message: "Failed to parse request body",
      },
    };

    return;
  }

  const parseResult = await CancelSubscriptionDtoSchema.safeParseAsync(body);

  if (!parseResult.success) {
    ctx.response.status = 403;
    ctx.response.body = {
      error: {
        message: "Validation error",
        details: parseResult.error,
      },
    };

    return;
  }

  const dto = parseResult.data;

  const { data: subscriptions, error: getSubscriptionsError } =
    await stripeService.cancelSubscription(dto, supabase);

  if (getSubscriptionsError) {
    ctx.response.status = 403;
    ctx.response.body = getSubscriptionsError;
    return;
  }

  ctx.response.status = 200;
  ctx.response.body = subscriptions;
  return;
});

export default router;
