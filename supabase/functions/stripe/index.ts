import {
  Application,
  Context,
  Next,
} from "https://deno.land/x/oak@v14.0.0/mod.ts";
import router from "./routes/index.ts";
import { RESPONSE_INIT } from "../_shared/common-types.ts";

const app = new Application();

app.use(router.routes());
app.use(router.allowedMethods());

app.use(async (context: Context, next: Next) => {
  try {
    await next();
  } catch (err) {
    context.response.status = 500;
    context.response.body = {
      error: "Internal Server Error",
      message: err.message,
    };
    console.log("<!> Error: ");
    console.error(err);
  }
});

Deno.serve(async (req) => {
  console.log("Serve Stripe route");

  const response = await app.handle(req);

  if (!response) {
    return new Response(
      JSON.stringify({ error: { message: "Failed to get response" } }),
      {
        headers: RESPONSE_INIT.headers,
        status: 500,
      }
    );
  }

  return new Response(response.body, {
    headers: RESPONSE_INIT.headers,
    status: response.status,
  });
});
