import {serve} from "https://deno.land/std@0.168.0/http/server.ts";

// CORS headers
const CORS_HEADERS = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Methods": "POST, OPTIONS",
  "Access-Control-Allow-Headers": "Content-Type, Authorization",
  "Content-Type": "application/json"
};

// Environment variables
const ZOHO_API_URL = Deno.env.get("ZOHO_API_URL");
const ZOHO_ACCOUNTS_URL = Deno.env.get("ZOHO_ACCOUNTS_URL");
const ZOHO_CLIENT_ID = Deno.env.get("ZOHO_CLIENT_ID");
const ZOHO_CLIENT_SECRET = Deno.env.get("ZOHO_CLIENT_SECRET");
const ZOHO_REFRESH_TOKEN = Deno.env.get("ZOHO_REFRESH_TOKEN");

// Get Zoho access token
async function getZohoAccessToken() {
  const tokenUrl = `${ZOHO_ACCOUNTS_URL}/oauth/v2/token?refresh_token=${ZOHO_REFRESH_TOKEN}&client_id=${ZOHO_CLIENT_ID}&client_secret=${ZOHO_CLIENT_SECRET}&grant_type=refresh_token`;
  
  const response = await fetch(tokenUrl, { method: "POST" });
  
  if (!response.ok) {
    throw new Error(`Failed to get access token: ${await response.text()}`);
  }
  
  const data = await response.json();
  return data.access_token;
}

// Format subcontractor data for Zoho
function formatSubcontractorData(formData) {
  return {
    data: [{
      First_Name: formData.firstName,
      Last_Name: formData.lastName,
      Email: formData.email,
      Mobile: formData.mobile,
      Street: formData.addressLine1,
      Street_2: formData.addressLine2 || "",
      Zip_Code: formData.postCode,
      Date_of_Birth: formData.dateOfBirth,
      Lead_Source: "Subcontractor Application",
      Description: `
        Gas Registered: ${formData.gasRegistered ? 'Yes' : 'No'}
        Years Experience: ${formData.yearsExperience}
        Travel Distance: ${formData.travelDistance}
        Has Own Van: ${formData.hasOwnVan ? 'Yes' : 'No'}
        Has Own Tools: ${formData.hasOwnTools ? 'Yes' : 'No'}
        Work Type: ${formData.workType}
        Central London: ${formData.centralLondon ? 'Yes' : 'No'}
        Driving License: ${formData.drivingLicense ? 'Yes' : 'No'}
        Public Liability Insurance: ${formData.publicLiabilityInsurance ? 'Yes' : 'No'}
        Available Days: ${formData.availableDays?.join(", ") || ""}
        Accepted Rates: ${formData.acceptedRates ? 'Yes' : 'No'}
        Out Of Hours Work: ${formData.outOfHoursWork ? 'Yes' : 'No'}
        Emergency Callouts: ${formData.emergencyCallouts ? 'Yes' : 'No'}
        Preferred Work Type: ${formData.preferredWorkType}
        Additional Qualifications: ${formData.additionalQualifications || ""}
      `
    }]
  };
}

// Send data to Zoho CRM
async function sendToZoho(formData) {
  const accessToken = await getZohoAccessToken();
  
  const zohoData = formatSubcontractorData(formData);
  
  const response = await fetch(`${ZOHO_API_URL}/crm/v2/Leads`, {
    method: "POST",
    headers: {
      "Authorization": `Zoho-oauthtoken ${accessToken}`,
      "Content-Type": "application/json"
    },
    body: JSON.stringify(zohoData)
  });
  
  if (!response.ok) {
    const errorText = await response.text();
    console.error("Zoho API error:", errorText);
    throw new Error(`Zoho API error: ${errorText}`);
  }
  
  return response.json();
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === "OPTIONS") {
    return new Response(null, {
      status: 204,
      headers: CORS_HEADERS
    });
  }
  
  // Only allow POST requests
  if (req.method !== "POST") {
    return new Response(
      JSON.stringify({ error: "Method not allowed" }),
      { status: 405, headers: CORS_HEADERS }
    );
  }
  
  try {
    // Parse request body
    const formData = await req.json();
    console.log("Received subcontractor application:", formData);
    
    // Validate required fields
    const requiredFields = ["firstName", "lastName", "email", "mobile"];
    for (const field of requiredFields) {
      if (!formData[field]) {
        return new Response(
          JSON.stringify({ error: `Missing required field: ${field}` }),
          { status: 400, headers: CORS_HEADERS }
        );
      }
    }
    
    // Send to Zoho
    const zohoResponse = await sendToZoho(formData);
    console.log("Zoho response:", zohoResponse);
    
    // Return success response
    return new Response(
      JSON.stringify({
        success: true,
        message: "Subcontractor application submitted successfully",
        data: zohoResponse
      }),
      { status: 201, headers: CORS_HEADERS }
    );
  } catch (error) {
    console.error("Error processing subcontractor application:", error);
    
    return new Response(
      JSON.stringify({
        success: false,
        error: error.message || "Failed to process subcontractor application"
      }),
      { status: 500, headers: CORS_HEADERS }
    );
  }
});