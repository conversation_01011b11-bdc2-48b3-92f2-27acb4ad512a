import { Context, Router } from "https://deno.land/x/oak@v14.0.0/mod.ts";
import {
  signIn,
  signUp,
  update,
  signInAndUpdateEmail,
  auth,
} from "../services/index.ts";
import { ServiceResponse } from "../../_shared/common-types.ts";

const router = new Router({
  prefix: "/example",
});

router.get("/auth", async (context: Context) => {
  const response = await auth(context);
  return response;
});

router.post("/sign-in", async (context: Context) => {
  console.log("Find sign-in route");

  return await signIn(context);
});

router.patch("/update-email", async (context: Context) => {
  console.log("Found Update Email route");
  const response: ServiceResponse = await signInAndUpdateEmail(context);
  return response;
});

router.patch("/update", async (context: Context) => {
  console.log("Found Update route");
  const response: Response = await update(context);
  return response;
});

router.post("/sign-up", async (context: Context) => {
  console.log("Found Sign-Up route");
  const response: Response = await signUp(context);
  return response;
});

export default router;
