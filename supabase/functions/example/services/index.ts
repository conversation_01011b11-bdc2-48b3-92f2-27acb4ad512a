import { env } from "../../_shared/env.ts";
import { Context } from "https://deno.land/x/oak@v14.0.0/mod.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2.39.7";
import { ServiceResponse } from "../../_shared/common-types.ts";

const supabase = createClient(env.SUPABASE_URL, env.SUPABASE_SERVICE_ROLE_KEY, {
  auth: { persistSession: false },
});

export const signIn = async (context: Context): Promise<any> => {
  const body = context.request.body;

  const { email, password } = await body.json();

  if (!email || !password) {
    context.response.status = 403;
    context.response.body = {
      error: {
        message: "Validation error. No email or password provided",
        details: {
          email,
          password,
        },
      },
    };

    return;
  }

  const { data, error } = await supabase.auth.signInWithPassword({
    email,
    password,
  });

  if (error) {
    context.response.status = 403;
    context.response.body = {
      error: {
        message: "Failed to sign in. Check details",
        details: error,
      },
    };
  }

  const { user, session } = data;

  console.log({
    user,
    session,
  });

  context.response.status = 200;
  context.response.body = {
    data: {
      user,
      session,
    },
  };

  return;
};

export const signUp = async (context: Context): Promise<any> => {
  if (!context.request.hasBody) {
    return new Response(
      JSON.stringify({
        message: "Body is missing",
      })
    );
  }

  const body = context.request.body;
  const payload = await body.json();

  const data = payload;

  const user = await supabase.auth.signUp({
    email: data.email,
    password: data.password,
    options: {
      data: {
        full_name: data.full_name,
        date_of_birth: data.date_of_birth,
        mobile_number: data.mobile_number,
        post_code: data.post_code,
        address_details: data.address_details,
        residential_status: data.residential_status,
      },
    },
  });

  console.log("Created user", user);

  return user;
};

export const update = async (context: Context): Promise<any> => {
  console.log("Update started...");

  const authHeader = context.request.headers.get("Authorization");
  if (!authHeader) {
    console.log("No header");
    return;
  }

  const jwt = authHeader.split("Bearer ")[1];

  if (!jwt) {
    console.log("No jwt");
    return;
  }

  const getUserResponse = await supabase.auth.getUser(jwt);

  if (getUserResponse.error) {
    console.log("Failed to get user by JWT");
    return;
  }

  console.log(getUserResponse);

  const id = getUserResponse.data.user.id;

  const updateProfileResponse = await supabase
    .from("profiles")
    .update({
      full_name: "Hello world",
    })
    .eq("id", id)
    .select("*")
    .single();

  if (updateProfileResponse.error) {
    console.log("Failed to update user");
    return;
  }

  console.log(updateProfileResponse.data);

  const updateUserEmailResponse = await supabase.auth.updateUser({
    email: "<EMAIL>",
  });

  console.log(updateUserEmailResponse);

  return updateProfileResponse.data;
};

export const signInAndUpdateEmail = async (
  context: Context
): Promise<ServiceResponse> => {
  const body = await context.request.body.json();

  const client = createClient(env.SUPABASE_URL, env.SUPABASE_SERVICE_ROLE_KEY);

  const signIn = await client.auth.signInWithPassword({
    email: body.email,
    password: body.password,
  });

  console.log(signIn);

  if (signIn.error) {
    return {
      status: signIn.error.status ?? 400,
      error: {
        message: "Failed to sign in",
        details: signIn.error,
      },
    };
  }

  const updateEmail = await client.auth.updateUser({ email: body.newEmail });

  console.log(updateEmail);

  if (updateEmail.error) {
    return {
      status: updateEmail.error.status ?? 400,
      error: {
        message: "Failed to update email",
        details: updateEmail.error,
      },
    };
  }

  return {
    status: 200,
    data: updateEmail.data,
  };
};

export const auth = async (context: Context): Promise<any> => {
  const selectResponse = await supabase.from("users").select("*");
  if (selectResponse.error) {
    console.log(selectResponse.error);
    return null;
  }

  console.log(selectResponse.data);
  return selectResponse.data;
};
