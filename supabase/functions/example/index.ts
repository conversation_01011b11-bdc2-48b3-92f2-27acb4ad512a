import { RESPONSE_INIT } from "../_shared/common-types.ts";
import router from "./routes/index.ts";
import { Application } from "https://deno.land/x/oak@v14.0.0/mod.ts";

const app = new Application();

app.use(router.routes());
app.use(router.allowedMethods());

Deno.serve(async (request: Request) => {
  console.log("Example index.ts");

  const response = await app.handle(request);

  if (!response) {
    const data: object = { message: "Failed to handle request" };
    return new Response(JSON.stringify(data), RESPONSE_INIT);
  }

  return response;
  // return new Response(
  //   response,
  //   RESPONSE_INIT
  // );
});
