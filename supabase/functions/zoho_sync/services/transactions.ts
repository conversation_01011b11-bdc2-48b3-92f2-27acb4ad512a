import { Context } from "https://deno.land/x/oak@v14.0.0/mod.ts";
import { ServiceResponse } from "../../_shared/common-types.ts";
import { env } from "../../_shared/env.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2.39.7";
import {
  CreateOrUpdateTransactionFromZohoDtoSchema,
  CreateOrUpdateTransactionFromZohoDto,
} from "../types.ts";

export class TransactionsService {
  constructor() {}

  async handleUpdateTransaction(context: Context): Promise<ServiceResponse> {
    const body = await context.request.body.json();

    const parseResult =
      await CreateOrUpdateTransactionFromZohoDtoSchema.safeParseAsync(body);

    if (!parseResult.success) {
      return {
        status: 403,
        error: {
          message: "Failed to create new transaction",
          details: parseResult.error,
        },
      };
    }

    const dto: CreateOrUpdateTransactionFromZohoDto = parseResult.data;

    const supabase = createClient(
      env.SUPABASE_URL,
      env.SUPABASE_SERVICE_ROLE_KEY
    );

    const updateTransaction = await supabase
      .from("transactions")
      .update({ status: dto.status })
      .eq("zoho_record_id", dto.zoho_record_id)
      .select("id, user_id, amount, status")
      .single();

    if (updateTransaction.error) {
      return {
        status: 400,
        error: {
          message: "Failed to update transaction",
          details: updateTransaction.error,
        },
      };
    }

    const transaction = updateTransaction.data;

    if (transaction.status === "Completed") {
      const increment = await supabase.rpc("increment_available_balance", {
        transaction_id: transaction.id,
        value: transaction.amount,
        user_id: transaction.user_id,
      });
      if (increment.error) {
        return {
          status: 500,
          error: {
            message: "Failed to increment available balance",
            details: increment.error,
          },
        };
      }
    }

    return {
      status: 200,
      data: {
        transaction,
      },
    };
  }

  async handleNewTransaction(context: Context): Promise<ServiceResponse> {
    const body = await context.request.body.json();

    const parseResult =
      await CreateOrUpdateTransactionFromZohoDtoSchema.safeParseAsync(body);

    if (!parseResult.success) {
      return {
        status: 403,
        error: {
          message: "Failed to create new transaction",
          details: parseResult.error,
        },
      };
    }

    const dto: CreateOrUpdateTransactionFromZohoDto = parseResult.data;

    const supabase = createClient(
      env.SUPABASE_URL,
      env.SUPABASE_SERVICE_ROLE_KEY
    );

    // Create new transaction

    const insertTransaction = await supabase
      .from("transactions")
      .insert(dto)
      .select("id, status, user_id, amount")
      .single();

    // Update user available or pending balance

    if (insertTransaction.error) {
      return {
        status: 400,
        error: {
          message: "Failed to insert new transaction",
          details: insertTransaction.error,
        },
      };
    }

    const { id, status, user_id, amount } = insertTransaction.data;

    if (status && user_id && status === "Completed") {
      const increment = await supabase.rpc("increment_available_balance", {
        transaction_id: id,
        value: amount,
        user_id,
      });

      if (increment.error) {
        return {
          status: 500,
          error: {
            message: "Failed to increment available balance",
            details: increment.error,
          },
        };
      }
    }

    return {
      status: 201,
      data: {
        message: "Created new transaction",
        transaction: {
          id,
          user_id,
          amount,
          status,
        },
      },
    };
  }
}
