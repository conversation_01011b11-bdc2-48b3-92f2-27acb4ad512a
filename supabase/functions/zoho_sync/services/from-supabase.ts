import { Context } from "https://deno.land/x/oak@v14.0.0/mod.ts";
import { env } from "../../_shared/env.ts";
import { DBOperation, TableName } from "../../_shared/types.ts";
import AccessTokenStore from "../../zoho_api/store.ts";
import { HttpMethod } from "../../zoho_api/types/common.ts";
import { ZohoApi } from "../../zoho_api/api/zoho.ts";
import { DataSynchronizer } from "../data-synchronizer.ts";
import { areObjectsEqual } from "../../_shared/common-types.ts";
import {
  appendZohoRecordId,
  getInsertDataByTableName,
  getUpdateDataByTableName,
  supabaseTableToZohoModuleName,
  getDataSynchronizer,
} from "./../utils.ts";
import { ServiceResponse } from "../../_shared/common-types.ts";

export async function syncFromSupabase(
  context: Context
): Promise<ServiceResponse> {
  console.log("POST > /zoho-sync/from-supabase");

  if (!context.request.hasBody) {
    return {
      status: 400,
      error: {
        message: "Request do not have body",
      },
    };
  }

  const body = await context.request.body.json();

  console.log("Webhook Body: ");
  console.log(body);

  const tableName: TableName = body.table;
  const operation: DBOperation = body.type;
  const new_record = body.record;
  const old_record = body.old_record;
  const rowId = new_record.id;
  const zohoRecordId = new_record.zohoRecordId;
  const dataSynchronizer: DataSynchronizer = getDataSynchronizer(tableName);

  async function afterInsertedEntity() {
    const tokenStore = AccessTokenStore.getInstance();
    const token = await tokenStore.getAccessToken();

    const MODULE_NAME = supabaseTableToZohoModuleName(tableName);
    const entityData = await getInsertDataByTableName(tableName, rowId);

    const endpoint = `${env.ZOHO_API_URL}/crm/v2/${MODULE_NAME}`;
    const method: HttpMethod = "POST";
    const headers = {
      Authorization: `Zoho-oauthtoken ${token.trim()}`,
      "Content-Type": "application/json",
    };

    const zohoApi = new ZohoApi(env.ZOHO_API_URL);
    const body = { data: [entityData] };

    const zohoResponse = await zohoApi.call({
      endpoint,
      method,
      headers,
      body,
    });

    if (!zohoResponse) {
      console.log("Failed to get response from Zoho API Call");
      return null;
    }

    console.log("Inserted data: ", zohoResponse.data);

    const zohoRecordId = zohoResponse.data[0].details.id;

    console.log("ZohoRecordId: ", zohoRecordId);

    if (zohoRecordId) {
      await appendZohoRecordId(tableName, rowId, zohoRecordId);
    }
    return zohoResponse.data;
  }

  async function afterUpdatedEntity() {
    // Check if object after update trigger are equal. If so - there is no need to proceed request
    if (tableName !== "profiles") {
      const objectsEqual = areObjectsEqual(new_record, old_record);

      if (objectsEqual) {
        console.log(
          "New and old objects are equal on DB level, no need to sync"
        );
        return null;
      }
    }

    // Check if Supabase and Zoho data for specific row is in sync. If so - there is no need to proceed request
    const isDataInSync = await dataSynchronizer.isDataInSync(
      rowId,
      zohoRecordId
    );

    if (isDataInSync) {
      console.log("Data is synced already. No actions needed");
      return null;
    }

    const tokenStore = AccessTokenStore.getInstance();
    const token = await tokenStore.getAccessToken();

    // Convert Supbase table name that changed to Zoho Module name
    const ZOHO_MODULE_NAME = supabaseTableToZohoModuleName(tableName);

    // Get payload that needs to be sent to Zoho to update it
    const entityData = await getUpdateDataByTableName(tableName, rowId);

    const endpoint = `${env.ZOHO_API_URL}/crm/v2/${ZOHO_MODULE_NAME}`;
    const method: HttpMethod = "PUT";
    const headers = {
      Authorization: `Zoho-oauthtoken ${token.trim()}`,
      "Content-Type": "application/json",
    };

    // Call Zoho
    const zohoApi = new ZohoApi(env.ZOHO_API_URL);
    const body = { data: [entityData] };
    const zohoResponse = await zohoApi.call({
      endpoint,
      method,
      headers,
      body,
    });

    if (!zohoResponse) {
      console.log("Failed to get response on entity insert in Zoho");
      return null;
    }

    return zohoResponse.data;
  }

  switch (operation) {
    case "INSERT":
      {
        const data = await afterInsertedEntity();
        if (data) {
          return {
            status: 200,
            data,
          };
        }
      }
      break;
    case "UPDATE":
      {
        const data = await afterUpdatedEntity();
        if (data) {
          return {
            status: 200,
            data,
          };
        }
      }
      break;
    default:
      return {
        status: 400,
        error: {
          message: "Undefined operation type",
          details: {
            operation,
          },
        },
      };
  }

  return {
    status: 200,
    error: {
      message: "Sync operation stopped. Check logs",
    },
  };
}
