import { Context } from "https://deno.land/x/oak@v14.0.0/mod.ts";
import { ServiceResponse } from "../../_shared/common-types.ts";
import { InsertedWithdrawalRequestSchema } from "../types.ts";
import { InsertedWithdrawalRequest } from "../types.ts";
import AccessTokenStore from "../../zoho_api/store.ts";
import { env } from "../../_shared/env.ts";
import { supabaseTableToZohoModuleName } from "../utils.ts";
import { HttpMethod } from "../../zoho_api/types/common.ts";
import { ZohoApi } from "../../zoho_api/api/zoho.ts";

export class WithdrawalRequestsService {
  async onInsert(context: Context): Promise<ServiceResponse> {
    const body = await context.request.body.json();
    const parseResult = await InsertedWithdrawalRequestSchema.safeParseAsync(
      body.record
    );
    if (!parseResult.success) {
      return {
        status: 403,
        error: {
          message: "Validation error",
          details: parseResult.error,
        },
      };
    }

    const dto: InsertedWithdrawalRequest = parseResult.data;

    const tokenStore = AccessTokenStore.getInstance();
    const token = await tokenStore.getAccessToken();
    const moduleName = supabaseTableToZohoModuleName("withdrawal_requests");

    const endpoint = `${env.ZOHO_API_URL}/crm/v2/${moduleName}`;
    const method: HttpMethod = "POST";
    const headers = {
      Authorization: `Zoho-oauthtoken ${token.trim()}`,
      "Content-Type": "application/json",
    };

    const zohoApi = new ZohoApi(env.ZOHO_API_URL);
    const zohoCallBody = { data: [dto] };

    const zohoResponse = await zohoApi.call({
      endpoint,
      method,
      headers,
      body: zohoCallBody,
    });

    if (!zohoResponse) {
      console.log("Failed to get response from Zoho API Call");
      return {
        status: 403,
        error: {
          message: "Failed to get response from Zoho API Call",
          details: zohoResponse,
        },
      };
    }

    console.log("Inserted data: ", zohoResponse.data);

    return {
      status: 200,
      data: {
        message: "Handled withdrawal request",
        data: zohoResponse,
      },
    };
  }
}
