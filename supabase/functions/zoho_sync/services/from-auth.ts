import { createClient } from "https://esm.sh/@supabase/supabase-js@2.39.7";
import { Context } from "https://deno.land/x/oak@v14.0.0/mod.ts";
import { ServiceResponse } from "../../_shared/common-types.ts";
import { env } from "../../_shared/env.ts";

export async function syncFromAuth(context: Context): Promise<ServiceResponse> {
  console.log("POST > /zoho-sync/from-auth");

  const body = await context.request.body.json();

  console.log("Webhook Body: ");
  console.log(body);

  const supabase = createClient(
    env.SUPABASE_URL,
    env.SUPABASE_SERVICE_ROLE_KEY
  );

  const new_record = body.record;
  const old_record = body.old_record;
  const rowId = new_record.id;

  if (new_record.email === old_record.email) {
    return {
      status: 400,
      data: {
        message: "Emails are the same, no need to trigger update",
      },
    };
  }

  const getProfile = await supabase
    .from("profiles")
    .select("*")
    .eq("id", rowId)
    .single();

  console.log("getProfile call");
  console.log(getProfile);

  if (getProfile.error) {
    const { error } = getProfile;
    return {
      status: 400,
      error: {
        message: "Failed to get profile",
        details: error,
      },
    };
  }

  // TODO: Find a way to call update trigger without previous SELECT call

  const updateProfile = await supabase
    .from("profiles")
    .update({ full_name: getProfile.data.full_name })
    .eq("id", rowId)
    .single();

  console.log("updateProfile call");
  console.log(updateProfile);

  if (updateProfile.error) {
    const { error } = updateProfile;
    return {
      status: 400,
      error: {
        message: "Failed to update profile",
        details: error,
      },
    };
  }

  return {
    status: 200,
    data: {
      updated: updateProfile.data,
    },
  };
}
