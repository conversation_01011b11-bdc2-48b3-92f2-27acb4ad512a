import { env } from "../../_shared/env.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2.39.7";
import { Context } from "https://deno.land/x/oak@v14.0.0/mod.ts";
import { ServiceResponse } from "../../_shared/common-types.ts";
import {
  UpdateSupabaseFromZohoDtoSchema,
  UpdateSupabaseFromZohoDto,
} from "../types.ts";

export async function syncFromZoho(context: Context): Promise<ServiceResponse> {
  console.log("POST > /zoho-sync/from-zoho");

  const supabase = createClient(
    env.SUPABASE_URL,
    env.SUPABASE_SERVICE_ROLE_KEY
  );

  if (!context.request.hasBody) {
    const message = "Request body is missing";
    console.log(message);
    return {
      status: 400,
      error: {
        message,
      },
    };
  }

  const payload = await context.request.body.json();

  console.log("Payload from ZOHO: ");
  console.log(payload);

  // <!> List here data that must be fetched from Zoho Function to update in Supabase
  // And also Added to
  const data: UpdateSupabaseFromZohoDto = {
    // Not using 'email' sent from Zoho here, because we should not be able to change user's email from Zoho
    available_balance: payload.available_balance,
    home_ownership_status: payload.home_ownership_status,
    residential_status: payload.residential_status,
    address_details: payload.address_details,
    airing_cupboard_cylinder: payload.airing_cupboard_cylinder,
    any_cover: payload.any_cover,
    bathrooms_count: payload.bathrooms_count,
    boiler_age: payload.boiler_age,
    boiler_fuel_source: payload.boiler_fuel_source,
    boiler_serviced_recently: payload.boiler_serviced_recently,
    carbon_monoxide_alarm: payload.carbon_monoxide_alarm,
    date_of_birth: payload.date_of_birth,
    full_name: payload.full_name,
    mobile_number: payload.mobile_number,
    post_code: payload.post_code,
    power_flush_carried_out: payload.power_flush_carried_out,
    property_type: payload.property_type,
    radiators_count: payload.radiators_count,
    smart_thermostat: payload.smart_thermostat,
    tanks_in_loft: payload.tanks_in_loft,
    thermostat_radiators_valves: payload.thermostat_radiators_valves,
    customer_id: payload.customer_id,
    status: payload.status,
    ref_by_id: payload.ref_by_id,
  };

  const parseResult = UpdateSupabaseFromZohoDtoSchema.safeParse(data);

  if (!parseResult.success) {
    const message = "Failed to parse data from Zoho. Validation error";
    console.log(message);
    return {
      status: 400,
      error: {
        message,
        details: parseResult.error,
      },
    };
  }

  const dto: UpdateSupabaseFromZohoDto = parseResult.data;

  const { data: updated, error } = await supabase
    .from("profiles")
    .update(dto)
    .eq("id", payload.user_id)
    .select("*")
    .single();

  if (error) {
    const message = "Failed to update data in supabase from Zoho";
    console.log(message);
    console.log(error);
    return {
      status: 400,
      error: {
        message,
        details: error,
      },
    };
  }

  console.log("Updated row in Supabase on /from-zoho");
  console.log(updated);

  return {
    status: 200,
    data: updated,
  };
}
