import { Context } from "https://deno.land/x/oak@v14.0.0/mod.ts";
import { ServiceResponse } from "../../_shared/common-types.ts";
import {
  CreateEarnProgramTaskDto,
  ZohoEarnProgramTaskDto,
  ZohoEarnProgramTaskDtoSchema,
  ZohoEarnProgramTaskStatus,
  EarnProgramTask,
} from "../types.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2.39.7";
import { EarnProgramTaskStatus } from "../types.ts";
import { ZohoEarnProgramTask } from "../types.ts";
import { env } from "../../_shared/env.ts";
export class EarnProgramService {
  constructor() {}

  private convertFromZohoEarnProgramTask(
    task: ZohoEarnProgramTask
  ): EarnProgramTask {
    switch (task) {
      case "Refer a friend for a 12 month Peace Plan":
        return "refer_friend_for_12_month_peace_plan";
      case "Refer a friend for a Boiler Service":
        return "refer_friend_for_boiler_service";
      case "Refer a friend for a new boiler installation":
        return "refer_friend_for_boiler_installation";
      case "Refer a friend for a Central Heating Powerflush":
        return "refer_friend_for_central_heating_powerflush";
      case "Refer a friend for a Central Heating Control Upgrade":
        return "refer_friend_for_central_heating_control_upgrade";
      case "Submit a Video Review":
        return "submit_video_review";
    }
  }

  private convertFromZohoEarnProgramTaskStatus(
    status: ZohoEarnProgramTaskStatus
  ): EarnProgramTaskStatus {
    switch (status) {
      case "Quote issued":
        return "quote_issued";
      case "Job Error/Problem":
        return "job_error";
      case "Job complete":
        return "job_complete";
      case "No contact made":
        return "no_contact";
      case "Service has been booked":
        return "service_booked";
      case "Your referral has declined our services":
        return "referral_declined_services";
      case "Purchase link sent":
        return "purchase_link_sent";
      case "Awaiting purchase":
        return "awaiting_purchase";
      case "Purchase completed":
        return "purchase_completed";
      case "Video is being reviewed":
        return "video_in_review";
      case "Video unsuitable":
        return "video_unsuitable";
      case "Video approved":
        return "video_approved";
    }
  }

  async handleEarnProgramTaskInsertFromZoho(
    context: Context
  ): Promise<ServiceResponse> {
    const body = await context.request.body.json();
    const parseResult = await ZohoEarnProgramTaskDtoSchema.safeParseAsync(body);

    if (!parseResult.success) {
      return {
        status: 400,
        error: {
          message: "HandleNewEarnProgramTask: Validation error",
          details: parseResult.error,
        },
      };
    }

    const zohoDto: ZohoEarnProgramTaskDto = parseResult.data;
    const dto: CreateEarnProgramTaskDto = this.sanitizeFromZoho(zohoDto);

    const supabase = createClient(
      env.SUPABASE_URL,
      env.SUPABASE_SERVICE_ROLE_KEY
    );

    const insertTask = await supabase
      .from("earn_program_tasks")
      .insert(dto)
      .select("id, status, user_id, task")
      .single();

    if (insertTask.error) {
      return {
        status: 500,
        error: {
          message: "Failed to create earn program task",
          details: insertTask.error,
        },
      };
    }

    return insertTask.data;
  }

  async handleEarnProgramTaskUpdateFromZoho(
    context: Context
  ): Promise<ServiceResponse> {
    // TODO:
    // If task is updated on Zoho side, we need to
    // create new row in earn_program_tasks table, with created_at column
    // this way, we store every change made to task throughout its life
    const body = await context.request.body.json();
    const parseResult = await ZohoEarnProgramTaskDtoSchema.safeParseAsync(body);

    if (!parseResult.success) {
      console.log("Failed to parse error");
      console.log(parseResult.error);

      return {
        status: 400,
        error: {
          message: "HandleUpdateEarnProgramTask: Validation error",
          details: parseResult.error,
        },
      };
    }

    const zohoDto: ZohoEarnProgramTaskDto = parseResult.data;
    const dto: CreateEarnProgramTaskDto = this.sanitizeFromZoho(zohoDto);
    const { record_id, ...updateFields } = dto;

    console.log("Parsed result: ");
    console.log("record_id", record_id);

    console.log("Update fields");
    console.log(updateFields);

    const supabase = createClient(
      env.SUPABASE_URL,
      env.SUPABASE_SERVICE_ROLE_KEY
    );

    const createTask = await supabase
      .from("earn_program_tasks")
      .insert(dto)
      .select("id, status, user_id, task, created_at")
      .single();

    if (createTask.error) {
      console.log("Error");
      console.log(createTask.error);

      return {
        status: 500,
        error: {
          message: "Failed to create earn program task",
          details: createTask.error,
        },
      };
    }

    return createTask.data;
  }

  sanitizeFromZoho(dto: ZohoEarnProgramTaskDto): CreateEarnProgramTaskDto {
    return {
      task: this.convertFromZohoEarnProgramTask(dto.task),
      status: this.convertFromZohoEarnProgramTaskStatus(dto.status),
      user_id: dto.user_id,
      record_id: dto.record_id,
    };
  }
}
