import { z } from "https://deno.land/x/zod@v3.22.4/mod.ts";

// Important! If Zoho update entity script update it's fields, we must update this validation schema according to it
// If somebody updates
export const UpdateSupabaseFromZohoDtoSchema = z.object({
  available_balance: z.number().nullable(),
  full_name: z.string().nullable(),
  date_of_birth: z.string().nullable(),
  mobile_number: z.string().nullable(),
  post_code: z.string().nullable(),
  address_details: z.string().nullable(),
  residential_status: z.string().nullable(),
  home_ownership_status: z.string().nullable(),
  property_type: z.string().nullable(),
  bathrooms_count: z.number().nullable(),
  boiler_fuel_source: z.string().nullable(),
  boiler_age: z.string().nullable(),
  airing_cupboard_cylinder: z.string().nullable(),
  tanks_in_loft: z.string().nullable(),
  radiators_count: z.number().nullable(),
  smart_thermostat: z.string().nullable(),
  thermostat_radiators_valves: z.string().nullable(),
  boiler_serviced_recently: z.string().nullable(),
  carbon_monoxide_alarm: z.string().nullable(),
  any_cover: z.string().nullable(),
  power_flush_carried_out: z.string().nullable(),
  customer_id: z.string().nullable(),
  status: z.string().nullable(),
  ref_by_id: z.string().nullable(),
});

export type UpdateSupabaseFromZohoDto = z.infer<
  typeof UpdateSupabaseFromZohoDtoSchema
>;

export const TransactionStatuses = ["Completed", "Pending"] as const;

export const CreateOrUpdateTransactionFromZohoDtoSchema = z.object({
  zoho_record_id: z.string(),
  user_id: z.string(),
  status: z.enum(TransactionStatuses),
  from: z.string().nullable().optional(),
  amount: z.number(),
});

export type CreateOrUpdateTransactionFromZohoDto = z.infer<
  typeof CreateOrUpdateTransactionFromZohoDtoSchema
>;

export const InsertedWithdrawalRequestSchema = z.object({
  id: z.string().uuid(),
  user_id: z.string().uuid(),
  sort_code: z.string(),
  created_at: z.string().optional(),
  account_holder: z.string(),
  account_number: z.string(),
  amount_to_withdraw: z.number(),
});

export type InsertedWithdrawalRequest = z.infer<
  typeof InsertedWithdrawalRequestSchema
>;

export const ZOHO_EARN_PROGRAM_TASK_STATUSES = [
  "No contact made",
  "Quote issued",
  "Service has been booked",
  "Job complete",
  "Your referral has declined our services",
  "Job Error/Problem",
  "Purchase link sent",
  "Awaiting purchase",
  "Purchase completed",
  "Video is being reviewed",
  "Video unsuitable",
  "Video approved",
] as const;

export type ZohoEarnProgramTaskStatus =
  | "No contact made"
  | "Quote issued"
  | "Service has been booked"
  | "Job complete"
  | "Your referral has declined our services"
  | "Job Error/Problem"
  | "Purchase link sent"
  | "Awaiting purchase"
  | "Purchase completed"
  | "Video is being reviewed"
  | "Video unsuitable"
  | "Video approved";

export const ZOHO_EARN_PROGRAM_TASKS = [
  "Refer a friend for a new boiler installation",
  "Refer a friend for a 12 month Peace Plan",
  "Refer a friend for a Central Heating Powerflush",
  "Refer a friend for a Boiler Service",
  "Refer a friend for a Central Heating Control Upgrade",
  "Submit a Video Review",
] as const;

export type ZohoEarnProgramTask =
  | "Refer a friend for a new boiler installation"
  | "Refer a friend for a 12 month Peace Plan"
  | "Refer a friend for a Central Heating Powerflush"
  | "Refer a friend for a Boiler Service"
  | "Refer a friend for a Central Heating Control Upgrade"
  | "Submit a Video Review";

export const ZohoEarnProgramTaskDtoSchema = z.object({
  record_id: z.string(),
  user_id: z.string().uuid(),
  status: z.enum(ZOHO_EARN_PROGRAM_TASK_STATUSES),
  task: z.enum(ZOHO_EARN_PROGRAM_TASKS),
});

export type ZohoEarnProgramTaskDto = z.infer<
  typeof ZohoEarnProgramTaskDtoSchema
>;

export const EARN_PROGRAM_TASK_STATUSES = [
  "no_contact",
  "quote_issued",
  "service_booked",
  "job_complete",
  "referral_declined_services",
  "job_error",
  "purchase_link_sent",
  "awaiting_purchase",
  "purchase_completed",
  "video_in_review",
  "video_unsuitable",
  "video_approved",
] as const;

export type EarnProgramTaskStatus =
  | "no_contact"
  | "quote_issued"
  | "service_booked"
  | "job_complete"
  | "referral_declined_services"
  | "job_error"
  | "purchase_link_sent"
  | "awaiting_purchase"
  | "purchase_completed"
  | "video_in_review"
  | "video_unsuitable"
  | "video_approved";

export const EARN_PROGRAM_TASKS = [
  "refer_friend_for_boiler_installation",
  "refer_friend_for_12_month_peace_plan",
  "refer_friend_for_central_heating_powerflush",
  "refer_friend_for_boiler_service",
  "refer_friend_for_central_heating_control_upgrade",
  "submit_video_review",
] as const;

export type EarnProgramTask =
  | "refer_friend_for_boiler_installation"
  | "refer_friend_for_12_month_peace_plan"
  | "refer_friend_for_central_heating_powerflush"
  | "refer_friend_for_boiler_service"
  | "refer_friend_for_central_heating_control_upgrade"
  | "submit_video_review";

export const CreateEarnProgramTaskDtoSchema = z.object({
  record_id: z.string(),
  user_id: z.string().uuid(),
  status: z.enum(EARN_PROGRAM_TASK_STATUSES),
  task: z.enum(EARN_PROGRAM_TASKS),
});
export type CreateEarnProgramTaskDto = z.infer<
  typeof CreateEarnProgramTaskDtoSchema
>;
