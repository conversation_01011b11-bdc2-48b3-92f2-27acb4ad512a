import { Context, Router } from 'https://deno.land/x/oak@v14.0.0/mod.ts';
import { EarnProgramService } from '../../services/earn-program.ts';

const router = new Router();
const earnProgramService = new EarnProgramService();

router.post('/', async (context: Context) => {
  console.log('POST /zoho_sync/earn-program-tasks/');

  const data =
    await earnProgramService.handleEarnProgramTaskInsertFromZoho(context);

  if (data.error) {
    context.response.status = data.status;
  }

  context.response.body = data;
  context.response.type = 'application/json';

  return data;
});

router.patch('/', async (context: Context) => {
  console.log('PATCH /zoho_sync/earn-program-tasks/');

  const data =
    await earnProgramService.handleEarnProgramTaskUpdateFromZoho(context);

  if (data.error) {
    context.response.status = data.status;
  }

  console.log('Final response');
  console.log(data);

  context.response.body = data;
  context.response.type = 'application/json';

  return data;
});

export default router;
