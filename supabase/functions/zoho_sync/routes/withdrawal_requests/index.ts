import { Context, Router } from 'https://deno.land/x/oak@v14.0.0/mod.ts';
import { WithdrawalRequestsService } from '../../services/withdrawal_requests.ts';

const router = new Router();
const withdrawalRequestsService = new WithdrawalRequestsService();

router.post('/', async (context: Context) => {
  console.log('POST /zoho_sync/withdrawal_requests/');

  const data = await withdrawalRequestsService.onInsert(context);

  if (data.error) {
    context.response.status = data.status;
  }

  context.response.body = data;
  context.response.type = 'application/json';

  return data;
});

export default router;
