import { Context, Router } from 'https://deno.land/x/oak@v14.0.0/mod.ts';
import { TransactionsService } from '../../services/transactions.ts';

const router = new Router();
const transactionsService = new TransactionsService();

// On ZOHO Insert. Create new transaction
router.post('/', async (context: Context) => {
  console.log('POST /zoho_sync/transactions/');

  const data = await transactionsService.handleNewTransaction(context);

  console.log('Handle new transaction response');
  console.log(data);

  if (data.error) {
    context.response.status = data.status;
  }

  context.response.body = data;
  context.response.type = 'application/json';

  return data;
});

router.patch('/', async (context: Context) => {
  console.log('PATCH /zoho_sync/transactions/');

  const data = await transactionsService.handleUpdateTransaction(context);

  console.log('Handle update transaction response');
  console.log(data);

  if (data.error) {
    context.response.status = data.status;
  }

  context.response.body = data;
  context.response.type = 'application/json';

  return data;
});

export default router;
