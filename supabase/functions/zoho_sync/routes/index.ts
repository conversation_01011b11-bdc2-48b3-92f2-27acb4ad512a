import { Context, Router } from "https://deno.land/x/oak@v14.0.0/mod.ts";

import transactionsRouter from "./transactions/index.ts";
import withdrawalRequestsRouter from "./withdrawal_requests/index.ts";
import earnProgramTasks from "./earn_program_tasks/index.ts";

import { syncFromZoho } from "../services/from-zoho.ts";
import { syncFromSupabase } from "../services/from-supabase.ts";
import { syncFromAuth } from "../services/from-auth.ts";

const router = new Router({
  prefix: "/zoho_sync",
});

router.post("/from_zoho", async (context: Context) => {
  const data = await syncFromZoho(context);
  if (data.error) {
    context.response.status = data.error ? 400 : 200;
  }

  context.response.body = data;
  context.response.type = "application/json";

  return data;
});

// user_email_update_trigger in Supabase
router.post("/auth", async (context: Context) => {
  const data = await syncFromAuth(context);
  if (data.error) {
    context.response.status = data.error ? 400 : 200;
  }

  context.response.body = data;
  context.response.type = "application/json";

  return data;
});

router.get("/transactions", transactionsRouter.routes());
router.get("/withdrawal_requests", withdrawalRequestsRouter.routes());
router.get("/earn-program-tasks", earnProgramTasks.routes());

router.post("/", async (context: Context) => {
  if (!context.request.hasBody) {
    return {
      status: 400,
      error: {
        message: "Request do not have body",
      },
    };
  }

  const data = await syncFromSupabase(context);

  if (data.error) {
    context.response.status = data.error ? 400 : 200;
  }

  context.response.body = data;
  context.response.type = "application/json";

  return data;
});

export default router;
