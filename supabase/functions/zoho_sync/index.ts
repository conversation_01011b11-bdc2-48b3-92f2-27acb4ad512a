import {
  Application,
  Context,
  Next,
} from "https://deno.land/x/oak@v14.0.0/mod.ts";

import { RESPONSE_INIT } from "../_shared/common-types.ts";
import router from "./routes/index.ts";

const app = new Application();

app.use(router.routes());
app.use(router.allowedMethods());

app.use(async (context: Context, next: Next) => {
  try {
    await next();
  } catch (err) {
    context.response.status = 500;
    context.response.body = {
      error: "Internal Server Error",
      message: err.message,
    };
    console.log("<!> Error: ");
    console.error(err);
  }
});

Deno.serve(async (request: Request) => {
  const response = await app.handle(request);

  if (!response) {
    const errorData: object = { message: "Failed to handle request" };
    return new Response(JSON.stringify(errorData), RESPONSE_INIT);
  }

  return new Response(response.body, RESPONSE_INIT);
});
