import { createClient } from "https://esm.sh/@supabase/supabase-js@2.39.7";
import AccessTokenStore from "../zoho_api/store.ts";
import { env } from "../_shared/env.ts";
import { TableName } from "../_shared/types.ts";
import { HttpMethod } from "../zoho_api/types/common.ts";
import { supabaseTableToZohoModuleName } from "./utils.ts";
import { ZohoApi } from "../zoho_api/api/zoho.ts";
import { areObjectsEqual } from "../_shared/common-types.ts";

type MemberDataToCompare = {
  full_name: string;
  date_of_birth: string;
  mobile_number: string;
  post_code: string;
  address_details: string;
  residential_status: string;
  home_ownership_status: string;
  property_type: string;
  bathrooms_count: number;
  boiler_fuel_source: string;
  boiler_age: string;
  airing_cupboard_cylinder: string;
  tanks_in_loft: string;
  radiators_count: number;
  smart_thermostat: string;
  thermostat_radiators_valves: string;
  boiler_serviced_recently: string;
  carbon_monoxide_alarm: string;
  any_cover: string;
  power_flush_carried_out: string;
  available_balance: number;
  // From auth.users
  email: string;
  // From Stripe
  customer_id: string;
  status: string;
  ref_by_id: string;
};

export abstract class DataSynchronizer {
  constructor() {}
  public abstract isDataInSync(
    rowId: string,
    zohoRecordId: string
  ): Promise<boolean>;
}

export class MembersDataSynchronizer extends DataSynchronizer {
  private tableName: TableName = "profiles";
  constructor() {
    super();
  }

  public async isDataInSync(
    rowId: string,
    zohoRecordId: string
  ): Promise<boolean> {
    const memberFromDatabase = await this.getFromDatabase(rowId);
    const memberFromZoho = await this.getFromZoho(zohoRecordId);

    console.log("MEMBER_FROM_ZOHO");
    console.log(memberFromZoho);

    console.log("MEMBER_FROM_DB");
    console.log(memberFromDatabase);

    // Determine if objects are same or not
    const isEqual = areObjectsEqual(memberFromZoho, memberFromDatabase);

    console.log("Is Equal: ", isEqual);

    return isEqual;
  }

  private async getFromDatabase(rowId: string): Promise<MemberDataToCompare> {
    const supabase = createClient(
      env.SUPABASE_URL,
      env.SUPABASE_SERVICE_ROLE_KEY
    );

    const supabaseSelectResponse = await supabase
      .from("profiles")
      .select("*")
      .eq("id", rowId)
      .single();

    if (supabaseSelectResponse.error) {
      console.log("Failed to find entity while data syncing");
      console.log(supabaseSelectResponse.error);
      throw new Error("Failed to find entity while data syncing");
    }

    console.log("Got data");
    console.log(supabaseSelectResponse.data);

    let data = supabaseSelectResponse.data;

    const getUserResponse = await supabase.auth.admin.getUserById(rowId);

    if (getUserResponse.error) {
      const message = "Failed to get user data in data-sync";
      console.log(message);
      throw new Error(message);
    }

    const email = getUserResponse.data.user.email;

    data = {
      ...data,
      email,
    };

    return this.sanitizeMemberDataToCompare(data);
  }

  private async getFromZoho(zohoRecordId: string) {
    const tokenStore = AccessTokenStore.getInstance();
    const token = await tokenStore.getAccessToken();
    const zohoModuleName = supabaseTableToZohoModuleName(this.tableName);

    const endpoint = `${env.ZOHO_API_URL}/crm/v2/${zohoModuleName}/${zohoRecordId}`;
    const method: HttpMethod = "GET";
    const headers = {
      Authorization: `Zoho-oauthtoken ${token.trim()}`,
      "Content-Type": "application/json",
    };

    const zohoApi = new ZohoApi(env.ZOHO_API_URL);
    const zohoResponse = await zohoApi.call({ endpoint, method, headers });

    if (!zohoResponse) {
      const message = "Failed to get zoho response";
      console.log({ message });
      throw new Error(message);
    }

    let data = zohoResponse.data[0];

    console.log("Data in response from zoho");
    console.log(data);

    data = {
      ...data,
      email: data.Email,
    };

    return this.sanitizeMemberDataToCompare(data);
  }

  private sanitizeMemberDataToCompare(data: any): MemberDataToCompare {
    const member: MemberDataToCompare = {
      address_details: data.address_details,
      airing_cupboard_cylinder: data.airing_cupboard_cylinder,
      any_cover: data.any_cover,
      available_balance: data.available_balance,
      bathrooms_count: data.bathrooms_count,
      boiler_age: data.boiler_age,
      boiler_fuel_source: data.boiler_fuel_source,
      boiler_serviced_recently: data.boiler_serviced_recently,
      carbon_monoxide_alarm: data.carbon_monoxide_alarm,
      date_of_birth: data.date_of_birth,
      full_name: data.full_name,
      home_ownership_status: data.home_ownership_status,
      mobile_number: data.mobile_number,
      post_code: data.post_code,
      power_flush_carried_out: data.power_flush_carried_out,
      property_type: data.property_type,
      radiators_count: data.radiators_count,
      residential_status: data.residential_status,
      smart_thermostat: data.smart_thermostat,
      tanks_in_loft: data.tanks_in_loft,
      thermostat_radiators_valves: data.thermostat_radiators_valves,
      email: data.email,
      customer_id: data.customer_id,
      status: data.status,
      ref_by_id: data.ref_by_id,
    };

    return member;
  }
}
