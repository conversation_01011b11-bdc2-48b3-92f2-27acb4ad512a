import { env } from "../_shared/env.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2.39.7";
import { ZohoMember, TableName, ZohoModuleName } from "../_shared/types.ts";
import {
  DataSynchronizer,
  MembersDataSynchronizer,
} from "./data-synchronizer.ts";

export const supabaseTableToZohoModuleName = (
  name: TableName
): ZohoModuleName => {
  switch (name) {
    case "profiles":
      return "Members";
    case "withdrawal_requests":
      return "withdrawal_requests";
    default:
      throw new Error(
        "Failed to find supported zoho module name by table name"
      );
  }
};

export const getInsertDataByTableName = async (
  tableName: TableName,
  id: string
) => {
  const supabase = createClient(
    env.SUPABASE_URL,
    env.SUPABASE_SERVICE_ROLE_KEY
  );

  const onProfileInsert = async (id: string) => {
    const getUserResponse = await supabase.auth.admin.getUserById(id);

    if (getUserResponse.error) {
      console.log("Failed to get User by ID");
      console.log(getUserResponse.error);
      throw new Error(getUserResponse.error.message);
    }

    const { data: userData } = getUserResponse;

    console.log("Found user");
    console.log(userData);

    const getProfileResponse = await supabase
      .from("profiles")
      .select("*")
      .eq("id", id)
      .single();

    if (getProfileResponse.error) {
      const { error } = getProfileResponse;
      console.log(error);
      throw new Error(error.message);
    }

    const { data: profile } = getProfileResponse;

    console.log("Found profile");
    console.log(profile);

    const member: ZohoMember = {
      ...profile,
      Email: userData.user.email,
      user_id: userData.user.id,
    };

    console.log("Construct member");
    console.log(member);

    return member;
  };

  switch (tableName) {
    case "profiles":
      return await onProfileInsert(id);
  }
};

export const getDataSynchronizer = (tableName: TableName): DataSynchronizer => {
  let dataSynchronizer: DataSynchronizer;
  switch (tableName) {
    case "profiles":
      dataSynchronizer = new MembersDataSynchronizer();
      break;
    default:
      throw `Data syncronizer for table '${tableName}' is not defined`;
  }

  return dataSynchronizer;
};

export const getUpdateDataByTableName = async (
  tableName: TableName,
  id: string
) => {
  const supabase = createClient(
    env.SUPABASE_URL,
    env.SUPABASE_SERVICE_ROLE_KEY
  );

  const onProfileUpdate = async (id: string) => {
    const getUserResponse = await supabase.auth.admin.getUserById(id);

    if (getUserResponse.error) {
      console.log("Failed to get user");
      console.log(getUserResponse.error);
      throw new Error(getUserResponse.error.message);
    }

    const { data: profile, error } = await supabase
      .from("profiles")
      .select("*")
      .eq("id", id)
      .single();

    if (error) {
      console.log(error);
      throw new Error(error.message);
    }

    console.log("Found profile");
    console.log(profile);

    if (!profile.zohoRecordId) {
      console.log("No Zoho Record ID - Failed to update");
      throw new Error("No Zoho Record ID - Failed to update");
    }

    const member: ZohoMember = {
      ...profile,
      user_id: getUserResponse.data.user.id,
      id: profile.zohoRecordId,
      Email: getUserResponse.data.user.email,
    };

    console.log("Construct member");
    console.log(member);

    return member;
  };

  switch (tableName) {
    case "profiles":
      return await onProfileUpdate(id);
  }
};

export const appendZohoRecordId = async (
  tableName: TableName,
  id: string,
  zohoRecordId: string
) => {
  const supabase = createClient(
    env.SUPABASE_URL,
    env.SUPABASE_SERVICE_ROLE_KEY
  );

  // Update supabase call, careful
  const { data, error } = await supabase
    .from(tableName)
    .update({ zohoRecordId })
    .eq("id", id)
    .single();

  if (error) {
    console.log(error);
    throw new Error(error.message);
  }

  return data;
};
