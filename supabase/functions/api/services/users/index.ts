import { env } from "../../../_shared/env.ts";
import { Context } from "https://deno.land/x/oak@v14.0.0/mod.ts";
import { SignInDtoSchema, SignInDto } from "../../types/sign-in.dto.ts";
import { SignUpDto, SignUpDtoSchema } from "../../types/sign-up.dto.ts";
import { ServiceResponse } from "../../../_shared/common-types.ts";
import { stripeService } from "../../../_shared/stripe/stripe.service.ts";
import {
  createClient,
  SupabaseClient,
} from "https://esm.sh/@supabase/supabase-js@2.39.7";

export class UsersService {
  constructor() {}

  // Deprecated
  private async generateRefId(
    dto: SignUpDto,
    supabase: SupabaseClient
  ): Promise<string> {
    const getInitials = (full_name: string): string => {
      const split = full_name.split(" ");
      const f = split[0][0] ?? "A";
      const s = split[1][0] ?? "A";

      return `${f.toUpperCase()}${s.toUpperCase()}`;
    };

    const getRandomCode = (length = 6) => {
      let result = "";
      for (let i = 0; i < length; i++) {
        result += Math.floor(Math.random() * 10).toString();
      }
      return result;
    };

    let createdUnique = false;

    const initials = getInitials(dto.full_name);
    let ref_id = "";

    while (createdUnique === false) {
      const code = getRandomCode();
      ref_id = `${initials}${code}FF`;

      const { data: exist } = await supabase
        .from("profiles")
        .select("*")
        .eq("ref_id", ref_id)
        .single();

      if (exist) continue;

      createdUnique = true;
    }
    return ref_id;
  }

  // Deprecated
  async signUp(ctx: Context): Promise<ServiceResponse> {
    try {
      const body = await ctx.request.body.json();
      const parseResult = await SignUpDtoSchema.safeParseAsync(body);

      if (!parseResult.success) {
        return {
          status: 400,
          error: {
            message: "Validation error",
            details: parseResult.error,
          },
        };
      }

      const dto: SignUpDto = parseResult.data;

      const supabase = createClient(
        env.SUPABASE_URL,
        env.SUPABASE_SERVICE_ROLE_KEY
      );

      const ref_id = await this.generateRefId(dto, supabase);

      const { data, error } = await supabase.auth.signUp({
        email: dto.email,
        password: dto.password,
        options: {
          data: {
            full_name: dto.full_name,
            date_of_birth: dto.date_of_birth,
            mobile_number: dto.mobile_number,
            post_code: dto.post_code,
            address_details: dto.address_details,
            residential_status: dto.residential_status,
            ref_id,
          },
        },
      });

      if (error) {
        return {
          status: 400,
          error: {
            message: "Failed to sign-up",
            details: error,
          },
        };
      }

      return {
        status: 200,
        data,
      };
    } catch (error) {
      console.error(error);
      return {
        status: 400,
        error: {
          message: "Failed to sign up",
          details: error,
        },
      };
    }
  }

  // Deprecated
  async signIn(ctx: Context): Promise<ServiceResponse> {
    try {
      const body = await ctx.request.body.json();
      const parseResult = await SignInDtoSchema.safeParseAsync(body);

      if (!parseResult.success) {
        return {
          status: 400,
          error: {
            message: "Validation error",
            details: parseResult.error,
          },
        };
      }

      const dto: SignInDto = parseResult.data;

      const supabase = createClient(
        env.SUPABASE_URL,
        env.SUPABASE_SERVICE_ROLE_KEY
      );

      const { data, error } = await supabase.auth.signInWithPassword({
        email: dto.email,
        password: dto.password,
      });

      if (error) {
        return {
          status: 400,
          error: {
            message: "Failed to sign in",
            details: error,
          },
        };
      }

      const token = data.session.access_token;
      const user = data.user;

      return {
        status: 200,
        data: {
          token,
          user,
        },
      };
    } catch (error) {
      console.error(error);
      return {
        status: 400,
        error: {
          message: "Failed sign in",
          details: error,
        },
      };
    }
  }

  async deleteAccount(
    _ctx: Context,
    supabase: SupabaseClient
  ): Promise<ServiceResponse> {
    const { data: getUserData, error: getUserError } =
      await supabase.auth.getUser();

    if (getUserError) {
      return {
        error: {
          message: "Failed to get current user",
        },
        status: 403,
      };
    }

    const { id } = getUserData.user;

    const { error: updateUserError } = await supabase
      .from("profiles")
      .update({ status: "deleted" })
      .match({ id })
      .single();

    if (updateUserError) {
      return {
        error: {
          message: "Failed to update user",
          details: updateUserError,
        },
        status: 403,
      };
    }

    const { error: cancelSubscriptionError } =
      await stripeService.cancelCurrentSubscription(supabase);

    if (cancelSubscriptionError) {
      const { error: restoreUserError } = await supabase
        .from("profiles")
        .update({ status: "active" })
        .match({ id })
        .single();

      if (restoreUserError) {
        console.error("Failed to restore users status");
      }

      return {
        error: cancelSubscriptionError,
        status: 403,
      };
    }

    return {
      data: {
        message: "Account deleted successfully",
      },
      status: 200,
    };
  }
}
