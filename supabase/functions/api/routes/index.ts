import { Context, Router } from "https://deno.land/x/oak@v14.0.0/mod.ts";
import usersRouter from "./users.ts";
import { ServiceResponse } from "../../_shared/common-types.ts";

const router = new Router({
  prefix: "/api",
});

router.get("/", (context: Context) => {
  const response: ServiceResponse = {
    data: {
      message: "Hello world!",
    },
    status: 200,
  };

  context.response.status = response.status;
  context.response.body = response;

  return response;
});

router.get("/users", usersRouter.routes());

export default router;
