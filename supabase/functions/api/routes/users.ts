import { createClient } from "https://esm.sh/@supabase/supabase-js@2.39.7";
import { env } from "../../_shared/env.ts";
import { Context, Router } from "https://deno.land/x/oak@v14.0.0/mod.ts";
import { UsersService } from "../services/users/index.ts";
import { ServiceResponse } from "../../_shared/common-types.ts";
import { EnsureAuthenticated } from "../../_shared/utils.ts";

const router = new Router();
const usersService = new UsersService();

router.post("/sign-in", async (context: Context) => {
  console.log("POST /api/users/sign-in");

  const response: ServiceResponse = await usersService.signIn(context);

  context.response.body = response;
  context.response.status = response.status;
  context.response.type = "application/json";

  return;
});

router.post("/sign-up", async (context: Context) => {
  console.log("POST /api/users/sign-up");

  const response: ServiceResponse = await usersService.signUp(context);

  context.response.body = response;
  context.response.status = response.status;
  context.response.type = "application/json";

  return;
});

router.delete("/", EnsureAuthenticated, async (context: Context) => {
  console.log("DELETE /api/users");

  const token: string = context.state.authorizationToken;

  const supabase = createClient(
    env.SUPABASE_URL,
    env.SUPABASE_SERVICE_ROLE_KEY,
    {
      global: {
        headers: {
          Authorization: token,
        },
      },
    }
  );

  const response: ServiceResponse = await usersService.deleteAccount(
    context,
    supabase
  );

  context.response.body = response;
  context.response.status = response.status;
  context.response.type = "application/json";

  return;
});

export default router;
