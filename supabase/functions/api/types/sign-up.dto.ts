import { z } from 'https://deno.land/x/zod@v3.22.4/mod.ts';
import { RESIDENTIAL_STATUSES } from './common.ts';

export const SignUpDtoSchema = z.object({
  email: z.string().email(),
  password: z.string(),
  // Full name should have 2 words separated by space
  full_name: z
    .string()
    .trim()
    .min(3)
    .max(50),
  date_of_birth: z.string(),
  mobile_number: z.string(),
  post_code: z.string(),
  address_details: z.string(),
  residential_status: z.enum(RESIDENTIAL_STATUSES),
});

export type SignUpDto = z.infer<typeof SignUpDtoSchema>;
