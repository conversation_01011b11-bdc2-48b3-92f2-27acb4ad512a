import { Stripe } from "npm:stripe@15.3.0";
import { <PERSON>e<PERSON>ventHandler } from "./event-handler.ts";
import { RefinedServiceResponse } from "../../_shared/common-types.ts";
import { env } from "../../_shared/env.ts";
import { EmailService, EmailType } from "../../_shared/email.service.ts";

export class InvoicesEventHandler extends StripeEventHandler {
  constructor(secretKey: string) {
    super(secretKey);
  }

  public async handleEvent(
    event: Stripe.Event
  ): Promise<RefinedServiceResponse> {
    switch (event.type) {
      case "invoice.payment_failed":
        return await this.onInvoicePaymentFailed(event);
      default:
        return {
          error: {
            message: `[InvoicesEventHandler]: No handler for '${event.type}' event`,
            details: {
              event,
            },
          },
        };
    }
  }

  public async onInvoicePaymentFailed(
    event: Stripe.InvoicePaymentFailedEvent
  ): Promise<RefinedServiceResponse> {
    console.log("handleInvoicePaymentFailed", event);

    const invoice = event.data.object;
    console.log("Invoice: ", invoice);

    const emailService = new EmailService(
      env.SENDGRID_API_KEY,
      env.SENDGRID_SENDER_EMAIL
    );

    if (!invoice.customer_email) {
      return {
        error: {
          message: "customer_email field is undefined",
          details: event,
        },
      };
    }

    const sendEmailResponse = await emailService.sendEmail({
      to: invoice.customer_email,
      emailType: EmailType.SUBSCRIPTION_INVOICE_PAYMENT_FAILED,
    });

    return {
      data: {
        message: "Invoice payment failed. Tried to send customer notification",
        details: sendEmailResponse,
      },
    };
  }
}
