import { <PERSON>e } from "npm:stripe@15.3.0";
import { StripeEventHandler } from "./event-handler.ts";
import { RefinedServiceResponse } from "../../_shared/common-types.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2.39.7";
import { env } from "../../_shared/env.ts";
import { UpsertSubscriptionDto } from "../types.ts";

const supabase = createClient(env.SUPABASE_URL, env.SUPABASE_SERVICE_ROLE_KEY);
export class SubscriptionsEventHandler extends StripeEventHandler {
  constructor(secretKey: string) {
    super(secretKey);
  }

  public async handleEvent(
    event: Stripe.Event
  ): Promise<RefinedServiceResponse> {
    switch (event.type) {
      case "customer.subscription.created":
        return await this.handleSubscriptionCreated(event);
      case "customer.subscription.updated":
        return await this.handleSubscriptionUpdated(event);
      case "customer.subscription.deleted":
        return await this.handleSubscriptionDeleted(event);
      default:
        return {
          error: {
            message: "Unsupported event type",
            details: {
              handler: "SubscriptionsEventHandler",
              event,
            },
          },
        };
    }
  }

  async handleSubscriptionCreated(
    event: Stripe.CustomerSubscriptionCreatedEvent
  ): Promise<RefinedServiceResponse> {
    console.log("handleSubscriptionCreated");
    console.log(event);

    const subscription = event.data.object;

    const customerId = subscription.customer.toString();
    const status = subscription.status;
    const subscription_metadata = subscription.metadata;
    const current_period_start = subscription.current_period_start;
    const current_period_end = subscription.current_period_end;

    const items = subscription.items.data;

    if (items.length > 1) {
      return {
        error: {
          message:
            "Something weird happend. Only single item per subscriptions is allowed",
          details: {
            event,
          },
        },
      };
    }

    const item = items[0];

    const price = item.price;

    const productId = price.product.toString();
    let product: Stripe.Product;

    try {
      product = await this.stripe.products.retrieve(productId);
    } catch (error) {
      return {
        error: {
          message: "Failed to retrieve product by id",
          details: {
            productId,
            stripeError: error,
          },
        },
      };
    }

    const { data: profile, error: getProfileError } = await supabase
      .from("profiles")
      .select("id")
      .eq("customer_id", customerId)
      .single();

    if (getProfileError) {
      return {
        error: {
          message: "Failed to get profile by 'customer_id'",
          details: {
            customerId,
            ...getProfileError,
          },
        },
      };
    }

    const { id: user_id } = profile;

    const dto: UpsertSubscriptionDto = {
      subscription_id: subscription.id,
      status,
      user_id,
      plan_name: product.metadata.plan_name,
      additional_appliances:
        subscription_metadata.additionalAppliances.split(";") ?? null,
      current_period_start,
      current_period_end,
    };

    const { data: createdSubscription, error: createSubscriptionError } =
      await supabase.from("plan_subscriptions").insert(dto).single();

    if (createSubscriptionError) {
      return {
        error: {
          message: "Failed to insert subscription",
          details: createSubscriptionError,
        },
      };
    }

    return {
      data: {
        subscription: createdSubscription,
      },
    };
  }

  async handleSubscriptionUpdated(
    event: Stripe.CustomerSubscriptionUpdatedEvent
  ): Promise<RefinedServiceResponse> {
    console.log("handleSubscriptionUpdate");
    console.log(event);

    const subscription = event.data.object;

    const customerId = subscription.customer.toString();
    const status = subscription.status;
    const subscription_metadata = subscription.metadata;
    const current_period_start = subscription.current_period_start;
    const current_period_end = subscription.current_period_end;

    const items = subscription.items.data;

    if (items.length > 1) {
      return {
        error: {
          message:
            "Something weird happend. Only single item per subscriptions is allowed",
          details: {
            event,
          },
        },
      };
    }

    const item = items[0];

    const price = item.price;

    const productId = price.product.toString();
    let product: Stripe.Product;

    try {
      product = await this.stripe.products.retrieve(productId);
    } catch (error) {
      return {
        error: {
          message: "Failed to retrieve product by id",
          details: {
            productId,
            stripeError: error,
          },
        },
      };
    }

    const { data: profile, error: getProfileError } = await supabase
      .from("profiles")
      .select("id")
      .eq("customer_id", customerId)
      .single();

    if (getProfileError) {
      return {
        error: {
          message: "Failed to get profile by 'customer_id'",
          details: {
            customerId,
            ...getProfileError,
          },
        },
      };
    }

    const { id: user_id } = profile;

    const dto: UpsertSubscriptionDto = {
      subscription_id: subscription.id,
      status,
      user_id,
      plan_name: product.metadata.plan_name,
      additional_appliances:
        subscription_metadata.additionalAppliances.split(";") ?? null,
      current_period_start,
      current_period_end,
    };

    const { data: updatedSubscription, error: updateSubscriptionError } =
      await supabase
        .from("plan_subscriptions")
        .update(dto)
        .eq("subscription_id", subscription.id)
        .single();

    if (updateSubscriptionError) {
      return {
        error: {
          message: "Failed to insert subscription",
          details: updateSubscriptionError,
        },
      };
    }

    return {
      data: {
        subscription: updatedSubscription,
      },
    };
  }

  async handleSubscriptionDeleted(
    event: Stripe.CustomerSubscriptionDeletedEvent
  ): Promise<RefinedServiceResponse> {
    console.log("handleSubscriptionDeleted");
    console.log(event);

    const subscription = event.data.object;

    const customerId = subscription.customer.toString();
    const status = subscription.status;
    const subscription_metadata = subscription.metadata;
    const current_period_start = subscription.current_period_start;
    const current_period_end = subscription.current_period_end;

    const items = subscription.items.data;

    if (items.length > 1) {
      return {
        error: {
          message:
            "Something weird happend. Only single item per subscriptions is allowed",
          details: {
            event,
          },
        },
      };
    }

    const item = items[0];

    const price = item.price;

    const productId = price.product.toString();
    let product: Stripe.Product;

    try {
      product = await this.stripe.products.retrieve(productId);
    } catch (error) {
      return {
        error: {
          message: "Failed to retrieve product by id",
          details: {
            productId,
            stripeError: error,
          },
        },
      };
    }

    const { data: profile, error: getProfileError } = await supabase
      .from("profiles")
      .select("id")
      .eq("customer_id", customerId)
      .single();

    if (getProfileError) {
      return {
        error: {
          message: "Failed to get profile by 'customer_id'",
          details: {
            customerId,
            ...getProfileError,
          },
        },
      };
    }

    const { id: user_id } = profile;

    const dto: UpsertSubscriptionDto = {
      subscription_id: subscription.id,
      status,
      user_id,
      plan_name: product.metadata.plan_name,
      additional_appliances:
        subscription_metadata.additionalAppliances.split(";") ?? null,
      current_period_start,
      current_period_end,
    };

    const { data: updatedSubscription, error: updateSubscriptionError } =
      await supabase
        .from("plan_subscriptions")
        .update(dto)
        .eq("subscription_id", subscription.id)
        .single();

    if (updateSubscriptionError) {
      return {
        error: {
          message: "Failed to insert subscription",
          details: updateSubscriptionError,
        },
      };
    }

    return {
      data: {
        subscription: updatedSubscription,
      },
    };
  }
}
