import { Stripe } from "npm:stripe@15.3.0";
import { RefinedServiceResponse } from "../_shared/common-types.ts";
import { StripeEventHandler } from "./event-handlers/event-handler.ts";
import { SubscriptionsEventHandler } from "./event-handlers/subscriptions.ts";
import { InvoicesEventHandler } from "./event-handlers/invoice.ts";

export class StripeWebhookHandler {
  private stripe: Stripe;
  private secretKey: string;
  private signingSecret: string;

  constructor(secretKey: string, signingSecret: string) {
    this.secretKey = secretKey;
    this.signingSecret = signingSecret;
    this.stripe = new Stripe(this.secretKey);
  }

  async validateSignature(
    request: Request
  ): Promise<RefinedServiceResponse<Stripe.Event>> {
    const signature = request.headers.get("Stripe-Signature");

    if (!signature) {
      return {
        error: {
          message: "Failed to get Stripe Signature header",
        },
      };
    }

    const body = await request.text();
    let receivedEvent;

    try {
      receivedEvent = await this.stripe.webhooks.constructEventAsync(
        body,
        signature,
        this.signingSecret,
        undefined
      );
    } catch (err) {
      return {
        error: {
          message: "Failed to recieve event",
          details: err.message,
        },
      };
    }

    // Secondly, we use this event to query the Stripe API in order to avoid
    // handling any forged event. If available, we use the idempotency key.
    const requestOptions =
      receivedEvent.request && receivedEvent.request.idempotency_key
        ? {
            idempotencyKey: receivedEvent.request.idempotency_key,
          }
        : {};

    try {
      const retrievedEvent: Stripe.Event = await this.stripe.events.retrieve(
        receivedEvent.id,
        requestOptions
      );

      return {
        data: retrievedEvent,
      };
    } catch (err) {
      return {
        error: {
          message: "Failed to retrieve event",
          details: {
            eventId: receivedEvent.id,
            requestOptions,
            stripeError: err.message,
          },
        },
      };
    }
  }

  getHandler(event: Stripe.Event): RefinedServiceResponse<StripeEventHandler> {
    switch (event.type) {
      case "customer.subscription.created":
      case "customer.subscription.updated":
      case "customer.subscription.deleted":
        return {
          data: new SubscriptionsEventHandler(this.secretKey),
        };

      case "invoice.paid":
      case "invoice.payment_failed":
        return {
          data: new InvoicesEventHandler(this.secretKey),
        };

      default:
        return {
          error: {
            message: "Unsupported event type. Failed to get proper handler",
            details: {
              event,
            },
          },
        };
    }
  }
}
