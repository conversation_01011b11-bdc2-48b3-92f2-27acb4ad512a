import { env } from "../_shared/env.ts";
import { StripeWebhookHandler } from "./webhook-handler.ts";

const responseInit: ResponseInit = {
  status: 200,
  headers: { "Content-Type": "application/json" },
};

// I decided to not use Stripe Webhooks for this project,
// because we simply don't need them in our case

// Stripe is a single point of proof, to protect us from de-synced data we just query Stripe on it's own
// Because we don't need constant information about current subscription status in our DB - we won't overuse Stripe API

// We will store user's 'customer_id' in Zoho - this way admins can check payment information
// directly from Stripe Dashboard, if they need to

Deno.serve(async (req: Request) => {
  const webhookHandler = new StripeWebhookHandler(
    env.STRIPE_SECRET_KEY,
    env.STRIPE_SIGNING_SECRET
  );

  const { data: event, error: signatureValidationError } =
    await webhookHandler.validateSignature(req);

  if (signatureValidationError) {
    responseInit.status = 400;
    return new Response(JSON.stringify(signatureValidationError), responseInit);
  }

  const { data: handler, error: getEventHandlerError } =
    webhookHandler.getHandler(event);

  if (getEventHandlerError) {
    responseInit.status = 400;
    return new Response(JSON.stringify(getEventHandlerError), responseInit);
  }

  const handleEventResponse = await handler.handleEvent(event);

  console.log("Handle event result: ", handleEventResponse);

  if (handleEventResponse.error) {
    responseInit.status = 400;
    return new Response(
      JSON.stringify(handleEventResponse.error),
      responseInit
    );
  }

  return new Response(JSON.stringify(handleEventResponse.data), responseInit);
});
