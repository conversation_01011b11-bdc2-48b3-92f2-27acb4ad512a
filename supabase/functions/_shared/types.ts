export type CommonAnswer = "Yes" | "No" | "Not sure";

export type ZohoMember = {
  id?: string;
  address_details: string;
  airing_cupboard_cylinder: CommonAnswer;
  any_cover: CommonAnswer;
  bathrooms_count: number;
  boiler_age: string;
  boiler_fuel_source: string;
  boiler_serviced_recently: CommonAnswer;
  carbon_monoxide_alarm: string;
  date_of_birth: string;
  Email: string;
  full_name: string;
  home_ownership_status: string;
  mobile_number: string;
  post_code: string;
  power_flush_carried_out: string;
  property_type: string;
  radiators_count: number;
  residential_status: string;
  smart_thermostat: CommonAnswer;
  tanks_in_loft: CommonAnswer;
  thermostat_radiators_valves: CommonAnswer;
  userId: string;
};

export type ZohoModuleName =
  | "Members"
  | "Careers"
  | "Bookings"
  | "withdrawal_requests";

export type DBOperation = "UPDATE" | "INSERT" | "DELETE";

export type TableName = "profiles" | "withdrawal_requests";
