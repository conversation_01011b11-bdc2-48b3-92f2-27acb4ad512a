import { z } from "https://deno.land/x/zod@v3.22.4/mod.ts";

const envSchema = z.object({
  SUPABASE_URL: z.string(),
  SUPABASE_SERVICE_ROLE_KEY: z.string(),
  SUPABASE_ANON_KEY: z.string(),
  ZOHO_ACCOUNTS_URL: z.string(),
  ZOHO_API_URL: z.string(),
  ZOHO_REFRESH_TOKEN: z.string(),
  ZOHO_CLIENT_ID: z.string(),
  ZOHO_CLIENT_SECRET: z.string(),
  STRIPE_PUBLIC_KEY: z.string(),
  STRIPE_SECRET_KEY: z.string(),
  STRIPE_SIGNING_SECRET: z.string(),
  ORIGIN_URL: z.string(),
  SENDGRID_API_KEY: z.string(),
  SENDGRID_SENDER_EMAIL: z.string(),
});

const parsedResults = envSchema.safeParse(Deno.env.toObject());

if (!parsedResults.success) {
  console.error("Environment validation failed:", parsedResults.error);
  // @ts-ignore: Deno is available in Supabase Edge Functions
  console.error("Available environment variables:", Object.keys(Deno.env.toObject()));
  throw new Error(`Environment validation failed: ${parsedResults.error.message}`);
}

export const env = parsedResults.data;
