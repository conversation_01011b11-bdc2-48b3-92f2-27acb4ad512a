import { env } from "./env.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2.39.7";
import { Context, Next, Request } from "https://deno.land/x/oak@v14.0.0/mod.ts";

export const getAuthorizationToken = (request: Request): string | null => {
  const headers = request.headers;
  const authorizationHeader = headers.get("Authorization");

  return authorizationHeader ?? null;
};

export const OptionallyAuthenticated = async (context: Context, next: Next) => {
  console.log("Optionally Authenticated middleware");
  const token = getAuthorizationToken(context.request);

  context.state.authorizationToken = token;

  await next();
};

export const EnsureAuthenticated = async (context: Context, next: Next) => {
  console.log("Ensure Authenticated middleware");

  const token = getAuthorizationToken(context.request);

  if (!token) {
    context.response.status = 401;
    context.response.body = {
      status: 401,
      error: {
        message: "Request to this endpoint must be authenticated",
      },
    };

    return;
  }

  console.log("Token: ", token);
  console.log({
    supabaseUrl: env.SUPABASE_URL,
    key: env.SUPABASE_SERVICE_ROLE_KEY,
  });

  const supabase = createClient(
    env.SUPABASE_URL,
    env.SUPABASE_SERVICE_ROLE_KEY,
    {
      global: {
        headers: {
          Authorization: token,
        },
      },
    }
  );

  const { data: getUserData, error: getUserError } =
    await supabase.auth.getUser();

  console.log(getUserData, getUserError);

  if (getUserError) {
    console.log(getUserError);
    context.response.body = {
      error: { message: "Failed to get current user" },
    };
    context.response.status = 403;
    return;
  }

  const { id } = getUserData.user;

  const { data: profile, error: getProfileError } = await supabase
    .from("profiles")
    .select("status")
    .match({ id })
    .single();

  console.log(profile, getProfileError);

  if (getProfileError) {
    console.log(getProfileError);
    context.response.body = {
      error: { message: "Failed to get current user profile" },
    };
    context.response.status = 403;
    return;
  }

  const { status } = profile;

  switch (status) {
    case "deleted": {
      context.response.status = 403;
      context.response.body = {
        status: 403,
        error: {
          message: "User is deleted",
          details: {
            action: "sign-out",
          },
        },
      };

      return;
    }
  }

  context.state.authorizationToken = token;

  console.log("Set state token");
  await next();
};
