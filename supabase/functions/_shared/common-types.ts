import { isDeepStrictEqual } from "https://deno.land/std@0.163.0/node/internal/util/comparisons.ts";

export function areObjectsEqual(a: object, b: object): boolean {
  return isDeepStrictEqual(a, b);
}

export const RESPONSE_INIT: ResponseInit = {
  headers: {
    "Access-Control-Allow-Origin": "*",
    "Access-Control-Allow-Headers":
      "authorization, x-client-info, apikey, content-type",
    "Access-Control-Allow-Methods": "GET, POST, OPTIONS, DELETE, PATCH",
    "content-type": "application/json",
  },
};

export type ServiceResponse = {
  data?: any;
  status: number;
  error?: {
    message: string;
    details?: any;
  };
};

export type RefinedServiceResponse<T = any> =
  | {
      data: T;
      error?: null;
    }
  | {
      data?: null;
      error: {
        message: string;
        details?: any;
      };
    };
