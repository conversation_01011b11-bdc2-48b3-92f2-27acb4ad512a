import { env } from "../env.ts";
import { Stripe } from "npm:stripe@15.3.0";
import { RefinedServiceResponse } from "../common-types.ts";
import { SupabaseClient } from "https://esm.sh/@supabase/supabase-js@2.39.7";
import {
  CancelSubscriptionDto,
  CreateCheckoutSessionDto,
  CreateCheckoutSessionResponse,
  CreateStripeCustomerDto,
  CustomerSubscription,
  RefinedStripePrice,
  SubscriptionPlanMetadata,
  ADDITIONAL_APPLIENCE,
  UpdateSubscriptionDto,
  RestoreSubscriptionDto,
} from "./types.ts";

// Insufficient funds:
// 4000000000009995

// All good:
// 42424242424242

class StripeService {
  private stripe: Stripe;

  constructor(secretKey: string) {
    this.stripe = new Stripe(secretKey);
  }

  async getSubscriptionPlans(): Promise<RefinedServiceResponse> {
    const { data } = await this.stripe.prices.list({
      expand: ["data.product"],
      active: true,
    });

    const prices: RefinedStripePrice[] = data.map((price) => {
      const product = price.product.valueOf() as Stripe.Product;

      return {
        id: price.id,
        active: price.active,
        currency: price.currency,
        metadata: price.metadata,
        type: price.type,
        unit_amount: price.unit_amount,
        unit_amount_decimal: price.unit_amount_decimal,
        product: {
          id: product.id,
          name: product.name,
          description: product.description,
          active: product.active,
          metadata: product.metadata,
        },
        recurring: {
          inverval: price.recurring?.interval,
          interval_count: price.recurring?.interval_count,
        },
      };
    });

    const additionalAppliances = [
      {
        name: ADDITIONAL_APPLIENCE.GAS_COOKER_HOB,
        recurring_interval: "month",
        unit_amount: 2.5 * 100,
        unit_amount_decimal: "2500",
      },
      {
        name: ADDITIONAL_APPLIENCE.GAS_COOKER_HOB,
        recurring_interval: "year",
        unit_amount: 28.5 * 100,
        unit_amount_decimal: "25800",
      },
      {
        name: ADDITIONAL_APPLIENCE.GAS_FIRE,
        recurring_interval: "month",
        unit_amount: 2.5 * 100,
        unit_amount_decimal: "2500",
      },
      {
        name: ADDITIONAL_APPLIENCE.GAS_FIRE,
        recurring_interval: "year",
        unit_amount: 28.5 * 100,
        unit_amount_decimal: "25800",
      },
      {
        name: ADDITIONAL_APPLIENCE.GAS_OVEN,
        recurring_interval: "month",
        unit_amount: 2.5 * 100,
        unit_amount_decimal: "2500",
      },
      {
        name: ADDITIONAL_APPLIENCE.GAS_OVEN,
        recurring_interval: "year",
        unit_amount: 28.5 * 100,
        unit_amount_decimal: "25800",
      },
    ];

    return {
      data: {
        prices,
        additionalAppliances,
      },
    };
  }

  async createCustomer(
    dto: CreateStripeCustomerDto
  ): Promise<RefinedServiceResponse<Stripe.Response<Stripe.Customer>>> {
    try {
      const customer = await this.stripe.customers.create({
        email: dto.email,
        name: dto.full_name,
        metadata: {
          user_id: dto.user_id,
          member_id: dto.member_id,
          zoho_record_id: dto.zoho_record_id,
        },
      });

      return {
        data: customer,
      };
    } catch (error) {
      return {
        error: {
          message: "Failed to create customer",
          details: error,
        },
      };
    }
  }

  async getOrCreateCustomer(
    supabase: SupabaseClient
  ): Promise<RefinedServiceResponse<{ userId: string; customerId: string }>> {
    const { data: getUserData, error: getUserError } =
      await supabase.auth.getUser();

    if (getUserError) {
      return {
        error: {
          message: "Failed to get current user",
          details: getUserError,
        },
      };
    }

    const { user } = getUserData;

    const { data: profile, error: getProfileError } = await supabase
      .from("profiles")
      .select(
        "id, full_name, customer_id, ref_id, address_details, zohoRecordId"
      )
      .match({ id: user.id })
      .single();

    if (getProfileError) {
      return {
        error: {
          message: "Failed to get user profile",
          details: getProfileError,
        },
      };
    }

    let customerId = profile.customer_id;

    if (!customerId) {
      const { data: getUserData, error: getUserError } =
        await supabase.auth.getUser();

      if (getUserError) {
        return {
          error: {
            message:
              "Failed to get current user while creating Stripe customer",
            details: getUserError,
          },
        };
      }

      const email = getUserData.user.email;

      if (!email) {
        return {
          error: {
            message:
              "User does not have an email attached to account - failed to create Stripe Customer",
          },
        };
      }

      const dto: CreateStripeCustomerDto = {
        email,
        full_name: profile.full_name,
        user_id: profile.id,
        member_id: profile.ref_id,
        zoho_record_id: profile.zohoRecordId,
      };

      const { data: customer, error: createCustomerError } =
        await this.createCustomer(dto);

      if (createCustomerError) {
        return {
          error: createCustomerError,
        };
      }

      const { error: updateProfileError } = await supabase
        .from("profiles")
        .update({ customer_id: customer.id })
        .match({ id: profile.id })
        .single();

      if (updateProfileError) {
        return {
          error: updateProfileError,
        };
      }

      customerId = customer.id;
    }

    return {
      data: {
        userId: profile.id,
        customerId,
      },
    };
  }

  async getCustomerActiveSubscriptions(
    supabase: SupabaseClient
  ): Promise<RefinedServiceResponse<CustomerSubscription[]>> {
    const { data: getOrCreateCustomerData, error: getOrCreateCustomerError } =
      await this.getOrCreateCustomer(supabase);

    if (getOrCreateCustomerError) {
      return {
        error: getOrCreateCustomerError,
      };
    }

    const { customerId } = getOrCreateCustomerData;

    console.log("GetCustomerActiveSubscriptions: ", customerId);

    const subscriptions = await this.stripe.subscriptions.list({
      customer: customerId,
      expand: ["data.plan.product"],
    });

    const refinedSubscriptions: CustomerSubscription[] = subscriptions.data.map(
      (subscription: Stripe.Subscription) => {
        const metadata: SubscriptionPlanMetadata = subscription.metadata;

        // I hate it
        const plan: Partial<Stripe.Plan> = (subscription as any).plan;

        return {
          id: subscription.id,
          current_period_end: subscription.current_period_end,
          current_period_start: subscription.current_period_start,
          cancel_at: subscription.cancel_at,
          cancel_at_period_end: subscription.cancel_at_period_end,
          status: subscription.status,
          items: subscription.items.data,
          metadata,
          plan,
        };
      }
    );

    return {
      data: refinedSubscriptions,
    };
  }

  async cancelSubscription(
    dto: CancelSubscriptionDto,
    supabase: SupabaseClient
  ): Promise<RefinedServiceResponse> {
    try {
      const subscription = await this.stripe.subscriptions.retrieve(
        dto.subscriptionId
      );

      if (subscription.cancel_at_period_end) {
        return {
          error: {
            message: "Subscription is already cancelled",
            details: {
              subscription,
            },
          },
        };
      }

      const { data, error: getCustomerIdError } =
        await this.getOrCreateCustomer(supabase);

      if (getCustomerIdError) {
        return {
          error: getCustomerIdError,
        };
      }

      const { customerId } = data;

      if (customerId !== subscription.customer.toString()) {
        return {
          error: {
            message:
              "This user does not have a permission to cancel this subscription",
            details: {
              customerId,
              subscription,
            },
          },
        };
      }

      const canceledSubscription = await this.stripe.subscriptions.update(
        dto.subscriptionId,
        {
          cancel_at_period_end: true,
        }
      );

      return {
        data: canceledSubscription,
      };
    } catch (error) {
      return {
        error: {
          message: "Failed to cancel subscription",
          details: error,
        },
      };
    }
  }

  async cancelCurrentSubscription(
    supabase: SupabaseClient
  ): Promise<RefinedServiceResponse> {
    const { data: subscriptions, error: getCustomerActiveSubscriptionsError } =
      await this.getCustomerActiveSubscriptions(supabase);

    if (getCustomerActiveSubscriptionsError) {
      console.log("Failed to get current customer's active subscriptions");

      return {
        error: {
          message: "Failed to get current customer's active subscriptions",
          details: getCustomerActiveSubscriptionsError,
        },
      };
    }

    const activeSubscriptions = subscriptions.filter(
      (s) => s.status === "active"
    );

    if (activeSubscriptions.length === 0) {
      console.log("User does not have any active subscriptions");

      return {
        data: {
          message: "User does not have any active subscriptions",
        },
      };
    }

    if (activeSubscriptions.length > 1) {
      console.log(
        "User can't have more than one active subscriptions. Contact administration"
      );

      return {
        error: {
          message:
            "User can't have more than one active subscriptions. Contact administration",
        },
      };
    }
    const subscription = subscriptions[0];

    const { data: cancelledSubscription, error: cancelSubscriptionError } =
      await this.cancelSubscription(
        {
          subscriptionId: subscription.id,
        },
        supabase
      );

    if (cancelSubscriptionError) {
      console.log("Failed to cancel subscription", cancelSubscriptionError);
      return {
        error: cancelSubscriptionError,
      };
    }

    console.log("Subscription is cancelled", cancelledSubscription);

    return {
      data: {
        message: "Subscription is cancelled",
        subscription: cancelledSubscription,
      },
    };
  }

  async restoreSubscription(
    dto: RestoreSubscriptionDto,
    supabase: SupabaseClient
  ) {
    try {
      const subscription = await this.stripe.subscriptions.retrieve(
        dto.subscriptionId
      );

      if (!subscription.cancel_at_period_end) {
        return {
          error: {
            message: "Subscription is not cancelled",
            details: {
              subscription,
            },
          },
        };
      }

      const { data, error: getCustomerIdError } =
        await this.getOrCreateCustomer(supabase);

      if (getCustomerIdError) {
        return {
          error: getCustomerIdError,
        };
      }

      const { customerId } = data;

      if (customerId !== subscription.customer.toString()) {
        return {
          error: {
            message:
              "This user does not have a permission to cancel this subscription",
            details: {
              customerId,
              subscription,
            },
          },
        };
      }

      const restoredSubscription = await this.stripe.subscriptions.update(
        dto.subscriptionId,
        {
          cancel_at_period_end: false,
        }
      );

      return {
        data: restoredSubscription,
      };
    } catch (error) {
      return {
        error: {
          message: "Failed to cancel subscription",
          details: error,
        },
      };
    }
  }

  async createCheckoutSession(
    dto: CreateCheckoutSessionDto,
    supabase: SupabaseClient
  ): Promise<RefinedServiceResponse<CreateCheckoutSessionResponse>> {
    const { data: getOrCreateCustomerData, error: getOrCreateCustomerError } =
      await this.getOrCreateCustomer(supabase);

    if (getOrCreateCustomerError) {
      return {
        error: getOrCreateCustomerError,
      };
    }

    const { data: subscriptions, error: getCustomerActiveSubscriptionsError } =
      await this.getCustomerActiveSubscriptions(supabase);

    if (getCustomerActiveSubscriptionsError) {
      return {
        error: getCustomerActiveSubscriptionsError,
      };
    }

    if (subscriptions.length) {
      return {
        error: {
          message:
            "You already have an active subscription, consider cancelling or updading current subscription",
          details: subscriptions,
        },
      };
    }

    const { userId, customerId } = getOrCreateCustomerData;

    try {
      let quantity = 1;

      const price = await this.stripe.prices.retrieve(dto.priceId);
      const product = await this.stripe.products.retrieve(
        price.product.toString()
      );

      const plan_name = product.metadata.plan_name;

      const metadata: Record<string, string> = {
        userId,
        plan_name: product.metadata.plan_name,
      };

      if (dto.additionalAppliances) {
        if (plan_name !== "peace_landlord") {
          return {
            error: {
              message:
                "You can add additional appliances only to Peace Plan (Landlord)",
              details: {
                plan_name,
              },
            },
          };
        }

        quantity += dto.additionalAppliances.length;
        metadata.additionalAppliances = dto.additionalAppliances.join(";");
      }

      // In production mode, use:
      // ui_mode: embedded
      // cancel/success urls

      // In development mode (Postman), use:
      // ui_mode: 'hosted'
      // return_url: '***'

      const session = await this.stripe.checkout.sessions.create({
        ui_mode: "embedded",
        // ui_mode: "hosted",
        line_items: [
          {
            price: dto.priceId,
            quantity,
          },
        ],
        subscription_data: {
          metadata,
        },
        // saved_payment_method_options: {
        //   allow_redisplay_filters: ["always"],
        // },
        customer: customerId,
        mode: "subscription",
        // Cancel URL is not supported in ui_mode: 'embeded'
        // cancel_url: `${env.ORIGIN_URL}/subscriptions/purchases?result=cancelled&session_id={CHECKOUT_SESSION_ID}`,
        // success_url: `${env.ORIGIN_URL}/subscriptions/purchases?result=success&session_id={CHECKOUT_SESSION_ID}`,
        redirect_on_completion: "never",
        // return_url: `${env.ORIGIN_URL}/subscriptions/purchases?result=success&session_id={CHECKOUT_SESSION_ID}`,
      });

      return {
        data: {
          url: session.url,
          clientSecret: session.client_secret,
        },
      };
    } catch (error) {
      console.error(error);
      return {
        error: {
          message: "Failed to create checkout session. Check logs",
          details: error,
        },
      };
    }
  }

  async getCustomerBillingPortal(
    supabase: SupabaseClient
  ): Promise<RefinedServiceResponse> {
    try {
      const { data: getOrCreateCustomerData, error: getOrCreateCustomerError } =
        await this.getOrCreateCustomer(supabase);

      if (getOrCreateCustomerError) {
        return {
          error: getOrCreateCustomerError,
        };
      }

      const portal = await this.stripe.billingPortal.sessions.create({
        customer: getOrCreateCustomerData.customerId,
      });

      return {
        data: {
          redirectUrl: portal.url,
        },
      };
    } catch (error) {
      return {
        error: {
          message: "Failed to get customer's portal",
          details: error,
        },
      };
    }
  }

  async updateCurrentSubscription(
    dto: UpdateSubscriptionDto,
    supabase: SupabaseClient
  ): Promise<RefinedServiceResponse> {
    const { data: getOrCreateCustomerData, error: getOrCreateCustomerError } =
      await this.getOrCreateCustomer(supabase);

    if (getOrCreateCustomerError) {
      return {
        error: getOrCreateCustomerError,
      };
    }

    const { data: subscriptions, error: getCustomerActiveSubscriptionsError } =
      await this.getCustomerActiveSubscriptions(supabase);

    if (getCustomerActiveSubscriptionsError) {
      return {
        error: getCustomerActiveSubscriptionsError,
      };
    }

    if (!subscriptions.length) {
      return {
        error: {
          message: "User does not have any active subscriptions",
        },
      };
    }

    // Is this ok?
    const subscription = subscriptions[0];
    const item = subscription.items[0];

    try {
      let quantity = 1;

      const price = await this.stripe.prices.retrieve(dto.priceId);
      const product = await this.stripe.products.retrieve(
        price.product.toString()
      );

      const plan_name = product.metadata.plan_name;

      const metadata: Record<string, string> = {
        userId: getOrCreateCustomerData.userId,
        plan_name: product.metadata.plan_name,
      };

      if (dto.additionalAppliances) {
        if (plan_name !== "peace_landlord") {
          return {
            error: {
              message:
                "You can add additional appliances only to Peace Plan (Landlord)",
              details: {
                plan_name,
              },
            },
          };
        }

        quantity += dto.additionalAppliances.length;
        metadata.additionalAppliances = dto.additionalAppliances.join(";");
      }

      const updatedSubscription = await this.stripe.subscriptions.update(
        subscription.id,
        {
          cancel_at_period_end: false,
          proration_behavior: "always_invoice",
          items: [
            {
              id: item.id,
              price: dto.priceId,
              quantity,
            },
          ],
          metadata,
        }
      );

      if (updatedSubscription.status !== "active") {
        return {
          error: {
            message:
              "Subscription was updated, but you might need to do some action to finalize update. Please, check your inbox and Customer Portal",
            details: {
              subscription: updatedSubscription,
            },
          },
        };
      }

      return {
        data: updatedSubscription,
      };
    } catch (error) {
      return {
        error: {
          message: "Failed to update subscription",
          details: {
            error,
          },
        },
      };
    }
  }
}

export const stripeService: StripeService = new StripeService(
  env.STRIPE_SECRET_KEY
);
