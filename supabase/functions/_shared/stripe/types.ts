import { Stripe } from "npm:stripe@15.3.0";
import { z } from "https://deno.land/x/zod@v3.22.4/mod.ts";

export enum ADDITIONAL_APPLIENCE {
  GAS_COOKER_HOB = "gas_cooker_hob",
  GAS_FIRE = "gas_fire",
  GAS_OVEN = "gas_oven",
}

export const CreateCheckoutSessionDtoSchema = z.object({
  priceId: z.string(),
  additionalAppliances: z
    .enum([
      ADDITIONAL_APPLIENCE.GAS_COOKER_HOB,
      ADDITIONAL_APPLIENCE.GAS_FIRE,
      ADDITIONAL_APPLIENCE.GAS_OVEN,
    ])
    .array()
    .optional(),
});

export type CreateCheckoutSessionDto = z.infer<
  typeof CreateCheckoutSessionDtoSchema
>;

export const CancelSubscriptionDtoSchema = z.object({
  subscriptionId: z.string(),
});

export type CancelSubscriptionDto = z.infer<typeof CancelSubscriptionDtoSchema>;

export const RestoreSubscriptionDtoSchema = z.object({
  subscriptionId: z.string(),
});

export type RestoreSubscriptionDto = z.infer<
  typeof RestoreSubscriptionDtoSchema
>;

export const UpdateSubscriptionDtoSchema = z.object({
  priceId: z.string(),
  additionalAppliances: z
    .enum([
      ADDITIONAL_APPLIENCE.GAS_COOKER_HOB,
      ADDITIONAL_APPLIENCE.GAS_FIRE,
      ADDITIONAL_APPLIENCE.GAS_OVEN,
    ])
    .array()
    .optional(),
});

export type UpdateSubscriptionDto = z.infer<typeof UpdateSubscriptionDtoSchema>;
export type StripeProductPrice = {
  id: string;
  recurring: {
    interval?: string;
    intervalCount?: number;
  };
};

export type RefinedStripePrice = {
  id: string;
  active?: boolean;
  currency?: string;
  metadata?: Record<string, unknown>;
  type?: string;
  unit_amount?: number | null;
  unit_amount_decimal?: string | null;
  product?: {
    id: string;
    active: boolean;
    name?: string;
    description?: string | null;
    metadata?: Record<string, unknown>;
  };
  recurring: {
    inverval?: string;
    interval_count?: number;
  };
};
export type CreateCheckoutSessionResponse = {
  clientSecret?: string | null;
  url?: string | null;
};

export type SubscriptionPlanMetadata = {
  userId?: string;
  plan_name?: string;
  additionalAppliances?: string;
};

export type CustomerSubscription = {
  id: string;
  status: string;
  current_period_end: number;
  current_period_start: number;
  metadata: SubscriptionPlanMetadata;
  items: Stripe.SubscriptionItem[];
  plan: Partial<Stripe.Plan>;
};

export type CreateStripeCustomerDto = {
  user_id: string;
  full_name: string;
  email: string;
  member_id: string;
  zoho_record_id: string;
};
