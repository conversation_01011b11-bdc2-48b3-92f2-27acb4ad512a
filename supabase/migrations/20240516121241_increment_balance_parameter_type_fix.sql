set check_function_bodies = off;

CREATE OR R<PERSON>LACE FUNCTION public.increment_available_balance(value integer, user_id uuid, transaction_id integer)
 RETURNS void
 LANGUAGE plpgsql
AS $function$
BEGIN
    -- Check if the transaction_id exists and is not already incremented
    IF EXISTS (
        SELECT 1
        FROM public.transactions
        WHERE id = transaction_id AND is_incremented = FALSE
    ) THEN
        -- Increment available_balance
        UPDATE public.profiles
        SET available_balance = available_balance + value
        WHERE id = user_id;

        -- Update the transaction record to mark it as incremented
        UPDATE public.transactions
        SET is_incremented = TRUE
        WHERE id = transaction_id;
        
        -- Optionally, you can do additional logging or operations here
    END IF;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.handle_new_user()
 RETURNS trigger
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$DECLARE
  full_name_param TEXT;
  first_param TEXT;
  last_param TEXT;
  ref_id_param TEXT;
  ref_id_exists BOOLEAN;
BEGIN
  full_name_param = new.raw_user_meta_data->>'full_name';

  IF full_name_param ~ '^\w+\s\w+$' THEN
    -- Split full_name into first and last names
    first_param := initcap(split_part(full_name_param, ' ', 1)); -- Uppercase first letter of each word
    last_param := initcap(split_part(full_name_param, ' ', 2));  -- Uppercase first letter of each word
  ELSE
    RAISE EXCEPTION 'Invalid full name format. Please provide two words separated by a space.';
  END IF;

  LOOP
    -- Extract first characters from first and last names
    ref_id_param := upper(substr(first_param, 1, 1)) || upper(substr(last_param, 1, 1));

    -- Generate 6 random digits
    ref_id_param := ref_id_param || lpad(trunc(random() * 1000000)::text, 6, '0');

    -- Add "PP" to the ref_id_param
    ref_id_param := ref_id_param || 'PP';

    -- Check if ref_id_param already exists in the table
    SELECT EXISTS(SELECT 1 FROM public.profiles WHERE ref_id = ref_id_param) INTO ref_id_exists;

    -- If ref_id_param is unique, exit the loop
    EXIT WHEN NOT ref_id_exists;
  END LOOP;

  -- Check if member_id is defined
  IF new.raw_user_meta_data ? 'member_id' THEN
    insert into public.profiles (id, full_name, ref_id, ref_by_id)
    values (
      new.id,
      new.raw_user_meta_data->>'full_name',
      ref_id_param,
      (new.raw_user_meta_data->>'member_id')::UUID
    );
  ELSE
    insert into public.profiles (id, full_name, ref_id)
    values (
      new.id,
      new.raw_user_meta_data->>'full_name',
      ref_id_param
    );
  END IF;

  return new;
END;
$function$
;


