CREATE TRIGGER profiles_operation_trigger AFTER INSERT OR UPDATE ON public.profiles FOR EACH ROW EXECUTE FUNCTION supabase_functions.http_request('https://zlldaikdinmwoxyqkxgc.supabase.co/functions/v1/zoho_sync', 'POST', '{"Content-type":"application/json","Authorization":"Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InpsbGRhaWtkaW5td294eXFreGdjIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTcwODA3MTkxMywiZXhwIjoyMDIzNjQ3OTEzfQ.W-HKZmL9CcxjtAtdOMaAQE0bFgmKY7l-tev-Xe5jnrk"}', '{}', '1000');
ALTER TABLE "public"."profiles" DISABLE TRIGGER "profiles_operation_trigger";

alter table "public"."profiles" alter column "ref_id" drop not null;

set check_function_bodies = off;

CREATE OR REPLACE FUNCTION public.handle_new_user()
 RETURNS trigger
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
begin
  insert into public.profiles (id, full_name, date_of_birth, mobile_number, post_code, address_details, residential_status, ref_id)
  values (
    new.id,
    new.raw_user_meta_data->>'full_name',
    (new.raw_user_meta_data->>'date_of_birth')::date,
    new.raw_user_meta_data->>'mobile_number',
    new.raw_user_meta_data->>'post_code',
    new.raw_user_meta_data->>'address_details',
    new.raw_user_meta_data->>'residential_status',
    new.raw_user_meta_data->>'ref_id'
    );
  return new;
end;
$function$
;