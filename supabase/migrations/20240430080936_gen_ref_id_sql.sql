set check_function_bodies = off;

CREATE OR REPLACE FUNCTION public.create_withdrawal_request(user_id uuid, amount_to_withdraw integer, sort_code text, account_number text, account_holder text)
 RETURNS integer
 LANGUAGE plpgsql
AS $function$

declare
  user_id_exist uuid;

begin
  select id
  into user_id_exist
  from public.profiles
  where id = user_id;

  -- If somehow passed user_id is unknown to database - return error code - 0
  if user_id_exist is null then
    return 0;
  end if;

  -- Insert withdrawal_request
  insert into public.withdrawal_requests (
    amount_to_withdraw,
    sort_code,
    account_number,
    account_holder,
    user_id
  ) values (
    amount_to_withdraw,
    sort_code,
    account_number,
    account_holder,
    user_id
  );

  -- Decrement from user's available_balance
  update public.profiles
  set available_balance = available_balance - amount_to_withdraw
  where id = user_id;

  return 1;
end;
$function$
;

CREATE OR REPLACE FUNCTION public.handle_new_user()
 RETURNS trigger
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$DECLARE
  full_name_param TEXT;
  first_param TEXT;
  last_param TEXT;
  ref_id_param TEXT;
  ref_id_exists BOOLEAN;
BEGIN
  full_name_param = new.raw_user_meta_data->>'full_name';

IF full_name_param ~ '^\w+\s\w+$' THEN
        -- Split full_name into first and last names
        first_param := initcap(split_part(full_name_param, ' ', 1)); -- Uppercase first letter of each word
        last_param := initcap(split_part(full_name_param, ' ', 2));  -- Uppercase first letter of each word
    ELSE
        RAISE EXCEPTION 'Invalid full name format. Please provide two words separated by a space.';
    END IF;

    LOOP
        -- Extract first characters from first and last names
        ref_id_param := upper(substr(first_param, 1, 1)) || upper(substr(last_param, 1, 1));

        -- Generate 6 random digits
        ref_id_param := ref_id_param || lpad(trunc(random() * 1000000)::text, 6, '0');

        -- Add "PP" to the ref_id_param
        ref_id_param := ref_id_param || 'PP';

        -- Check if ref_id_param already exists in the table
        SELECT EXISTS(SELECT 1 FROM public.profiles WHERE ref_id = ref_id_param) INTO ref_id_exists;

        -- If ref_id_param is unique, exit the loop
        EXIT WHEN NOT ref_id_exists;
    END LOOP;

  insert into public.profiles (id, full_name, date_of_birth, mobile_number, post_code, address_details, residential_status, ref_id)
  values (
    new.id,
    new.raw_user_meta_data->>'full_name',
    (new.raw_user_meta_data->>'date_of_birth')::date,
    new.raw_user_meta_data->>'mobile_number',
    new.raw_user_meta_data->>'post_code',
    new.raw_user_meta_data->>'address_details',
    new.raw_user_meta_data->>'residential_status',
    ref_id_param
    );
  return new;
end;$function$
;

CREATE OR REPLACE FUNCTION public.increment_available_balance(value integer, user_id uuid)
 RETURNS void
 LANGUAGE sql
AS $function$
  update public.profiles
  set available_balance = available_balance + value
  where id = user_id
$function$
;


