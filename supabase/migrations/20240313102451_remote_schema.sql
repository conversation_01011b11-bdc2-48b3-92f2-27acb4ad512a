set check_function_bodies = off;

CREATE OR REPLACE FUNCTION public.handle_new_user()
 <PERSON><PERSON><PERSON><PERSON> trigger
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
begin
  insert into public.profiles (id, full_name, date_of_birth, mobile_number, post_code, address_details, residential_status)
  values (
    new.id, 
    new.raw_user_meta_data->>'full_name', 
    (new.raw_user_meta_data->>'date_of_birth')::date, 
    new.raw_user_meta_data->>'mobile_number', 
    new.raw_user_meta_data->>'post_code', 
    new.raw_user_meta_data->>'address_details', 
    new.raw_user_meta_data->>'residential_status'
    );
  return new;
end;
$function$
;


