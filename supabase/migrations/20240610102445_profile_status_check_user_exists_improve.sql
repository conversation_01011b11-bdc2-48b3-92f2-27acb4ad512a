alter table "public"."profiles" add column "status" text default 'active'::text;

set check_function_bodies = off;

create type "public"."user_status" as ("user_id" uuid, "status" text);

DROP FUNCTION IF EXISTS public.check_user_exists(email_input text);

CREATE OR REPLACE FUNCTION public.check_user_exists(email_input text)
 RETURNS user_status
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
DECLARE
    result public.user_status;
BEGIN
    SELECT u.id, p.status
    INTO result
    FROM auth.users u
    LEFT JOIN public.profiles p ON u.id = p.id
    WHERE u.email = email_input;

    IF result.user_id IS NULL THEN
        RETURN (NULL, NULL)::public.user_status;
    ELSE
        RETURN result;
    END IF;
END;
$function$
;


