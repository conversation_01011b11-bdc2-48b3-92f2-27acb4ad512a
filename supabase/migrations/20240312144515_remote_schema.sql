drop trigger if exists "zoho_sync_wh" on "public"."profiles";

set check_function_bodies = off;

CREATE OR REPLACE FUNCTION public.handle_new_user()
 RETURNS trigger
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
begin
  insert into public.profiles (id, full_name, date_of_birth, mobile_number, post_code, address_details, residential_status, home_ownership_status, property_type, bathrooms_count, boiler_fuel_source, boiler_age, airing_cupboard_cylinder, tanks_in_loft, radiators_count, smart_thermostat, thermostat_radiators_valves, boiler_serviced_recently, carbon_monoxide_alarm, any_cover, power_flush_carried_out)
  values ( new.id, 
    new.raw_user_meta_data->>'full_name', 
    (new.raw_user_meta_data->>'date_of_birth')::date, 
    new.raw_user_meta_data->>'mobile_number', 
    new.raw_user_meta_data->>'post_code', 
    new.raw_user_meta_data->>'address_details', 
    new.raw_user_meta_data->>'residential_status',
    new.raw_user_meta_data->>'home_ownership_status',
    new.raw_user_meta_data->>'property_type',
    new.raw_user_meta_data->>'bathrooms_count',
    new.raw_user_meta_data->>'boiler_fuel_source',
    new.raw_user_meta_data->>'boiler_age',
    new.raw_user_meta_data->>'airing_cupboard_cylinder',
    new.raw_user_meta_data->>'tanks_in_loft',
    new.raw_user_meta_data->>'radiators_count',
    new.raw_user_meta_data->>'smart_thermostat',
    new.raw_user_meta_data->>'thermostat_radiators_valves',
    new.raw_user_meta_data->>'boiler_serviced_recently',
    new.raw_user_meta_data->>'carbon_monoxide_alarm',
    new.raw_user_meta_data->>'any_cover',
    new.raw_user_meta_data->>'power_flush_carried_out'
    );
  return new;
end;
$function$
;

CREATE TRIGGER profiles_operation_trigger AFTER INSERT OR UPDATE ON public.profiles FOR EACH ROW EXECUTE FUNCTION supabase_functions.http_request('https://zlldaikdinmwoxyqkxgc.supabase.co/functions/v1/zoho_sync', 'POST', '{"Content-type":"application/json","Authorization":"Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InpsbGRhaWtkaW5td294eXFreGdjIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTcwODA3MTkxMywiZXhwIjoyMDIzNjQ3OTEzfQ.W-HKZmL9CcxjtAtdOMaAQE0bFgmKY7l-tev-Xe5jnrk"}', '{}', '1000');


