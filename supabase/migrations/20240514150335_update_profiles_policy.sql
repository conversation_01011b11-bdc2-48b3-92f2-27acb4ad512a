drop policy "Select only own profile" on "public"."profiles";

set check_function_bodies = off;

CREATE OR REPLACE FUNCTION public.handle_new_user()
 RETURNS trigger
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$DECLARE
  full_name_param TEXT;
  first_param TEXT;
  last_param TEXT;
  ref_id_param TEXT;
  ref_id_exists BOOLEAN;
BEGIN
  full_name_param = new.raw_user_meta_data->>'full_name';

  IF full_name_param ~ '^\w+\s\w+$' THEN
    -- Split full_name into first and last names
    first_param := initcap(split_part(full_name_param, ' ', 1)); -- Uppercase first letter of each word
    last_param := initcap(split_part(full_name_param, ' ', 2));  -- Uppercase first letter of each word
  ELSE
    RAISE EXCEPTION 'Invalid full name format. Please provide two words separated by a space.';
  END IF;

  LOOP
    -- Extract first characters from first and last names
    ref_id_param := upper(substr(first_param, 1, 1)) || upper(substr(last_param, 1, 1));

    -- Generate 6 random digits
    ref_id_param := ref_id_param || lpad(trunc(random() * 1000000)::text, 6, '0');

    -- Add "PP" to the ref_id_param
    ref_id_param := ref_id_param || 'PP';

    -- Check if ref_id_param already exists in the table
    SELECT EXISTS(SELECT 1 FROM public.profiles WHERE ref_id = ref_id_param) INTO ref_id_exists;

    -- If ref_id_param is unique, exit the loop
    EXIT WHEN NOT ref_id_exists;
  END LOOP;

  -- Check if member_id is defined
  IF new.raw_user_meta_data ? 'member_id' THEN
    insert into public.profiles (id, full_name, ref_id, ref_by_id)
    values (
      new.id,
      new.raw_user_meta_data->>'full_name',
      ref_id_param,
      (new.raw_user_meta_data->>'member_id')::UUID
    );
  ELSE
    insert into public.profiles (id, full_name, ref_id)
    values (
      new.id,
      new.raw_user_meta_data->>'full_name',
      ref_id_param
    );
  END IF;

  return new;
END;
$function$
;

create policy "Select all profiles"
on "public"."profiles"
as permissive
for select
to authenticated
using (true);



