create table "public"."zohoAccessTokens" (
    "id" bigint generated by default as identity not null,
    "token" character varying,
    "expiresAt" timestamp without time zone
);


alter table "public"."zohoAccessTokens" enable row level security;

CREATE UNIQUE INDEX "zohoAccessTokens_pkey" ON public."zohoAccessTokens" USING btree (id);

alter table "public"."zohoAccessTokens" add constraint "zohoAccessTokens_pkey" PRIMARY KEY using index "zohoAccessTokens_pkey";

set check_function_bodies = off;

CREATE OR REPLACE FUNCTION public.handle_new_user()
 RETURNS trigger
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
begin
  insert into public.profiles (id, full_name, date_of_birth, mobile_number, post_code, address_details, residential_status)
  values ( new.id, 
    new.raw_user_meta_data->>'full_name', 
    (new.raw_user_meta_data->>'date_of_birth')::date, 
    new.raw_user_meta_data->>'mobile_number', 
    new.raw_user_meta_data->>'post_code', 
    new.raw_user_meta_data->>'address_details', 
    new.raw_user_meta_data->>'residential_status');
  return new;
end;
$function$
;

grant delete on table "public"."zohoAccessTokens" to "anon";

grant insert on table "public"."zohoAccessTokens" to "anon";

grant references on table "public"."zohoAccessTokens" to "anon";

grant select on table "public"."zohoAccessTokens" to "anon";

grant trigger on table "public"."zohoAccessTokens" to "anon";

grant truncate on table "public"."zohoAccessTokens" to "anon";

grant update on table "public"."zohoAccessTokens" to "anon";

grant delete on table "public"."zohoAccessTokens" to "authenticated";

grant insert on table "public"."zohoAccessTokens" to "authenticated";

grant references on table "public"."zohoAccessTokens" to "authenticated";

grant select on table "public"."zohoAccessTokens" to "authenticated";

grant trigger on table "public"."zohoAccessTokens" to "authenticated";

grant truncate on table "public"."zohoAccessTokens" to "authenticated";

grant update on table "public"."zohoAccessTokens" to "authenticated";

grant delete on table "public"."zohoAccessTokens" to "service_role";

grant insert on table "public"."zohoAccessTokens" to "service_role";

grant references on table "public"."zohoAccessTokens" to "service_role";

grant select on table "public"."zohoAccessTokens" to "service_role";

grant trigger on table "public"."zohoAccessTokens" to "service_role";

grant truncate on table "public"."zohoAccessTokens" to "service_role";

grant update on table "public"."zohoAccessTokens" to "service_role";


