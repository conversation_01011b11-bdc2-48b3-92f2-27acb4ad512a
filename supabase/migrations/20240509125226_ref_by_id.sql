drop trigger if exists "profiles_operation_trigger" on "public"."profiles";

drop policy "Users can insert their own profile." on "public"."profiles";

alter table "public"."profiles" add column "ref_by_id" uuid;

alter table "public"."profiles" add constraint "profiles_ref_by_id_fkey" FOREIGN KEY (ref_by_id) REFERENCES profiles(id) ON DELETE SET NULL not valid;

alter table "public"."profiles" validate constraint "profiles_ref_by_id_fkey";

create policy "Users can insert their own profile."
on "public"."profiles"
as permissive
for insert
to public
with check (( SELECT (auth.uid() = profiles.id)));


CREATE TRIGGER profiles_operation_trigger AFTER INSERT OR UPDATE ON public.profiles FOR EACH ROW EXECUTE FUNCTION supabase_functions.http_request('https://zlldaikdinmwoxyqkxgc.supabase.co/functions/v1/zoho_sync', 'POST', '{"Content-type":"application/json","Authorization":"Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InpsbGRhaWtkaW5td294eXFreGdjIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTcwODA3MTkxMywiZXhwIjoyMDIzNjQ3OTEzfQ.W-HKZmL9CcxjtAtdOMaAQE0bFgmKY7l-tev-Xe5jnrk"}', '{}', '1000');
ALTER TABLE "public"."profiles" DISABLE TRIGGER "profiles_operation_trigger";


