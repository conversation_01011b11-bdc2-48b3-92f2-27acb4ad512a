create table "public"."emergency_location_reviews" (
    "id" uuid not null default gen_random_uuid(),
    "author_name" text,
    "author_url" text,
    "language" text,
    "original_language" text,
    "profile_photo_url" text,
    "rating" smallint,
    "relative_time_description" text,
    "text" text,
    "time" bigint,
    "translated" boolean,
    "emergency_location_id" uuid
);


alter table "public"."emergency_location_reviews" enable row level security;

create table "public"."emergency_locations" (
    "id" uuid not null default gen_random_uuid(),
    "place_id" text,
    "uuid" text,
    "expire_at" timestamp without time zone,
    "created_at" timestamp with time zone not null default now()
);


alter table "public"."emergency_locations" enable row level security;

CREATE UNIQUE INDEX emergency_location_reviews_pkey ON public.emergency_location_reviews USING btree (id);

CREATE UNIQUE INDEX emergency_locations_pkey ON public.emergency_locations USING btree (id);

alter table "public"."emergency_location_reviews" add constraint "emergency_location_reviews_pkey" PRIMARY KEY using index "emergency_location_reviews_pkey";

alter table "public"."emergency_locations" add constraint "emergency_locations_pkey" PRIMARY KEY using index "emergency_locations_pkey";

alter table "public"."emergency_location_reviews" add constraint "emergency_location_reviews_emergency_location_id_fkey" FOREIGN KEY (emergency_location_id) REFERENCES emergency_locations(id) ON UPDATE CASCADE ON DELETE CASCADE not valid;

alter table "public"."emergency_location_reviews" validate constraint "emergency_location_reviews_emergency_location_id_fkey";

set check_function_bodies = off;

create type "public"."location_review_status" as ("location_id" uuid, "is_expired" boolean);

CREATE OR REPLACE FUNCTION public.get_emergency_location_id(location text)
 RETURNS location_review_status
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
DECLARE
    result location_review_status;
    loc_record RECORD;
BEGIN
    -- Attempt to find the location in the emergency_locations table
    SELECT id, expire_at INTO loc_record
    FROM public.emergency_locations
    WHERE uuid = location;

    -- Check if the location was found
    IF loc_record IS NOT NULL THEN
        result.location_id := loc_record.id;
        result.is_expired := (loc_record.expire_at <= NOW());
    ELSE
        result.location_id := NULL;
        result.is_expired := NULL;
    END IF;

    RETURN result;
END
$function$
;

CREATE OR REPLACE FUNCTION public.upsert_emergency_location_reviews(locationid uuid, reviews jsonb)
 RETURNS void
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
DECLARE
  review JSONB;
BEGIN
  DELETE FROM public.emergency_location_reviews where emergency_location_id = locationId;
  FOR review IN SELECT * FROM jsonb_array_elements(reviews)
  LOOP
    INSERT INTO public.emergency_location_reviews 
    (
      author_name,
      author_url,
      language,
      original_language,
      profile_photo_url,
      rating,
      relative_time_description,
      text,
      time,
      translated,
      emergency_location_id
    ) VALUES (
      review ->> 'author_name',
      review ->> 'author_url',
      review ->> 'language',
      review ->> 'original_language',
      review ->> 'profile_photo_url',
      (review ->> 'rating')::integer,
      review ->> 'relative_time_description',
      review ->> 'text',
      (review ->> 'time')::bigint,
      (review ->> 'translated')::boolean,
      locationId
    );
  END LOOP;

  UPDATE public.emergency_locations
  SET expire_at = NOW() + interval '6 hours'
  WHERE id = locationId;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.handle_new_user()
 RETURNS trigger
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$DECLARE
  full_name_param TEXT;
  first_param TEXT;
  last_param TEXT;
  ref_id_param TEXT;
  ref_id_exists BOOLEAN;
BEGIN
  full_name_param = new.raw_user_meta_data->>'full_name';

  IF full_name_param ~ '^\w+\s\w+$' THEN
    -- Split full_name into first and last names
    first_param := initcap(split_part(full_name_param, ' ', 1)); -- Uppercase first letter of each word
    last_param := initcap(split_part(full_name_param, ' ', 2));  -- Uppercase first letter of each word
  ELSE
    RAISE EXCEPTION 'Invalid full name format. Please provide two words separated by a space.';
  END IF;

  LOOP
    -- Extract first characters from first and last names
    ref_id_param := upper(substr(first_param, 1, 1)) || upper(substr(last_param, 1, 1));

    -- Generate 6 random digits
    ref_id_param := ref_id_param || lpad(trunc(random() * 1000000)::text, 6, '0');

    -- Add "PP" to the ref_id_param
    ref_id_param := ref_id_param || 'PP';

    -- Check if ref_id_param already exists in the table
    SELECT EXISTS(SELECT 1 FROM public.profiles WHERE ref_id = ref_id_param) INTO ref_id_exists;

    -- If ref_id_param is unique, exit the loop
    EXIT WHEN NOT ref_id_exists;
  END LOOP;

  -- Check if member_id is defined
  IF new.raw_user_meta_data ? 'member_id' THEN
    insert into public.profiles (id, full_name, mobile_number, ref_id, ref_by_id)
    values (
      new.id,
      new.raw_user_meta_data->>'full_name',
      new.raw_user_meta_data->>'mobile_number',
      ref_id_param,
      (new.raw_user_meta_data->>'member_id')::UUID
    );
  ELSE
    insert into public.profiles (id, full_name, mobile_number, ref_id)
    values (
      new.id,
      new.raw_user_meta_data->>'full_name',
      new.raw_user_meta_data->>'mobile_number',
      ref_id_param
    );
  END IF;

  return new;
END;
$function$
;

grant delete on table "public"."emergency_location_reviews" to "anon";

grant insert on table "public"."emergency_location_reviews" to "anon";

grant references on table "public"."emergency_location_reviews" to "anon";

grant select on table "public"."emergency_location_reviews" to "anon";

grant trigger on table "public"."emergency_location_reviews" to "anon";

grant truncate on table "public"."emergency_location_reviews" to "anon";

grant update on table "public"."emergency_location_reviews" to "anon";

grant delete on table "public"."emergency_location_reviews" to "authenticated";

grant insert on table "public"."emergency_location_reviews" to "authenticated";

grant references on table "public"."emergency_location_reviews" to "authenticated";

grant select on table "public"."emergency_location_reviews" to "authenticated";

grant trigger on table "public"."emergency_location_reviews" to "authenticated";

grant truncate on table "public"."emergency_location_reviews" to "authenticated";

grant update on table "public"."emergency_location_reviews" to "authenticated";

grant delete on table "public"."emergency_location_reviews" to "service_role";

grant insert on table "public"."emergency_location_reviews" to "service_role";

grant references on table "public"."emergency_location_reviews" to "service_role";

grant select on table "public"."emergency_location_reviews" to "service_role";

grant trigger on table "public"."emergency_location_reviews" to "service_role";

grant truncate on table "public"."emergency_location_reviews" to "service_role";

grant update on table "public"."emergency_location_reviews" to "service_role";

grant delete on table "public"."emergency_locations" to "anon";

grant insert on table "public"."emergency_locations" to "anon";

grant references on table "public"."emergency_locations" to "anon";

grant select on table "public"."emergency_locations" to "anon";

grant trigger on table "public"."emergency_locations" to "anon";

grant truncate on table "public"."emergency_locations" to "anon";

grant update on table "public"."emergency_locations" to "anon";

grant delete on table "public"."emergency_locations" to "authenticated";

grant insert on table "public"."emergency_locations" to "authenticated";

grant references on table "public"."emergency_locations" to "authenticated";

grant select on table "public"."emergency_locations" to "authenticated";

grant trigger on table "public"."emergency_locations" to "authenticated";

grant truncate on table "public"."emergency_locations" to "authenticated";

grant update on table "public"."emergency_locations" to "authenticated";

grant delete on table "public"."emergency_locations" to "service_role";

grant insert on table "public"."emergency_locations" to "service_role";

grant references on table "public"."emergency_locations" to "service_role";

grant select on table "public"."emergency_locations" to "service_role";

grant trigger on table "public"."emergency_locations" to "service_role";

grant truncate on table "public"."emergency_locations" to "service_role";

grant update on table "public"."emergency_locations" to "service_role";


