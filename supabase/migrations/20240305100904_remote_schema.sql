alter table "public"."profiles" add column "airing_cupboard_cylinder" text;

alter table "public"."profiles" add column "any_cover" text;

alter table "public"."profiles" add column "bathrooms_count" smallint;

alter table "public"."profiles" add column "boiler_age" text;

alter table "public"."profiles" add column "boiler_fuel_source" text;

alter table "public"."profiles" add column "boiler_serviced_recently" text;

alter table "public"."profiles" add column "carbon_monoxide_alarm" text;

alter table "public"."profiles" add column "home_ownership_status" text;

alter table "public"."profiles" add column "power_flush_carried_out" text;

alter table "public"."profiles" add column "property_type" text;

alter table "public"."profiles" add column "radiators_count" smallint;

alter table "public"."profiles" add column "smart_thermostat" text;

alter table "public"."profiles" add column "tanks_in_loft" text;

alter table "public"."profiles" add column "thermostat_radiators_valves" text;

CREATE TRIGGER zoho_sync_wh AFTER INSERT OR UPDATE ON public.profiles FOR EACH ROW EXECUTE FUNCTION supabase_functions.http_request('https://zlldaikdinmwoxyqkxgc.supabase.co/functions/v1/zoho_sync', 'POST', '{"Content-type":"application/json","Authorization":"Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InpsbGRhaWtkaW5td294eXFreGdjIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTcwODA3MTkxMywiZXhwIjoyMDIzNjQ3OTEzfQ.W-HKZmL9CcxjtAtdOMaAQE0bFgmKY7l-tev-Xe5jnrk"}', '{}', '1000');


