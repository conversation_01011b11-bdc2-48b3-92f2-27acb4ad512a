drop trigger if exists "profiles_operation_trigger" on "public"."profiles";

create table "public"."plan_purchases" (
    "id" bigint generated by default as identity not null,
    "starts_at" timestamp without time zone not null,
    "ends_at" timestamp without time zone,
    "plan_name" text,
    "user_id" uuid,
    "duration_months" smallint
);


alter table "public"."plan_purchases" enable row level security;

create table "public"."withdrawal_requests" (
    "id" uuid not null default gen_random_uuid(),
    "amount_to_withdraw" bigint not null,
    "sort_code" text,
    "account_number" text,
    "account_holder" text,
    "created_at" timestamp without time zone default now(),
    "user_id" uuid
);


alter table "public"."withdrawal_requests" enable row level security;

alter table "public"."profiles" add column "customer_id" text;

CREATE UNIQUE INDEX plan_purchases_pkey ON public.plan_purchases USING btree (id);

CREATE UNIQUE INDEX withdrawal_requests_pkey ON public.withdrawal_requests USING btree (id);

alter table "public"."plan_purchases" add constraint "plan_purchases_pkey" PRIMARY KEY using index "plan_purchases_pkey";

alter table "public"."withdrawal_requests" add constraint "withdrawal_requests_pkey" PRIMARY KEY using index "withdrawal_requests_pkey";

alter table "public"."plan_purchases" add constraint "plan_purchases_user_id_fkey" FOREIGN KEY (user_id) REFERENCES profiles(id) ON UPDATE CASCADE ON DELETE CASCADE not valid;

alter table "public"."plan_purchases" validate constraint "plan_purchases_user_id_fkey";

alter table "public"."withdrawal_requests" add constraint "withdrawal_requests_user_id_fkey" FOREIGN KEY (user_id) REFERENCES profiles(id) ON UPDATE CASCADE ON DELETE CASCADE not valid;

alter table "public"."withdrawal_requests" validate constraint "withdrawal_requests_user_id_fkey";

set check_function_bodies = off;

CREATE OR REPLACE FUNCTION public.create_withdrawal_request(user_id uuid, amount_to_withdraw integer, sort_code text, account_number text, account_holder text)
 RETURNS integer
 LANGUAGE plpgsql
AS $function$

declare
  user_id_exist uuid;

begin
  select id
  into user_id_exist
  from public.profiles
  where id = user_id;

  -- If somehow passed user_id is unknown to database - return error code - 0
  if user_id_exist is null then
    return 0;
  end if;

  -- Insert withdrawal_request
  insert into public.withdrawal_requests (
    amount_to_withdraw,
    sort_code,
    account_number,
    account_holder,
    user_id
  ) values (
    amount_to_withdraw,
    sort_code,
    account_number,
    account_holder,
    user_id
  );

  -- Decrement from user's available_balance
  update public.profiles
  set available_balance = available_balance - amount_to_withdraw
  where id = user_id;

  return 1;
end;
$function$
;

CREATE OR REPLACE FUNCTION public.handle_new_user()
 RETURNS trigger
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
begin
  insert into public.profiles (id, full_name, date_of_birth, mobile_number, post_code, address_details, residential_status, ref_id, customer_id)
  values (
    new.id,
    new.raw_user_meta_data->>'full_name',
    (new.raw_user_meta_data->>'date_of_birth')::date,
    new.raw_user_meta_data->>'mobile_number',
    new.raw_user_meta_data->>'post_code',
    new.raw_user_meta_data->>'address_details',
    new.raw_user_meta_data->>'residential_status',
    new.raw_user_meta_data->>'ref_id',
    new.raw_user_meta_data->>'customer_id'
    );
  return new;
end;
$function$
;

CREATE OR REPLACE FUNCTION public.increment_available_balance(value integer, user_id uuid)
 RETURNS void
 LANGUAGE sql
AS $function$
  update public.profiles
  set available_balance = available_balance + value
  where id = user_id
$function$
;

grant delete on table "public"."plan_purchases" to "anon";

grant insert on table "public"."plan_purchases" to "anon";

grant references on table "public"."plan_purchases" to "anon";

grant select on table "public"."plan_purchases" to "anon";

grant trigger on table "public"."plan_purchases" to "anon";

grant truncate on table "public"."plan_purchases" to "anon";

grant update on table "public"."plan_purchases" to "anon";

grant delete on table "public"."plan_purchases" to "authenticated";

grant insert on table "public"."plan_purchases" to "authenticated";

grant references on table "public"."plan_purchases" to "authenticated";

grant select on table "public"."plan_purchases" to "authenticated";

grant trigger on table "public"."plan_purchases" to "authenticated";

grant truncate on table "public"."plan_purchases" to "authenticated";

grant update on table "public"."plan_purchases" to "authenticated";

grant delete on table "public"."plan_purchases" to "service_role";

grant insert on table "public"."plan_purchases" to "service_role";

grant references on table "public"."plan_purchases" to "service_role";

grant select on table "public"."plan_purchases" to "service_role";

grant trigger on table "public"."plan_purchases" to "service_role";

grant truncate on table "public"."plan_purchases" to "service_role";

grant update on table "public"."plan_purchases" to "service_role";

grant delete on table "public"."withdrawal_requests" to "anon";

grant insert on table "public"."withdrawal_requests" to "anon";

grant references on table "public"."withdrawal_requests" to "anon";

grant select on table "public"."withdrawal_requests" to "anon";

grant trigger on table "public"."withdrawal_requests" to "anon";

grant truncate on table "public"."withdrawal_requests" to "anon";

grant update on table "public"."withdrawal_requests" to "anon";

grant delete on table "public"."withdrawal_requests" to "authenticated";

grant insert on table "public"."withdrawal_requests" to "authenticated";

grant references on table "public"."withdrawal_requests" to "authenticated";

grant select on table "public"."withdrawal_requests" to "authenticated";

grant trigger on table "public"."withdrawal_requests" to "authenticated";

grant truncate on table "public"."withdrawal_requests" to "authenticated";

grant update on table "public"."withdrawal_requests" to "authenticated";

grant delete on table "public"."withdrawal_requests" to "service_role";

grant insert on table "public"."withdrawal_requests" to "service_role";

grant references on table "public"."withdrawal_requests" to "service_role";

grant select on table "public"."withdrawal_requests" to "service_role";

grant trigger on table "public"."withdrawal_requests" to "service_role";

grant truncate on table "public"."withdrawal_requests" to "service_role";

grant update on table "public"."withdrawal_requests" to "service_role";

create policy "Enable insert for authenticated users only"
on "public"."withdrawal_requests"
as permissive
for insert
to authenticated
with check (true);


CREATE TRIGGER on_withdrawal_request_insert AFTER INSERT ON public.withdrawal_requests FOR EACH ROW EXECUTE FUNCTION supabase_functions.http_request('http://host.docker.internal:54321/functions/v1/zoho_sync/withdrawal_requests', 'POST', '{"Content-type":"application/json","Authorization":"Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImV4cCI6MTk4MzgxMjk5Nn0.EGIM96RAZx35lJzdJsyH-qQwv8Hdp7fsn3W0YpN81IU"}', '{}', '1000');

CREATE TRIGGER profiles_operation_trigger AFTER INSERT OR UPDATE ON public.profiles FOR EACH ROW EXECUTE FUNCTION supabase_functions.http_request('https://zlldaikdinmwoxyqkxgc.supabase.co/functions/v1/zoho_sync', 'POST', '{"Content-type":"application/json","Authorization":"Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InpsbGRhaWtkaW5td294eXFreGdjIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTcwODA3MTkxMywiZXhwIjoyMDIzNjQ3OTEzfQ.W-HKZmL9CcxjtAtdOMaAQE0bFgmKY7l-tev-Xe5jnrk"}', '{}', '1000');
ALTER TABLE "public"."profiles" DISABLE TRIGGER "profiles_operation_trigger";