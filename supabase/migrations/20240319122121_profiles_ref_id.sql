drop trigger if exists "profiles_operation_trigger" on "public"."profiles";

alter table "public"."profiles" add column "ref_id" text not null;

CREATE UNIQUE INDEX profiles_ref_id_key ON public.profiles USING btree (ref_id);

alter table "public"."profiles" add constraint "profiles_ref_id_key" UNIQUE using index "profiles_ref_id_key";

set check_function_bodies = off;

CREATE OR REPLACE FUNCTION public.handle_new_user()
 RETURNS trigger
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
begin
  insert into public.profiles (id, full_name, date_of_birth, mobile_number, post_code, address_details, residential_status, ref_id)
  values (
    new.id,
    new.raw_user_meta_data->>'full_name',
    (new.raw_user_meta_data->>'date_of_birth')::date,
    new.raw_user_meta_data->>'mobile_number',
    new.raw_user_meta_data->>'post_code',
    new.raw_user_meta_data->>'address_details',
    new.raw_user_meta_data->>'residential_status',
    new.raw_user_meta_data->>'ref_id'
    );
  return new;
end;
$function$
;