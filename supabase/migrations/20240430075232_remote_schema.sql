drop trigger if exists "profiles_operation_trigger" on "public"."profiles";

drop trigger if exists "on_withdrawal_request_insert" on "public"."withdrawal_requests";

drop policy "Public profiles are viewable by everyone." on "public"."profiles";

drop policy "Users can update own profile." on "public"."profiles";

drop policy "select_own_transactions" on "public"."transactions";

create table "public"."earn_program_tasks" (
    "id" uuid not null default gen_random_uuid(),
    "user_id" uuid,
    "status" text,
    "task" text,
    "record_id" text,
    "created_at" timestamp with time zone not null default now()
);


alter table "public"."earn_program_tasks" enable row level security;

CREATE UNIQUE INDEX earn_program_tasks_pkey ON public.earn_program_tasks USING btree (id);

alter table "public"."earn_program_tasks" add constraint "earn_program_tasks_pkey" PRIMARY KEY using index "earn_program_tasks_pkey";

alter table "public"."earn_program_tasks" add constraint "earn_program_tasks_user_id_fkey" FOREIGN KEY (user_id) REFERENCES profiles(id) ON UPDATE CASCADE ON DELETE CASCADE not valid;

alter table "public"."earn_program_tasks" validate constraint "earn_program_tasks_user_id_fkey";

set check_function_bodies = off;

CREATE OR REPLACE FUNCTION public.create_withdrawal_request(user_id uuid, amount_to_withdraw integer, sort_code text, account_number text, account_holder text)
 RETURNS integer
 LANGUAGE plpgsql
AS $function$

declare
  user_id_exist uuid;

begin
  select id
  into user_id_exist
  from public.profiles
  where id = user_id;

  -- If somehow passed user_id is unknown to database - return error code - 0
  if user_id_exist is null then
    return 0;
  end if;

  -- Insert withdrawal_request
  insert into public.withdrawal_requests (
    amount_to_withdraw,
    sort_code,
    account_number,
    account_holder,
    user_id
  ) values (
    amount_to_withdraw,
    sort_code,
    account_number,
    account_holder,
    user_id
  );

  -- Decrement from user's available_balance
  update public.profiles
  set available_balance = available_balance - amount_to_withdraw
  where id = user_id;

  return 1;
end;
$function$
;

CREATE OR REPLACE FUNCTION public.handle_new_user()
 RETURNS trigger
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
begin
  insert into public.profiles (id, full_name, date_of_birth, mobile_number, post_code, address_details, residential_status, ref_id, customer_id)
  values (
    new.id,
    new.raw_user_meta_data->>'full_name',
    (new.raw_user_meta_data->>'date_of_birth')::date,
    new.raw_user_meta_data->>'mobile_number',
    new.raw_user_meta_data->>'post_code',
    new.raw_user_meta_data->>'address_details',
    new.raw_user_meta_data->>'residential_status',
    new.raw_user_meta_data->>'ref_id',
    new.raw_user_meta_data->>'customer_id'
    );
  return new;
end;
$function$
;

CREATE OR REPLACE FUNCTION public.increment_available_balance(value integer, user_id uuid)
 RETURNS void
 LANGUAGE sql
AS $function$
  update public.profiles
  set available_balance = available_balance + value
  where id = user_id
$function$
;

grant delete on table "public"."earn_program_tasks" to "anon";

grant insert on table "public"."earn_program_tasks" to "anon";

grant references on table "public"."earn_program_tasks" to "anon";

grant select on table "public"."earn_program_tasks" to "anon";

grant trigger on table "public"."earn_program_tasks" to "anon";

grant truncate on table "public"."earn_program_tasks" to "anon";

grant update on table "public"."earn_program_tasks" to "anon";

grant delete on table "public"."earn_program_tasks" to "authenticated";

grant insert on table "public"."earn_program_tasks" to "authenticated";

grant references on table "public"."earn_program_tasks" to "authenticated";

grant select on table "public"."earn_program_tasks" to "authenticated";

grant trigger on table "public"."earn_program_tasks" to "authenticated";

grant truncate on table "public"."earn_program_tasks" to "authenticated";

grant update on table "public"."earn_program_tasks" to "authenticated";

grant delete on table "public"."earn_program_tasks" to "service_role";

grant insert on table "public"."earn_program_tasks" to "service_role";

grant references on table "public"."earn_program_tasks" to "service_role";

grant select on table "public"."earn_program_tasks" to "service_role";

grant trigger on table "public"."earn_program_tasks" to "service_role";

grant truncate on table "public"."earn_program_tasks" to "service_role";

grant update on table "public"."earn_program_tasks" to "service_role";

create policy "Select only own profile"
on "public"."profiles"
as permissive
for select
to authenticated
using ((( SELECT auth.uid() AS uid) = id));


create policy "Users can update own profile."
on "public"."profiles"
as permissive
for update
to public
using ((( SELECT auth.uid() AS uid) = id));


create policy "select_own_transactions"
on "public"."transactions"
as permissive
for select
to authenticated
using ((( SELECT auth.uid() AS uid) = user_id));


CREATE TRIGGER profiles_operation_trigger AFTER INSERT OR UPDATE ON public.profiles FOR EACH ROW EXECUTE FUNCTION supabase_functions.http_request('https://zlldaikdinmwoxyqkxgc.supabase.co/functions/v1/zoho_sync', 'POST', '{"Content-type":"application/json","Authorization":"Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InpsbGRhaWtkaW5td294eXFreGdjIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTcwODA3MTkxMywiZXhwIjoyMDIzNjQ3OTEzfQ.W-HKZmL9CcxjtAtdOMaAQE0bFgmKY7l-tev-Xe5jnrk"}', '{}', '1000');

CREATE TRIGGER on_withdrawal_request_insert AFTER INSERT ON public.withdrawal_requests FOR EACH ROW EXECUTE FUNCTION supabase_functions.http_request('https://zlldaikdinmwoxyqkxgc.supabase.co/functions/v1/zoho_sync/withdrawal_requests', 'POST', '{"Content-type":"application/json","Authorization":"Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImV4cCI6MTk4MzgxMjk5Nn0.EGIM96RAZx35lJzdJsyH-qQwv8Hdp7fsn3W0YpN81IU"}', '{}', '1000');


