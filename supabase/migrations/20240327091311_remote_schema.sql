create table "public"."transactions" (
    "id" bigint generated by default as identity not null,
    "user_id" uuid default gen_random_uuid(),
    "status" text,
    "zoho_record_id" text,
    "from" text,
    "amount" bigint
);


alter table "public"."transactions" enable row level security;

CREATE UNIQUE INDEX transactions_pkey ON public.transactions USING btree (id);

alter table "public"."transactions" add constraint "transactions_pkey" PRIMARY KEY using index "transactions_pkey";

alter table "public"."transactions" add constraint "public_transactions_user_id_fkey" FOREIGN KEY (user_id) REFERENCES profiles(id) ON UPDATE CASCADE ON DELETE CASCADE not valid;

alter table "public"."transactions" validate constraint "public_transactions_user_id_fkey";

set check_function_bodies = off;

CREATE OR REPLACE FUNCTION public.increment_available_balance(value integer, user_id uuid)
 RETURNS void
 LANGUAGE sql
AS $function$
  update public.profiles
  set available_balance = available_balance + value
  where id = user_id
$function$
;

CREATE OR REPLACE FUNCTION public.handle_new_user()
 <PERSON><PERSON><PERSON>NS trigger
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
begin
  insert into public.profiles (id, full_name, date_of_birth, mobile_number, post_code, address_details, residential_status, ref_id)
  values (
    new.id,
    new.raw_user_meta_data->>'full_name',
    (new.raw_user_meta_data->>'date_of_birth')::date,
    new.raw_user_meta_data->>'mobile_number',
    new.raw_user_meta_data->>'post_code',
    new.raw_user_meta_data->>'address_details',
    new.raw_user_meta_data->>'residential_status',
    new.raw_user_meta_data->>'ref_id'
    );
  return new;
end;
$function$
;

grant delete on table "public"."transactions" to "anon";

grant insert on table "public"."transactions" to "anon";

grant references on table "public"."transactions" to "anon";

grant select on table "public"."transactions" to "anon";

grant trigger on table "public"."transactions" to "anon";

grant truncate on table "public"."transactions" to "anon";

grant update on table "public"."transactions" to "anon";

grant delete on table "public"."transactions" to "authenticated";

grant insert on table "public"."transactions" to "authenticated";

grant references on table "public"."transactions" to "authenticated";

grant select on table "public"."transactions" to "authenticated";

grant trigger on table "public"."transactions" to "authenticated";

grant truncate on table "public"."transactions" to "authenticated";

grant update on table "public"."transactions" to "authenticated";

grant delete on table "public"."transactions" to "service_role";

grant insert on table "public"."transactions" to "service_role";

grant references on table "public"."transactions" to "service_role";

grant select on table "public"."transactions" to "service_role";

grant trigger on table "public"."transactions" to "service_role";

grant truncate on table "public"."transactions" to "service_role";

grant update on table "public"."transactions" to "service_role";


